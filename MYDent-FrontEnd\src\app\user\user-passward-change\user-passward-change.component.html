<div class="page-background">
  <div class="rec1"></div>
  <div class="rec2"></div>
  <div class="verification-container">
    <div class="verification-content">
      <h2>{{ userType }} Email Verification</h2>

      <p *ngIf="loading">Loading...</p>
      
      <p *ngIf="!loading">
        <ng-container *ngIf="message === 'Email verified successfully!'">
          Thank you for verifying your email!<br>
          You can now Create your New password.
        </ng-container>
        
        <ng-container *ngIf="message === 'Your email is already verified.'">
          Your email is already verified.<br>
          You can now Create your New password.
        </ng-container>
        
        <ng-container *ngIf="message !== 'Email verified successfully!' && message !== 'Your email is already verified.'">
          {{ message }}
        </ng-container>
      </p>
<form [formGroup]="resetForm" (ngSubmit)="resect()">
  <!-- Password -->
  <div class="col-12 mb-3">
    <label for="password" class="input-label">New Password</label>
    <input
      type="password"
      id="password"
      formControlName="password"
      class="form-control"
    />

    <div class="px-1" style="font-weight: 500;">
      <small
        class="text-danger"
        *ngIf="resetForm.get('password')?.hasError('required') && resetForm.get('password')?.touched"
      >
        Password is required.
      </small>
      <small
        class="text-danger"
        *ngIf="resetForm.get('password')?.hasError('minlength') && resetForm.get('password')?.touched"
      >
        Must be at least 6 characters.
      </small>
      <small
        class="text-danger"
        *ngIf="resetForm.get('password')?.hasError('pattern') && resetForm.get('password')?.touched"
      >
        Must contain at least one uppercase letter and one number.
      </small>
    </div>
  </div>

  <!-- Confirm Password -->
  <div class="col-12 mb-3">
    <label for="confirmPassword" class="input-label">Confirm Password</label>
    <input
      type="password"
      id="confirmPassword"
      formControlName="confirmPassword"
      class="form-control"
    />

    <div class="px-1" style="font-weight: 500;">
      <small
        class="text-danger"
        *ngIf="resetForm.get('confirmPassword')?.hasError('required') && resetForm.get('confirmPassword')?.touched"
      >
        Confirm password is required.
      </small>

      <small
        class="text-danger"
        *ngIf="resetForm.hasError('passwordsMismatch') && resetForm.get('confirmPassword')?.touched"
      >
        Passwords do not match.
      </small>
    </div>
  </div>

  <button type="submit" class=" mt-2">Reset Password</button>
</form>

    </div>
  </div>
</div>
