package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.io.Serializable;

@Entity
@Table(name= "doctor")
public class Doctor implements Serializable {

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "doctor_id")
	private Integer doctorId;

	@OneToOne(fetch = FetchType.EAGER)
	@NotFound(action = NotFoundAction.IGNORE)
	@JoinColumn(name = "user_id", referencedColumnName = "user_id")
	private User userId;

	@Column(name= "title")
	private String title;

	@Column(name= "first_name")
	private String firstName;
	
	@Column(name= "last_name")
	private String lastName;

	@Column(name= "reg_no")
	private String regNo;

	@Column(name= "telephone")
	private String telephone;

	@Column(name= "email")
	private String email;

	@Column(name= "qualifications")
	private String qualifications;

	public Doctor() {}

	public Doctor(Integer doctorId, User userId, String title, String firstName, String lastName, String regNo, String telephone, String email, String qualifications) {
		this.doctorId = doctorId;
		this.userId = userId;
		this.title = title;
		this.firstName = firstName;
		this.lastName = lastName;
		this.regNo = regNo;
		this.telephone = telephone;
		this.email = email;
		this.qualifications = qualifications;
	}

	public Doctor(User userId, String title, String firstName, String lastName, String regNo, String telephone, String email) {
		this.userId = userId;
		this.title = title;
		this.firstName = firstName;
		this.lastName = lastName;
		this.regNo = regNo;
		this.telephone = telephone;
		this.email = email;
	}

	public Integer getDoctorId() {
		return doctorId;
	}

	public void setDoctorId(Integer doctorId) {
		this.doctorId = doctorId;
	}

	public User getUserId() {
		return userId;
	}

	public void setUserId(User userId) {
		this.userId = userId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getRegNo() {
		return regNo;
	}

	public void setRegNo(String regNo) {
		this.regNo = regNo;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getQualifications() {
		return qualifications;
	}

	public void setQualifications(String qualifications) {
		this.qualifications = qualifications;
	}
}

