import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';

@Component({
  selector: 'app-user-forget-password',
  templateUrl: './user-forget-password.component.html',
  styleUrls: ['./user-forget-password.component.css'],
})
export class UserForgetPasswordComponent implements OnInit {
  loginForm: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
    });
  }

  ngOnInit(): void {}

  verifyUrlGenarate(): void {
    const emailValue = this.loginForm.get('email')?.value;
    this.userService.verifyEmailForget(emailValue).subscribe(
      (response: any) => {
        // <-- Type the response as any
        console.log(response);
        if (response.status === 'true') {
          this.router.navigate(['/']);
        } else {
          console.log('Password change failed or user not found');
          // Show error message
        }
      },
      (error) => {
        console.error('Error calling backend:', error);
        // Handle network/server error
      }
    );
  }
}
