{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"fms-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["sweetalert2"], "outputPath": "dist/fms-frontend", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/datatables.net-dt/css/jquery.dataTables.css", "node_modules/@ng-select/ng-select/themes/default.theme.css", "node_modules/datatables.net-bs/css/dataTables.bootstrap.min.css", "src/styles.css", "src/assets/css/style.css", "src/assets/vendor/bootstrap-icons/bootstrap-icons.css", "src/assets/vendor/boxicons/css/boxicons.min.css", "src/assets/vendor/quill/quill.snow.css", "src/assets/vendor/quill/quill.bubble.css", "src/assets/vendor/remixicon/remixicon.css"], "scripts": ["node_modules/jquery/dist/jquery.js", "node_modules/@popperjs/core/dist/umd/popper.min.js", "node_modules/bootstrap/dist/js/bootstrap.js", "node_modules/datatables.net/js/jquery.dataTables.js", "node_modules/datatables.net-bs/js/dataTables.bootstrap.min.js", "src/js/scripts.js", "src/js/all.min.js"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "6kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "fms-frontend:build:production"}, "development": {"browserTarget": "fms-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "fms-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "8daa9dcc-fa62-4135-b9eb-23f071e14e20"}}