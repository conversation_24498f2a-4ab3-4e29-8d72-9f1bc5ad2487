import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UserPasswardChangeComponent } from './user-passward-change.component';

describe('UserPasswardChangeComponent', () => {
  let component: UserPasswardChangeComponent;
  let fixture: ComponentFixture<UserPasswardChangeComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [UserPasswardChangeComponent]
    });
    fixture = TestBed.createComponent(UserPasswardChangeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
