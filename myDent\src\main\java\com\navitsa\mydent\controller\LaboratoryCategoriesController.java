package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.LaboratoryCategories;
import com.navitsa.mydent.services.LaboratoryCategoriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class LaboratoryCategoriesController {

    private final LaboratoryCategoriesService laboratoryCategoriesService;

    @Autowired
    public LaboratoryCategoriesController(LaboratoryCategoriesService laboratoryCategoriesService) {
        this.laboratoryCategoriesService = laboratoryCategoriesService;
    }

    @PostMapping("/saveLaboratoryService")
    public LaboratoryCategories saveService(@RequestBody LaboratoryCategories laboratoryCategories) {
        return laboratoryCategoriesService.saveLaboratoryCategory(laboratoryCategories);
    }

    @GetMapping("/getLaboratoryCategoriesList")
    public List<LaboratoryCategories> getAllCategories() {
        return laboratoryCategoriesService.getAllLaboratoryCategories();
    }

    @GetMapping("/getLaboratoryServiceById/{id}")
    public LaboratoryCategories getCategoryById(@PathVariable int id) {
        return laboratoryCategoriesService.getLaboratoryCategoryById(id);
    }
}
