{"ast": null, "code": "import { User } from \"../user/user\";\nexport class Supplier {\n  constructor() {\n    this.supplierId = 0;\n    this.userId = new User();\n    this.supplierCategories = new SupplierCategories();\n    this.name = '';\n    this.address = '';\n    this.city = '';\n    this.state = '';\n    this.country = '';\n    this.tele = '';\n    this.designation = '';\n    this.email = '';\n    this.web = '';\n    this.registeredDate = '';\n    this.latitude = '';\n    this.longitude = '';\n    this.contactPerson = '';\n  }\n}\nexport class SupplierCategories {\n  constructor() {\n    this.supplierCategoryId = 0;\n    this.supplierCategory = '';\n  }\n}\nexport class SupplierInventory {\n  constructor() {\n    this.supplierInventoryId = 0;\n    this.supplierId = new Supplier();\n    this.category = '';\n    this.subCategory = '';\n    this.description = '';\n    this.price = 0;\n    this.quantity = 0;\n    this.itemStatus = '';\n    this.itemImage = '';\n  }\n}\nexport class SupplierCategoryItemList {\n  constructor() {\n    this.id = 0;\n    this.name = '';\n  }\n}\nexport var ClinicSupplierOrderStatus;\n(function (ClinicSupplierOrderStatus) {\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"CLINIC_CREATED\"] = 0] = \"CLINIC_CREATED\";\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"SUPPLIER_APPROVED\"] = 1] = \"SUPPLIER_APPROVED\";\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"SUPPLIER_REJECTED\"] = 2] = \"SUPPLIER_REJECTED\";\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"CLINIC_REJECTED\"] = 3] = \"CLINIC_REJECTED\";\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"SUPPLIER_COMPLETED\"] = 4] = \"SUPPLIER_COMPLETED\";\n  ClinicSupplierOrderStatus[ClinicSupplierOrderStatus[\"SUPPLIER_PROCESSING\"] = 5] = \"SUPPLIER_PROCESSING\";\n})(ClinicSupplierOrderStatus || (ClinicSupplierOrderStatus = {}));", "map": {"version": 3, "names": ["User", "Supplier", "constructor", "supplierId", "userId", "supplierCategories", "SupplierCategories", "name", "address", "city", "state", "country", "tele", "designation", "email", "web", "registeredDate", "latitude", "longitude", "<PERSON><PERSON><PERSON>", "supplierCategoryId", "supplierCategory", "SupplierInventory", "supplierInventoryId", "category", "subCategory", "description", "price", "quantity", "itemStatus", "itemImage", "SupplierCategoryItemList", "id", "ClinicSupplierOrderStatus"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\supplier\\supplier.ts"], "sourcesContent": ["import { Clinic } from \"../clinic/clinic\";\r\nimport { User } from \"../user/user\";\r\n\r\nexport class Supplier {\r\n  supplierId: number = 0;\r\n  userId: User = new User();\r\n  supplierCategories: SupplierCategories = new SupplierCategories();\r\n  name: string = '';\r\n  address: string = '';\r\n  city: string = '';\r\n  state: string = '';\r\n  country: string = '';\r\n  tele: string = '';\r\n  designation: string = '';\r\n  email: string = '';\r\n  web: string = '';\r\n  registeredDate: string = '';\r\n  latitude: string = '';\r\n  longitude: string = '';\r\n  contactPerson: string = '';\r\n}\r\n\r\nexport class SupplierCategories {\r\n  supplierCategoryId: number = 0;\r\n  supplierCategory: string = '';\r\n}\r\n\r\nexport class SupplierInventory {\r\n  supplierInventoryId: number = 0;\r\n  supplierId: Supplier = new Supplier();\r\n  category: string = '';\r\n  subCategory: string = '';\r\n  description: string = '';\r\n  price: number = 0;\r\n  quantity: number = 0;\r\n  itemStatus: string = '';\r\n  itemImage: string = '';\r\n}\r\n\r\n\r\n\r\n\r\nexport class SupplierCategoryItemList{\r\n  id: number =0;\r\n  name: string='';\r\n}\r\n\r\nexport interface SupplierOrderHeader {\r\n  supplierOrderHeaderId: number;\r\n  clinic: Clinic;\r\n  supplier: Supplier;\r\n  orderStatus: ClinicSupplierOrderStatus;\r\n  createdDateTime: string;\r\n}\r\n\r\n\r\nexport interface SupplierOrderDetails {\r\n  supplierOrderDetailsId: number;\r\n  supplierInventory: SupplierInventory;\r\n  requestedItemQty: number;\r\n  approvedItemQty: number;\r\n  supplierOrderHeader: SupplierOrderHeader;\r\n}\r\n\r\nexport enum ClinicSupplierOrderStatus {\r\n  CLINIC_CREATED ,SUPPLIER_APPROVED,SUPPLIER_REJECTED,CLINIC_REJECTED,SUPPLIER_COMPLETED,SUPPLIER_PROCESSING\r\n}\r\n\r\nexport interface SupplierClinicQuoteDto{\r\n  orderHeaderId:number\r\n  quouteItemDetails:Map<number,QuoteDetails>;\r\n}\r\n\r\nexport interface QuoteDetails{\r\n  orderDetailId:number;\r\n  requestedinventoryQty:number;\r\n  acceptedinventoryQty:number;\r\n  inventorySellingPrice:number;\r\n}\r\n"], "mappings": "AACA,SAASA,IAAI,QAAQ,cAAc;AAEnC,OAAM,MAAOC,QAAQ;EAArBC,YAAA;IACE,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,MAAM,GAAS,IAAIJ,IAAI,EAAE;IACzB,KAAAK,kBAAkB,GAAuB,IAAIC,kBAAkB,EAAE;IACjE,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAW,EAAE;EAC5B;;AAEA,OAAM,MAAOb,kBAAkB;EAA/BJ,YAAA;IACE,KAAAkB,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;EAC/B;;AAEA,OAAM,MAAOC,iBAAiB;EAA9BpB,YAAA;IACE,KAAAqB,mBAAmB,GAAW,CAAC;IAC/B,KAAApB,UAAU,GAAa,IAAIF,QAAQ,EAAE;IACrC,KAAAuB,QAAQ,GAAW,EAAE;IACrB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAW,CAAC;IACjB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAW,EAAE;EACxB;;AAKA,OAAM,MAAOC,wBAAwB;EAArC7B,YAAA;IACE,KAAA8B,EAAE,GAAU,CAAC;IACb,KAAAzB,IAAI,GAAS,EAAE;EACjB;;AAmBA,WAAY0B,yBAEX;AAFD,WAAYA,yBAAyB;EACnCA,yBAAA,CAAAA,yBAAA,0CAAc;EAAEA,yBAAA,CAAAA,yBAAA,gDAAiB;EAACA,yBAAA,CAAAA,yBAAA,gDAAiB;EAACA,yBAAA,CAAAA,yBAAA,4CAAe;EAACA,yBAAA,CAAAA,yBAAA,kDAAkB;EAACA,yBAAA,CAAAA,yBAAA,oDAAmB;AAC5G,CAAC,EAFWA,yBAAyB,KAAzBA,yBAAyB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}