package com.navitsa.mydent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name= "company_master")
public class Company{
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "company_id")
	private Integer companyId;
	
	@Column(name= "company_name")
	private String companyName;
	
	@Column(name="address")
	private String address;
	
	@Column(name="city")
	private String city;
	
	@Column(name="country")
	private String country;
	
	@Column(name="state")
	private String state;
	
	@Column(name="postal_code")
	private String postalCode;
	
	@Column(name="telephone")
	private String telephone;
	
	@Column(name="email")
	private String email;
	
	@Column(name="web")
	private String web;
	
	@Column(name="status")
	private String status;

	@Column(name="created_user")
	private Integer createdUser;

    @Column(name="created_date")
    private String createdDate;
    
    @Column(name="created_time")
    private String createdTime;
    
    @Column(name="updated_user")
	private Integer updatedUser;
    
    @Column(name="updated_date")
    private String updatedDate;
    
    @Column(name="updated_time")
    private String updatedTime;
    
    public Company() {
    	
    }

	public Company(Integer companyId, String companyName, String address, String city, String country, String state,
			String postalCode, String telephone, String email, String web, String status, Integer createdUser,
			String createdDate, String createdTime, Integer updatedUser, String updatedDate, String updatedTime) {
		super();
		this.companyId = companyId;
		this.companyName = companyName;
		this.address = address;
		this.city = city;
		this.country = country;
		this.state = state;
		this.postalCode = postalCode;
		this.telephone = telephone;
		this.email = email;
		this.web = web;
		this.status = status;
		this.createdUser = createdUser;
		this.createdDate = createdDate;
		this.createdTime = createdTime;
		this.updatedUser = updatedUser;
		this.updatedDate = updatedDate;
		this.updatedTime = updatedTime;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getWeb() {
		return web;
	}

	public void setWeb(String web) {
		this.web = web;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getCreatedUser() {
		return createdUser;
	}

	public void setCreatedUser(Integer createdUser) {
		this.createdUser = createdUser;
	}

	public String getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(String createdDate) {
		this.createdDate = createdDate;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public Integer getUpdatedUser() {
		return updatedUser;
	}

	public void setUpdatedUser(Integer updatedUser) {
		this.updatedUser = updatedUser;
	}

	public String getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(String updatedDate) {
		this.updatedDate = updatedDate;
	}

	public String getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
	
	
	
	
}

