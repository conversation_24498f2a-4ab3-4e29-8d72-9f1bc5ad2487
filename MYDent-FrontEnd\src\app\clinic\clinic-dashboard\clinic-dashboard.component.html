<div class="row">
  <div class="col-12 g-0">
    <div class="appointment-dashboard-container ">
        <div class="row">
            <div class="col-12">
                <div class="row">
                    <div class="col-6">
                        <h2>Appointments</h2>
                    </div>
                    <div class="col-6 position-relative">
                        <app-primary-action-button
                            class="float-end"
                            (buttonClicked)="goToCreateNewAppointment()"
                            buttonText="New Appointment"
                            buttonUI="secondary"
                            buttonType="button"
                        >
                        </app-primary-action-button>
                    </div>
                </div>
                <hr>
            </div>
        </div>

      <div class="appointments-summary" *ngIf="appointments.length">
          <div class="appointment-status pending">
              <h2>Pending Appointments</h2>
              <p>{{ getPendingAppointmentsCount() }}</p>
          </div>
          <div class="pending-appointments-notification-middle">
            <p>You have {{ getPendingAppointmentsCount() }} pending appointments.</p>
        </div>
          <div class="appointment-status confirmed">
              <h2>Completed Appointments</h2>
              <p>{{ getCompletedAppointmentsCount() }}</p>
          </div>
      </div>

      <div class="pending-appointments-notification">
          <p>You have {{ getPendingAppointmentsCount() }} pending appointments.</p>
      </div>
      <div class="pending-appointments-list">
          <h6><strong>Pending Appointment Requests for Today</strong></h6>
      </div>
      <br>
      <div class="appointment-list">
        <ng-container *ngFor="let appointment of todayAppointments; let i = index">
            <div   class="appointment-request" (click)="openAppointmentDetails(appointment)" style="display: flex;">
                <div class="appointment-info">
                    <div ><h6 style="font-weight: 700; font-size: 17px;">{{ appointment.firstName }} {{ appointment.lastName }}</h6></div>
                    <div class="appointment-details">
                        <h6> {{ appointment.fromTime }} - {{ appointment.toTime }}</h6>
                        <h6>| {{ appointment.fromDate }}</h6>
                        <!-- <span>{{ appointment.timeAgo }}</span> -->
                        <!-- <h6>| {{ appointment.address }}</h6>
                        <h6>| {{appointment.telephone }}</h6> -->
                    </div>
                </div>

                
                <div class="appointment-actions" style=" margin-right: 5px;">
                    <div style="display: flex; width: 100%; justify-content: end; flex-direction: row;  gap: 20px; " class="accept-reject-buttons">
                        <!-- <button *ngIf="appointment.status == 'Pending'" style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;" (click)="acceptAppointment(appointment)">Accept</button>
                        <button *ngIf="appointment.status == 'Pending'" style="width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;"  (click)="rejectAppointment(appointment)">Reject</button> -->
                        <button *ngIf="appointment.status == 'Pending'" style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;" (click)="completedAppoinment(appointment)">Complete</button>
                        <button *ngIf="appointment.status == 'Pending'" style="width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;"  (click)="acceptAppointment(appointment)">No Show</button>

                        <button *ngIf="appointment.status == 'Completed'" style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;">Completed</button>
                        <button *ngIf="appointment.status == 'NoShow'" style="width: 120px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;">No Showing</button>


                        <!-- <button *ngIf="appointment.status == 'Rejected'"  style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white; cursor:default;" >Rejected</button>
                        <button *ngIf="appointment.status == 'Accepted'"  style="width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white; cursor:default;" >Accepted</button> -->
                    </div>
                    <!-- Display 'Accepted' or 'Rejected' after updating the status -->
            
                </div>
            </div>
        </ng-container>
    </div>

      <div class="pagination">
          <!-- Implement pagination controls here -->
      </div>
  </div>
  </div>
</div>
