package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface ClinicRepository extends JpaRepository<Clinic, Integer> {

    Optional<Clinic> findByName(String clinicName);
    
    @Query("SELECT c.clinicId FROM Clinic c WHERE c.userId.userId = :userId")
    Integer findClinicIdByUserId(@Param("userId") Integer userId);

    Optional<Clinic> findByUserId(User userId);

	Optional<Clinic> findByUserId_UserId(Integer userId);

    @Query("SELECT c FROM Clinic c WHERE c.userId.userId = :userId")
    Optional<Clinic> findClinicByUserId(@Param("userId") Integer userId);
    
 

    @Query("select cls.clinics from ClinicServices cls,ClinicSchedule cs where cls.clinics.clinicId=cs.clinics.clinicId "
  	      + "And cs.date =:date and cs.fromTime<:time and cs.toTime>:time "
  	      + "and cls.clinics.city = :city and cls.services.clinicServiceCategoryId=:serviceId")  // Integer comparison for service ID
  	List<Clinic> findClinicsByTimeDateCityAndService(@Param("date") String date, 
  	                                                 @Param("time") String time,
  	                                                 @Param("city") String city, 
  	                                                 @Param("serviceId") Integer serviceId);

    

    
  
}


