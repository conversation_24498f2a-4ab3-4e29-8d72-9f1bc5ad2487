{"name": "lc-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.0.0", "@angular/cdk": "^16.2.13", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/material": "^16.2.13", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@angular/service-worker": "^16.0.0", "@ng-select/ng-select": "^11.2.0", "@popperjs/core": "^2.10.2", "@tinymce/tinymce-angular": "^5.0.1", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.3", "datatables.net": "^1.13.8", "datatables.net-bs": "^1.12.1", "datatables.net-dt": "^1.12.1", "jquery": "^3.7.1", "ng2-file-upload": "^5.0.0", "ngx-cookie-service": "^16.1.0", "ngx-mat-select-search": "^7.0.5", "ngx-sweetalert2": "^0.2.7", "rxjs": "~7.8.0", "sweetalert2": "^11.14.4", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.3", "@angular/cli": "^16.2.12", "@angular/compiler-cli": "^16.0.0", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.0.2"}}