package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;

@Entity
@Table(name = "supplier_quote_head")
public class SupplierQuoteHead implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "supplier_quote_head_id")
    private Integer supplierQuoteHeadId;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "supplier_order_header_id",referencedColumnName = "supplier_order_header_id")
    private SupplierOrderHeader supplierOrderHeader;

    @NotNull
    @Column(name ="quote_file_name")
    private String quoteFileName;

    @NotNull
    @Column(name ="quote_file_path")
    private String quoteFilePath;


}
