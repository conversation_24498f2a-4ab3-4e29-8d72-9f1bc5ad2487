package com.navitsa.mydent.services;

import com.mailjet.client.errors.MailjetException;
import com.mailjet.client.MailjetClient;
import com.mailjet.client.MailjetRequest;
import com.mailjet.client.MailjetResponse;
import com.mailjet.client.resource.Emailv31;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.stream.Collectors;
import java.net.URISyntaxException;

@Service
public class EmailService {

    private final MailjetClient mailjetClient;

    // Constructor to initialize MailjetClient with your API keys
    public EmailService() {
        String apiKey = "4f5665d9c63c7c5779afa558e2a10cb9"; // Replace with your Mailjet public API key
        String secretKey = "74e33e0a1fb23d33a283bb8a3b87f459"; // Replace with your Mailjet private API key
        mailjetClient = new MailjetClient(apiKey, secretKey);
    }

    // Separate sendEmail method for general use
    public void sendEmail(String to, String subject, String htmlContent, String logoBase64) {
        try {
            // Build the email request
            MailjetRequest emailRequest = new MailjetRequest(Emailv31.resource)
                .property(Emailv31.MESSAGES, new JSONArray()
                    .put(new JSONObject()
                        .put(Emailv31.Message.FROM, new JSONObject()
                            .put("Email", "<EMAIL>") // Replace with your sender email
                            .put("Name", "MyDent")) // Replace with your sender name
                        .put(Emailv31.Message.TO, new JSONArray()
                            .put(new JSONObject()
                                .put("Email", to))) // Recipient email
                        .put(Emailv31.Message.SUBJECT, subject) // Email subject
                        .put(Emailv31.Message.HTMLPART, htmlContent) // HTML content
                        .put(Emailv31.Message.INLINEDATTACHMENTS, new JSONArray()
                            .put(new JSONObject()
                                .put("ContentType", "image/png")
                                .put("Filename", "logo.png")
                                .put("ContentID", "logo")  
                                .put("Base64Content", logoBase64)))));

            // Send the email
            MailjetResponse response = mailjetClient.post(emailRequest);

            // Logging the response
            System.out.println("Response Status: " + response.getStatus());
            System.out.println("Response Data: " + response.getData());

        } catch (MailjetException e) {
            System.err.println("MailjetException while sending email: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Method to send registration email
    public void sendRegistrationEmail(String to, String username, String verificationLink, String userType) {
        try {
            // Create a specialized email body structure for registration email
            String htmlContent = getRegistrationEmailContent(username, verificationLink, userType);

            // Set the subject line specific to the registration email
            String subject = userType + " Registration Successful";

            // Base64 encode the logo image
            String logoBase64 = encodeImageToBase64("LOGO-MYDENT.png");


            // Call the general sendEmail method
            sendEmail(to, subject, htmlContent, logoBase64);

        } catch (IOException e) {
            System.err.println("IOException while preparing email: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Method to create the registration email content with specific structure
    private String getRegistrationEmailContent(String username, String verificationLink, String userType) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<style>" +
                "body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }" +
                ".email-container { background-color: #ffffff; margin: 20px auto; padding: 20px; max-width: 600px; border: 1px solid #dddddd; border-radius: 5px; }" +
                ".header { text-align: center; padding: 10px 0; }" +
                ".header img { max-width: 150px; }" +
                ".header h1 { margin: 0; font-size: 24px; color: #333333; }" +
                ".content { background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center; }" +
                ".content h2 { color: #333333; font-size: 20px; }" +
                ".content p { color: #555555; font-size: 16px; }" +
                ".footer { text-align: center; font-size: 12px; color: #888888; padding: 10px 0; }" +
                ".button { display: inline-block; padding: 10px 20px; margin: 10px 0; font-size: 16px; color: #ffffff; background-color: #007bff; text-decoration: none; border-radius: 5px; }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<div class='email-container'>" +
                "<div class='header'>" +
                "<img src='cid:logo' alt='Promender Logo'>" +
                "<h1>Welcome to MyDent</h1>" +
                "</div>" +
                "<div class='content'>" +
                "<h2>Hello " + (username != null ? username : "") + ",</h2>" +
                "<p>Welcome to the MyDent community! We're excited to have you on board as a "+ userType +"</p>" +
                "<p>To get started, please verify your email address by clicking the button below:</p>" +
                "<p><a href='" + verificationLink + "' class='button'>Verify Email Address</a></p>" +
                "<p>If you have any questions or need assistance, don't hesitate to reach out. We're here to help!</p>" +
                "<p>Thank you for joining MyDent – we're looking forward to serving you!</p>" +
                "</div>" +
                "<div class='footer'>" +
                "<p>&copy; 2024 MyDent. All rights reserved.</p>" +
                "</div>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    // Utility to encode an image to Base64
    private String encodeImageToBase64(String imagePath) throws IOException {
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(imagePath)) {
            if (inputStream == null) {
                throw new IOException("Image not found: " + imagePath);
            }
            byte[] imageBytes = inputStream.readAllBytes();
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            throw new IOException("Failed to load image", e);
        }
    }

    // Method to send registration email
    public void forgetPassword(String to, String username, String verificationLink, String userType) {
        try {
            // Create a specialized email body structure for forget password email
            String htmlContent = getVerificationEmailDtails(username, verificationLink, userType);

            // Set the subject line specific to the forget password email
            String subject =  userType +" Verify Your Account";

            // Base64 encode the logo image
            String logoBase64 = encodeImageToBase64("LOGO-MYDENT.png");

            // Call the general sendEmail method
            sendEmail(to, subject, htmlContent, logoBase64);
        } catch (IOException e) {
            System.err.println("IOException while preparing email: " + e.getMessage());
            e.printStackTrace();
        }
    }

    // Method to create the forget password email content with specific structure
    private String getVerificationEmailDtails(String username, String verificationLink, String userType) {
        return  "<!DOCTYPE html>" +
                "<html lang='en'>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                "<title>MyDent Password Reset</title>" +
                "<style>" +
                "body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }" +
                ".email-container { background-color: #ffffff; margin: 20px auto; padding: 20px; max-width: 600px; border: 1px solid #dddddd; border-radius: 5px; }" +
                ".header { text-align: center; padding: 10px 0; }" +
                ".header img { max-width: 150px; }" +
                ".header h1 { margin: 0; font-size: 24px; color: #333333; }" +
                ".content { background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center; }" +
                ".content h2 { color: #333333; font-size: 20px; }" +
                ".content p { color: #555555; font-size: 16px; line-height: 1.5; }" +
                ".button { display: inline-block; padding: 10px 20px; margin: 15px 0; font-size: 16px; color: #ffffff; background-color: #007bff; text-decoration: none; border-radius: 5px; }" +
                ".footer { text-align: center; font-size: 12px; color: #888888; padding: 10px 0; }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<div class='email-container'>" +
                "  <div class='header'>" +
                "    <img src='cid:logo' alt='MyDent Logo'>" +
                "    <h1>MyDent Password Reset</h1>" +
                "  </div>" +
                "  <div class='content'>" +
                "    <h2>Hello " + (username != null ? username : "User") + ",</h2>" +
                "    <p>We received a request to reset the password for your " + userType + " account.</p>" +
                "    <p>To change your password, please click the button below:</p>" +
                "    <p><a href='" + verificationLink + "' class='button'>Reset Your Password</a></p>" +
                "    <p>If you did not request this, you can ignore this email. Your account remains safe.</p>" +
                "    <p>For any help, contact our support team — we're happy to assist!</p>" +
                "  </div>" +
                "  <div class='footer'>" +
                "    <p>&copy; 2025 MyDent. All rights reserved.</p>" +
                "  </div>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

}

// "<!DOCTYPE html>" +
//         "<html lang='en'>" +
//         "<head>" +
//         "<meta charset='UTF-8'>" +
//         "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
//         "<title>MyDent Password Reset</title>" +
//         "<style>" +
//         "body { font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0; }" +
//         ".email-container { background-color: #ffffff; margin: 20px auto; padding: 20px; max-width: 600px; border: 1px solid #dddddd; border-radius: 5px; }" +
//         ".header { text-align: center; padding: 10px 0; }" +
//         ".header img { max-width: 150px; }" +
//         ".header h1 { margin: 0; font-size: 24px; color: #333333; }" +
//         ".content { background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; text-align: center; }" +
//         ".content h2 { color: #333333; font-size: 20px; }" +
//         ".content p { color: #555555; font-size: 16px; line-height: 1.5; }" +
//         ".button { display: inline-block; padding: 10px 20px; margin: 15px 0; font-size: 16px; color: #ffffff; background-color: #007bff; text-decoration: none; border-radius: 5px; }" +
//         ".footer { text-align: center; font-size: 12px; color: #888888; padding: 10px 0; }" +
//         "</style>" +
//         "</head>" +
//         "<body>" +
//         "<div class='email-container'>" +
//         "  <div class='header'>" +
//         "    <img src='cid:logo' alt='MyDent Logo'>" +
//         "    <h1>MyDent Password Reset</h1>" +
//         "  </div>" +
//         "  <div class='content'>" +
//         "    <h2>Hello " + (username != null ? username : "User") + ",</h2>" +
//        "    <p>We received a request to reset the password for your " + userType + " account.</p>" +
//        "    <p>To change your password, please click the button below:</p>" +
//        "    <p><a href='" + verificationLink + "' class='button'>Reset Your Password</a></p>" +
//        "    <p>If you did not request this, you can ignore this email. Your account remains safe.</p>" +
//        "    <p>For any help, contact our support team — we're happy to assist!</p>" +
//        "  </div>" +
//        "  <div class='footer'>" +
//        "    <p>&copy; 2025 MyDent. All rights reserved.</p>" +
//        "  </div>" +
//        "</div>" +
//        "</body>" +
//        "</html>"