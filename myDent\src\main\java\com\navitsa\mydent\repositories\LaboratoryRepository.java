package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.entity.User;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface LaboratoryRepository extends JpaRepository<Laboratory, Integer> {
    Optional<Laboratory> findByName(String laboratoryName);
    
    Laboratory findByUserId(User userId);
}
