<app-default-navbar loggedUser="Book Appointment"/>

<div class="appointment-booking-container">
  <div class="header">
    <h2>Book Your Appointment</h2>
    <p>Fill in your details and select your preferred date to book an appointment</p>
  </div>

  <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" class="appointment-form">
    <!-- Patient Information Section -->
    <div class="form-section">
      <h3>Patient Information</h3>
      <div class="row">
        <div class="col-md-6">
          <label for="firstName" class="form-label">First Name *</label>
          <input 
            type="text" 
            id="firstName" 
            formControlName="firstName" 
            class="form-control"
            [class.is-invalid]="appointmentForm.get('firstName')?.invalid && appointmentForm.get('firstName')?.touched">
          <div class="invalid-feedback" *ngIf="appointmentForm.get('firstName')?.invalid && appointmentForm.get('firstName')?.touched">
            <small *ngIf="appointmentForm.get('firstName')?.errors?.['required']">First name is required</small>
            <small *ngIf="appointmentForm.get('firstName')?.errors?.['pattern']">Only letters and spaces allowed</small>
          </div>
        </div>
        
        <div class="col-md-6">
          <label for="lastName" class="form-label">Last Name *</label>
          <input 
            type="text" 
            id="lastName" 
            formControlName="lastName" 
            class="form-control"
            [class.is-invalid]="appointmentForm.get('lastName')?.invalid && appointmentForm.get('lastName')?.touched">
          <div class="invalid-feedback" *ngIf="appointmentForm.get('lastName')?.invalid && appointmentForm.get('lastName')?.touched">
            <small *ngIf="appointmentForm.get('lastName')?.errors?.['required']">Last name is required</small>
            <small *ngIf="appointmentForm.get('lastName')?.errors?.['pattern']">Only letters and spaces allowed</small>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label for="email" class="form-label">Email *</label>
          <input 
            type="email" 
            id="email" 
            formControlName="email" 
            class="form-control"
            [class.is-invalid]="appointmentForm.get('email')?.invalid && appointmentForm.get('email')?.touched">
          <div class="invalid-feedback" *ngIf="appointmentForm.get('email')?.invalid && appointmentForm.get('email')?.touched">
            <small *ngIf="appointmentForm.get('email')?.errors?.['required']">Email is required</small>
            <small *ngIf="appointmentForm.get('email')?.errors?.['email']">Please enter a valid email</small>
          </div>
        </div>
        
        <div class="col-md-6">
          <label for="telephone" class="form-label">Telephone Number *</label>
          <input 
            type="tel" 
            id="telephone" 
            formControlName="telephone" 
            class="form-control"
            [class.is-invalid]="appointmentForm.get('telephone')?.invalid && appointmentForm.get('telephone')?.touched">
          <div class="invalid-feedback" *ngIf="appointmentForm.get('telephone')?.invalid && appointmentForm.get('telephone')?.touched">
            <small *ngIf="appointmentForm.get('telephone')?.errors?.['required']">Telephone number is required</small>
            <small *ngIf="appointmentForm.get('telephone')?.errors?.['pattern']">Please enter a valid 10-digit phone number</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Appointment Details Section -->
    <div class="form-section">
      <h3>Appointment Details</h3>
      <div class="row">
        <div class="col-md-6">
          <label for="preferredService" class="form-label">Preferred Service *</label>
          <select 
            id="preferredService" 
            formControlName="preferredService" 
            class="form-control"
            [class.is-invalid]="appointmentForm.get('preferredService')?.invalid && appointmentForm.get('preferredService')?.touched">
            <option value="">Select a service</option>
            <option *ngFor="let service of services" [value]="service.clinicServiceCategoryId">
              {{service.serviceName}}
            </option>
          </select>
          <div class="invalid-feedback" *ngIf="appointmentForm.get('preferredService')?.invalid && appointmentForm.get('preferredService')?.touched">
            <small>Please select a service</small>
          </div>
        </div>
        
        <div class="col-md-6">
          <label for="preferredDate" class="form-label">Preferred Date *</label>
          <input 
            type="date" 
            id="preferredDate" 
            formControlName="preferredDate" 
            class="form-control"
            [min]="getTodayDate()"
            (change)="onDateChange()"
            [class.is-invalid]="appointmentForm.get('preferredDate')?.invalid && appointmentForm.get('preferredDate')?.touched">
          <div class="invalid-feedback" *ngIf="appointmentForm.get('preferredDate')?.invalid && appointmentForm.get('preferredDate')?.touched">
            <small>Please select a date</small>
          </div>
        </div>
      </div>

      <!-- Selected Doctor and Time Display -->
      <div class="row" *ngIf="selectedDoctor && selectedTimeSlot">
        <div class="col-12">
          <div class="selected-appointment-info">
            <h4>Selected Appointment</h4>
            <p><strong>Doctor:</strong> {{selectedDoctor.title}} {{selectedDoctor.firstName}} {{selectedDoctor.lastName}}</p>
            <p><strong>Time:</strong> {{selectedTimeSlot.displayTime}}</p>
            <p><strong>Date:</strong> {{selectedDate | date:'fullDate'}}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="form-actions">
      <button type="button" class="btn btn-secondary me-3" (click)="router.navigate(['/appointments'])">
        Cancel
      </button>
      <button type="submit" class="btn btn-primary" [disabled]="appointmentForm.invalid || !selectedDoctor">
        Book Appointment
      </button>
    </div>
  </form>
</div>

<!-- Doctor & Time Slot Selection Modal -->
<div class="modal fade show" [style.display]="showDoctorModal ? 'block' : 'none'" tabindex="-1" *ngIf="showDoctorModal">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Select Doctor & Time Slot</h5>
        <button type="button" class="btn-close" (click)="closeModal()"></button>
      </div>
      <div class="modal-body">
        <p>Available appointments for {{selectedDate | date:'fullDate'}}</p>
        
        <!-- Time slots will be populated here -->
        <div class="time-slots-container">
          <div *ngFor="let timeSlot of availableTimeSlots" class="time-slot-group">
            <h6>{{timeSlot.displayTime}}</h6>
            <div class="doctors-list">
              <div *ngFor="let doctor of timeSlot.availableDoctors" 
                   class="doctor-card" 
                   (click)="selectDoctorAndTimeSlot(doctor, timeSlot)">
                <div class="doctor-info">
                  <h6>{{doctor.title}} {{doctor.firstName}} {{doctor.lastName}}</h6>
                  <p>{{doctor.qualifications}}</p>
                </div>
                <button class="btn btn-sm btn-primary">Select</button>
              </div>
            </div>
          </div>
        </div>
        
        <div *ngIf="availableTimeSlots.length === 0" class="no-slots-message">
          <p>No available time slots for the selected date. Please choose a different date.</p>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-backdrop fade show" *ngIf="showDoctorModal"></div>
