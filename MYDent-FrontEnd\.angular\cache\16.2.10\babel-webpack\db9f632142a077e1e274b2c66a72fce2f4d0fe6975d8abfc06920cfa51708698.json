{"ast": null, "code": "export class UserTemp {\n  constructor() {\n    this.userTempId = 0;\n    this.mainName = '';\n    this.userEmail = '';\n    this.userPassword = '';\n  }\n}\nexport var UserTempType;\n(function (UserTempType) {\n  UserTempType[\"DOCTOR\"] = \"DOCTOR\";\n  UserTempType[\"CLINIC\"] = \"CLINIC\";\n  UserTempType[\"LABORATORY\"] = \"LABORATORY\";\n  UserTempType[\"SUPPLIER\"] = \"SUPPLIER\";\n  UserTempType[\"FUTURE_DENTIST\"] = \"FUTURE_DENTIST\";\n  UserTempType[\"ADMIN\"] = \"ADMIN\";\n  UserTempType[\"CUSTOMER\"] = \"CUSTOMER\";\n})(UserTempType || (UserTempType = {}));\nexport var UserTempStatus;\n(function (UserTempStatus) {\n  UserTempStatus[\"CREATED\"] = \"CREATED\";\n  UserTempStatus[\"SENT_USER_VERIFICATION\"] = \"SENT_USER_VERIFICATION\";\n  UserTempStatus[\"USER_VERIFIED\"] = \"USER_VERIFIED\";\n  UserTempStatus[\"ADMIN_APPROVED\"] = \"ADMIN_APPROVED\";\n  UserTempStatus[\"ADMIN_REJECTED\"] = \"ADMIN_REJECTED\";\n})(UserTempStatus || (UserTempStatus = {}));", "map": {"version": 3, "names": ["UserTemp", "constructor", "userTempId", "mainName", "userEmail", "userPassword", "UserTempType", "UserTempStatus"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\auth\\auth.ts"], "sourcesContent": ["export class UserTemp {\r\n  userTempId: number =0;\r\n  mainName: string = '';\r\n  additionalName?: string;\r\n  userEmail: string = '';\r\n  userPassword: string = '';\r\n  userTempType?: UserTempType\r\n  userTempStatus?: UserTempStatus;\r\n  userTitle?: string;\r\n  contactNumber?: string;\r\n  contactPerson?: string;\r\n  contactPersonDesignation?: string;\r\n  registrationNumber?: string;\r\n  address?: string;\r\n  district?: string;\r\n  city?: string;\r\n  state?: string;\r\n  createDateTime?: Date;\r\n  verificationToken?: string;\r\n}\r\n\r\nexport enum UserTempType {\r\n  DOCTOR = 'DOCTOR',\r\n  CLINIC = 'CLINIC',\r\n  LABORATORY = 'LABORATORY',\r\n  SUPPLIER = 'SUPPLIER',\r\n  FUTURE_DENTIST = 'FUTURE_DENTIST',\r\n  ADMIN = 'ADMIN',\r\n  CUSTOMER = 'CUSTOMER'\r\n}\r\n\r\nexport enum UserTempStatus {\r\n  CREATED = 'CREATED',\r\n  SENT_USER_VERIFICATION = 'SENT_USER_VERIFICATION',\r\n  USER_VERIFIED = 'USER_VERIFIED',\r\n  ADMIN_APPROVED = 'ADMIN_APPROVED',\r\n  ADMIN_REJECTED = 'ADMIN_REJECTED'\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,QAAQ;EAArBC,YAAA;IACE,KAAAC,UAAU,GAAU,CAAC;IACrB,KAAAC,QAAQ,GAAW,EAAE;IAErB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,YAAY,GAAW,EAAE;EAc3B;;AAEA,WAAYC,YAQX;AARD,WAAYA,YAAY;EACtBA,YAAA,qBAAiB;EACjBA,YAAA,qBAAiB;EACjBA,YAAA,6BAAyB;EACzBA,YAAA,yBAAqB;EACrBA,YAAA,qCAAiC;EACjCA,YAAA,mBAAe;EACfA,YAAA,yBAAqB;AACvB,CAAC,EARWA,YAAY,KAAZA,YAAY;AAUxB,WAAYC,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,uBAAmB;EACnBA,cAAA,qDAAiD;EACjDA,cAAA,mCAA+B;EAC/BA,cAAA,qCAAiC;EACjCA,cAAA,qCAAiC;AACnC,CAAC,EANWA,cAAc,KAAdA,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}