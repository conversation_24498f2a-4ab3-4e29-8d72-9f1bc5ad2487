{"ast": null, "code": "// import { User } from '../admin/components/user/user';\nimport { Clinic } from 'src/app/clinic/clinic';\nimport { Customer } from './customer';\nexport class Appointments {\n  constructor() {\n    this.appointmentId = 0;\n    // userId: number = 0;\n    this.firstName = '';\n    this.lastName = '';\n    this.address = '';\n    this.city = '';\n    this.state = '';\n    this.district = '';\n    this.telephone = '';\n    this.email = '';\n    this.preferredservice = '';\n    this.nearestCity = '';\n    this.fromDate = '';\n    this.toDate = '';\n    this.fromTime = '';\n    this.toTime = '';\n    this.clinics = new Clinic();\n    this.userName = '';\n    this.password = '';\n    this.status = '';\n    this.customer = new Customer(); // Reference to the Customer class\n  }\n}", "map": {"version": 3, "names": ["Clinic", "Customer", "Appointments", "constructor", "appointmentId", "firstName", "lastName", "address", "city", "state", "district", "telephone", "email", "preferredservice", "nearestCity", "fromDate", "toDate", "fromTime", "toTime", "clinics", "userName", "password", "status", "customer"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointments.ts"], "sourcesContent": ["// import { User } from '../admin/components/user/user';\r\nimport { Clinic } from 'src/app/clinic/clinic';\r\nimport { Customer } from './customer';\r\nimport { Doctor } from 'src/app/doctor/doctor';\r\n\r\nexport class Appointments {\r\n  appointmentId: number = 0;\r\n  // userId: number = 0;\r\n  firstName: string = '';\r\n  lastName: string='';\r\n  address: string = '';\r\n  city: string = '';\r\n  state: string = '';\r\n  district: string = '';\r\n  telephone: string = '';\r\n  email: string = '';\r\n  preferredservice: string = '';\r\n  nearestCity: string = '';\r\n  fromDate: string = '';\r\n  toDate: string = '';\r\n  fromTime: string = '';\r\n  toTime: string = '';\r\n  clinics: Clinic= new Clinic();\r\n  userName:string='';\r\n  password:string='';\r\n  status:string='';\r\n  customer: Customer = new Customer(); // Reference to the Customer class\r\n}\r\n\r\n\r\n"], "mappings": "AAAA;AACA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,QAAQ,QAAQ,YAAY;AAGrC,OAAM,MAAOC,YAAY;EAAzBC,YAAA;IACE,KAAAC,aAAa,GAAW,CAAC;IACzB;IACA,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAS,EAAE;IACnB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,OAAO,GAAU,IAAInB,MAAM,EAAE;IAC7B,KAAAoB,QAAQ,GAAQ,EAAE;IAClB,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,MAAM,GAAQ,EAAE;IAChB,KAAAC,QAAQ,GAAa,IAAItB,QAAQ,EAAE,CAAC,CAAC;EACvC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}