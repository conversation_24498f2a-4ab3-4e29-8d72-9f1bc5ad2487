<div style="padding: 20px; min-width: 400px">
  <h4 mat-dialog-title style="font-weight: 700">Appointment Request</h4>
  <br />
  <div mat-dialog-content style="display: flex; gap: 30px">
    <div style="display: flex; gap: 20px; align-items: flex-start">
      <div style="width: 150px; font-weight: 600; color: #333">
        <p>Name:</p>
        <p>Telephone:</p>
        <p>Requested Date:</p>
        <p>Requested Time:</p>
        <!-- <p>Address:</p> -->
      </div>

      <div style="flex: 1; color: #555">
        <p>{{ data.firstName }} {{ data.lastName }}</p>
        <p>{{ data.telephone }}</p>
        <p>{{ data.fromDate | date : "longDate" }}</p>
        <p>{{ data.fromTime }} - {{ data.toTime }}</p>
        <!-- <p>{{ data.address }}</p> -->
      </div>
    </div>
  </div>
  <div
    mat-dialog-actions
    align="end"
    style="
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 20px;
    "
  >
    <button
      mat-button
      style="
        width: 100px;
        border-radius: 50px;
        padding-block: 4px;
        background: linear-gradient(to right, #00c820, #0e6001);
        border: none;
        color: white;
      "
      (click)="accept()"
    >
      Accept
    </button>
    <button
      mat-button
      style="
        width: 100px;
        border-radius: 50px;
        padding-block: 4px;
        background: linear-gradient(to right, #fb751e, #b93426);
        border: none;
        color: white;
      "
      (click)="reject()"
    >
      Reject
    </button>
    <button
      mat-button
      (click)="close()"
      style="
        width: 100px;
        border-radius: 50px;
        padding-block: 4px;
        background: linear-gradient(to right, #fb751e, #b93426);
        border: none;
        color: white;
      "
    >
      Close
    </button>
  </div>
</div>
