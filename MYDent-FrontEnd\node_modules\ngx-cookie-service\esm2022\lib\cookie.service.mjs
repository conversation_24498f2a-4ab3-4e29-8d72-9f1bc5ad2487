// This service is based on the `ng2-cookies` package which sadly is not a service and does
// not use `DOCUMENT` injection and therefore doesn't work well with AoT production builds.
// Package: https://github.com/BCJTI/ng2-cookies
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import * as i0 from "@angular/core";
export class CookieService {
    constructor(document, 
    // Get the `PLATFORM_ID` so we can check if we're in a browser.
    platformId) {
        this.document = document;
        this.platformId = platformId;
        this.documentIsAccessible = isPlatformBrowser(this.platformId);
    }
    /**
     * Get cookie Regular Expression
     *
     * @param name Cookie name
     * @returns property RegExp
     *
     * @author: <PERSON>an <PERSON>
     * @since: 1.0.0
     */
    static getCookieRegExp(name) {
        const escapedName = name.replace(/([\[\]{}()|=;+?,.*^$])/gi, '\\$1');
        return new RegExp('(?:^' + escapedName + '|;\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');
    }
    /**
     * Gets the unencoded version of an encoded component of a Uniform Resource Identifier (URI).
     *
     * @param encodedURIComponent A value representing an encoded URI component.
     *
     * @returns The unencoded version of an encoded component of a Uniform Resource Identifier (URI).
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    static safeDecodeURIComponent(encodedURIComponent) {
        try {
            return decodeURIComponent(encodedURIComponent);
        }
        catch {
            // probably it is not uri encoded. return as is
            return encodedURIComponent;
        }
    }
    /**
     * Return `true` if {@link Document} is accessible, otherwise return `false`
     *
     * @param name Cookie name
     * @returns boolean - whether cookie with specified name exists
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    check(name) {
        if (!this.documentIsAccessible) {
            return false;
        }
        name = encodeURIComponent(name);
        const regExp = CookieService.getCookieRegExp(name);
        return regExp.test(this.document.cookie);
    }
    /**
     * Get cookies by name
     *
     * @param name Cookie name
     * @returns property value
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    get(name) {
        if (this.documentIsAccessible && this.check(name)) {
            name = encodeURIComponent(name);
            const regExp = CookieService.getCookieRegExp(name);
            const result = regExp.exec(this.document.cookie);
            return result[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';
        }
        else {
            return '';
        }
    }
    /**
     * Get all cookies in JSON format
     *
     * @returns all the cookies in json
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    getAll() {
        if (!this.documentIsAccessible) {
            return {};
        }
        const cookies = {};
        const document = this.document;
        if (document.cookie && document.cookie !== '') {
            document.cookie.split(';').forEach((currentCookie) => {
                const [cookieName, cookieValue] = currentCookie.split('=');
                cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);
            });
        }
        return cookies;
    }
    set(name, value, expiresOrOptions, path, domain, secure, sameSite) {
        if (!this.documentIsAccessible) {
            return;
        }
        if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {
            const optionsBody = {
                expires: expiresOrOptions,
                path,
                domain,
                secure,
                sameSite: sameSite ? sameSite : 'Lax',
            };
            this.set(name, value, optionsBody);
            return;
        }
        let cookieString = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';
        const options = expiresOrOptions ? expiresOrOptions : {};
        if (options.expires) {
            if (typeof options.expires === 'number') {
                const dateExpires = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);
                cookieString += 'expires=' + dateExpires.toUTCString() + ';';
            }
            else {
                cookieString += 'expires=' + options.expires.toUTCString() + ';';
            }
        }
        if (options.path) {
            cookieString += 'path=' + options.path + ';';
        }
        if (options.domain) {
            cookieString += 'domain=' + options.domain + ';';
        }
        if (options.secure === false && options.sameSite === 'None') {
            options.secure = true;
            console.warn(`[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` +
                `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`);
        }
        if (options.secure) {
            cookieString += 'secure;';
        }
        if (!options.sameSite) {
            options.sameSite = 'Lax';
        }
        cookieString += 'sameSite=' + options.sameSite + ';';
        this.document.cookie = cookieString;
    }
    /**
     * Delete cookie by name
     *
     * @param name   Cookie name
     * @param path   Cookie path
     * @param domain Cookie domain
     * @param secure Cookie secure flag
     * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    delete(name, path, domain, secure, sameSite = 'Lax') {
        if (!this.documentIsAccessible) {
            return;
        }
        const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');
        this.set(name, '', { expires: expiresDate, path, domain, secure, sameSite });
    }
    /**
     * Delete all cookies
     *
     * @param path   Cookie path
     * @param domain Cookie domain
     * @param secure Is the Cookie secure
     * @param sameSite Is the cookie same site
     *
     * @author: Stepan Suvorov
     * @since: 1.0.0
     */
    deleteAll(path, domain, secure, sameSite = 'Lax') {
        if (!this.documentIsAccessible) {
            return;
        }
        const cookies = this.getAll();
        for (const cookieName in cookies) {
            if (cookies.hasOwnProperty(cookieName)) {
                this.delete(cookieName, path, domain, secure, sameSite);
            }
        }
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "16.2.1", ngImport: i0, type: CookieService, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }
    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "16.2.1", ngImport: i0, type: CookieService, providedIn: 'root' }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "16.2.1", ngImport: i0, type: CookieService, decorators: [{
            type: Injectable,
            args: [{
                    providedIn: 'root',
                }]
        }], ctorParameters: function () { return [{ type: Document, decorators: [{
                    type: Inject,
                    args: [DOCUMENT]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [PLATFORM_ID]
                }] }]; } });
//# sourceMappingURL=data:application/json;base64,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