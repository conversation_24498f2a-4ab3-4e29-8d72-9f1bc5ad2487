{"ast": null, "code": "import { map, mapTo, of, tap } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport { User, UserCategory } from 'src/app/user/user';\nimport { Doctor } from 'src/app/doctor/doctor';\nimport Swal from 'sweetalert2';\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/doctor/doctor.service\";\nimport * as i4 from \"src/app/auth/auth.service\";\nimport * as i5 from \"src/app/user/user.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../core/default-navbar/default-navbar.component\";\nconst _c0 = [\"RegisterButton\"];\nfunction DoctorRegistrationComponent_div_17_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Title is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_17_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.doctorForm.get(\"title\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction DoctorRegistrationComponent_div_23_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"First Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_23_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"First Name can only contain letters.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_23_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵtemplate(2, DoctorRegistrationComponent_div_23_small_2_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.doctorForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.doctorForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction DoctorRegistrationComponent_div_28_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Last Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_28_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Last Name can only contain letters.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_28_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵtemplate(2, DoctorRegistrationComponent_div_28_small_2_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.doctorForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.doctorForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction DoctorRegistrationComponent_div_34_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"SLMC Register Number is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_34_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.doctorForm.get(\"regNo\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction DoctorRegistrationComponent_small_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.slmcNumberExistsMessage);\n  }\n}\nfunction DoctorRegistrationComponent_div_40_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Contact Number is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_40_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Invalid Contact number.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_40_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵtemplate(2, DoctorRegistrationComponent_div_40_small_2_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.doctorForm.get(\"telephone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r5.doctorForm.get(\"telephone\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction DoctorRegistrationComponent_div_46_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Email is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_46_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.doctorForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction DoctorRegistrationComponent_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.userEmailExistsMessage);\n  }\n}\nfunction DoctorRegistrationComponent_div_53_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Password is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_53_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Password must be at least 8 characters long.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_53_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_53_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵtemplate(2, DoctorRegistrationComponent_div_53_small_2_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtemplate(4, DoctorRegistrationComponent_div_53_small_4_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.doctorForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r8.doctorForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r8.doctorForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction DoctorRegistrationComponent_div_58_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Please re-enter the password.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DoctorRegistrationComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DoctorRegistrationComponent_div_58_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.doctorForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction DoctorRegistrationComponent_small_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 29);\n    i0.ɵɵtext(1, \"Password do not match.\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass DoctorRegistrationComponent {\n  constructor(fb, router, doctorService, authService, userService) {\n    this.fb = fb;\n    this.router = router;\n    this.doctorService = doctorService;\n    this.authService = authService;\n    this.userService = userService;\n    this.doctor = new Doctor();\n    this.user = new User();\n    this.userCategory = new UserCategory();\n    this.isEmailRegistered = false;\n    this.isSLMCRegistered = false;\n    this.userEmailExistsMessage = '';\n    this.slmcNumberExistsMessage = '';\n    // User temp\n    this.userTemp = new UserTemp();\n    this.doctorForm = this.fb.group({\n      title: ['', Validators.required],\n      firstName: ['', [Validators.required, Validators.pattern('^[a-zA-Z]*$')]],\n      lastName: ['', [Validators.required, Validators.pattern('^[a-zA-Z]*$')]],\n      regNo: ['', Validators.required],\n      telephone: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$')]],\n      confirmPassword: ['', Validators.required]\n    }, {\n      validator: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    localStorage.clear();\n    this.getUserCategoryFromDB();\n    this.userTemp.userTempType = UserTempType.DOCTOR;\n  }\n  ngAfterViewInit() {}\n  getUserCategoryFromDB() {\n    this.userService.getUserCategoryById(2).subscribe(response => {\n      this.userCategory = response;\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password?.value && confirmPassword?.value && (confirmPassword.dirty || confirmPassword.touched)) {\n      return password.value === confirmPassword.value ? null : {\n        mismatch: true\n      };\n    }\n    return null;\n  }\n  updateEmail() {\n    this.user.username = this.doctor.email;\n  }\n  setFirstName() {\n    this.user.firstName = this.doctor.firstName;\n  }\n  setLastName() {\n    this.user.lastName = this.doctor.lastName;\n  }\n  onUserRegister() {\n    if (this.userCategory == null) {\n      this.getUserCategoryFromDB();\n    }\n    this.user.userCategoryId = this.userCategory;\n    console.table(this.user);\n    return this.userService.register(this.user).pipe(tap(response => {\n      this.user.userId = response.id;\n      this.doctor.userId = this.user;\n    }, error => {\n      console.log(error);\n    }), mapTo(void 0));\n  }\n  onDoctorRegister() {\n    return this.doctorService.saveDoctor(this.doctor).pipe(tap(() => {\n      Swal.fire({\n        title: 'Registration Successful!',\n        text: 'Thank you for registering! Please verify your email to complete the login process.',\n        icon: 'success',\n        confirmButtonText: 'Continue'\n      }).then(confirmation => {\n        if (confirmation.isConfirmed) {\n          this.router.navigate(['/user-login']);\n        }\n      });\n    }, error => {\n      console.log(error);\n    }), mapTo(void 0));\n  }\n  // onSubmitRegister() {\n  //   if (this.doctorForm.invalid) {\n  //     this.doctorForm.markAllAsTouched();\n  //     return;\n  //   }\n  //   this.checkRegisterNumber().subscribe((isSLMCRegistered) => {\n  //     if (!isSLMCRegistered) {\n  //       this.checkUserEmail().subscribe((isEmailRegistered) => {\n  //         if (!isEmailRegistered) {\n  //           this.onUserRegister().subscribe(() => {\n  //             this.onDoctorRegister().subscribe(() => {\n  //               console.log('Doctor registered successfully');\n  //             });\n  //           });\n  //         }\n  //       });\n  //     }\n  //   });\n  // }\n  onSubmitRegister() {\n    Swal.fire({\n      title: \"Wait until approval!\",\n      text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\n      icon: 'success',\n      confirmButtonText: 'OK'\n    });\n    // if (this.doctorForm.invalid) {\n    //   this.doctorForm.markAllAsTouched();\n    //   return;\n    // }\n    // this.checkUserEmail().subscribe((isEmailRegistered) => {\n    //   if (!isEmailRegistered) {\n    //     this.onUserRegister().subscribe(() => {\n    //       this.onDoctorRegister().subscribe(() => {\n    //         console.log('Doctor registered successfully');\n    //       });\n    //     });\n    //   }\n    // });\n  }\n  // UserTemp Saving\n  onUserTempRegister() {\n    console.log(\"Working............\");\n    if (this.doctorForm.invalid) {\n      this.doctorForm.markAllAsTouched();\n      return;\n    }\n    console.log(\"Working Here............\");\n    // Disable the register button and show a loading indicator\n    this.registerButton.nativeElement.disabled = true;\n    this.registerButton.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\n    // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\n    // this.authService\n    //   .checkUserTempAvailability(this.userTemp.userEmail)\n    //   .subscribe((resp) => {\n    //     if (resp !=null) {\n    //       Swal.fire({\n    //         title: 'Registration Already Exists!',\n    //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\n    //         icon: 'info',\n    //         confirmButtonText: 'OK',\n    //       });\n    //       // Reset the button state\n    //       this.registerButton.nativeElement.disabled = false;\n    //       this.registerButton.nativeElement.innerHTML = 'Register';\n    //       return;\n    //     }\n    console.log(this.userTemp);\n    this.authService.saveUserTemp(this.userTemp).subscribe(userTempSaved => {\n      console.log('Full userTempSaved object:', userTempSaved);\n      const receivedUserTemp = userTempSaved;\n      let title = 'Registration Completed!';\n      let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\n      let iconName = 'success';\n      if (!receivedUserTemp) {\n        title = 'Registration Failed!';\n        message = 'An error occurred while registering. Please try again.';\n        iconName = 'error';\n      }\n      Swal.fire({\n        title: title,\n        text: message,\n        icon: iconName,\n        confirmButtonText: 'OK'\n      });\n      // Reset button state\n      this.registerButton.nativeElement.disabled = false;\n      this.registerButton.nativeElement.innerHTML = 'Register';\n    }, error => {\n      Swal.fire({\n        title: 'Registration Failed!',\n        text: 'An error occurred during registration. Please try again later.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n      this.registerButton.nativeElement.disabled = false;\n      this.registerButton.nativeElement.innerHTML = 'Register';\n    });\n    // }); // COMMENTED OUT FOR TESTING\n  }\n\n  checkUserEmail() {\n    if (this.doctorForm.get('email')?.valid) {\n      const userEmail = this.doctorForm.get('email')?.value;\n      return this.userService.checkUser(userEmail).pipe(map(data => {\n        if (data) {\n          this.isEmailRegistered = true;\n          this.userEmailExistsMessage = 'Email already registered. Try another.';\n        } else {\n          this.isEmailRegistered = false;\n          this.userEmailExistsMessage = '';\n        }\n        return this.isEmailRegistered;\n      }));\n    } else {\n      this.isEmailRegistered = false;\n      this.userEmailExistsMessage = '';\n      return of(this.isEmailRegistered);\n    }\n  }\n  checkRegisterNumber() {\n    if (this.doctorForm.get('regNo')?.valid) {\n      const regNo = this.doctorForm.get('regNo')?.value;\n      return this.doctorService.checkSlmcNumber(regNo).pipe(map(data => {\n        if (data) {\n          this.isSLMCRegistered = true;\n          this.slmcNumberExistsMessage = 'That number already registered. Try another.';\n        } else {\n          this.isSLMCRegistered = false;\n          this.slmcNumberExistsMessage = '';\n        }\n        return this.isSLMCRegistered;\n      }));\n    } else {\n      this.isSLMCRegistered = false;\n      this.slmcNumberExistsMessage = '';\n      return of(this.isSLMCRegistered);\n    }\n  }\n  navigateUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  static #_ = this.ɵfac = function DoctorRegistrationComponent_Factory(t) {\n    return new (t || DoctorRegistrationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DoctorService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.UserService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DoctorRegistrationComponent,\n    selectors: [[\"app-doctor-registration\"]],\n    viewQuery: function DoctorRegistrationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.registerButton = _t.first);\n      }\n    },\n    decls: 63,\n    vars: 19,\n    consts: [[\"loggedUser\", \"Hello Doctor\"], [1, \"background-container\"], [1, \"form-container\"], [1, \"backtoselection-button\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"title\"], [\"id\", \"title\", \"name\", \"title\", \"formControlName\", \"title\", 2, \"width\", \"262px\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"Prof\"], [\"value\", \"Dr\"], [4, \"ngIf\"], [\"for\", \"firstName\"], [\"type\", \"text\", \"id\", \"firstName\", \"name\", \"firstName\", \"formControlName\", \"firstName\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"lastName\"], [\"type\", \"text\", \"id\", \"lastName\", \"name\", \"lastName\", \"formControlName\", \"lastName\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"regNo\"], [\"type\", \"text\", \"id\", \"regNo\", \"name\", \"regNo\", \"formControlName\", \"regNo\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"telephone\"], [\"type\", \"text\", \"id\", \"telephone\", \"name\", \"telephone\", \"formControlName\", \"telephone\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"formControlName\", \"email\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"formControlName\", \"password\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"confirmPassword\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"name\", \"confirmPassword\", \"formControlName\", \"confirmPassword\"], [\"type\", \"submit\", 1, \"register-button\"], [\"RegisterButton\", \"\"], [1, \"text-danger\"]],\n    template: function DoctorRegistrationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n        i0.ɵɵtext(4, \"Doctor Registration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function DoctorRegistrationComponent_Template_button_click_5_listener() {\n          return ctx.navigateUserSelection();\n        });\n        i0.ɵɵtext(6, \" Selection Menu \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function DoctorRegistrationComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onUserTempRegister();\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"label\", 7);\n        i0.ɵɵtext(11, \"Title\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"select\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_select_ngModelChange_12_listener($event) {\n          return ctx.userTemp.userTitle = $event;\n        });\n        i0.ɵɵelementStart(13, \"option\", 9);\n        i0.ɵɵtext(14, \"Prof.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"option\", 10);\n        i0.ɵɵtext(16, \"Dr.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(17, DoctorRegistrationComponent_div_17_Template, 2, 1, \"div\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 6)(20, \"label\", 12);\n        i0.ɵɵtext(21, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"input\", 13);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_22_listener($event) {\n          return ctx.userTemp.mainName = $event;\n        })(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_22_listener() {\n          return ctx.setFirstName();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(23, DoctorRegistrationComponent_div_23_Template, 3, 2, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\", 6)(25, \"label\", 14);\n        i0.ɵɵtext(26, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"input\", 15);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_27_listener($event) {\n          return ctx.userTemp.additionalName = $event;\n        })(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_27_listener() {\n          return ctx.setLastName();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(28, DoctorRegistrationComponent_div_28_Template, 3, 2, \"div\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(29, \"div\", 5)(30, \"div\", 6)(31, \"label\", 16);\n        i0.ɵɵtext(32, \"SLMC Reg. Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"input\", 17);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_33_listener($event) {\n          return ctx.userTemp.registrationNumber = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(34, DoctorRegistrationComponent_div_34_Template, 2, 1, \"div\", 11);\n        i0.ɵɵtemplate(35, DoctorRegistrationComponent_small_35_Template, 2, 1, \"small\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 6)(37, \"label\", 19);\n        i0.ɵɵtext(38, \"Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"input\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_39_listener($event) {\n          return ctx.userTemp.contactNumber = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(40, DoctorRegistrationComponent_div_40_Template, 3, 2, \"div\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(41, \"div\", 5)(42, \"div\", 6)(43, \"label\", 21);\n        i0.ɵɵtext(44, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"input\", 22);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_45_listener($event) {\n          return ctx.userTemp.userEmail = $event;\n        })(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_45_listener() {\n          return ctx.updateEmail();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(46, DoctorRegistrationComponent_div_46_Template, 2, 1, \"div\", 11);\n        i0.ɵɵtemplate(47, DoctorRegistrationComponent_small_47_Template, 2, 1, \"small\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"div\", 5)(49, \"div\", 6)(50, \"label\", 23);\n        i0.ɵɵtext(51, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"input\", 24);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorRegistrationComponent_Template_input_ngModelChange_52_listener($event) {\n          return ctx.userTemp.userPassword = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(53, DoctorRegistrationComponent_div_53_Template, 5, 3, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"div\", 6)(55, \"label\", 25);\n        i0.ɵɵtext(56, \"Re-enter Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(57, \"input\", 26);\n        i0.ɵɵtemplate(58, DoctorRegistrationComponent_div_58_Template, 2, 1, \"div\", 11);\n        i0.ɵɵtemplate(59, DoctorRegistrationComponent_small_59_Template, 2, 0, \"small\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(60, \"button\", 27, 28);\n        i0.ɵɵtext(62, \"Register\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_4_0;\n        let tmp_6_0;\n        let tmp_8_0;\n        let tmp_11_0;\n        let tmp_13_0;\n        let tmp_16_0;\n        let tmp_17_0;\n        let tmp_18_0;\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"formGroup\", ctx.doctorForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userTitle);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.doctorForm.get(\"title\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx.doctorForm.get(\"title\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx.doctorForm.get(\"title\")) == null ? null : tmp_2_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.mainName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.doctorForm.get(\"firstName\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx.doctorForm.get(\"firstName\")) == null ? null : tmp_4_0.dirty) || ((tmp_4_0 = ctx.doctorForm.get(\"firstName\")) == null ? null : tmp_4_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.additionalName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.doctorForm.get(\"lastName\")) == null ? null : tmp_6_0.invalid) && (((tmp_6_0 = ctx.doctorForm.get(\"lastName\")) == null ? null : tmp_6_0.dirty) || ((tmp_6_0 = ctx.doctorForm.get(\"lastName\")) == null ? null : tmp_6_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.registrationNumber);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.doctorForm.get(\"regNo\")) == null ? null : tmp_8_0.invalid) && (((tmp_8_0 = ctx.doctorForm.get(\"regNo\")) == null ? null : tmp_8_0.dirty) || ((tmp_8_0 = ctx.doctorForm.get(\"regNo\")) == null ? null : tmp_8_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isSLMCRegistered);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.contactNumber);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.doctorForm.get(\"telephone\")) == null ? null : tmp_11_0.invalid) && (((tmp_11_0 = ctx.doctorForm.get(\"telephone\")) == null ? null : tmp_11_0.dirty) || ((tmp_11_0 = ctx.doctorForm.get(\"telephone\")) == null ? null : tmp_11_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userEmail);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.doctorForm.get(\"email\")) == null ? null : tmp_13_0.invalid) && (((tmp_13_0 = ctx.doctorForm.get(\"email\")) == null ? null : tmp_13_0.dirty) || ((tmp_13_0 = ctx.doctorForm.get(\"email\")) == null ? null : tmp_13_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEmailRegistered);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userPassword);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.doctorForm.get(\"password\")) == null ? null : tmp_16_0.invalid) && (((tmp_16_0 = ctx.doctorForm.get(\"password\")) == null ? null : tmp_16_0.dirty) || ((tmp_16_0 = ctx.doctorForm.get(\"password\")) == null ? null : tmp_16_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.doctorForm.get(\"confirmPassword\")) == null ? null : tmp_17_0.invalid) && (((tmp_17_0 = ctx.doctorForm.get(\"confirmPassword\")) == null ? null : tmp_17_0.dirty) || ((tmp_17_0 = ctx.doctorForm.get(\"confirmPassword\")) == null ? null : tmp_17_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.doctorForm.errors == null ? null : ctx.doctorForm.errors[\"mismatch\"]) && ((tmp_18_0 = ctx.doctorForm.get(\"confirmPassword\")) == null ? null : tmp_18_0.dirty));\n      }\n    },\n    dependencies: [i6.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.DefaultNavbarComponent],\n    styles: [\".background-container[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n  background: #ffffff;\\n  background-image: url('/assets/images/background.png');\\n  background-size: cover;\\n  background-position: center;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n  align-items: center;\\n  \\n\\n  min-height: 90vh;\\n  padding-inline: 10px;\\n  padding-block: 20px;\\n  min-height: 90vh;\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding-inline: 2px;\\n  border-radius: 25px;\\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n  \\n\\n  \\n\\n  \\n\\n  border: 1px solid #fb751e;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  width: 100%;\\n  height: auto;\\n  padding-inline: 10px;\\n  padding-top: 10px;\\n  padding-block: 20px;\\n}\\n\\nh3[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-size: 30px;\\n  font-weight: 700;\\n  text-align: center;\\n  color: #fb751e;\\n  margin-bottom: 8.5px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 10px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-size: 16px;\\n  font-weight: 400;\\n  text-align: left;\\n  margin-bottom: 5px;\\n  color: #000000;\\n  display: block;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 33px;\\n  padding: 5px;\\n  font-size: 14px;\\n  color: #495057;\\n  border-radius: 4px;\\n  border: 1px solid #b3b3b3;\\n  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),\\n    linear-gradient(135deg, #ff7a00 50%, transparent 50%);\\n  background-position: calc(100% - 20px) center,\\n    calc(100% - 15px) center;\\n  background-size: 5px 5px, 5px 5px;\\n  background-repeat: no-repeat;\\n  appearance: none;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%], input[type=\\\"email\\\"][_ngcontent-%COMP%], input[type=\\\"password\\\"][_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 5px;\\n  border: 1px solid #b3b3b3;\\n  border-radius: 4px;\\n}\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"email\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"password\\\"][_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n  width: 100%;\\n  padding: 5px;\\n  border: 1px solid #b3b3b3;\\n  border-radius: 4px;\\n  \\n\\n}\\ntextarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, #fb751e, #333333);\\n  border: none;\\n  border-radius: 20px;\\n  color: white;\\n  font-size: 16px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n  margin-top: 30px;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(to left, #fb751e, #333333);\\n}\\n\\n.backtoselection-button[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 40px;\\n  background: none;\\n  border: 2px solid #fb751e;\\n  color: #fb751e;\\n  font-size: 16px;\\n  font-weight: bold;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n    justify-content: center;\\n      margin-bottom: 20px;\\n      margin-top: 20px;\\n}\\n\\n@media (min-width: 768px) {\\n  .background-container[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .form-container[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding-inline: 2px;\\n    border-radius: 25px;\\n    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n    \\n\\n    \\n\\n    \\n\\n    border: 1px solid #fb751e;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    width: 100%;\\n    height: auto;\\n    padding-inline: 20px;\\n    padding-top: 10px;\\n    padding-block: 20px;\\n  }\\n}\\n\\n@media (min-width: 1024px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding: 30px;\\n    border-radius: 25px;\\n    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n    \\n\\n    \\n\\n    \\n\\n    border: 1px solid #fb751e;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    width: 620px;\\n    height: auto;\\n  }\\n\\n  .background-container[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .backtoselection-button[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 40px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 16px;\\n    font-weight: bold;\\n    border-radius: 20px;\\n    cursor: pointer;\\n    position: absolute;\\n    left: 0;\\n    top: 107px;\\n    margin-left: 5%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { DoctorRegistrationComponent };", "map": {"version": 3, "names": ["map", "mapTo", "of", "tap", "Validators", "User", "UserCategory", "Doctor", "<PERSON><PERSON>", "UserTemp", "UserTempType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "DoctorRegistrationComponent_div_17_small_1_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "<PERSON><PERSON><PERSON>", "get", "errors", "DoctorRegistrationComponent_div_23_small_1_Template", "DoctorRegistrationComponent_div_23_small_2_Template", "ctx_r1", "tmp_1_0", "DoctorRegistrationComponent_div_28_small_1_Template", "DoctorRegistrationComponent_div_28_small_2_Template", "ctx_r2", "DoctorRegistrationComponent_div_34_small_1_Template", "ctx_r3", "ɵɵtextInterpolate", "ctx_r4", "slmcNumberExistsMessage", "DoctorRegistrationComponent_div_40_small_1_Template", "DoctorRegistrationComponent_div_40_small_2_Template", "ctx_r5", "DoctorRegistrationComponent_div_46_small_1_Template", "ctx_r6", "ctx_r7", "userEmailExistsMessage", "DoctorRegistrationComponent_div_53_small_1_Template", "DoctorRegistrationComponent_div_53_small_2_Template", "ɵɵelement", "DoctorRegistrationComponent_div_53_small_4_Template", "ctx_r8", "tmp_2_0", "DoctorRegistrationComponent_div_58_small_1_Template", "ctx_r9", "DoctorRegistrationComponent", "constructor", "fb", "router", "doctorService", "authService", "userService", "doctor", "user", "userCategory", "isEmailRegistered", "isSLMCRegistered", "userTemp", "group", "title", "required", "firstName", "pattern", "lastName", "regNo", "telephone", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "validator", "passwordMatchValidator", "ngOnInit", "localStorage", "clear", "getUserCategoryFromDB", "userTempType", "DOCTOR", "ngAfterViewInit", "getUserCategoryById", "subscribe", "response", "form", "value", "dirty", "touched", "mismatch", "updateEmail", "username", "setFirstName", "setLastName", "onUserRegister", "userCategoryId", "console", "table", "register", "pipe", "userId", "id", "error", "log", "onDoctorRegister", "saveDoctor", "fire", "text", "icon", "confirmButtonText", "then", "confirmation", "isConfirmed", "navigate", "onSubmitRegister", "onUserTempRegister", "invalid", "mark<PERSON>llAsTouched", "registerButton", "nativeElement", "disabled", "innerHTML", "saveUserTemp", "userTempSaved", "receivedUserTemp", "message", "iconName", "checkUserEmail", "valid", "userEmail", "checkUser", "data", "checkRegisterNumber", "checkSlmcNumber", "navigateUserSelection", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "DoctorService", "i4", "AuthService", "i5", "UserService", "_2", "selectors", "viewQuery", "DoctorRegistrationComponent_Query", "rf", "ctx", "ɵɵlistener", "DoctorRegistrationComponent_Template_button_click_5_listener", "DoctorRegistrationComponent_Template_form_ngSubmit_7_listener", "DoctorRegistrationComponent_Template_select_ngModelChange_12_listener", "$event", "userTitle", "DoctorRegistrationComponent_div_17_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_22_listener", "mainName", "DoctorRegistrationComponent_div_23_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_27_listener", "additionalName", "DoctorRegistrationComponent_div_28_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_33_listener", "registrationNumber", "DoctorRegistrationComponent_div_34_Template", "DoctorRegistrationComponent_small_35_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_39_listener", "contactNumber", "DoctorRegistrationComponent_div_40_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_45_listener", "DoctorRegistrationComponent_div_46_Template", "DoctorRegistrationComponent_small_47_Template", "DoctorRegistrationComponent_Template_input_ngModelChange_52_listener", "userPassword", "DoctorRegistrationComponent_div_53_Template", "DoctorRegistrationComponent_div_58_Template", "DoctorRegistrationComponent_small_59_Template", "tmp_4_0", "tmp_6_0", "tmp_8_0", "tmp_11_0", "tmp_13_0", "tmp_16_0", "tmp_17_0", "tmp_18_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\doctor\\doctor-registration\\doctor-registration.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\doctor\\doctor-registration\\doctor-registration.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { map, mapTo, Observable, of, tap } from 'rxjs';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { UserService } from 'src/app/user/user.service';\r\nimport { User, UserCategory } from 'src/app/user/user';\r\nimport { Doctor } from 'src/app/doctor/doctor';\r\nimport { DoctorService } from 'src/app/doctor/doctor.service';\r\nimport Swal from 'sweetalert2';\r\nimport { AuthService } from 'src/app/auth/auth.service';\r\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\r\n\r\n@Component({\r\n  selector: 'app-doctor-registration',\r\n  templateUrl: './doctor-registration.component.html',\r\n  styleUrls: ['./doctor-registration.component.css'],\r\n})\r\nexport class DoctorRegistrationComponent implements OnInit,AfterViewInit {\r\n  doctor: Doctor = new Doctor();\r\n  user: User = new User();\r\n  userCategory: UserCategory = new UserCategory();\r\n  doctorForm: FormGroup;\r\n  isEmailRegistered: boolean = false;\r\n  isSLMCRegistered: boolean = false;\r\n  userEmailExistsMessage: string = '';\r\n  slmcNumberExistsMessage: string = '';\r\n\r\n  // User temp\r\n  protected userTemp: UserTemp = new UserTemp();\r\n  @ViewChild('RegisterButton') registerButton!: ElementRef<HTMLButtonElement>;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private doctorService: DoctorService,\r\n    private authService: AuthService,\r\n    private userService: UserService\r\n  ) {\r\n    this.doctorForm = this.fb.group(\r\n      {\r\n        title: ['', Validators.required],\r\n        firstName: [\r\n          '',\r\n          [Validators.required, Validators.pattern('^[a-zA-Z]*$')],\r\n        ],\r\n        lastName: [\r\n          '',\r\n          [Validators.required, Validators.pattern('^[a-zA-Z]*$')],\r\n        ],\r\n        regNo: ['', Validators.required],\r\n        telephone: [\r\n          '',\r\n          [Validators.required, Validators.pattern('^[0-9]{10}$')],\r\n        ],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        password: [\r\n          '',\r\n          [\r\n            Validators.required,\r\n            Validators.minLength(8),\r\n            Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$'),\r\n          ],\r\n        ],\r\n        confirmPassword: ['', Validators.required],\r\n      },\r\n      { validator: this.passwordMatchValidator }\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    localStorage.clear();\r\n    this.getUserCategoryFromDB();\r\n    this.userTemp.userTempType = UserTempType.DOCTOR;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n  }\r\n\r\n  private getUserCategoryFromDB() {\r\n    this.userService.getUserCategoryById(2).subscribe((response) => {\r\n      this.userCategory = response;\r\n    });\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const password = form.get('password');\r\n    const confirmPassword = form.get('confirmPassword');\r\n\r\n    if (\r\n      password?.value &&\r\n      confirmPassword?.value &&\r\n      (confirmPassword.dirty || confirmPassword.touched)\r\n    ) {\r\n      return password.value === confirmPassword.value\r\n        ? null\r\n        : { mismatch: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  updateEmail() {\r\n    this.user.username = this.doctor.email;\r\n  }\r\n\r\n  setFirstName() {\r\n    this.user.firstName = this.doctor.firstName;\r\n  }\r\n\r\n  setLastName() {\r\n    this.user.lastName = this.doctor.lastName;\r\n  }\r\n\r\n  onUserRegister(): Observable<void> {\r\n    if (this.userCategory == null) {\r\n      this.getUserCategoryFromDB();\r\n    }\r\n    this.user.userCategoryId = this.userCategory;\r\n    console.table(this.user);\r\n\r\n    return this.userService.register(this.user).pipe(\r\n      tap(\r\n        (response) => {\r\n          this.user.userId = response.id;\r\n          this.doctor.userId = this.user;\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      ),\r\n      mapTo(void 0)\r\n    );\r\n  }\r\n\r\n  onDoctorRegister(): Observable<void> {\r\n    return this.doctorService.saveDoctor(this.doctor).pipe(\r\n      tap(\r\n        () => {\r\n          Swal.fire({\r\n            title: 'Registration Successful!',\r\n            text: 'Thank you for registering! Please verify your email to complete the login process.',\r\n            icon: 'success',\r\n            confirmButtonText: 'Continue',\r\n          }).then((confirmation) => {\r\n            if (confirmation.isConfirmed) {\r\n              this.router.navigate(['/user-login']);\r\n            }\r\n          });\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      ),\r\n      mapTo(void 0)\r\n    );\r\n  }\r\n\r\n  // onSubmitRegister() {\r\n  //   if (this.doctorForm.invalid) {\r\n  //     this.doctorForm.markAllAsTouched();\r\n  //     return;\r\n  //   }\r\n  //   this.checkRegisterNumber().subscribe((isSLMCRegistered) => {\r\n  //     if (!isSLMCRegistered) {\r\n  //       this.checkUserEmail().subscribe((isEmailRegistered) => {\r\n  //         if (!isEmailRegistered) {\r\n  //           this.onUserRegister().subscribe(() => {\r\n  //             this.onDoctorRegister().subscribe(() => {\r\n  //               console.log('Doctor registered successfully');\r\n  //             });\r\n  //           });\r\n  //         }\r\n  //       });\r\n  //     }\r\n  //   });\r\n  // }\r\n\r\n  onSubmitRegister() {\r\n    Swal.fire({\r\n      title: \"Wait until approval!\",\r\n      text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\r\n      icon: 'success',\r\n      confirmButtonText: 'OK',\r\n    });\r\n    // if (this.doctorForm.invalid) {\r\n    //   this.doctorForm.markAllAsTouched();\r\n    //   return;\r\n    // }\r\n    // this.checkUserEmail().subscribe((isEmailRegistered) => {\r\n    //   if (!isEmailRegistered) {\r\n    //     this.onUserRegister().subscribe(() => {\r\n    //       this.onDoctorRegister().subscribe(() => {\r\n    //         console.log('Doctor registered successfully');\r\n    //       });\r\n    //     });\r\n    //   }\r\n    // });\r\n  }\r\n\r\n    // UserTemp Saving\r\n    onUserTempRegister() {\r\n      console.log(\"Working............\");\r\n\r\n      if (this.doctorForm.invalid) {\r\n        this.doctorForm.markAllAsTouched();\r\n        return;\r\n      }\r\n\r\n      console.log(\"Working Here............\");\r\n\r\n\r\n      // Disable the register button and show a loading indicator\r\n      this.registerButton.nativeElement.disabled = true;\r\n      this.registerButton.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\r\n\r\n      // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\r\n      // this.authService\r\n      //   .checkUserTempAvailability(this.userTemp.userEmail)\r\n      //   .subscribe((resp) => {\r\n\r\n      //     if (resp !=null) {\r\n      //       Swal.fire({\r\n      //         title: 'Registration Already Exists!',\r\n      //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\r\n      //         icon: 'info',\r\n      //         confirmButtonText: 'OK',\r\n      //       });\r\n\r\n      //       // Reset the button state\r\n      //       this.registerButton.nativeElement.disabled = false;\r\n      //       this.registerButton.nativeElement.innerHTML = 'Register';\r\n      //       return;\r\n      //     }\r\n\r\n      console.log(this.userTemp)\r\n\r\n          this.authService.saveUserTemp(this.userTemp).subscribe(\r\n            (userTempSaved: UserTemp) => {\r\n              console.log('Full userTempSaved object:', userTempSaved);\r\n\r\n              const receivedUserTemp: UserTemp = userTempSaved;\r\n              let title = 'Registration Completed!';\r\n              let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\r\n              let iconName:\r\n                | 'success'\r\n                | 'info'\r\n                | 'error'\r\n                | 'warning'\r\n                | 'question' = 'success';\r\n\r\n              if (!receivedUserTemp) {\r\n                title = 'Registration Failed!';\r\n                message ='An error occurred while registering. Please try again.';\r\n                iconName = 'error';\r\n              }\r\n\r\n              Swal.fire({\r\n                title: title,\r\n                text: message,\r\n                icon: iconName,\r\n                confirmButtonText: 'OK',\r\n              });\r\n\r\n              // Reset button state\r\n              this.registerButton.nativeElement.disabled = false;\r\n              this.registerButton.nativeElement.innerHTML = 'Register';\r\n            },\r\n            (error) => {\r\n              Swal.fire({\r\n                title: 'Registration Failed!',\r\n                text: 'An error occurred during registration. Please try again later.',\r\n                icon: 'error',\r\n                confirmButtonText: 'OK',\r\n              });\r\n\r\n              this.registerButton.nativeElement.disabled = false;\r\n              this.registerButton.nativeElement.innerHTML = 'Register';\r\n            }\r\n          );\r\n        // }); // COMMENTED OUT FOR TESTING\r\n    }\r\n\r\n\r\n  checkUserEmail(): Observable<boolean> {\r\n    if (this.doctorForm.get('email')?.valid) {\r\n      const userEmail = this.doctorForm.get('email')?.value;\r\n      return this.userService.checkUser(userEmail).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isEmailRegistered = true;\r\n            this.userEmailExistsMessage =\r\n              'Email already registered. Try another.';\r\n          } else {\r\n            this.isEmailRegistered = false;\r\n            this.userEmailExistsMessage = '';\r\n          }\r\n          return this.isEmailRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isEmailRegistered = false;\r\n      this.userEmailExistsMessage = '';\r\n      return of(this.isEmailRegistered);\r\n    }\r\n  }\r\n\r\n  checkRegisterNumber(): Observable<boolean> {\r\n    if (this.doctorForm.get('regNo')?.valid) {\r\n      const regNo = this.doctorForm.get('regNo')?.value;\r\n      return this.doctorService.checkSlmcNumber(regNo).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isSLMCRegistered = true;\r\n            this.slmcNumberExistsMessage =\r\n              'That number already registered. Try another.';\r\n          } else {\r\n            this.isSLMCRegistered = false;\r\n            this.slmcNumberExistsMessage = '';\r\n          }\r\n          return this.isSLMCRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isSLMCRegistered = false;\r\n      this.slmcNumberExistsMessage = '';\r\n      return of(this.isSLMCRegistered);\r\n    }\r\n  }\r\n\r\n  navigateUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n\r\n", "<app-default-navbar loggedUser=\"Hello Doctor\" />\r\n<div class=\"background-container\">\r\n  <div class=\"form-container\">\r\n    <h3>Doctor Registration</h3>\r\n    <button class=\"backtoselection-button\" (click)=\"navigateUserSelection()\">\r\n      Selection Menu\r\n    </button>\r\n    <form [formGroup]=\"doctorForm\" (ngSubmit)=\"onUserTempRegister()\">\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"title\">Title</label>\r\n          <select\r\n            id=\"title\"\r\n            name=\"title\"\r\n            formControlName=\"title\"\r\n            [(ngModel)]=\"userTemp.userTitle\"\r\n            style=\"width: 262px\"\r\n          >\r\n            <option value=\"Prof\">Prof.</option>\r\n            <option value=\"Dr\">Dr.</option>\r\n          </select>\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('title')?.invalid &&\r\n              (doctorForm.get('title')?.dirty ||\r\n                doctorForm.get('title')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('title')?.errors?.['required']\"\r\n              >Title is required.</small\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"firstName\">First Name</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"firstName\"\r\n            name=\"firstName\"\r\n            formControlName=\"firstName\"\r\n            [(ngModel)]=\"userTemp.mainName\"\r\n            (ngModelChange)=\"setFirstName()\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('firstName')?.invalid &&\r\n              (doctorForm.get('firstName')?.dirty ||\r\n                doctorForm.get('firstName')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('firstName')?.errors?.['required']\"\r\n              >First Name is required.</small\r\n            >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('firstName')?.errors?.['pattern']\"\r\n              >First Name can only contain letters.</small\r\n            >\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"lastName\">Last Name</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"lastName\"\r\n            name=\"lastName\"\r\n            formControlName=\"lastName\"\r\n            [(ngModel)]=\"userTemp.additionalName\"\r\n            (ngModelChange)=\"setLastName()\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('lastName')?.invalid &&\r\n              (doctorForm.get('lastName')?.dirty ||\r\n                doctorForm.get('lastName')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('lastName')?.errors?.['required']\"\r\n              >Last Name is required.</small\r\n            >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('lastName')?.errors?.['pattern']\"\r\n              >Last Name can only contain letters.</small\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"regNo\">SLMC Reg. Number</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"regNo\"\r\n            name=\"regNo\"\r\n            formControlName=\"regNo\"\r\n            [(ngModel)]=\"userTemp.registrationNumber\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('regNo')?.invalid &&\r\n              (doctorForm.get('regNo')?.dirty ||\r\n                doctorForm.get('regNo')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('regNo')?.errors?.['required']\"\r\n              >SLMC Register Number is required.</small\r\n            >\r\n          </div>\r\n          <small class=\"text-danger\" *ngIf=\"isSLMCRegistered\">{{\r\n            slmcNumberExistsMessage\r\n          }}</small>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"telephone\">Contact Number</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"telephone\"\r\n            name=\"telephone\"\r\n            formControlName=\"telephone\"\r\n            [(ngModel)]=\"userTemp.contactNumber\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('telephone')?.invalid &&\r\n              (doctorForm.get('telephone')?.dirty ||\r\n                doctorForm.get('telephone')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('telephone')?.errors?.['required']\"\r\n              >Contact Number is required.</small\r\n            >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('telephone')?.errors?.['pattern']\"\r\n              >Invalid Contact number.</small\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"email\">Email Address</label>\r\n          <input\r\n            type=\"email\"\r\n            id=\"email\"\r\n            name=\"email\"\r\n            formControlName=\"email\"\r\n            [(ngModel)]=\"userTemp.userEmail\"\r\n            (ngModelChange)=\"updateEmail()\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('email')?.invalid &&\r\n              (doctorForm.get('email')?.dirty ||\r\n                doctorForm.get('email')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('email')?.errors?.['required']\"\r\n              >Email is required.</small\r\n            >\r\n          </div>\r\n          <small class=\"text-danger\" *ngIf=\"isEmailRegistered\">{{\r\n            userEmailExistsMessage\r\n          }}</small>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"password\">Password</label>\r\n          <input\r\n            type=\"password\"\r\n            id=\"password\"\r\n            name=\"password\"\r\n            formControlName=\"password\"\r\n            [(ngModel)]=\"userTemp.userPassword\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('password')?.invalid &&\r\n              (doctorForm.get('password')?.dirty ||\r\n                doctorForm.get('password')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('password')?.errors?.['required']\"\r\n              >Password is required.</small\r\n            >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('password')?.errors?.['minlength']\"\r\n              >Password must be at least 8 characters long.</small\r\n            ><br />\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('password')?.errors?.['pattern']\"\r\n              >Password must contain at least one uppercase letter, one\r\n              lowercase letter, one digit, and one special character.</small\r\n            >\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"confirmPassword\">Re-enter Password</label>\r\n          <input\r\n            type=\"password\"\r\n            id=\"confirmPassword\"\r\n            name=\"confirmPassword\"\r\n            formControlName=\"confirmPassword\"\r\n          />\r\n          <div\r\n            *ngIf=\"\r\n              doctorForm.get('confirmPassword')?.invalid &&\r\n              (doctorForm.get('confirmPassword')?.dirty ||\r\n                doctorForm.get('confirmPassword')?.touched)\r\n            \"\r\n          >\r\n            <small\r\n              class=\"text-danger\"\r\n              *ngIf=\"doctorForm.get('confirmPassword')?.errors?.['required']\"\r\n              >Please re-enter the password.</small\r\n            >\r\n          </div>\r\n          <small\r\n            class=\"text-danger\"\r\n            *ngIf=\"doctorForm.errors?.['mismatch'] && doctorForm.get('confirmPassword')?.dirty\"\r\n            >Password do not match.</small\r\n          >\r\n        </div>\r\n      </div>\r\n      <button #RegisterButton type=\"submit\" class=\"register-button\">Register</button>\r\n    </form>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,GAAG,EAAEC,KAAK,EAAcC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AACtD,SAAiCC,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,IAAI,EAAEC,YAAY,QAAQ,mBAAmB;AACtD,SAASC,MAAM,QAAQ,uBAAuB;AAE9C,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,QAAQ,EAAEC,YAAY,QAAQ,mBAAmB;;;;;;;;;;;;ICkB9CC,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EACpB;;;;;IAXHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAC,mDAAA,oBAIC;IACHL,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAmD;;;;;IAyBtDZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EACzB;;;;;IACDH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,2CAAoC;IAAAF,EAAA,CAAAG,YAAA,EACtC;;;;;IAhBHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAS,mDAAA,oBAIC;IACDb,EAAA,CAAAI,UAAA,IAAAU,mDAAA,oBAIC;IACHd,EAAA,CAAAG,YAAA,EAAM;;;;;;IARDH,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAO,MAAA,CAAAL,UAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAuD;IAKvDZ,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAO,UAAA,UAAAS,OAAA,GAAAD,MAAA,CAAAL,UAAA,CAAAC,GAAA,gCAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,YAAsD;;;;;IAuBzDZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EACxB;;;;;IACDH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,0CAAmC;IAAAF,EAAA,CAAAG,YAAA,EACrC;;;;;IAhBHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAa,mDAAA,oBAIC;IACDjB,EAAA,CAAAI,UAAA,IAAAc,mDAAA,oBAIC;IACHlB,EAAA,CAAAG,YAAA,EAAM;;;;;;IARDH,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAW,MAAA,CAAAT,UAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAsD;IAKtDZ,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAO,UAAA,UAAAS,OAAA,GAAAG,MAAA,CAAAT,UAAA,CAAAC,GAAA,+BAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,YAAqD;;;;;IAwBxDZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EACnC;;;;;IAXHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAgB,mDAAA,oBAIC;IACHpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAa,MAAA,CAAAX,UAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAmD;;;;;IAIxDZ,EAAA,CAAAC,cAAA,gBAAoD;IAAAD,EAAA,CAAAE,MAAA,GAElD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAF0CH,EAAA,CAAAM,SAAA,GAElD;IAFkDN,EAAA,CAAAsB,iBAAA,CAAAC,MAAA,CAAAC,uBAAA,CAElD;;;;;IAmBAxB,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAC7B;;;;;IACDH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EACzB;;;;;IAhBHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAqB,mDAAA,oBAIC;IACDzB,EAAA,CAAAI,UAAA,IAAAsB,mDAAA,oBAIC;IACH1B,EAAA,CAAAG,YAAA,EAAM;;;;;;IARDH,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAmB,MAAA,CAAAjB,UAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAuD;IAKvDZ,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAO,UAAA,UAAAS,OAAA,GAAAW,MAAA,CAAAjB,UAAA,CAAAC,GAAA,gCAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,YAAsD;;;;;IAyBzDZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EACpB;;;;;IAXHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAwB,mDAAA,oBAIC;IACH5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAqB,MAAA,CAAAnB,UAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAmD;;;;;IAIxDZ,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,GAEnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAF2CH,EAAA,CAAAM,SAAA,GAEnD;IAFmDN,EAAA,CAAAsB,iBAAA,CAAAQ,MAAA,CAAAC,sBAAA,CAEnD;;;;;IAqBA/B,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EACvB;;;;;IACDH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAC9C;;;;;IACDH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,uHACsD;IAAAF,EAAA,CAAAG,YAAA,EACxD;;;;;IAtBHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAA4B,mDAAA,oBAIC;IACDhC,EAAA,CAAAI,UAAA,IAAA6B,mDAAA,oBAIC;IAAAjC,EAAA,CAAAkC,SAAA,SAAM;IACPlC,EAAA,CAAAI,UAAA,IAAA+B,mDAAA,oBAKC;IACHnC,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAdDH,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAA4B,MAAA,CAAA1B,UAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAsD;IAKtDZ,EAAA,CAAAM,SAAA,GAAuD;IAAvDN,EAAA,CAAAO,UAAA,UAAAS,OAAA,GAAAoB,MAAA,CAAA1B,UAAA,CAAAC,GAAA,+BAAAK,OAAA,CAAAJ,MAAA,kBAAAI,OAAA,CAAAJ,MAAA,cAAuD;IAKvDZ,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAO,UAAA,UAAA8B,OAAA,GAAAD,MAAA,CAAA1B,UAAA,CAAAC,GAAA,+BAAA0B,OAAA,CAAAzB,MAAA,kBAAAyB,OAAA,CAAAzB,MAAA,YAAqD;;;;;IAsBxDZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAC/B;;;;;IAXHH,EAAA,CAAAC,cAAA,UAMC;IACCD,EAAA,CAAAI,UAAA,IAAAkC,mDAAA,oBAIC;IACHtC,EAAA,CAAAG,YAAA,EAAM;;;;;IAHDH,EAAA,CAAAM,SAAA,GAA6D;IAA7DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAA+B,MAAA,CAAA7B,UAAA,CAAAC,GAAA,sCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA6D;;;;;IAIlEZ,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EACxB;;;AD5OX,MAKaqC,2BAA2B;EActCC,YACUC,EAAe,EACfC,MAAc,EACdC,aAA4B,EAC5BC,WAAwB,EACxBC,WAAwB;IAJxB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAlBrB,KAAAC,MAAM,GAAW,IAAInD,MAAM,EAAE;IAC7B,KAAAoD,IAAI,GAAS,IAAItD,IAAI,EAAE;IACvB,KAAAuD,YAAY,GAAiB,IAAItD,YAAY,EAAE;IAE/C,KAAAuD,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAApB,sBAAsB,GAAW,EAAE;IACnC,KAAAP,uBAAuB,GAAW,EAAE;IAEpC;IACU,KAAA4B,QAAQ,GAAa,IAAItD,QAAQ,EAAE;IAU3C,IAAI,CAACY,UAAU,GAAG,IAAI,CAACgC,EAAE,CAACW,KAAK,CAC7B;MACEC,KAAK,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAAC8D,QAAQ,CAAC;MAChCC,SAAS,EAAE,CACT,EAAE,EACF,CAAC/D,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACgE,OAAO,CAAC,aAAa,CAAC,CAAC,CACzD;MACDC,QAAQ,EAAE,CACR,EAAE,EACF,CAACjE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACgE,OAAO,CAAC,aAAa,CAAC,CAAC,CACzD;MACDE,KAAK,EAAE,CAAC,EAAE,EAAElE,UAAU,CAAC8D,QAAQ,CAAC;MAChCK,SAAS,EAAE,CACT,EAAE,EACF,CAACnE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACgE,OAAO,CAAC,aAAa,CAAC,CAAC,CACzD;MACDI,KAAK,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAAC8D,QAAQ,EAAE9D,UAAU,CAACoE,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CACR,EAAE,EACF,CACErE,UAAU,CAAC8D,QAAQ,EACnB9D,UAAU,CAACsE,SAAS,CAAC,CAAC,CAAC,EACvBtE,UAAU,CAACgE,OAAO,CAAC,8CAA8C,CAAC,CACnE,CACF;MACDO,eAAe,EAAE,CAAC,EAAE,EAAEvE,UAAU,CAAC8D,QAAQ;KAC1C,EACD;MAAEU,SAAS,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC3C;EACH;EAEAC,QAAQA,CAAA;IACNC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,qBAAqB,EAAE;IAC5B,IAAI,CAAClB,QAAQ,CAACmB,YAAY,GAAGxE,YAAY,CAACyE,MAAM;EAClD;EAEAC,eAAeA,CAAA,GACf;EAEQH,qBAAqBA,CAAA;IAC3B,IAAI,CAACxB,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC7D,IAAI,CAAC3B,YAAY,GAAG2B,QAAQ;IAC9B,CAAC,CAAC;EACJ;EAEAV,sBAAsBA,CAACW,IAAe;IACpC,MAAMf,QAAQ,GAAGe,IAAI,CAAClE,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMqD,eAAe,GAAGa,IAAI,CAAClE,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IACEmD,QAAQ,EAAEgB,KAAK,IACfd,eAAe,EAAEc,KAAK,KACrBd,eAAe,CAACe,KAAK,IAAIf,eAAe,CAACgB,OAAO,CAAC,EAClD;MACA,OAAOlB,QAAQ,CAACgB,KAAK,KAAKd,eAAe,CAACc,KAAK,GAC3C,IAAI,GACJ;QAAEG,QAAQ,EAAE;MAAI,CAAE;;IAExB,OAAO,IAAI;EACb;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClC,IAAI,CAACmC,QAAQ,GAAG,IAAI,CAACpC,MAAM,CAACc,KAAK;EACxC;EAEAuB,YAAYA,CAAA;IACV,IAAI,CAACpC,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACT,MAAM,CAACS,SAAS;EAC7C;EAEA6B,WAAWA,CAAA;IACT,IAAI,CAACrC,IAAI,CAACU,QAAQ,GAAG,IAAI,CAACX,MAAM,CAACW,QAAQ;EAC3C;EAEA4B,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACrC,YAAY,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACqB,qBAAqB,EAAE;;IAE9B,IAAI,CAACtB,IAAI,CAACuC,cAAc,GAAG,IAAI,CAACtC,YAAY;IAC5CuC,OAAO,CAACC,KAAK,CAAC,IAAI,CAACzC,IAAI,CAAC;IAExB,OAAO,IAAI,CAACF,WAAW,CAAC4C,QAAQ,CAAC,IAAI,CAAC1C,IAAI,CAAC,CAAC2C,IAAI,CAC9CnG,GAAG,CACAoF,QAAQ,IAAI;MACX,IAAI,CAAC5B,IAAI,CAAC4C,MAAM,GAAGhB,QAAQ,CAACiB,EAAE;MAC9B,IAAI,CAAC9C,MAAM,CAAC6C,MAAM,GAAG,IAAI,CAAC5C,IAAI;IAChC,CAAC,EACA8C,KAAK,IAAI;MACRN,OAAO,CAACO,GAAG,CAACD,KAAK,CAAC;IACpB,CAAC,CACF,EACDxG,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAEA0G,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACpD,aAAa,CAACqD,UAAU,CAAC,IAAI,CAAClD,MAAM,CAAC,CAAC4C,IAAI,CACpDnG,GAAG,CACD,MAAK;MACHK,IAAI,CAACqG,IAAI,CAAC;QACR5C,KAAK,EAAE,0BAA0B;QACjC6C,IAAI,EAAE,oFAAoF;QAC1FC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC,CAACC,IAAI,CAAEC,YAAY,IAAI;QACvB,IAAIA,YAAY,CAACC,WAAW,EAAE;UAC5B,IAAI,CAAC7D,MAAM,CAAC8D,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;MAEzC,CAAC,CAAC;IACJ,CAAC,EACAX,KAAK,IAAI;MACRN,OAAO,CAACO,GAAG,CAACD,KAAK,CAAC;IACpB,CAAC,CACF,EACDxG,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAoH,gBAAgBA,CAAA;IACd7G,IAAI,CAACqG,IAAI,CAAC;MACR5C,KAAK,EAAE,sBAAsB;MAC7B6C,IAAI,EAAE,yHAAyH;MAC/HC,IAAI,EAAE,SAAS;MACfC,iBAAiB,EAAE;KACpB,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEE;EACAM,kBAAkBA,CAAA;IAChBnB,OAAO,CAACO,GAAG,CAAC,qBAAqB,CAAC;IAElC,IAAI,IAAI,CAACrF,UAAU,CAACkG,OAAO,EAAE;MAC3B,IAAI,CAAClG,UAAU,CAACmG,gBAAgB,EAAE;MAClC;;IAGFrB,OAAO,CAACO,GAAG,CAAC,0BAA0B,CAAC;IAGvC;IACA,IAAI,CAACe,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,IAAI;IACjD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,yCAAyC;IAEvF;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEAzB,OAAO,CAACO,GAAG,CAAC,IAAI,CAAC3C,QAAQ,CAAC;IAEtB,IAAI,CAACP,WAAW,CAACqE,YAAY,CAAC,IAAI,CAAC9D,QAAQ,CAAC,CAACuB,SAAS,CACnDwC,aAAuB,IAAI;MAC1B3B,OAAO,CAACO,GAAG,CAAC,4BAA4B,EAAEoB,aAAa,CAAC;MAExD,MAAMC,gBAAgB,GAAaD,aAAa;MAChD,IAAI7D,KAAK,GAAG,yBAAyB;MACrC,IAAI+D,OAAO,GAAG,8JAA8J;MAC5K,IAAIC,QAAQ,GAKK,SAAS;MAE1B,IAAI,CAACF,gBAAgB,EAAE;QACrB9D,KAAK,GAAG,sBAAsB;QAC9B+D,OAAO,GAAE,wDAAwD;QACjEC,QAAQ,GAAG,OAAO;;MAGpBzH,IAAI,CAACqG,IAAI,CAAC;QACR5C,KAAK,EAAEA,KAAK;QACZ6C,IAAI,EAAEkB,OAAO;QACbjB,IAAI,EAAEkB,QAAQ;QACdjB,iBAAiB,EAAE;OACpB,CAAC;MAEF;MACA,IAAI,CAACS,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,KAAK;MAClD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,UAAU;IAC1D,CAAC,EACAnB,KAAK,IAAI;MACRjG,IAAI,CAACqG,IAAI,CAAC;QACR5C,KAAK,EAAE,sBAAsB;QAC7B6C,IAAI,EAAE,gEAAgE;QACtEC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;MAEF,IAAI,CAACS,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,KAAK;MAClD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,UAAU;IAC1D,CAAC,CACF;IACH;EACJ;;EAGFM,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7G,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE6G,KAAK,EAAE;MACvC,MAAMC,SAAS,GAAG,IAAI,CAAC/G,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEmE,KAAK;MACrD,OAAO,IAAI,CAAChC,WAAW,CAAC4E,SAAS,CAACD,SAAS,CAAC,CAAC9B,IAAI,CAC/CtG,GAAG,CAAEsI,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAACzE,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACnB,sBAAsB,GACzB,wCAAwC;SAC3C,MAAM;UACL,IAAI,CAACmB,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACnB,sBAAsB,GAAG,EAAE;;QAElC,OAAO,IAAI,CAACmB,iBAAiB;MAC/B,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACnB,sBAAsB,GAAG,EAAE;MAChC,OAAOxC,EAAE,CAAC,IAAI,CAAC2D,iBAAiB,CAAC;;EAErC;EAEA0E,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAClH,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE6G,KAAK,EAAE;MACvC,MAAM7D,KAAK,GAAG,IAAI,CAACjD,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEmE,KAAK;MACjD,OAAO,IAAI,CAAClC,aAAa,CAACiF,eAAe,CAAClE,KAAK,CAAC,CAACgC,IAAI,CACnDtG,GAAG,CAAEsI,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAACxE,gBAAgB,GAAG,IAAI;UAC5B,IAAI,CAAC3B,uBAAuB,GAC1B,8CAA8C;SACjD,MAAM;UACL,IAAI,CAAC2B,gBAAgB,GAAG,KAAK;UAC7B,IAAI,CAAC3B,uBAAuB,GAAG,EAAE;;QAEnC,OAAO,IAAI,CAAC2B,gBAAgB;MAC9B,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAAC3B,uBAAuB,GAAG,EAAE;MACjC,OAAOjC,EAAE,CAAC,IAAI,CAAC4D,gBAAgB,CAAC;;EAEpC;EAEA2E,qBAAqBA,CAAA;IACnB,IAAI,CAACnF,MAAM,CAAC8D,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAAC,QAAAsB,CAAA,G;qBAzTUvF,2BAA2B,EAAAxC,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApI,EAAA,CAAAgI,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAtI,EAAA,CAAAgI,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAxI,EAAA,CAAAgI,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BnG,2BAA2B;IAAAoG,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCjBxC/I,EAAA,CAAAkC,SAAA,4BAAgD;QAChDlC,EAAA,CAAAC,cAAA,aAAkC;QAE1BD,EAAA,CAAAE,MAAA,0BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,gBAAyE;QAAlCD,EAAA,CAAAiJ,UAAA,mBAAAC,6DAAA;UAAA,OAASF,GAAA,CAAAlB,qBAAA,EAAuB;QAAA,EAAC;QACtE9H,EAAA,CAAAE,MAAA,uBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,cAAiE;QAAlCD,EAAA,CAAAiJ,UAAA,sBAAAE,8DAAA;UAAA,OAAYH,GAAA,CAAArC,kBAAA,EAAoB;QAAA,EAAC;QAC9D3G,EAAA,CAAAC,cAAA,aAAsB;QAECD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChCH,EAAA,CAAAC,cAAA,iBAMC;QAFCD,EAAA,CAAAiJ,UAAA,2BAAAG,sEAAAC,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAkG,SAAA,GAAAD,MAAA;QAAA,EAAgC;QAGhCrJ,EAAA,CAAAC,cAAA,iBAAqB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACnCH,EAAA,CAAAC,cAAA,kBAAmB;QAAAD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEjCH,EAAA,CAAAI,UAAA,KAAAmJ,2CAAA,kBAYM;QACRvJ,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAEKD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzCH,EAAA,CAAAC,cAAA,iBAOE;QAFAD,EAAA,CAAAiJ,UAAA,2BAAAO,qEAAAH,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAqG,QAAA,GAAAJ,MAAA;QAAA,EAA+B,2BAAAG,qEAAA;UAAA,OACdR,GAAA,CAAA5D,YAAA,EAAc;QAAA,EADA;QALjCpF,EAAA,CAAAG,YAAA,EAOE;QACFH,EAAA,CAAAI,UAAA,KAAAsJ,2CAAA,kBAiBM;QACR1J,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAwB;QACAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvCH,EAAA,CAAAC,cAAA,iBAOE;QAFAD,EAAA,CAAAiJ,UAAA,2BAAAU,qEAAAN,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAwG,cAAA,GAAAP,MAAA;QAAA,EAAqC,2BAAAM,qEAAA;UAAA,OACpBX,GAAA,CAAA3D,WAAA,EAAa;QAAA,EADO;QALvCrF,EAAA,CAAAG,YAAA,EAOE;QACFH,EAAA,CAAAI,UAAA,KAAAyJ,2CAAA,kBAiBM;QACR7J,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAECD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC3CH,EAAA,CAAAC,cAAA,iBAME;QADAD,EAAA,CAAAiJ,UAAA,2BAAAa,qEAAAT,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAA2G,kBAAA,GAAAV,MAAA;QAAA,EAAyC;QAL3CrJ,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAI,UAAA,KAAA4J,2CAAA,kBAYM;QACNhK,EAAA,CAAAI,UAAA,KAAA6J,6CAAA,oBAEU;QACZjK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAwB;QACCD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7CH,EAAA,CAAAC,cAAA,iBAME;QADAD,EAAA,CAAAiJ,UAAA,2BAAAiB,qEAAAb,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAA+G,aAAA,GAAAd,MAAA;QAAA,EAAoC;QALtCrJ,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAI,UAAA,KAAAgK,2CAAA,kBAiBM;QACRpK,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAECD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxCH,EAAA,CAAAC,cAAA,iBAOE;QAFAD,EAAA,CAAAiJ,UAAA,2BAAAoB,qEAAAhB,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,GAAA4B,MAAA;QAAA,EAAgC,2BAAAgB,qEAAA;UAAA,OACfrB,GAAA,CAAA9D,WAAA,EAAa;QAAA,EADE;QALlClF,EAAA,CAAAG,YAAA,EAOE;QACFH,EAAA,CAAAI,UAAA,KAAAkK,2CAAA,kBAYM;QACNtK,EAAA,CAAAI,UAAA,KAAAmK,6CAAA,oBAEU;QACZvK,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAEID,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtCH,EAAA,CAAAC,cAAA,iBAME;QADAD,EAAA,CAAAiJ,UAAA,2BAAAuB,qEAAAnB,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAqH,YAAA,GAAApB,MAAA;QAAA,EAAmC;QALrCrJ,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAI,UAAA,KAAAsK,2CAAA,kBAuBM;QACR1K,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAwB;QACOD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtDH,EAAA,CAAAkC,SAAA,iBAKE;QACFlC,EAAA,CAAAI,UAAA,KAAAuK,2CAAA,kBAYM;QACN3K,EAAA,CAAAI,UAAA,KAAAwK,6CAAA,oBAIC;QACH5K,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,sBAA8D;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;QApP3EH,EAAA,CAAAM,SAAA,GAAwB;QAAxBN,EAAA,CAAAO,UAAA,cAAAyI,GAAA,CAAAtI,UAAA,CAAwB;QAQtBV,EAAA,CAAAM,SAAA,GAAgC;QAAhCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAkG,SAAA,CAAgC;QAO/BtJ,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA8B,OAAA,GAAA2G,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAA0B,OAAA,CAAAuE,OAAA,QAAAvE,OAAA,GAAA2G,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAA0B,OAAA,CAAA0C,KAAA,OAAA1C,OAAA,GAAA2G,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAA0B,OAAA,CAAA2C,OAAA,GAIF;QAmBChF,EAAA,CAAAM,SAAA,GAA+B;QAA/BN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAqG,QAAA,CAA+B;QAI9BzJ,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAsK,OAAA,GAAA7B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAkK,OAAA,CAAAjE,OAAA,QAAAiE,OAAA,GAAA7B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAkK,OAAA,CAAA9F,KAAA,OAAA8F,OAAA,GAAA7B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAkK,OAAA,CAAA7F,OAAA,GAIF;QAsBChF,EAAA,CAAAM,SAAA,GAAqC;QAArCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAwG,cAAA,CAAqC;QAIpC5J,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAuK,OAAA,GAAA9B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAmK,OAAA,CAAAlE,OAAA,QAAAkE,OAAA,GAAA9B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAmK,OAAA,CAAA/F,KAAA,OAAA+F,OAAA,GAAA9B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAmK,OAAA,CAAA9F,OAAA,GAIF;QAwBChF,EAAA,CAAAM,SAAA,GAAyC;QAAzCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAA2G,kBAAA,CAAyC;QAGxC/J,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAwK,OAAA,GAAA/B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAoK,OAAA,CAAAnE,OAAA,QAAAmE,OAAA,GAAA/B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAoK,OAAA,CAAAhG,KAAA,OAAAgG,OAAA,GAAA/B,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAoK,OAAA,CAAA/F,OAAA,GAIF;QAQ2BhF,EAAA,CAAAM,SAAA,GAAsB;QAAtBN,EAAA,CAAAO,UAAA,SAAAyI,GAAA,CAAA7F,gBAAA,CAAsB;QAYhDnD,EAAA,CAAAM,SAAA,GAAoC;QAApCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAA+G,aAAA,CAAoC;QAGnCnK,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAyK,QAAA,GAAAhC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAqK,QAAA,CAAApE,OAAA,QAAAoE,QAAA,GAAAhC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAqK,QAAA,CAAAjG,KAAA,OAAAiG,QAAA,GAAAhC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,gCAAAqK,QAAA,CAAAhG,OAAA,GAIF;QAwBChF,EAAA,CAAAM,SAAA,GAAgC;QAAhCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,CAAgC;QAI/BzH,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA0K,QAAA,GAAAjC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAsK,QAAA,CAAArE,OAAA,QAAAqE,QAAA,GAAAjC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAsK,QAAA,CAAAlG,KAAA,OAAAkG,QAAA,GAAAjC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,4BAAAsK,QAAA,CAAAjG,OAAA,GAIF;QAQ2BhF,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAO,UAAA,SAAAyI,GAAA,CAAA9F,iBAAA,CAAuB;QAcjDlD,EAAA,CAAAM,SAAA,GAAmC;QAAnCN,EAAA,CAAAO,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAqH,YAAA,CAAmC;QAGlCzK,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA2K,QAAA,GAAAlC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAuK,QAAA,CAAAtE,OAAA,QAAAsE,QAAA,GAAAlC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAuK,QAAA,CAAAnG,KAAA,OAAAmG,QAAA,GAAAlC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,+BAAAuK,QAAA,CAAAlG,OAAA,GAIF;QA8BEhF,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA4K,QAAA,GAAAnC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,sCAAAwK,QAAA,CAAAvE,OAAA,QAAAuE,QAAA,GAAAnC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,sCAAAwK,QAAA,CAAApG,KAAA,OAAAoG,QAAA,GAAAnC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,sCAAAwK,QAAA,CAAAnG,OAAA,GAIF;QAUEhF,EAAA,CAAAM,SAAA,GAAiF;QAAjFN,EAAA,CAAAO,UAAA,UAAAyI,GAAA,CAAAtI,UAAA,CAAAE,MAAA,kBAAAoI,GAAA,CAAAtI,UAAA,CAAAE,MAAA,mBAAAwK,QAAA,GAAApC,GAAA,CAAAtI,UAAA,CAAAC,GAAA,sCAAAyK,QAAA,CAAArG,KAAA,EAAiF;;;;;;;SDrOjFvC,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}