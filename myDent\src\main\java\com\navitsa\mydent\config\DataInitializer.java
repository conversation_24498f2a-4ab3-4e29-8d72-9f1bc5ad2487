package com.navitsa.mydent.config;

import com.navitsa.mydent.entity.ClinicServicesCategory;
import com.navitsa.mydent.entity.Company;
import com.navitsa.mydent.entity.UserCategory;
import com.navitsa.mydent.entity.LaboratoryCategories;
import com.navitsa.mydent.entity.LaboratorySubCategories;
import com.navitsa.mydent.repositories.ClinicServicesCategoryRepositories;
import com.navitsa.mydent.repositories.CompanyRepository;
import com.navitsa.mydent.repositories.UserCategoryRepository;
import com.navitsa.mydent.repositories.LaboratoryCategoriesRepository;
import com.navitsa.mydent.repositories.LaboratorySubCategoriesRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class DataInitializer implements CommandLineRunner {

    private final CompanyRepository companyRepository;
    private final UserCategoryRepository userCategoryRepository;
    private final ClinicServicesCategoryRepositories clinicServicesCategoryRepositories;
    private final LaboratoryCategoriesRepository laboratoryCategoriesRepository;
    private final LaboratorySubCategoriesRepository laboratorySubCategoriesRepository;

    @Autowired
    public DataInitializer(CompanyRepository companyRepository,
                           UserCategoryRepository userCategoryRepository,
                           ClinicServicesCategoryRepositories clinicServicesCategoryRepositories,
                           LaboratoryCategoriesRepository laboratoryCategoriesRepository,
                           LaboratorySubCategoriesRepository laboratorySubCategoriesRepository) {
        this.companyRepository = companyRepository;
        this.userCategoryRepository = userCategoryRepository;
        this.clinicServicesCategoryRepositories = clinicServicesCategoryRepositories;
        this.laboratoryCategoriesRepository = laboratoryCategoriesRepository;
        this.laboratorySubCategoriesRepository = laboratorySubCategoriesRepository;
    }

    @Override
    public void run(String... args) throws Exception {
        // Initialize company
        if (companyRepository.count() == 0) {
            Company company = new Company();
            company.setCompanyName("Navitsa");
            company.setAddress("Railway Road");
            company.setCity("Maharagama");
            company.setCountry("Sri Lanka");
            company.setState("Colombo");
            company.setPostalCode("10200");
            company.setTelephone("0112475687");
            company.setEmail("<EMAIL>");
            company.setWeb("www.navitsa.com");
            company.setStatus("Active");

            companyRepository.save(company);
            System.out.println("Company initialized successfully.");
        } else {
            System.out.println("Company already exists.");
        }

        // Initialize user categories
        String[] categories = {"Admin", "Doctor", "Clinic", "Supplier", "Customer", "Laboratory", "Future Dentist"};
        String[] routerPath = {"admin", "doctor", "clinic", "supplier", "customer", "laboratory", "future-dentist"};
        int i = 0;
        for (String category : categories) {
            if (userCategoryRepository.findByCategoryName(category) == null) {
                UserCategory userCategory = new UserCategory();
                userCategory.setUserCategory(category);
                userCategory.setRouterPath(routerPath[i]);
                userCategoryRepository.save(userCategory);
                System.out.println("User category '" + category + "' initialized successfully.");
                i++;
            } else {
                System.out.println("User category '" + category + "' already exists.");
            }

            // Initialize clinic service categories
            String[] clinicCategories = {"Dental Bonding", "Cosmetic Fillings", "Invisalign", "Teeth Cleanings", "Root Canal Therapy", "Dental Sealants"};
            for (String clinicCategory : clinicCategories) {
                if (clinicServicesCategoryRepositories.findByClinicServiceCategoryName(clinicCategory) == null) {
                    ClinicServicesCategory clinicServicesCategory = new ClinicServicesCategory();
                    clinicServicesCategory.setClinicServiceCategoryName(clinicCategory);
                    clinicServicesCategoryRepositories.save(clinicServicesCategory);
                    System.out.println("Clinic service '" + clinicCategory + "' initialized successfully.");
                } else {
                    System.out.println("Clinic service '" + clinicCategory + "' already exists.");
                }
            }

            String[] laboratoryCategories = {
                    "Removable Dentures",
                    "Crown and Bridges",
                    "Implant Crown and Bridges",
                    "Splints/Special Trays",
                    "Corrections",
                    "Orthodontic Appliances",
                    "Digital Workflows",
                    "3D-Printings"
            };

            Map<String, String[]> subCategoryMap = new HashMap<>();
            subCategoryMap.put("Removable Dentures", new String[]{"Acrylic Full Dentures", "Flexible Full Dentures", "Acrylic Partial Dentures", "Flexible Partial Dentures", "CO-CH Partial Dentures"});
            subCategoryMap.put("Crown and Bridges", new String[]{"Study Models", "Wax Mock-ups", "Temporary Crowns and Bridges", "Acrylic Crown and Bridges", "Metal Crown and Bridges", "Full Ceramic Crown and Bridges", "Zirconia Crown and Bridges", "Post and Cores", "Inlays and Onlays"});
            subCategoryMap.put("Implant Crown and Bridges", new String[]{"Cement Retained", "Screw Retained"});
            subCategoryMap.put("Splints/Special Trays", new String[]{"Special Impression Trays", "Bleaching Trays", "Mouth Guards", "Night Guards", "Surgical Acrylic Splints", "Implant Guided Splints"});
            subCategoryMap.put("Corrections", new String[]{"Repair", "Relining", "Rebasing", "Additions"});
            subCategoryMap.put("Orthodontic Appliances", new String[]{""});
            subCategoryMap.put("Digital Workflows", new String[]{"Cast Scanning", "Crown and Bridge design", "Implant Placement Planning", "Implant Splint Design", "Full Mouth Rehabilitations", "Surgical Plate Design", "Orthodontic Devices"});
            subCategoryMap.put("3D-Printings", new String[]{"Temporary Crowns and Bridges", "Metal Crown and Bridges", "Full Ceramic Crown and Bridges", "Zirconia Crown and Bridges", "Implant Crown and Bridges", "Implant Guided Splints", "Surgical Guided Splints,", "Surgical Plates and Devices", "Orthodontic Appliances", "Anatomical Modules"});

            for (String laboratoryCategoryName : laboratoryCategories) {
                LaboratoryCategories laboratoryCategory = laboratoryCategoriesRepository.findByLaboratoryCategoryName(laboratoryCategoryName);
                if (laboratoryCategory == null) {
                    laboratoryCategory = new LaboratoryCategories();
                    laboratoryCategory.setLaboratoryCategoryName(laboratoryCategoryName);
                    laboratoryCategoriesRepository.save(laboratoryCategory);
                }

                String[] subCategories = subCategoryMap.get(laboratoryCategoryName);
                for (String subCategoryName : subCategories) {
                    if (subCategoryName == null || subCategoryName.trim().isEmpty()) {
                        continue;
                    }
                    if (laboratorySubCategoriesRepository.findByLaboratorySubCategoryNameAndLaboratoryCategory(subCategoryName, laboratoryCategoryName) == null) {
                        LaboratorySubCategories subCategory = new LaboratorySubCategories();
                        subCategory.setLaboratorySubCategoryName(subCategoryName);
                        subCategory.setLaboratoryCategoryId(laboratoryCategory);
                        laboratorySubCategoriesRepository.save(subCategory);
                    }
                }
            }
        }
    }
}