import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../user.service';
import { User } from '../user';
import Swal from 'sweetalert2'; // Import SweetAlert
import { CookieService } from 'ngx-cookie-service';

@Component({
  selector: 'app-user-login',
  templateUrl: './user-login.component.html',
  styleUrls: ['./user-login.component.css'],
})
export class UserLoginComponent {
  username: string = '';
  password: string = '';
  user: User = new User();
  remember: boolean = false;
  passwordVisible: boolean = false;

  constructor(
    private router: Router,
    private userService: UserService,
    private cookieService: CookieService
  ) {}

  ngOnInit(): void {
    const encodedData = this.cookieService.get('authData');

    if (encodedData) {
      try {
        const decoded = atob(encodedData);
        const [token, username, routerPath] = decoded.split('::');
console.log('Decoded Cookie Data:', { token, username, routerPath });
        if (username) {
          this.username = username;
          this.remember = true; // checked only if cookie exists
        }

        if (token && routerPath) {
          this.router.navigate(['/' + routerPath]);
        }
      } catch (e) {
        console.error('Invalid cookie format');
        this.remember = false; // reset if cookie invalid
      }
    } else {
      this.remember = false; // no cookie → checkbox unchecked
    }
  }

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
    const passwordField = document.getElementById(
      'password'
    ) as HTMLInputElement;
    passwordField.type = this.passwordVisible ? 'text' : 'password';
  }

  onSubmitLogin() {
    localStorage.clear();
    this.onLogin();
  }

  onLogin() {
    this.userService.login(this.user).subscribe(
      (response) => {
        console.log('Login Successful', response); // Log the successful response
        // Set the auth token after successful login
        this.userService.setAuthToken(response.token);

        // Log the complete response to check for clinicId
        console.log('API Response:', response.username);

        // Store the full user object and other relevant data
        localStorage.setItem('userid', response.id.toString());
        localStorage.setItem('firstName', response.firstName || '');
        localStorage.setItem('lastName', response.lastName || '');
        localStorage.setItem('companyId', response.companyId || '');
        localStorage.setItem('username', response.username || '');
        console.table(response);

        // ✅ Combine token, username, and routerPath into one string
        const combined = `${response.token}::${response.username}::${
          response.userCategoryId?.routerPath || ''
        }`;

        // ✅ Encode it using Base64 to hide content
        const encoded = btoa(combined);

        // ✅ Save cookie (persistent if Remember Me checked)
        if (this.remember) {
          this.cookieService.set('authData', encoded, 30, '/'); // 30 days, path = /
          console.log('Cookie Set:', this.cookieService.get('authData'));
        }

        if (
          response.userCategoryId != null &&
          response.userCategoryId.routerPath != null
        ) {
          this.router.navigate(['/' + response.userCategoryId.routerPath]);
        }
      },
      (error) => {
        // Handle login failure
        this.userService.setAuthToken(null);

        // Display a user-friendly error message based on the error response
        const errorMessage =
          error.error?.message || 'Login Failed: Invalid username or password.';

        // Use SweetAlert instead of alert
        Swal.fire({
          icon: 'error',
          title: 'Login Failed',
          text: errorMessage,
          confirmButtonText: 'OK',
        });

        console.log('Login Failed', error); // Log the full error details for further debugging
      }
    );
  }

  public logout(): void {
    localStorage.removeItem('authToken');
    this.cookieService.delete('authData', '/');
    
    // Reset Remember Me checkbox
    this.remember = false;

    localStorage.clear();
    this.router.navigate(['/home-page']);
  }

  navigateToUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
