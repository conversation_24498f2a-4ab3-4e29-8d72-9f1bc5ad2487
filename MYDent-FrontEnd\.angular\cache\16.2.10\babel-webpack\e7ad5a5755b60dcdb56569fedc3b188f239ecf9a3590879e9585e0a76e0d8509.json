{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { UserRoutingModule } from './user-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { UserLayoutComponent } from './user-layout/user-layout.component';\nimport { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';\nimport { CoreModule } from '../core/core.module';\nimport { UserForgetPasswordComponent } from './user-forget-password/user-forget-password.component';\nimport { UserPasswardChangeComponent } from './user-passward-change/user-passward-change.component';\nimport * as i0 from \"@angular/core\";\nclass UserModule {\n  static #_ = this.ɵfac = function UserModule_Factory(t) {\n    return new (t || UserModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UserModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, UserRoutingModule, FormsModule, ReactiveFormsModule, CoreModule]\n  });\n}\nexport { UserModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserModule, {\n    declarations: [UserLayoutComponent, UserMailVerificationComponent, UserForgetPasswordComponent, UserPasswardChangeComponent],\n    imports: [CommonModule, UserRoutingModule, FormsModule, ReactiveFormsModule, CoreModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "UserRoutingModule", "FormsModule", "ReactiveFormsModule", "UserLayoutComponent", "UserMailVerificationComponent", "CoreModule", "UserForgetPasswordComponent", "UserPasswardChangeComponent", "UserModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { UserRoutingModule } from './user-routing.module';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { UserLayoutComponent } from './user-layout/user-layout.component';\r\nimport { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';\r\nimport { CoreModule } from '../core/core.module';\r\nimport { UserForgetPasswordComponent } from './user-forget-password/user-forget-password.component';\r\nimport { UserPasswardChangeComponent } from './user-passward-change/user-passward-change.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    UserLayoutComponent,\r\n    UserMailVerificationComponent,\r\n    UserForgetPasswordComponent,\r\n    UserPasswardChangeComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    UserRoutingModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CoreModule\r\n  ]\r\n})\r\nexport class UserModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,2BAA2B,QAAQ,uDAAuD;;AAGnG,MAeaC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAPnBZ,YAAY,EACZC,iBAAiB,EACjBC,WAAW,EACXC,mBAAmB,EACnBG,UAAU;EAAA;;SAGDG,UAAU;;2EAAVA,UAAU;IAAAI,YAAA,GAbnBT,mBAAmB,EACnBC,6BAA6B,EAC7BE,2BAA2B,EAC3BC,2BAA2B;IAAAM,OAAA,GAG3Bd,YAAY,EACZC,iBAAiB,EACjBC,WAAW,EACXC,mBAAmB,EACnBG,UAAU;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}