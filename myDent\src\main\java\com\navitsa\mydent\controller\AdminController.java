package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.UserTemp;
import com.navitsa.mydent.services.UserTempServices;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin")
public class AdminController {

    private final UserTempServices userTempServices;

    @Autowired
    public AdminController(UserTempServices userTempServices) {
        this.userTempServices = userTempServices;
    }

    @GetMapping(path = "/getAllUserTemps")
    public List<UserTemp> getALLUserTemps(){
        return userTempServices.getAllUserTemps();
    }

    @GetMapping(path = "/updateUserTempToUserPermanent")
    public ResponseEntity<String> updateUserAsAVerified(@RequestParam("userTempId") Long userTempId){
        String responseText = userTempServices.approveUserTemp(userTempId);
        return ResponseEntity.ok(responseText);
    }
}
