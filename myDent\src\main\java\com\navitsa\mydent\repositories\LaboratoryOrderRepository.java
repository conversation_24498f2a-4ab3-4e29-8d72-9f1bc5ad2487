package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.entity.LaboratoryOrder;
import com.navitsa.mydent.entity.Clinic;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface LaboratoryOrderRepository extends JpaRepository<LaboratoryOrder,Integer> {
    @Query("SELECT lo FROM LaboratoryOrder lo WHERE lo.laboratorySetupId.laboratoryId.userId.userId= :userId")
    List<LaboratoryOrder> getClinicOrdersByLabUserId(@Param("userId") Integer userId);
    
    List <LaboratoryOrder> findByClinicId(Clinic clinicId);
}