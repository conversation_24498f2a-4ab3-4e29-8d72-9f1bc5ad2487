package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.UserCategory;
import com.navitsa.mydent.repositories.UserCategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserCategoryService {

    private final UserCategoryRepository userCategoryRepository;

    @Autowired
    public UserCategoryService(UserCategoryRepository userCategoryRepository) {
        this.userCategoryRepository = userCategoryRepository;
    }

    public UserCategory saveUserCategory(UserCategory userCategory) {
        try {
            return userCategoryRepository.save(userCategory);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the user category.");
        }
    }

    public List<UserCategory> findAllUserCategories() {
        try {
            return userCategoryRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of user categories.");
        }
    }

    public UserCategory getUserCategoryById(int id) {
        return userCategoryRepository.findById(id).orElse(null);
    }

    public UserCategory getUserCategoryByName(String name) {
        return userCategoryRepository.findByCategoryName(name);
    }

    public UserCategory updateUserCategory(int id, UserCategory userCategoryDetails) {
        try {
            UserCategory userCategory = userCategoryRepository.findById(id).orElse(null);
            if (userCategory == null) {
                throw new RuntimeException("User category not found with id: " + id);
            }

            userCategory.setUserCategory(userCategoryDetails.getUserCategory());
            return userCategoryRepository.save(userCategory);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the user category.");
        }
    }

    public void deleteUserCategory(int id) {
        try {
            userCategoryRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the user category.");
        }
    }
}
