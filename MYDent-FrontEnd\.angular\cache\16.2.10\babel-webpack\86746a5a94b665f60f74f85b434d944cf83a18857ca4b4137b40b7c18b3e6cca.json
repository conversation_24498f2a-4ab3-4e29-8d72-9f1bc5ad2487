{"ast": null, "code": "import Swal from 'sweetalert2';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../user.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction UserPasswardChangeComponent_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserPasswardChangeComponent_p_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Thank you for verifying your email!\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" You can now Create your New password. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UserPasswardChangeComponent_p_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Your email is already verified.\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" You can now Create your New password. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UserPasswardChangeComponent_p_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.message, \" \");\n  }\n}\nfunction UserPasswardChangeComponent_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtemplate(1, UserPasswardChangeComponent_p_8_ng_container_1_Template, 4, 0, \"ng-container\", 5);\n    i0.ɵɵtemplate(2, UserPasswardChangeComponent_p_8_ng_container_2_Template, 4, 0, \"ng-container\", 5);\n    i0.ɵɵtemplate(3, UserPasswardChangeComponent_p_8_ng_container_3_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message === \"Email verified successfully!\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message === \"Your email is already verified.\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message !== \"Email verified successfully!\" && ctx_r1.message !== \"Your email is already verified.\");\n  }\n}\nfunction UserPasswardChangeComponent_small_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1, \" Password is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserPasswardChangeComponent_small_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1, \" Must be at least 6 characters. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserPasswardChangeComponent_small_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1, \" Must contain at least one uppercase letter and one number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserPasswardChangeComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1, \" Confirm password is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserPasswardChangeComponent_small_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 15);\n    i0.ɵɵtext(1, \" Passwords do not match. \");\n    i0.ɵɵelementEnd();\n  }\n}\nclass UserPasswardChangeComponent {\n  constructor(route, userService, router, fb) {\n    this.route = route;\n    this.userService = userService;\n    this.router = router;\n    this.fb = fb;\n    this.loading = true;\n    this.message = '';\n    this.userType = '';\n    this.verificationToken = '';\n    this.password = '';\n    this.rePassword = '';\n  }\n  ngOnInit() {\n    this.resetForm = this.fb.group({\n      password: ['', [Validators.required, Validators.minLength(6), Validators.pattern(/^(?=.*[A-Z])(?=.*\\d).+$/) // At least 1 uppercase + 1 number\n      ]],\n\n      confirmPassword: ['', Validators.required]\n    }, {\n      validators: this.passwordsMatchValidator\n    } // ✅ Custom validator\n    );\n\n    this.route.queryParams.subscribe(params => {\n      const verificationToken = params['token'];\n      this.userType = params['userType'];\n      if (!verificationToken) {\n        this.message = 'No verification token provided.';\n        this.loading = false;\n        return;\n      }\n      this.verifyEmail(verificationToken);\n    });\n  }\n  // Custom validator to check both passwords match\n  passwordsMatchValidator(control) {\n    const password = control.get('password')?.value;\n    const confirmPassword = control.get('confirmPassword')?.value;\n    return password === confirmPassword ? null : {\n      passwordsMismatch: true\n    };\n  }\n  verifyEmail(token) {\n    this.userService.verifyEmailAllUsers(token).subscribe(response => {\n      if (response && typeof response === 'object') {\n        this.message = response.message || 'No message provided.';\n        this.verificationToken = token;\n      } else {\n        this.message = response;\n      }\n      this.loading = false;\n    }, error => {\n      this.message = error.error && error.error.message ? error.error.message : 'An error occurred. Please try again.';\n      this.loading = false;\n    });\n  }\n  resect() {\n    if (this.resetForm.invalid) {\n      this.resetForm.markAllAsTouched();\n      return;\n    }\n    this.userService.changePassword(this.resetForm.get('password')?.value, this.verificationToken).subscribe(response => {\n      console.log(response.status);\n      if (response.status == 'true') {\n        Swal.fire({\n          icon: 'success',\n          title: 'Password Changed',\n          text: 'Your password has been updated successfully!',\n          confirmButtonText: 'OK'\n        });\n        this.router.navigate(['/user-login']);\n      } else {\n        Swal.fire({\n          icon: 'error',\n          title: 'Error',\n          text: 'Something went wrong. Please try again.',\n          confirmButtonText: 'OK'\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function UserPasswardChangeComponent_Factory(t) {\n    return new (t || UserPasswardChangeComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.FormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserPasswardChangeComponent,\n    selectors: [[\"app-user-passward-change\"]],\n    decls: 27,\n    vars: 9,\n    consts: [[1, \"page-background\"], [1, \"rec1\"], [1, \"rec2\"], [1, \"verification-container\"], [1, \"verification-content\"], [4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"password\", 1, \"input-label\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", 1, \"form-control\"], [1, \"px-1\", 2, \"font-weight\", \"500\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"confirmPassword\", 1, \"input-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", 1, \"form-control\"], [\"type\", \"submit\", 1, \"mt-2\"], [1, \"text-danger\"]],\n    template: function UserPasswardChangeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\");\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, UserPasswardChangeComponent_p_7_Template, 2, 0, \"p\", 5);\n        i0.ɵɵtemplate(8, UserPasswardChangeComponent_p_8_Template, 4, 3, \"p\", 5);\n        i0.ɵɵelementStart(9, \"form\", 6);\n        i0.ɵɵlistener(\"ngSubmit\", function UserPasswardChangeComponent_Template_form_ngSubmit_9_listener() {\n          return ctx.resect();\n        });\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"label\", 8);\n        i0.ɵɵtext(12, \"New Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(13, \"input\", 9);\n        i0.ɵɵelementStart(14, \"div\", 10);\n        i0.ɵɵtemplate(15, UserPasswardChangeComponent_small_15_Template, 2, 0, \"small\", 11);\n        i0.ɵɵtemplate(16, UserPasswardChangeComponent_small_16_Template, 2, 0, \"small\", 11);\n        i0.ɵɵtemplate(17, UserPasswardChangeComponent_small_17_Template, 2, 0, \"small\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 7)(19, \"label\", 12);\n        i0.ɵɵtext(20, \"Confirm Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(21, \"input\", 13);\n        i0.ɵɵelementStart(22, \"div\", 10);\n        i0.ɵɵtemplate(23, UserPasswardChangeComponent_small_23_Template, 2, 0, \"small\", 11);\n        i0.ɵɵtemplate(24, UserPasswardChangeComponent_small_24_Template, 2, 0, \"small\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"button\", 14);\n        i0.ɵɵtext(26, \"Reset Password\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"\", ctx.userType, \" Email Verification\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.resetForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\")) && ((tmp_4_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_4_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_5_0.hasError(\"minlength\")) && ((tmp_5_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"pattern\")) && ((tmp_6_0 = ctx.resetForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.resetForm.get(\"confirmPassword\")) == null ? null : tmp_7_0.hasError(\"required\")) && ((tmp_7_0 = ctx.resetForm.get(\"confirmPassword\")) == null ? null : tmp_7_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.resetForm.hasError(\"passwordsMismatch\") && ((tmp_8_0 = ctx.resetForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.touched));\n      }\n    },\n    dependencies: [i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName],\n    styles: [\"html[_ngcontent-%COMP%], body[_ngcontent-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n  font-family: 'Arial', sans-serif;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  height: 100%;\\n  width: 100%;\\n  background-color: #f4f4f9;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.verification-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  z-index: 10;\\n  position: relative;\\n  padding: 20px; \\n\\n}\\n\\n.verification-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #ff6b00;\\n  font-weight: bold;\\n  margin-bottom: 30px;\\n  font-size: 24px; \\n\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background-color: #ff6b00;\\n  border: #ff6b00 1px solid;\\n  border-radius: 15px;\\n  color: white;\\n  margin-top: 20px;\\n  padding: 10px 20px;\\n  font-size: 16px; \\n\\n  width: 100%; \\n\\n  max-width: 200px;\\n}\\n\\np[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px; \\n\\n}\\n\\n.rec1[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  left: 740px;\\n  width: 900px;\\n  height: 1600px;\\n  background: #FB751E;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(72deg);\\n}\\n\\n.rec2[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 225px;\\n  left: -400px;\\n  width: 900px;\\n  height: 1800px;\\n  border: #FB751E 1px solid;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(40deg);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 20px; \\n\\n    margin-bottom: 20px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px; \\n\\n    max-width: 100%; \\n\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 600px; \\n\\n    height: 1200px;\\n    left: 500px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 600px;\\n    height: 1400px;\\n    left: -300px; \\n\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 15px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    padding: 8px 15px;\\n    max-width: 100%;\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1000px;\\n    left: 400px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1200px;\\n    left: -250px;\\n  }\\n}\\n\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #fb751e !important;\\n  box-shadow:none;\\n  transition: 0.2s ease-in-out;\\n}\\n\\n\\n\\n.input-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  color: #333;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { UserPasswardChangeComponent };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r9", "message", "ɵɵtemplate", "UserPasswardChangeComponent_p_8_ng_container_1_Template", "UserPasswardChangeComponent_p_8_ng_container_2_Template", "UserPasswardChangeComponent_p_8_ng_container_3_Template", "ɵɵproperty", "ctx_r1", "UserPasswardChangeComponent", "constructor", "route", "userService", "router", "fb", "loading", "userType", "verificationToken", "password", "rePassword", "ngOnInit", "resetForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "confirmPassword", "validators", "passwordsMatchValidator", "queryParams", "subscribe", "params", "verifyEmail", "control", "get", "value", "passwordsMismatch", "token", "verifyEmailAllUsers", "response", "error", "resect", "invalid", "mark<PERSON>llAsTouched", "changePassword", "console", "log", "status", "fire", "icon", "title", "text", "confirmButtonText", "navigate", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "UserService", "Router", "i3", "FormBuilder", "_2", "selectors", "decls", "vars", "consts", "template", "UserPasswardChangeComponent_Template", "rf", "ctx", "UserPasswardChangeComponent_p_7_Template", "UserPasswardChangeComponent_p_8_Template", "ɵɵlistener", "UserPasswardChangeComponent_Template_form_ngSubmit_9_listener", "UserPasswardChangeComponent_small_15_Template", "UserPasswardChangeComponent_small_16_Template", "UserPasswardChangeComponent_small_17_Template", "UserPasswardChangeComponent_small_23_Template", "UserPasswardChangeComponent_small_24_Template", "tmp_4_0", "<PERSON><PERSON><PERSON><PERSON>", "touched", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-passward-change\\user-passward-change.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-passward-change\\user-passward-change.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UserService } from '../user.service';\r\nimport Swal from 'sweetalert2';\r\nimport {\r\n  AbstractControl,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Clinic } from 'src/app/clinic/clinic';\r\nimport { User, UserCategory } from '../user';\r\n\r\n@Component({\r\n  selector: 'app-user-passward-change',\r\n  templateUrl: './user-passward-change.component.html',\r\n  styleUrls: ['./user-passward-change.component.css'],\r\n})\r\nexport class UserPasswardChangeComponent implements OnInit {\r\n  loading = true;\r\n  message = '';\r\n  userType = '';\r\n  verificationToken = '';\r\n  password = '';\r\n  rePassword = '';\r\n  resetForm!: FormGroup;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.resetForm = this.fb.group(\r\n      {\r\n        password: [\r\n          '',\r\n          [\r\n            Validators.required,\r\n            Validators.minLength(6),\r\n            Validators.pattern(/^(?=.*[A-Z])(?=.*\\d).+$/), // At least 1 uppercase + 1 number\r\n          ],\r\n        ],\r\n        confirmPassword: ['', Validators.required],\r\n      },\r\n      { validators: this.passwordsMatchValidator } // ✅ Custom validator\r\n    );\r\n\r\n    this.route.queryParams.subscribe((params) => {\r\n      const verificationToken = params['token'];\r\n      this.userType = params['userType'];\r\n\r\n      if (!verificationToken) {\r\n        this.message = 'No verification token provided.';\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      this.verifyEmail(verificationToken);\r\n    });\r\n  }\r\n\r\n  // Custom validator to check both passwords match\r\n  passwordsMatchValidator(\r\n    control: AbstractControl\r\n  ): { [key: string]: boolean } | null {\r\n    const password = control.get('password')?.value;\r\n    const confirmPassword = control.get('confirmPassword')?.value;\r\n    return password === confirmPassword ? null : { passwordsMismatch: true };\r\n  }\r\n\r\n  verifyEmail(token: string): void {\r\n    this.userService.verifyEmailAllUsers(token).subscribe(\r\n      (response: any) => {\r\n        if (response && typeof response === 'object') {\r\n          this.message = response.message || 'No message provided.';\r\n          this.verificationToken = token;\r\n        } else {\r\n          this.message = response;\r\n        }\r\n        this.loading = false;\r\n      },\r\n      (error) => {\r\n        this.message =\r\n          error.error && error.error.message\r\n            ? error.error.message\r\n            : 'An error occurred. Please try again.';\r\n        this.loading = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  resect(): void {\r\n    if (this.resetForm.invalid) {\r\n      this.resetForm.markAllAsTouched();\r\n      return;\r\n    }\r\n\r\n    this.userService\r\n      .changePassword(this.resetForm.get('password')?.value, this.verificationToken)\r\n      .subscribe((response: any) => {\r\n        console.log(response.status);\r\n        if (response.status == 'true') {\r\n          Swal.fire({\r\n            icon: 'success',\r\n            title: 'Password Changed',\r\n            text: 'Your password has been updated successfully!',\r\n            confirmButtonText: 'OK',\r\n          });\r\n          this.router.navigate(['/user-login']);\r\n        } else {\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Error',\r\n            text: 'Something went wrong. Please try again.',\r\n            confirmButtonText: 'OK',\r\n          });\r\n        }\r\n      });\r\n  }\r\n}\r\n", "<div class=\"page-background\">\r\n  <div class=\"rec1\"></div>\r\n  <div class=\"rec2\"></div>\r\n  <div class=\"verification-container\">\r\n    <div class=\"verification-content\">\r\n      <h2>{{ userType }} Email Verification</h2>\r\n\r\n      <p *ngIf=\"loading\">Loading...</p>\r\n      \r\n      <p *ngIf=\"!loading\">\r\n        <ng-container *ngIf=\"message === 'Email verified successfully!'\">\r\n          Thank you for verifying your email!<br>\r\n          You can now Create your New password.\r\n        </ng-container>\r\n        \r\n        <ng-container *ngIf=\"message === 'Your email is already verified.'\">\r\n          Your email is already verified.<br>\r\n          You can now Create your New password.\r\n        </ng-container>\r\n        \r\n        <ng-container *ngIf=\"message !== 'Email verified successfully!' && message !== 'Your email is already verified.'\">\r\n          {{ message }}\r\n        </ng-container>\r\n      </p>\r\n<form [formGroup]=\"resetForm\" (ngSubmit)=\"resect()\">\r\n  <!-- Password -->\r\n  <div class=\"col-12 mb-3\">\r\n    <label for=\"password\" class=\"input-label\">New Password</label>\r\n    <input\r\n      type=\"password\"\r\n      id=\"password\"\r\n      formControlName=\"password\"\r\n      class=\"form-control\"\r\n    />\r\n\r\n    <div class=\"px-1\" style=\"font-weight: 500;\">\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"resetForm.get('password')?.hasError('required') && resetForm.get('password')?.touched\"\r\n      >\r\n        Password is required.\r\n      </small>\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"resetForm.get('password')?.hasError('minlength') && resetForm.get('password')?.touched\"\r\n      >\r\n        Must be at least 6 characters.\r\n      </small>\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"resetForm.get('password')?.hasError('pattern') && resetForm.get('password')?.touched\"\r\n      >\r\n        Must contain at least one uppercase letter and one number.\r\n      </small>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Confirm Password -->\r\n  <div class=\"col-12 mb-3\">\r\n    <label for=\"confirmPassword\" class=\"input-label\">Confirm Password</label>\r\n    <input\r\n      type=\"password\"\r\n      id=\"confirmPassword\"\r\n      formControlName=\"confirmPassword\"\r\n      class=\"form-control\"\r\n    />\r\n\r\n    <div class=\"px-1\" style=\"font-weight: 500;\">\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"resetForm.get('confirmPassword')?.hasError('required') && resetForm.get('confirmPassword')?.touched\"\r\n      >\r\n        Confirm password is required.\r\n      </small>\r\n\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"resetForm.hasError('passwordsMismatch') && resetForm.get('confirmPassword')?.touched\"\r\n      >\r\n        Passwords do not match.\r\n      </small>\r\n    </div>\r\n  </div>\r\n\r\n  <button type=\"submit\" class=\" mt-2\">Reset Password</button>\r\n</form>\r\n\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA,OAAOA,IAAI,MAAM,aAAa;AAC9B,SAIEC,UAAU,QACL,gBAAgB;;;;;;;;ICFjBC,EAAA,CAAAC,cAAA,QAAmB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG/BH,EAAA,CAAAI,uBAAA,GAAiE;IAC/DJ,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAK,SAAA,SAAI;IACvCL,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;;IAEfN,EAAA,CAAAI,uBAAA,GAAoE;IAClEJ,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAK,SAAA,SAAI;IACnCL,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;;IAEfN,EAAA,CAAAI,uBAAA,GAAkH;IAChHJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;IADbN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAbFV,EAAA,CAAAC,cAAA,QAAoB;IAClBD,EAAA,CAAAW,UAAA,IAAAC,uDAAA,0BAGe;IAEfZ,EAAA,CAAAW,UAAA,IAAAE,uDAAA,0BAGe;IAEfb,EAAA,CAAAW,UAAA,IAAAG,uDAAA,0BAEe;IACjBd,EAAA,CAAAG,YAAA,EAAI;;;;IAbaH,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,oCAAgD;IAKhDV,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,uCAAmD;IAKnDV,EAAA,CAAAO,SAAA,GAAiG;IAAjGP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,uCAAAM,MAAA,CAAAN,OAAA,uCAAiG;;;;;IAgBlHV,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAeRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAERH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;ADnEd,MAKac,2BAA2B;EAStCC,YACUC,KAAqB,EACrBC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAHf,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAZZ,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAb,OAAO,GAAG,EAAE;IACZ,KAAAc,QAAQ,GAAG,EAAE;IACb,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,UAAU,GAAG,EAAE;EAQZ;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAC5B;MACEJ,QAAQ,EAAE,CACR,EAAE,EACF,CACE3B,UAAU,CAACgC,QAAQ,EACnBhC,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,EACvBjC,UAAU,CAACkC,OAAO,CAAC,yBAAyB,CAAC,CAAE;MAAA,CAChD,CACF;;MACDC,eAAe,EAAE,CAAC,EAAE,EAAEnC,UAAU,CAACgC,QAAQ;KAC1C,EACD;MAAEI,UAAU,EAAE,IAAI,CAACC;IAAuB,CAAE,CAAC;KAC9C;;IAED,IAAI,CAACjB,KAAK,CAACkB,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1C,MAAMd,iBAAiB,GAAGc,MAAM,CAAC,OAAO,CAAC;MACzC,IAAI,CAACf,QAAQ,GAAGe,MAAM,CAAC,UAAU,CAAC;MAElC,IAAI,CAACd,iBAAiB,EAAE;QACtB,IAAI,CAACf,OAAO,GAAG,iCAAiC;QAChD,IAAI,CAACa,OAAO,GAAG,KAAK;QACpB;;MAGF,IAAI,CAACiB,WAAW,CAACf,iBAAiB,CAAC;IACrC,CAAC,CAAC;EACJ;EAEA;EACAW,uBAAuBA,CACrBK,OAAwB;IAExB,MAAMf,QAAQ,GAAGe,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;IAC/C,MAAMT,eAAe,GAAGO,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEC,KAAK;IAC7D,OAAOjB,QAAQ,KAAKQ,eAAe,GAAG,IAAI,GAAG;MAAEU,iBAAiB,EAAE;IAAI,CAAE;EAC1E;EAEAJ,WAAWA,CAACK,KAAa;IACvB,IAAI,CAACzB,WAAW,CAAC0B,mBAAmB,CAACD,KAAK,CAAC,CAACP,SAAS,CAClDS,QAAa,IAAI;MAChB,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC5C,IAAI,CAACrC,OAAO,GAAGqC,QAAQ,CAACrC,OAAO,IAAI,sBAAsB;QACzD,IAAI,CAACe,iBAAiB,GAAGoB,KAAK;OAC/B,MAAM;QACL,IAAI,CAACnC,OAAO,GAAGqC,QAAQ;;MAEzB,IAAI,CAACxB,OAAO,GAAG,KAAK;IACtB,CAAC,EACAyB,KAAK,IAAI;MACR,IAAI,CAACtC,OAAO,GACVsC,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACtC,OAAO,GAC9BsC,KAAK,CAACA,KAAK,CAACtC,OAAO,GACnB,sCAAsC;MAC5C,IAAI,CAACa,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEA0B,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACpB,SAAS,CAACqB,OAAO,EAAE;MAC1B,IAAI,CAACrB,SAAS,CAACsB,gBAAgB,EAAE;MACjC;;IAGF,IAAI,CAAC/B,WAAW,CACbgC,cAAc,CAAC,IAAI,CAACvB,SAAS,CAACa,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,EAAE,IAAI,CAAClB,iBAAiB,CAAC,CAC7Ea,SAAS,CAAES,QAAa,IAAI;MAC3BM,OAAO,CAACC,GAAG,CAACP,QAAQ,CAACQ,MAAM,CAAC;MAC5B,IAAIR,QAAQ,CAACQ,MAAM,IAAI,MAAM,EAAE;QAC7BzD,IAAI,CAAC0D,IAAI,CAAC;UACRC,IAAI,EAAE,SAAS;UACfC,KAAK,EAAE,kBAAkB;UACzBC,IAAI,EAAE,8CAA8C;UACpDC,iBAAiB,EAAE;SACpB,CAAC;QACF,IAAI,CAACvC,MAAM,CAACwC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;OACtC,MAAM;QACL/D,IAAI,CAAC0D,IAAI,CAAC;UACRC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE,yCAAyC;UAC/CC,iBAAiB,EAAE;SACpB,CAAC;;IAEN,CAAC,CAAC;EACN;EAAC,QAAAE,CAAA,G;qBAvGU7C,2BAA2B,EAAAjB,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjE,EAAA,CAAA+D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnE,EAAA,CAAA+D,iBAAA,CAAAC,EAAA,CAAAI,MAAA,GAAApE,EAAA,CAAA+D,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BtD,2BAA2B;IAAAuD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClBxC9E,EAAA,CAAAC,cAAA,aAA6B;QAC3BD,EAAA,CAAAK,SAAA,aAAwB;QAExBL,EAAA,CAAAC,cAAA,aAAoC;QAE5BD,EAAA,CAAAE,MAAA,GAAiC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1CH,EAAA,CAAAW,UAAA,IAAAqE,wCAAA,eAAiC;QAEjChF,EAAA,CAAAW,UAAA,IAAAsE,wCAAA,eAcI;QACVjF,EAAA,CAAAC,cAAA,cAAoD;QAAtBD,EAAA,CAAAkF,UAAA,sBAAAC,8DAAA;UAAA,OAAYJ,GAAA,CAAA9B,MAAA,EAAQ;QAAA,EAAC;QAEjDjD,EAAA,CAAAC,cAAA,cAAyB;QACmBD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9DH,EAAA,CAAAK,SAAA,gBAKE;QAEFL,EAAA,CAAAC,cAAA,eAA4C;QAC1CD,EAAA,CAAAW,UAAA,KAAAyE,6CAAA,oBAKQ;QACRpF,EAAA,CAAAW,UAAA,KAAA0E,6CAAA,oBAKQ;QACRrF,EAAA,CAAAW,UAAA,KAAA2E,6CAAA,oBAKQ;QACVtF,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,cAAyB;QAC0BD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzEH,EAAA,CAAAK,SAAA,iBAKE;QAEFL,EAAA,CAAAC,cAAA,eAA4C;QAC1CD,EAAA,CAAAW,UAAA,KAAA4E,6CAAA,oBAKQ;QAERvF,EAAA,CAAAW,UAAA,KAAA6E,6CAAA,oBAKQ;QACVxF,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,kBAAoC;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;QA/EnDH,EAAA,CAAAO,SAAA,GAAiC;QAAjCP,EAAA,CAAAQ,kBAAA,KAAAuE,GAAA,CAAAvD,QAAA,wBAAiC;QAEjCxB,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAe,UAAA,SAAAgE,GAAA,CAAAxD,OAAA,CAAa;QAEbvB,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAe,UAAA,UAAAgE,GAAA,CAAAxD,OAAA,CAAc;QAelBvB,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAe,UAAA,cAAAgE,GAAA,CAAAlD,SAAA,CAAuB;QAcpB7B,EAAA,CAAAO,SAAA,GAA2F;QAA3FP,EAAA,CAAAe,UAAA,WAAA0E,OAAA,GAAAV,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAA+C,OAAA,CAAAC,QAAA,mBAAAD,OAAA,GAAAV,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAA+C,OAAA,CAAAE,OAAA,EAA2F;QAM3F3F,EAAA,CAAAO,SAAA,GAA4F;QAA5FP,EAAA,CAAAe,UAAA,WAAA6E,OAAA,GAAAb,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAAkD,OAAA,CAAAF,QAAA,oBAAAE,OAAA,GAAAb,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAAkD,OAAA,CAAAD,OAAA,EAA4F;QAM5F3F,EAAA,CAAAO,SAAA,GAA0F;QAA1FP,EAAA,CAAAe,UAAA,WAAA8E,OAAA,GAAAd,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAAmD,OAAA,CAAAH,QAAA,kBAAAG,OAAA,GAAAd,GAAA,CAAAlD,SAAA,CAAAa,GAAA,+BAAAmD,OAAA,CAAAF,OAAA,EAA0F;QAoB1F3F,EAAA,CAAAO,SAAA,GAAyG;QAAzGP,EAAA,CAAAe,UAAA,WAAA+E,OAAA,GAAAf,GAAA,CAAAlD,SAAA,CAAAa,GAAA,sCAAAoD,OAAA,CAAAJ,QAAA,mBAAAI,OAAA,GAAAf,GAAA,CAAAlD,SAAA,CAAAa,GAAA,sCAAAoD,OAAA,CAAAH,OAAA,EAAyG;QAOzG3F,EAAA,CAAAO,SAAA,GAA0F;QAA1FP,EAAA,CAAAe,UAAA,SAAAgE,GAAA,CAAAlD,SAAA,CAAA6D,QAAA,2BAAAK,OAAA,GAAAhB,GAAA,CAAAlD,SAAA,CAAAa,GAAA,sCAAAqD,OAAA,CAAAJ,OAAA,EAA0F;;;;;;;SD3DtF1E,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}