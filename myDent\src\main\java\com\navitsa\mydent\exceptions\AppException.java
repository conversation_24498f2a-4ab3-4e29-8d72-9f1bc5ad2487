package com.navitsa.mydent.exceptions;

import org.springframework.http.HttpStatus;

public class AppException extends RuntimeException {

    private static final long serialVersionUID = 1L; // Add this line

    private final HttpStatus httpStatus;
    private final String errorCode; // Custom error code

    // Constructor using a builder
    private AppException(Builder builder) {
        super(builder.message);
        this.httpStatus = builder.httpStatus;
        this.errorCode = builder.errorCode;
    }

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }

    public String getErrorCode() {
        return errorCode;
    }

    // Builder class for easy construction
    public static class Builder {
        private String message;
        private HttpStatus httpStatus;
        private String errorCode;

        public Builder message(String message) {
            this.message = message;
            return this;
        }

        public Builder httpStatus(HttpStatus httpStatus) {
            this.httpStatus = httpStatus;
            return this;
        }

        public Builder errorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public AppException build() {
            return new AppException(this);
        }
    }

    // Convenience constructors
    public AppException(String message, HttpStatus httpStatus, String errorCode) {
        super(message);
        this.httpStatus = httpStatus;
        this.errorCode = errorCode;
    }

    public AppException(String message, HttpStatus httpStatus) {
        super(message);
        this.httpStatus = httpStatus;
        this.errorCode = null; // No specific error code
    }

    // Example usage:
    // throw new AppException.Builder()
    //         .message("Something went wrong")
    //         .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    //         .errorCode("5001")
    //         .build();
}
