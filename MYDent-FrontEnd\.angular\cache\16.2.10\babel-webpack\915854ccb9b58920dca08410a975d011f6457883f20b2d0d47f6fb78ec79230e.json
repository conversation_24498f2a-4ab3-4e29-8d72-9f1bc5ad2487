{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { UserLoginComponent } from 'src/app/user/user-login/user-login.component';\nlet ClinicSideBarComponent = class ClinicSideBarComponent extends UserLoginComponent {};\nClinicSideBarComponent = __decorate([Component({\n  selector: 'app-clinic-side-bar',\n  templateUrl: './clinic-side-bar.component.html',\n  styleUrls: ['./clinic-side-bar.component.css']\n})], ClinicSideBarComponent);\nexport { ClinicSideBarComponent };", "map": {"version": 3, "names": ["Component", "UserLoginComponent", "ClinicSideBarComponent", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\components\\clinic-side-bar\\clinic-side-bar.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { UserLoginComponent } from 'src/app/user/user-login/user-login.component';\r\n\r\n@Component({\r\n  selector: 'app-clinic-side-bar',\r\n  templateUrl: './clinic-side-bar.component.html',\r\n  styleUrls: ['./clinic-side-bar.component.css']\r\n})\r\nexport class ClinicSideBarComponent extends UserLoginComponent{\r\n\r\n  \r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,kBAAkB,QAAQ,8CAA8C;AAOjF,IAAaC,sBAAsB,GAAnC,MAAaA,sBAAuB,SAAQD,kBAAkB,GAG7D;AAHYC,sBAAsB,GAAAC,UAAA,EALlCH,SAAS,CAAC;EACTI,QAAQ,EAAE,qBAAqB;EAC/BC,WAAW,EAAE,kCAAkC;EAC/CC,SAAS,EAAE,CAAC,iCAAiC;CAC9C,CAAC,C,EACWJ,sBAAsB,CAGlC;SAHYA,sBAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}