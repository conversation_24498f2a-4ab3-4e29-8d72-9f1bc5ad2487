{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../shared/download-buttons/download-buttons.component\";\nimport * as i3 from \"../../core/default-navbar/default-navbar.component\";\nclass HomePageComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  navigateUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  navigateAppointment() {\n    this.router.navigate(['/appointment']);\n  }\n  static #_ = this.ɵfac = function HomePageComponent_Factory(t) {\n    return new (t || HomePageComponent)(i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomePageComponent,\n    selectors: [[\"app-home-page\"]],\n    decls: 21,\n    vars: 0,\n    consts: [[\"loggedUser\", \"Test Home\"], [1, \"cover-page\", \"container-fluid\"], [1, \"color-shape\", \"bottom-right\"], [1, \"text-section\"], [1, \"gradient-text\"], [1, \"register-now-button\", 3, \"click\"], [1, \"appointment-button\", 3, \"click\"], [\"src\", \"assets/images/arrow.png\", \"alt\", \"arrow\", 1, \"arrow\"], [1, \"btn-text\"]],\n    template: function HomePageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"h1\", 4);\n        i0.ɵɵtext(5, \"Don't Delay It Any Longer.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"h1\", 4);\n        i0.ɵɵtext(7, \"Book Your Dentist Appointment Here.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"h1\", 4);\n        i0.ɵɵtext(9, \"Find Relief And Smile Again.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function HomePageComponent_Template_button_click_10_listener() {\n          return ctx.navigateUserSelection();\n        });\n        i0.ɵɵtext(11, \"Register Now\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"h4\");\n        i0.ɵɵtext(13, \"Find your nearest dental clinic\");\n        i0.ɵɵelement(14, \"br\");\n        i0.ɵɵtext(15, \"Book your appointment...\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function HomePageComponent_Template_button_click_16_listener() {\n          return ctx.navigateAppointment();\n        });\n        i0.ɵɵelement(17, \"img\", 7);\n        i0.ɵɵelementStart(18, \"div\", 8);\n        i0.ɵɵtext(19, \"MAKE APPOINTMENT\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelement(20, \"app-download-buttons\");\n      }\n    },\n    dependencies: [i2.DownloadButtonsComponent, i3.DefaultNavbarComponent],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  height: 100%;\\n}\\n\\napp-header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n}\\n\\n.cover-page[_ngcontent-%COMP%] {\\n  \\n\\n  position: relative;\\n    width: 100%;\\n    height: 100vh;\\n    padding-top: 8%;\\n  background-color: white;\\n}\\n\\n.color-shape.bottom-right[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 900px;\\n  \\n\\n  height: 600px;\\n  \\n\\n  background-image: url(\\\"/assets/images/bgImage.png\\\");\\n  \\n\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  background-position: bottom right;\\n}\\n\\n\\n.text-section[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  margin-left: 5%;\\n  position: relative;\\n}\\n\\n.gradient-text[_ngcontent-%COMP%] {\\n  \\n\\n  background: linear-gradient(\\n    268.23deg,\\n    #000000 3.24%,\\n    #d85322 34.72%,\\n    #000000 66.19%,\\n    #fb751e 97.67%\\n  );\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-size: 40px;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  background-clip: text;\\n}\\n\\nh4[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 32px;\\n  font-style: italic;\\n  margin: 30px 0;\\n  font-weight: 300;\\n}\\n\\n.appointment-button[_ngcontent-%COMP%] {\\n  width: 329px;\\n  height: 52px;\\n  background: linear-gradient(to right, #333333, #fb751e);\\n  color: white;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 23px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.appointment-button[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n}\\n\\n.btn-text[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  margin-left: 30px;\\n}\\n\\n@media (max-width:1440px){\\n  .cover-page[_ngcontent-%COMP%]{\\n    height: 90vw;\\n  }\\n}\\n\\n\\n@media (max-width:1100px){\\n.gradient-text[_ngcontent-%COMP%] {\\n  \\n\\n  background: linear-gradient(\\n    268.23deg,\\n    #000000 3.24%,\\n    #d85322 34.72%,\\n    #000000 66.19%,\\n    #fb751e 97.67%\\n  );\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-size:4.3vw;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  background-clip: text;\\n}\\n.cover-page[_ngcontent-%COMP%]{\\n  height:110vw;\\n}\\n.color-shape.bottom-right[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 100%;\\n  \\n\\n  height: 600px;\\n  \\n\\n  background-image: url(\\\"/assets/images/bgImage.png\\\");\\n  \\n\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  background-position: bottom right;\\n}\\n\\n\\n}\\n\\n@media (max-width:1365px){\\n  .register-now-button[_ngcontent-%COMP%] {\\n    width: 287px;\\n    height: 52px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 20px;\\n    font-weight: bold;\\n    border-radius: 23px;\\n    cursor: pointer;\\n    margin-top: 10px;\\n    right: 0;\\n    top: 0;\\n    margin-right: 5%;\\n    margin-bottom: 30px;\\n  }\\n}\\n\\n@media (max-width:1365px){\\n\\n  .register-now-button[_ngcontent-%COMP%] {\\n    width: 287px;\\n    height: 52px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 20px;\\n    font-weight: bold;\\n    border-radius: 23px;\\n    cursor: pointer;\\n    margin-top: 10px;\\n    right: 0;\\n    top: 0;\\n    margin-right: 5%;\\n    margin-bottom: 30px;\\n  }\\n\\n    .color-shape.bottom-right[_ngcontent-%COMP%] {\\n      position: absolute;\\n      bottom: 0;\\n      right: 0;\\n      width: 100%;\\n      \\n\\n      height: 600px;\\n      \\n\\n      background-image: url(\\\"/assets/images/bgImage.png\\\");\\n      \\n\\n      background-size: contain;\\n      background-repeat: no-repeat;\\n      background-position: bottom right;\\n    }\\n  \\n}\\n\\n\\n@media (min-width:1366px){\\n\\n  .register-now-button[_ngcontent-%COMP%] {\\n    width: 287px;\\n    height: 52px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 20px;\\n    font-weight: bold;\\n    border-radius: 23px;\\n    cursor: pointer;\\n    position: absolute;\\n    right: 0;\\n    top: 0;\\n    margin-right: 5%;\\n  }\\n  \\n\\n\\n\\n  \\n}\\n\\n@media (max-width:940px){\\n.text-section[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  margin-left: 5%;\\n  position: relative;\\n  padding-top: 5%;\\n}\\n.cover-page[_ngcontent-%COMP%]{\\n  height: 135vw;\\n  \\n}\\nh4[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 29px;\\n  font-style: italic;\\n  margin: 30px 0;\\n  font-weight: 300;\\n}\\n\\n.color-shape.bottom-right[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 100%;\\n  \\n\\n  height: 600px;\\n  \\n\\n  background-image: url(\\\"/assets/images/bgImage.png\\\");\\n  \\n\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  background-position: bottom right;\\n}\\n\\n}\\n\\n@media (max-width:700px){\\n  .gradient-text[_ngcontent-%COMP%] {\\n    \\n\\n    background: linear-gradient(\\n      268.23deg,\\n      #000000 3.24%,\\n      #d85322 34.72%,\\n      #000000 66.19%,\\n      #fb751e 97.67%\\n    );\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    font-size:4.8vw;\\n    font-weight: 700;\\n    margin: 10px 0;\\n    background-clip: text;\\n  }\\n  .text-section[_ngcontent-%COMP%] {\\n    z-index: 1;\\n    margin-left: 5%;\\n    position: relative;\\n    padding-top: 10%;\\n  }\\n  h4[_ngcontent-%COMP%] {\\n    color: #555;\\n    font-size: 4.5vw;\\n    font-style: italic;\\n    margin: 30px 0;\\n    font-weight: 300;\\n  }\\n  .cover-page[_ngcontent-%COMP%]{\\n    height:160vw;\\n  }\\n    .color-shape.bottom-right[_ngcontent-%COMP%] {\\n      position: absolute;\\n      bottom: 0;\\n      right: 0;\\n      width: 100%;\\n      \\n\\n      height: 600px;\\n      \\n\\n      background-image: url(\\\"/assets/images/bgImage.png\\\");\\n      \\n\\n      background-size: contain;\\n      background-repeat: no-repeat;\\n      background-position: bottom right;\\n    }\\n  \\n}\\n\\n\\n@media(max-width:500px){\\n.text-section[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  margin-left: 5%;\\n  position: relative;\\n  padding-top: 20%;\\n}\\n.gradient-text[_ngcontent-%COMP%] {\\n  \\n\\n  background: linear-gradient(\\n    268.23deg,\\n    #000000 3.24%,\\n    #d85322 34.72%,\\n    #000000 66.19%,\\n    #fb751e 97.67%\\n  );\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-size:5.7vw;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  background-clip: text;\\n}\\n.btn-text[_ngcontent-%COMP%] {\\n  font-size: 4vw;\\n  font-weight: 700;\\n  margin-left: 30px;\\n}\\n.appointment-button[_ngcontent-%COMP%] {\\n  width: 80%;\\n  height: 48px;\\n  background: linear-gradient(to right, #333333, #fb751e);\\n  color: white;\\n  border: none;\\n  cursor: pointer;\\n  border-radius: 23px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n\\n.appointment-button[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  width: 38px;\\n  height: 38px;\\n}\\nh4[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 5.5vw;\\n  font-style: italic;\\n  margin: 30px 0;\\n  font-weight: 300;\\n}\\n.register-now-button[_ngcontent-%COMP%] {\\n  width: 80%;\\n  height: 48px;\\n  background: none;\\n  border: 2px solid #fb751e;\\n  color: #fb751e;\\n  font-size: 4.8vw;\\n  font-weight: bold;\\n  border-radius: 23px;\\n  cursor: pointer;\\n  margin-top: 10px;\\n  right: 0;\\n  top: 0;\\n  margin-right: 5%;\\n  margin-bottom: 30px;\\n}\\n.cover-page[_ngcontent-%COMP%]{\\n  height: 200vw;\\n}\\n\\n.color-shape.bottom-right[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0;\\n  right: 0;\\n  width: 100%;\\n  \\n\\n  height: 600px;\\n  \\n\\n  background-image: url(\\\"/assets/images/bgImage.png\\\");\\n  \\n\\n  background-size: contain;\\n  background-repeat: no-repeat;\\n  background-position: bottom right;\\n}\\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { HomePageComponent };", "map": {"version": 3, "names": ["HomePageComponent", "constructor", "router", "navigateUserSelection", "navigate", "navigateAppointment", "_", "i0", "ɵɵdirectiveInject", "i1", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "HomePageComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "HomePageComponent_Template_button_click_10_listener", "HomePageComponent_Template_button_click_16_listener"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\home-page\\home-page.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\home-page\\home-page.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home-page',\r\n  templateUrl: './home-page.component.html',\r\n  styleUrls: ['./home-page.component.css']\r\n})\r\nexport class HomePageComponent {\r\n  constructor( private router: Router ) {}\r\n\r\n  navigateUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n\r\n  navigateAppointment() {\r\n    this.router.navigate(['/appointment']);\r\n  }\r\n}\r\n", "<app-default-navbar loggedUser=\"Test Home\"/>\r\n\r\n<div class=\"cover-page container-fluid\">\r\n    <div class=\"color-shape bottom-right\"></div> <!-- This will hold the background image -->\r\n    <div class=\"text-section\">\r\n        <h1 class=\"gradient-text\">Don't Delay It Any Longer.</h1>\r\n        <h1 class=\"gradient-text\">Book Your Dentist Appointment Here.</h1>\r\n        <h1 class=\"gradient-text\">Find Relief And Smile Again.</h1>\r\n\r\n        <button class=\"register-now-button\" (click)=\"navigateUserSelection()\">Register Now</button>\r\n\r\n        <h4>Find your nearest dental clinic<br>Book your appointment...</h4>\r\n\r\n        <button class=\"appointment-button\" (click)=\"navigateAppointment()\">\r\n            <img src=\"assets/images/arrow.png\" alt=\"arrow\" class=\"arrow\" />\r\n            <div class=\"btn-text\">MAKE APPOINTMENT</div>\r\n        </button>\r\n    </div>\r\n</div>\r\n<app-download-buttons></app-download-buttons>\r\n"], "mappings": ";;;;AAGA,MAKaA,iBAAiB;EAC5BC,YAAqBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAY;EAEvCC,qBAAqBA,CAAA;IACnB,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAACH,MAAM,CAACE,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;EAAC,QAAAE,CAAA,G;qBATUN,iBAAiB,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBX,iBAAiB;IAAAY,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR9BX,EAAA,CAAAa,SAAA,4BAA4C;QAE5Cb,EAAA,CAAAc,cAAA,aAAwC;QACpCd,EAAA,CAAAa,SAAA,aAA4C;QAC5Cb,EAAA,CAAAc,cAAA,aAA0B;QACId,EAAA,CAAAe,MAAA,iCAA0B;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QACzDhB,EAAA,CAAAc,cAAA,YAA0B;QAAAd,EAAA,CAAAe,MAAA,0CAAmC;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAClEhB,EAAA,CAAAc,cAAA,YAA0B;QAAAd,EAAA,CAAAe,MAAA,mCAA4B;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAE3DhB,EAAA,CAAAc,cAAA,iBAAsE;QAAlCd,EAAA,CAAAiB,UAAA,mBAAAC,oDAAA;UAAA,OAASN,GAAA,CAAAhB,qBAAA,EAAuB;QAAA,EAAC;QAACI,EAAA,CAAAe,MAAA,oBAAY;QAAAf,EAAA,CAAAgB,YAAA,EAAS;QAE3FhB,EAAA,CAAAc,cAAA,UAAI;QAAAd,EAAA,CAAAe,MAAA,uCAA+B;QAAAf,EAAA,CAAAa,SAAA,UAAI;QAAAb,EAAA,CAAAe,MAAA,gCAAwB;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAEpEhB,EAAA,CAAAc,cAAA,iBAAmE;QAAhCd,EAAA,CAAAiB,UAAA,mBAAAE,oDAAA;UAAA,OAASP,GAAA,CAAAd,mBAAA,EAAqB;QAAA,EAAC;QAC9DE,EAAA,CAAAa,SAAA,cAA+D;QAC/Db,EAAA,CAAAc,cAAA,cAAsB;QAAAd,EAAA,CAAAe,MAAA,wBAAgB;QAAAf,EAAA,CAAAgB,YAAA,EAAM;QAIxDhB,EAAA,CAAAa,SAAA,4BAA6C;;;;;;;SDXhCpB,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}