package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Table(name= "supplier")
public class Supplier{
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "supplier_id")
	private Integer supplierId;
	
	@ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "supplier_category_id", referencedColumnName = "supplier_category_id")
    private SupplierCategories supplierCategories;

	@OneToOne(fetch = FetchType.EAGER)
	@NotFound(action = NotFoundAction.IGNORE)
	@JoinColumn(name = "user_id", referencedColumnName = "user_id")
	private User userId;
	
	@Column(name= "name")
	private String name;
	
	@Column(name= "address")
	private String address;
	
	@Column(name= "city")
	private String city;
	
	@Column(name= "state")
	private String state;
	
	@Column(name= "country")
	private String country;
	
	@Column(name= "contact_person")
	private String contactPerson;

	@Column(name = "designation")
	private String designation;
	
	@Column(name= "tele")
	private String tele;
	
	@Column(name= "email")
	private String email;
	
	@Column(name= "web")
	private String web;
	
	@Column(name= "registered_date")
	private String registeredDate;
	
	@Column(name= "latitude")
	private String latitude;
	
	@Column(name= "longitude")
	private String longitude;

	public Supplier() {}

	public Supplier(Integer supplierId, SupplierCategories supplierCategories, User userId, String name, String address, String city, String state, String country, String contactPerson, String designation, String tele, String email, String web, String registeredDate, String latitude, String longitude) {
		this.supplierId = supplierId;
		this.supplierCategories = supplierCategories;
		this.userId = userId;
		this.name = name;
		this.address = address;
		this.city = city;
		this.state = state;
		this.country = country;
		this.contactPerson = contactPerson;
		this.designation = designation;
		this.tele = tele;
		this.email = email;
		this.web = web;
		this.registeredDate = registeredDate;
		this.latitude = latitude;
		this.longitude = longitude;
	}

	public Supplier(User userId, String name, String address, String city, String state, String country, String contactPerson, String designation, String tele, String email) {
		this.userId = userId;
		this.name = name;
		this.address = address;
		this.city = city;
		this.state = state;
		this.country = country;
		this.contactPerson = contactPerson;
		this.designation = designation;
		this.tele = tele;
		this.email = email;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public User getUserId() {
		return userId;
	}

	public void setUserId(User userId) {
		this.userId = userId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public SupplierCategories getSupplierCategories() {
		return supplierCategories;
	}

	public void setSupplierCategories(SupplierCategories supplierCategories) {
		this.supplierCategories = supplierCategories;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getContactPerson() {
		return contactPerson;
	}

	public void setContactPerson(String contactPerson) {
		this.contactPerson = contactPerson;
	}

	public String getTele() {
		return tele;
	}

	public void setTele(String tele) {
		this.tele = tele;
	}

	public String getDesignation() {
		return designation;
	}

	public void setDesignation(String designation) {
		this.designation = designation;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getWeb() {
		return web;
	}

	public void setWeb(String web) {
		this.web = web;
	}

	public String getRegisteredDate() {
		return registeredDate;
	}

	public void setRegisteredDate(String registeredDate) {
		this.registeredDate = registeredDate;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
}

