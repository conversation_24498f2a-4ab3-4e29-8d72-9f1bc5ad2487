import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';
import Swal from 'sweetalert2';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { Clinic } from 'src/app/clinic/clinic';
import { User, UserCategory } from '../user';

@Component({
  selector: 'app-user-passward-change',
  templateUrl: './user-passward-change.component.html',
  styleUrls: ['./user-passward-change.component.css'],
})
export class UserPasswardChangeComponent implements OnInit {
  loading = true;
  message = '';
  userType = '';
  verificationToken = '';
  password = '';
  rePassword = '';
  resetForm!: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private userService: UserService,
    private router: Router,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.resetForm = this.fb.group(
      {
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(6),
            Validators.pattern(/^(?=.*[A-Z])(?=.*\d).+$/), // At least 1 uppercase + 1 number
          ],
        ],
        confirmPassword: ['', Validators.required],
      },
      { validators: this.passwordsMatchValidator } // ✅ Custom validator
    );

    this.route.queryParams.subscribe((params) => {
      const verificationToken = params['token'];
      this.userType = params['userType'];

      if (!verificationToken) {
        this.message = 'No verification token provided.';
        this.loading = false;
        return;
      }

      this.verifyEmail(verificationToken);
    });
  }

  // Custom validator to check both passwords match
  passwordsMatchValidator(
    control: AbstractControl
  ): { [key: string]: boolean } | null {
    const password = control.get('password')?.value;
    const confirmPassword = control.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { passwordsMismatch: true };
  }

  verifyEmail(token: string): void {
    this.userService.verifyEmailAllUsers(token).subscribe(
      (response: any) => {
        if (response && typeof response === 'object') {
          this.message = response.message || 'No message provided.';
          this.verificationToken = token;
        } else {
          this.message = response;
        }
        this.loading = false;
      },
      (error) => {
        this.message =
          error.error && error.error.message
            ? error.error.message
            : 'An error occurred. Please try again.';
        this.loading = false;
      }
    );
  }

  resect(): void {
    if (this.resetForm.invalid) {
      this.resetForm.markAllAsTouched();
      return;
    }

    this.userService
      .changePassword(this.resetForm.get('password')?.value, this.verificationToken)
      .subscribe((response: any) => {
        console.log(response.status);
        if (response.status == 'true') {
          Swal.fire({
            icon: 'success',
            title: 'Password Changed',
            text: 'Your password has been updated successfully!',
            confirmButtonText: 'OK',
          });
          this.router.navigate(['/user-login']);
        } else {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Something went wrong. Please try again.',
            confirmButtonText: 'OK',
          });
        }
      });
  }
}
