{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nclass FutureDentistNavbarComponent {\n  constructor(renderer, document) {\n    this.renderer = renderer;\n    this.document = document;\n    this.msgCount = 0;\n    this.notificationCount = 0;\n    this.loginStatus = 0;\n    this.assignedUserId = 0;\n    this.notifications = [];\n    this.description = '';\n    this.notificationId = 0;\n    this.notificationStatus = '';\n    this.compName = '';\n    this.taskHeads = [];\n    this.countArray = [];\n    this.isIframe = false;\n    this.isShow = false;\n    this.loginDisplay = false;\n    this._destroying$ = new Subject();\n  }\n  ngOnInit() {}\n  toggleSidebar() {\n    this.isShow = !this.isShow;\n    if (this.isShow == true) {\n      this.renderer.addClass(this.document.body, 'toggle-sidebar');\n    } else {\n      this.renderer.removeClass(this.document.body, 'toggle-sidebar');\n    }\n  }\n  static #_ = this.ɵfac = function FutureDentistNavbarComponent_Factory(t) {\n    return new (t || FutureDentistNavbarComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FutureDentistNavbarComponent,\n    selectors: [[\"app-future-dentist-navbar\"]],\n    decls: 15,\n    vars: 3,\n    consts: [[1, \"row\"], [1, \"col-12\", \"g-0\"], [1, \"navbar\", \"navbar-expand-lg\"], [1, \"container-fluid\"], [1, \"navbar-content\", \"d-flex\", \"align-items-center\", \"w-100\"], [\"src\", \"../../assets/images/logo2.png\", \"alt\", \"Logo\", 1, \"logo\"], [\"type\", \"button\", 1, \"navbar-toggler\", \"ms-auto\", 3, \"click\"], [1, \"navbar-toggler-icon\"], [1, \"welcome-message\"], [\"alt\", \"User Profile Picture\", 1, \"profile-pic\", \"ms-3\", 3, \"src\"], [1, \"username\", \"ms-2\"]],\n    template: function FutureDentistNavbarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementStart(6, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function FutureDentistNavbarComponent_Template_button_click_6_listener() {\n          return ctx.toggleSidebar();\n        });\n        i0.ɵɵelement(7, \"span\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"span\", 8);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(10, \"img\", 9);\n        i0.ɵɵelementStart(11, \"span\", 10);\n        i0.ɵɵtext(12, \"Hello, \");\n        i0.ɵɵelement(13, \"br\");\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate1(\"Welcome Back, \", ctx.userName, \"!\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"src\", ctx.userProfilePic, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.userName);\n      }\n    },\n    styles: [\".navbar[_ngcontent-%COMP%] {\\n  height: 80px;\\n  background: linear-gradient(to right,  #ffffff, #FB751E, #B93426); \\n\\n}\\n\\n.navbar-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  margin-left: 2%;\\n  height: 3vw; \\n\\n  width: 12vw; \\n\\n  margin-right: 2%; \\n\\n}\\n\\n.welcome-message[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  margin-left: 10%;\\n  font-size: 2vw;\\n  color: white;\\n  margin-right: 35%;\\n}\\n\\n\\n.profile-pic[_ngcontent-%COMP%] {\\n  height: 6vh; \\n\\n  width: 6vh; \\n\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  margin-right: 2%; \\n\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  margin-right: 2%; \\n\\n  font-size: 1vw; \\n\\n  color: white;\\n}\\n\\n.navbar-toggler[_ngcontent-%COMP%] {\\n  height: 6vw; \\n\\n  width: 6vw; \\n\\n  margin-left: auto;\\n}\\n\\n@media (max-width: 768px) {\\n  .navbar-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: start;\\n  }\\n  .navbar-toggler[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n  .spacer[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { FutureDentistNavbarComponent };", "map": {"version": 3, "names": ["DOCUMENT", "Subject", "FutureDentistNavbarComponent", "constructor", "renderer", "document", "msgCount", "notificationCount", "loginStatus", "assignedUserId", "notifications", "description", "notificationId", "notificationStatus", "compName", "taskHeads", "count<PERSON><PERSON><PERSON>", "isIframe", "isShow", "loginDisplay", "_destroying$", "ngOnInit", "toggleSidebar", "addClass", "body", "removeClass", "_", "i0", "ɵɵdirectiveInject", "Renderer2", "_2", "selectors", "decls", "vars", "consts", "template", "FutureDentistNavbarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵlistener", "FutureDentistNavbarComponent_Template_button_click_6_listener", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "userName", "ɵɵproperty", "userProfilePic", "ɵɵsanitizeUrl", "ɵɵtextInterpolate"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\future-dentist\\components\\future-dentist-navbar\\future-dentist-navbar.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\future-dentist\\components\\future-dentist-navbar\\future-dentist-navbar.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { Component, Inject, OnInit, Renderer2 } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-future-dentist-navbar',\r\n  templateUrl: './future-dentist-navbar.component.html',\r\n  styleUrls: ['./future-dentist-navbar.component.css'],\r\n})\r\nexport class FutureDentistNavbarComponent implements OnInit {\r\n  msgCount: number = 0;\r\n  notificationCount: number = 0;\r\n  loginStatus: number = 0;\r\n  assignedUserId: number = 0;\r\n  notifications: Notification[] = [];\r\n  description: string = '';\r\n  notificationId: number = 0;\r\n  notificationStatus: string = '';\r\n  public compName: string = '';\r\n  taskHeads: any = [];\r\n  countArray: any = [];\r\n\r\n  isIframe = false;\r\n  isShow = false;\r\n  loginDisplay = false;\r\n  private readonly _destroying$ = new Subject<void>();\r\n  userName: any;\r\n  userProfilePic: any;\r\n\r\n  constructor(\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: Document\r\n  ) {}\r\n\r\n  ngOnInit(): void {}\r\n\r\n  toggleSidebar() {\r\n    this.isShow = !this.isShow;\r\n    if (this.isShow == true) {\r\n      this.renderer.addClass(this.document.body, 'toggle-sidebar');\r\n    } else {\r\n      this.renderer.removeClass(this.document.body, 'toggle-sidebar');\r\n    }\r\n  }\r\n}\r\n", "<div class=\"row\">\r\n  <div class=\"col-12 g-0\">\r\n    <nav class=\"navbar navbar-expand-lg\">\r\n      <div class=\"container-fluid\">\r\n        <div class=\"navbar-content d-flex align-items-center w-100\">\r\n          <img src=\"../../assets/images/logo2.png\" alt=\"Logo\" class=\"logo\">\r\n          <button class=\"navbar-toggler ms-auto\" type=\"button\" (click)=\"toggleSidebar()\">\r\n            <span class=\"navbar-toggler-icon\"></span>\r\n          </button>\r\n          <span class=\"welcome-message\">Welcome Back, {{ userName }}!</span>\r\n          <!-- <span class=\"spacer\"></span> -->\r\n          <img [src]=\"userProfilePic\" alt=\"User Profile Picture\" class=\"profile-pic ms-3\">\r\n          <span class=\"username ms-2\">Hello, <br>{{ userName }}</span>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAE1C,SAASC,OAAO,QAAQ,MAAM;;AAE9B,MAKaC,4BAA4B;EAoBvCC,YACUC,QAAmB,EACDC,QAAkB;IADpC,KAAAD,QAAQ,GAARA,QAAQ;IACU,KAAAC,QAAQ,GAARA,QAAQ;IArBpC,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,cAAc,GAAW,CAAC;IAC1B,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,cAAc,GAAW,CAAC;IAC1B,KAAAC,kBAAkB,GAAW,EAAE;IACxB,KAAAC,QAAQ,GAAW,EAAE;IAC5B,KAAAC,SAAS,GAAQ,EAAE;IACnB,KAAAC,UAAU,GAAQ,EAAE;IAEpB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,YAAY,GAAG,KAAK;IACH,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAQ;EAOhD;EAEHoB,QAAQA,CAAA,GAAU;EAElBC,aAAaA,CAAA;IACX,IAAI,CAACJ,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1B,IAAI,IAAI,CAACA,MAAM,IAAI,IAAI,EAAE;MACvB,IAAI,CAACd,QAAQ,CAACmB,QAAQ,CAAC,IAAI,CAAClB,QAAQ,CAACmB,IAAI,EAAE,gBAAgB,CAAC;KAC7D,MAAM;MACL,IAAI,CAACpB,QAAQ,CAACqB,WAAW,CAAC,IAAI,CAACpB,QAAQ,CAACmB,IAAI,EAAE,gBAAgB,CAAC;;EAEnE;EAAC,QAAAE,CAAA,G;qBAlCUxB,4BAA4B,EAAAyB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,SAAA,GAAAF,EAAA,CAAAC,iBAAA,CAsB7B5B,QAAQ;EAAA;EAAA,QAAA8B,EAAA,G;UAtBP5B,4BAA4B;IAAA6B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTzCV,EAAA,CAAAY,cAAA,aAAiB;QAKPZ,EAAA,CAAAa,SAAA,aAAiE;QACjEb,EAAA,CAAAY,cAAA,gBAA+E;QAA1BZ,EAAA,CAAAc,UAAA,mBAAAC,8DAAA;UAAA,OAASJ,GAAA,CAAAhB,aAAA,EAAe;QAAA,EAAC;QAC5EK,EAAA,CAAAa,SAAA,cAAyC;QAC3Cb,EAAA,CAAAgB,YAAA,EAAS;QACThB,EAAA,CAAAY,cAAA,cAA8B;QAAAZ,EAAA,CAAAiB,MAAA,GAA6B;QAAAjB,EAAA,CAAAgB,YAAA,EAAO;QAElEhB,EAAA,CAAAa,SAAA,cAAgF;QAChFb,EAAA,CAAAY,cAAA,gBAA4B;QAAAZ,EAAA,CAAAiB,MAAA,eAAO;QAAAjB,EAAA,CAAAa,SAAA,UAAI;QAAAb,EAAA,CAAAiB,MAAA,IAAc;QAAAjB,EAAA,CAAAgB,YAAA,EAAO;;;QAH9BhB,EAAA,CAAAkB,SAAA,GAA6B;QAA7BlB,EAAA,CAAAmB,kBAAA,mBAAAR,GAAA,CAAAS,QAAA,MAA6B;QAEtDpB,EAAA,CAAAkB,SAAA,GAAsB;QAAtBlB,EAAA,CAAAqB,UAAA,QAAAV,GAAA,CAAAW,cAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAsB;QACYvB,EAAA,CAAAkB,SAAA,GAAc;QAAdlB,EAAA,CAAAwB,iBAAA,CAAAb,GAAA,CAAAS,QAAA,CAAc;;;;;;SDHlD7C,4BAA4B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}