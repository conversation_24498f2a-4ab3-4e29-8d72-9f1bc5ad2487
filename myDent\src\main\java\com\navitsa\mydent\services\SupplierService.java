package com.navitsa.mydent.services;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.SupplierRepository;

@Service
public class SupplierService {

    private final SupplierRepository supplierRepository;
    
    @Autowired
    public SupplierService(SupplierRepository supplierRepository) {
        this.supplierRepository = supplierRepository;
    }
    
    @Autowired
    EmailService emailService;

    public Supplier saveSupplier(Supplier supplier) {
        try {
        	if (supplierRepository.findByName(supplier.getName()).isPresent()) {
                throw new RuntimeException("Laboratory with " + supplier.getName() + " already exists.");
            }

            SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
            Date currentDate = new Date();
            String formattedDate = dateFormatter.format(currentDate);

            supplier.setRegisteredDate(formattedDate);
            Supplier savedSupplier = supplierRepository.save(supplier);
            
           	User user = savedSupplier.getUserId();
            String verificationToken = user.getVerificationToken();
            String verificationTokenWithUserType = verificationToken + "&userType=Supplier"; 
            System.out.println("token: " + verificationTokenWithUserType);
            
            CompletableFuture.runAsync(() -> 
                    emailService.sendRegistrationEmail(
                    	savedSupplier.getEmail(),
                    	savedSupplier.getName(),
                        "http://localhost:4200/user/verify?token=" + verificationTokenWithUserType,
                        "Supplier"
                    ));
            
            return savedSupplier;

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the supplier.");
        }
    }

    public Supplier save(Supplier supplier){
        if (supplierRepository.findByName(supplier.getName()).isPresent()) {
            throw new RuntimeException("Laboratory with " + supplier.getName() + " already exists.");
        }
        return supplierRepository.save(supplier);
    }

    public List<Supplier> findAllSuppliers() {
        try {
            return (List<Supplier>) supplierRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of suppliers.");
        }
    }

    public Supplier getSupplierById(int id) {
        return supplierRepository.findById(id).orElseThrow(() -> new RuntimeException("Supplier not found with id: " + id));
    }

    public Supplier updateSupplier(int id, Supplier supplierDetails) {
        try {
            Supplier supplier = supplierRepository.findById(id).orElse(null);

            if (supplier == null) {
                throw new RuntimeException("Supplier not found with id: " + id);
            }

            supplier.setName(supplierDetails.getName());
            supplier.setAddress(supplierDetails.getAddress());
            supplier.setCity(supplierDetails.getCity());
            supplier.setState(supplierDetails.getState());
            supplier.setCountry(supplierDetails.getCountry());
            supplier.setContactPerson(supplierDetails.getContactPerson());
            supplier.setTele(supplierDetails.getTele());
            supplier.setDesignation((supplierDetails.getDesignation()));
            supplier.setEmail(supplierDetails.getEmail());
            supplier.setWeb(supplierDetails.getWeb());
            supplier.setRegisteredDate(supplierDetails.getRegisteredDate());
            supplier.setLatitude(supplierDetails.getLatitude());
            supplier.setLongitude(supplierDetails.getLongitude());
            supplier.setSupplierCategories(supplierDetails.getSupplierCategories());

            return supplierRepository.save(supplier);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the supplier.");
        }
    }

    public void deleteSupplier(int id) {
        try {
            supplierRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the supplier.");
        }
    }
    public boolean supplierNameExists(String supplierName){
        Optional<Supplier> existingClinic = supplierRepository.findByName(supplierName);
        return existingClinic.isPresent();
    }

    public Supplier getSupplierByUserId(int userId) {
        return supplierRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("Supplier not found with id: " + userId));
    }

  
}
