import { signal, WritableSignal } from "@angular/core";
import { LaboratorySetup } from "../laboratory/laboratory";
import { Supplier } from "../supplier/supplier";
import { User } from "../user/user";

export class Clinic {
  clinicId: number = 0;
  clinicCategories: ClinicCategories = new ClinicCategories();
  userId: User = new User();
  name: string = '';
  address: string = '';
  city: string = '';
  state: string = '';
  country: string = '';
  contactPerson: string = '';
  tele: string = '';
  email: string = '';
  web: string = '';
  registeredDate: string = '';
  latitude: string = '';
  longitude: string = '';
}

export class ClinicCategories {
  clinicCategoryId: number = 0;
  clinicCategory: string = '';
}

export class ClinicLaboratoryOrder {
  laboratoryOrderId: number = 0;
  clinicId: number = 0;
  laboratorySetupId: LaboratorySetup = new LaboratorySetup();
  status: string = '';
  patientName: string = '';
  dateOfBirth: string = '';
  contactNumber: string = '';
  expectedDate: string = '';
  toothNumber: number = 0;
  toothSurface: string = '';
  typeOfCrown: string = '';
  shade: string = '';
  materialSpecifications: string = '';
  occlusion: string = '';
  marginType: string = '';
  crownDesign: string = '';
  additionalInstructions: string = '';
  laboratoryOrderDate: string = '';
  laboratoryOrderTime: string = '';
}

export class ClinicDoctor {}

export class ClinicServices{
  clinicServiceCategoryId:number = 0;
  clinicServiceCategoryName: string = '';
}

export interface ClinicAppointment{
  id: number;
  patientName: string;
  time: string;
  date: string;
  timeAgo: string;
  isConfirmed: boolean;
}


// Clinic - Supplier Inventory Items

export interface SupplierInventoryItem {
  inventoryItemId: string;
  inventoryName: string;
  inventoryPrice: number;
  inventoryQty: number;
  imageURL: string;
}

export interface CartItemHead{
  supplierId:Supplier
}

export interface CartItemDetails {
  item: SupplierInventoryItem;
  quantity: number;
  selected?: boolean;
}

export interface SupplierCategoryItemList{
  id: number;
  name: string;
}

export interface InventoryOrderRequestDto {
  supplierId: number;
  cartItemList: CartItemDetails[];
  userId: number;
}
export interface TimeSlot {
  time: string;
  status: 'available' | 'disabled' | 'selected';
}


