package com.navitsa.mydent.entity;

import jakarta.persistence.*;

import java.io.Serializable;

@Entity
@Table(name = "supplier_order_details")
public class SupplierOrderDetails implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "supplier_order_details_id")
    private Integer supplierOrderDetailsId;

    @ManyToOne
    @JoinColumn(name = "supplier_inventory_id", referencedColumnName = "supplier_inventory_id")
    private SupplierInventory supplierInventory;

    @Column(name = "requested_item_qty")
    private Integer requestedItemQty;
    @Column(name = "approved_item_qty")
    private Integer approvedItemQty;

    @ManyToOne
    @JoinColumn(name = "supplier_order_header_id",referencedColumnName = "supplier_order_header_id")
    private SupplierOrderHeader supplierOrderHeader;

    public SupplierOrderDetails() {
    }

    public SupplierOrderDetails(SupplierInventory supplierInventory, Integer requestedItemQty, SupplierOrderHeader supplierOrderHeader) {
        this.supplierInventory = supplierInventory;
        this.requestedItemQty = requestedItemQty;
        this.supplierOrderHeader = supplierOrderHeader;
    }

    public SupplierOrderDetails(SupplierInventory supplierInventory, Integer requestedItemQty, Integer approvedItemQty, SupplierOrderHeader supplierOrderHeader) {
        this.supplierInventory = supplierInventory;
        this.requestedItemQty = requestedItemQty;
        this.approvedItemQty = approvedItemQty;
        this.supplierOrderHeader = supplierOrderHeader;
    }

    public Integer getSupplierOrderDetailsId() {
        return supplierOrderDetailsId;
    }

    public void setSupplierOrderDetailsId(Integer supplierOrderDetailsId) {
        this.supplierOrderDetailsId = supplierOrderDetailsId;
    }

    public SupplierInventory getSupplierInventory() {
        return supplierInventory;
    }

    public void setSupplierInventory(SupplierInventory supplierInventory) {
        this.supplierInventory = supplierInventory;
    }

    public Integer getRequestedItemQty() {
        return requestedItemQty;
    }

    public void setRequestedItemQty(Integer requestedItemQty) {
        this.requestedItemQty = requestedItemQty;
    }

    public Integer getApprovedItemQty() {
        return approvedItemQty;
    }

    public void setApprovedItemQty(Integer approvedItemQty) {
        this.approvedItemQty = approvedItemQty;
    }

    public SupplierOrderHeader getSupplierOrderHeader() {
        return supplierOrderHeader;
    }

    public void setSupplierOrderHeader(SupplierOrderHeader supplierOrderHeader) {
        this.supplierOrderHeader = supplierOrderHeader;
    }

    @Override
    public String toString() {
        return "SupplierOrderDetails{" +
                "supplierOrderDetailsId=" + supplierOrderDetailsId +
                ", supplierInventory=" + supplierInventory +
                ", requestedItemQty=" + requestedItemQty +
                ", approvedItemQty=" + approvedItemQty +
                ", supplierOrderHeader=" + supplierOrderHeader +
                '}';
    }
}
