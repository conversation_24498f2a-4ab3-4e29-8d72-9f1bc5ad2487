package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.SupplierOrderDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SupplierOrderDetailsRepository extends JpaRepository<SupplierOrderDetails, Integer> {

    @Query("SELECT sod FROM SupplierOrderDetails sod WHERE sod.supplierOrderHeader.supplierOrderHeaderId = :headerId")
    Optional<List<SupplierOrderDetails>> getAllSupplierOrderDetailsByHeadId(@Param("headerId") Integer headerId);

}
