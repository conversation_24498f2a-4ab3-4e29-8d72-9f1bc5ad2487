package com.navitsa.mydent.services;
import com.navitsa.mydent.repositories.AppointmentsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.ClinicServices;
import com.navitsa.mydent.repositories.ClinicServicesRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class ClinicServicesService {

    @Autowired
    private ClinicServicesRepository clinicServicesRepository;

    @Autowired
    private AppointmentsRepository appointmentsRepository;

    // Save a new ClinicService
    public ClinicServices saveClinicService(ClinicServices clinicServices) {
        return clinicServicesRepository.save(clinicServices);
    }

    // Update an existing ClinicService
    public ClinicServices updateClinicService(ClinicServices clinicServices) {
        if (clinicServicesRepository.existsById(clinicServices.getClinicServiceId())) {
            return clinicServicesRepository.save(clinicServices);
        }
        throw new RuntimeException("Clinic service not found");
    }

    // Get a list of all ClinicServices
    public List<ClinicServices> getAllClinicServices() {
        return clinicServicesRepository.findAll();
    }

    // Find a ClinicService by ID
    public Optional<ClinicServices> findClinicServiceById(Integer id) {
        return clinicServicesRepository.findById(id);
    }

    // Delete a ClinicService by ID
    public Map<String, Object> deleteClinicService(Integer id, Integer clinicId) {
        Map<String, Object> response = new HashMap<>();

        boolean isHaveAppoinmentInThisService = appointmentsRepository.existsByClinicIdAndPreferredService(clinicId,id);

        if (isHaveAppoinmentInThisService) {
            response.put("status", "false");
            response.put("message", "This service has already appoinment");
            return response;
        }
        clinicServicesRepository.deleteById(id);
        response.put("status", "true");
        response.put("message", "Service deleted successfully");
        return response;
    }
    
    public List<ClinicServices> getServicesByClinicId(Integer clinicId) {
        return clinicServicesRepository.findByClinics_ClinicId(clinicId); // Adjust based on your repository method
    }
}
