import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UserRoutingModule } from './user-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UserLayoutComponent } from './user-layout/user-layout.component';
import { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';
import { CoreModule } from '../core/core.module';


@NgModule({
  declarations: [
    UserLayoutComponent,
    UserMailVerificationComponent,
  ],
  imports: [
    CommonModule,
    UserRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    CoreModule
  ]
})
export class UserModule { }
