package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.LaboratorySetup;
import com.navitsa.mydent.entity.LaboratoryCategories;
import com.navitsa.mydent.services.LaboratorySetupService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class LaboratorySetupController {

    private final LaboratorySetupService laboratorySetupService;

    @Autowired
    public LaboratorySetupController(LaboratorySetupService laboratorySetupService) {
        this.laboratorySetupService = laboratorySetupService;
    }

    @PostMapping("/saveLaboratorySetup/{id}")
    public ResponseEntity<LaboratorySetup> saveSetup(@PathVariable int id, @RequestBody LaboratorySetup laboratorySetup) {
        try {
            LaboratorySetup savedSetup = laboratorySetupService.saveLaboratorySetup(id, laboratorySetup);
            return ResponseEntity.ok(savedSetup);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }


    @GetMapping("/getLaboratorySetupsList")
    public List<LaboratorySetup> getAllSetups() {
        return laboratorySetupService.getAllLaboratorySetups();
    }

    @GetMapping("/getLaboratorySetupById/{id}")
    public LaboratorySetup getSetupById(@PathVariable int id) {
        return laboratorySetupService.getLaboratorySetupById(id);
    }
    
    @GetMapping("/getLaboratorySetupByUserId/{id}")
    public List<LaboratorySetup>  getSetupByUserId(@PathVariable int id) {
        return laboratorySetupService.getLaboratorySetupByUserId(id);
    }
}
