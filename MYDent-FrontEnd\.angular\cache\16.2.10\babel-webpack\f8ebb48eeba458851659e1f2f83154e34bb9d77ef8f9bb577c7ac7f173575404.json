{"ast": null, "code": "import * as i0 from \"@angular/core\";\nclass LaboratoryDashboardComponent {\n  static #_ = this.ɵfac = function LaboratoryDashboardComponent_Factory(t) {\n    return new (t || LaboratoryDashboardComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LaboratoryDashboardComponent,\n    selectors: [[\"app-laboratory-dashboard\"]],\n    decls: 0,\n    vars: 0,\n    template: function LaboratoryDashboardComponent_Template(rf, ctx) {},\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { LaboratoryDashboardComponent };", "map": {"version": 3, "names": ["LaboratoryDashboardComponent", "_", "_2", "selectors", "decls", "vars", "template", "LaboratoryDashboardComponent_Template", "rf", "ctx", "styles"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\laboratory-dashboard\\laboratory-dashboard.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-laboratory-dashboard',\r\n  templateUrl: './laboratory-dashboard.component.html',\r\n  styleUrls: ['./laboratory-dashboard.component.css']\r\n})\r\nexport class LaboratoryDashboardComponent {\r\n\r\n}\r\n"], "mappings": ";AAEA,MAKaA,4BAA4B;EAAA,QAAAC,CAAA,G;qBAA5BD,4BAA4B;EAAA;EAAA,QAAAE,EAAA,G;UAA5BF,4BAA4B;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;EAAA;;SAA5BV,4BAA4B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}