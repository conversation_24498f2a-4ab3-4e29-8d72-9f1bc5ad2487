package com.navitsa.mydent.services;

import java.util.ArrayList;
import java.util.List;
import com.navitsa.mydent.dtos.DoctorClinicDto;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.Doctor;
import com.navitsa.mydent.repositories.DoctorRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.navitsa.mydent.entity.ClinicDoctor;
import com.navitsa.mydent.repositories.ClinicDoctorRepository;
import com.navitsa.mydent.repositories.ClinicRepository;
import jakarta.transaction.Transactional;
import java.util.Optional;

@Service
public class ClinicDoctorService {

    @Autowired
    private ClinicDoctorRepository clinicDoctorRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    @Autowired
    private DoctorRepository doctorRepository; // Inject DoctorRepository to fetch doctor details

    // Assign a doctor to a clinic based on the userId and doctorId
    public ClinicDoctor assignDoctor(Integer userId, Integer doctorId) {

        Optional<Clinic> clinic = clinicRepository.findClinicByUserId(userId);
        Optional<Doctor> doctor = doctorRepository.findById(doctorId);


        if (doctor.isPresent() && clinic.isPresent() ) {
            // Create a new ClinicDoctor entry
            ClinicDoctor clinicDoctor = new ClinicDoctor();
            clinicDoctor.setClinic(clinic.get());
            clinicDoctor.setDoctor(doctor.get());
            return clinicDoctorRepository.save(clinicDoctor);
        }else{
            throw new RuntimeException("Clinic not found for user ID: " + userId);
        }
    }

    public List<DoctorClinicDto> getAssignedClinics(Integer userId) {
        Optional<List<DoctorClinicDto>> clinicsByDoctorId = clinicDoctorRepository.findClinicsByDoctorId(userId);
        return clinicsByDoctorId.orElse(null);
    }
 
    // Get all doctors associated with a clinic by userId
    public List<Doctor> getDoctorsByUserId(Integer userId) {
    	List<Doctor> doctors = new ArrayList<>(); // Initialize the doctors list
        try {
            // Try to fetch the doctors associated with the clinic user ID
            Optional<List<Doctor>> optionalDoctors = clinicDoctorRepository.findDoctorsByClinicId(userId);
            
            // Check if the result is present
            if (optionalDoctors.isPresent()) {
                doctors = optionalDoctors.get(); // Extract the list from the Optional
            } else {
                // Handle the case where no doctors are found
                System.err.println("No doctors found for user ID: " + userId);
            }
        } catch (Exception e) {
            System.err.println("An error occurred: " + e.getMessage());
            e.printStackTrace();
        }
        
        return doctors; 
    }
    
    @Transactional
    public void deleteDoctorFromClinic(Integer userId, Integer doctorId) {
    	Integer clinicId;
    	clinicId = clinicDoctorRepository.findClinicIdByUserId(userId);
        clinicDoctorRepository.deleteByClinicIdAndDoctorId(clinicId, doctorId);
    }

}
