{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, inject, Optional, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, Directive, Output, NgModule } from '@angular/core';\nimport * as i2$1 from '@angular/material/core';\nimport { MatRipple, mixinColor, mixinDisableRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\n\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst _c0 = [\"knob\"];\nconst _c1 = [\"valueIndicatorContainer\"];\nfunction MatSliderVisualThumb_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4, 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.valueIndicatorText);\n  }\n}\nconst _c2 = [\"trackActive\"];\nfunction MatSlider_div_6_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const tickMark_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(tickMark_r6 === 0 ? \"mdc-slider__tick-mark--active\" : \"mdc-slider__tick-mark--inactive\");\n    i0.ɵɵstyleProp(\"transform\", ctx_r5._calcTickMarkTransform(i_r7));\n  }\n}\nfunction MatSlider_div_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MatSlider_div_6_ng_container_2_div_1_Template, 1, 4, \"div\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4._tickMarks);\n  }\n}\nfunction MatSlider_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 9);\n    i0.ɵɵtemplate(2, MatSlider_div_6_ng_container_2_Template, 2, 1, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._cachedWidth);\n  }\n}\nfunction MatSlider_mat_slider_visual_thumb_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-slider-visual-thumb\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"discrete\", ctx_r2.discrete)(\"thumbPosition\", 1)(\"valueIndicatorText\", ctx_r2.startValueIndicatorText);\n  }\n}\nconst _c3 = [\"*\"];\nconst MAT_SLIDER = new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nclass MatSliderVisualThumb {\n  constructor(_cdr, _ngZone, _elementRef, _slider) {\n    this._cdr = _cdr;\n    this._ngZone = _ngZone;\n    this._slider = _slider;\n    /** Whether the slider thumb is currently being hovered. */\n    this._isHovered = false;\n    /** Whether the slider thumb is currently being pressed. */\n    this._isActive = false;\n    /** Whether the value indicator tooltip is visible. */\n    this._isValueIndicatorVisible = false;\n    this._onPointerMove = event => {\n      if (this._sliderInput._isFocused) {\n        return;\n      }\n      const rect = this._hostElement.getBoundingClientRect();\n      const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n      this._isHovered = isHovered;\n      if (isHovered) {\n        this._showHoverRipple();\n      } else {\n        this._hideRipple(this._hoverRippleRef);\n      }\n    };\n    this._onMouseLeave = () => {\n      this._isHovered = false;\n      this._hideRipple(this._hoverRippleRef);\n    };\n    this._onFocus = () => {\n      // We don't want to show the hover ripple on top of the focus ripple.\n      // Happen when the users cursor is over a thumb and then the user tabs to it.\n      this._hideRipple(this._hoverRippleRef);\n      this._showFocusRipple();\n      this._hostElement.classList.add('mdc-slider__thumb--focused');\n    };\n    this._onBlur = () => {\n      // Happens when the user tabs away while still dragging a thumb.\n      if (!this._isActive) {\n        this._hideRipple(this._focusRippleRef);\n      }\n      // Happens when the user tabs away from a thumb but their cursor is still over it.\n      if (this._isHovered) {\n        this._showHoverRipple();\n      }\n      this._hostElement.classList.remove('mdc-slider__thumb--focused');\n    };\n    this._onDragStart = event => {\n      if (event.button !== 0) {\n        return;\n      }\n      this._isActive = true;\n      this._showActiveRipple();\n    };\n    this._onDragEnd = () => {\n      this._isActive = false;\n      this._hideRipple(this._activeRippleRef);\n      // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n      if (!this._sliderInput._isFocused) {\n        this._hideRipple(this._focusRippleRef);\n      }\n    };\n    this._hostElement = _elementRef.nativeElement;\n  }\n  ngAfterViewInit() {\n    this._ripple.radius = 24;\n    this._sliderInput = this._slider._getInput(this.thumbPosition);\n    this._sliderInputEl = this._sliderInput._hostElement;\n    const input = this._sliderInputEl;\n    // These listeners don't update any data bindings so we bind them outside\n    // of the NgZone to prevent Angular from needlessly running change detection.\n    this._ngZone.runOutsideAngular(() => {\n      input.addEventListener('pointermove', this._onPointerMove);\n      input.addEventListener('pointerdown', this._onDragStart);\n      input.addEventListener('pointerup', this._onDragEnd);\n      input.addEventListener('pointerleave', this._onMouseLeave);\n      input.addEventListener('focus', this._onFocus);\n      input.addEventListener('blur', this._onBlur);\n    });\n  }\n  ngOnDestroy() {\n    const input = this._sliderInputEl;\n    input.removeEventListener('pointermove', this._onPointerMove);\n    input.removeEventListener('pointerdown', this._onDragStart);\n    input.removeEventListener('pointerup', this._onDragEnd);\n    input.removeEventListener('pointerleave', this._onMouseLeave);\n    input.removeEventListener('focus', this._onFocus);\n    input.removeEventListener('blur', this._onBlur);\n  }\n  /** Handles displaying the hover ripple. */\n  _showHoverRipple() {\n    if (!this._isShowingRipple(this._hoverRippleRef)) {\n      this._hoverRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      });\n      this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n    }\n  }\n  /** Handles displaying the focus ripple. */\n  _showFocusRipple() {\n    // Show the focus ripple event if noop animations are enabled.\n    if (!this._isShowingRipple(this._focusRippleRef)) {\n      this._focusRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      }, true);\n      this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n    }\n  }\n  /** Handles displaying the active ripple. */\n  _showActiveRipple() {\n    if (!this._isShowingRipple(this._activeRippleRef)) {\n      this._activeRippleRef = this._showRipple({\n        enterDuration: 225,\n        exitDuration: 400\n      });\n      this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n    }\n  }\n  /** Whether the given rippleRef is currently fading in or visible. */\n  _isShowingRipple(rippleRef) {\n    return rippleRef?.state === 0 /* RippleState.FADING_IN */ || rippleRef?.state === 1 /* RippleState.VISIBLE */;\n  }\n  /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n  _showRipple(animation, ignoreGlobalRippleConfig) {\n    if (this._slider.disabled) {\n      return;\n    }\n    this._showValueIndicator();\n    if (this._slider._isRange) {\n      const sibling = this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n      sibling._showValueIndicator();\n    }\n    if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n      return;\n    }\n    return this._ripple.launch({\n      animation: this._slider._noopAnimations ? {\n        enterDuration: 0,\n        exitDuration: 0\n      } : animation,\n      centered: true,\n      persistent: true\n    });\n  }\n  /**\n   * Fades out the given ripple.\n   * Also hides the value indicator if no ripple is showing.\n   */\n  _hideRipple(rippleRef) {\n    rippleRef?.fadeOut();\n    if (this._isShowingAnyRipple()) {\n      return;\n    }\n    if (!this._slider._isRange) {\n      this._hideValueIndicator();\n    }\n    const sibling = this._getSibling();\n    if (!sibling._isShowingAnyRipple()) {\n      this._hideValueIndicator();\n      sibling._hideValueIndicator();\n    }\n  }\n  /** Shows the value indicator ui. */\n  _showValueIndicator() {\n    this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n  }\n  /** Hides the value indicator ui. */\n  _hideValueIndicator() {\n    this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n  }\n  _getSibling() {\n    return this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n  }\n  /** Gets the value indicator container's native HTML element. */\n  _getValueIndicatorContainer() {\n    return this._valueIndicatorContainer?.nativeElement;\n  }\n  /** Gets the native HTML element of the slider thumb knob. */\n  _getKnob() {\n    return this._knob.nativeElement;\n  }\n  _isShowingAnyRipple() {\n    return this._isShowingRipple(this._hoverRippleRef) || this._isShowingRipple(this._focusRippleRef) || this._isShowingRipple(this._activeRippleRef);\n  }\n  static #_ = this.ɵfac = function MatSliderVisualThumb_Factory(t) {\n    return new (t || MatSliderVisualThumb)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSliderVisualThumb,\n    selectors: [[\"mat-slider-visual-thumb\"]],\n    viewQuery: function MatSliderVisualThumb_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatRipple, 5);\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._ripple = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._knob = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._valueIndicatorContainer = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mdc-slider__thumb\", \"mat-mdc-slider-visual-thumb\"],\n    inputs: {\n      discrete: \"discrete\",\n      thumbPosition: \"thumbPosition\",\n      valueIndicatorText: \"valueIndicatorText\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SLIDER_VISUAL_THUMB,\n      useExisting: MatSliderVisualThumb\n    }])],\n    decls: 4,\n    vars: 2,\n    consts: [[\"class\", \"mdc-slider__value-indicator-container\", 4, \"ngIf\"], [1, \"mdc-slider__thumb-knob\"], [\"knob\", \"\"], [\"matRipple\", \"\", 1, \"mat-mdc-focus-indicator\", 3, \"matRippleDisabled\"], [1, \"mdc-slider__value-indicator-container\"], [\"valueIndicatorContainer\", \"\"], [1, \"mdc-slider__value-indicator\"], [1, \"mdc-slider__value-indicator-text\"]],\n    template: function MatSliderVisualThumb_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MatSliderVisualThumb_div_0_Template, 5, 1, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1, 2)(3, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.discrete);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"matRippleDisabled\", true);\n      }\n    },\n    dependencies: [i2.NgIf, i2$1.MatRipple],\n    styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderVisualThumb, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider-visual-thumb',\n      host: {\n        'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_SLIDER_VISUAL_THUMB,\n        useExisting: MatSliderVisualThumb\n      }],\n      template: \"<div class=\\\"mdc-slider__value-indicator-container\\\" *ngIf=\\\"discrete\\\" #valueIndicatorContainer>\\n  <div class=\\\"mdc-slider__value-indicator\\\">\\n    <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n  </div>\\n</div>\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\",\n      styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SLIDER]\n      }]\n    }];\n  }, {\n    discrete: [{\n      type: Input\n    }],\n    thumbPosition: [{\n      type: Input\n    }],\n    valueIndicatorText: [{\n      type: Input\n    }],\n    _ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }],\n    _knob: [{\n      type: ViewChild,\n      args: ['knob']\n    }],\n    _valueIndicatorContainer: [{\n      type: ViewChild,\n      args: ['valueIndicatorContainer']\n    }]\n  });\n})();\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n// Boilerplate for applying mixins to MatSlider.\nconst _MatSliderMixinBase = mixinColor(mixinDisableRipple(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}), 'primary');\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nclass MatSlider extends _MatSliderMixinBase {\n  /** Whether the slider is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(v) {\n    this._disabled = coerceBooleanProperty(v);\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    if (endInput) {\n      endInput.disabled = this._disabled;\n    }\n    if (startInput) {\n      startInput.disabled = this._disabled;\n    }\n  }\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  get discrete() {\n    return this._discrete;\n  }\n  set discrete(v) {\n    this._discrete = coerceBooleanProperty(v);\n    this._updateValueIndicatorUIs();\n  }\n  /** Whether the slider displays tick marks along the slider track. */\n  get showTickMarks() {\n    return this._showTickMarks;\n  }\n  set showTickMarks(v) {\n    this._showTickMarks = coerceBooleanProperty(v);\n  }\n  /** The minimum value that the slider can have. */\n  get min() {\n    return this._min;\n  }\n  set min(v) {\n    const min = coerceNumberProperty(v, this._min);\n    if (this._min !== min) {\n      this._updateMin(min);\n    }\n  }\n  _updateMin(min) {\n    const prevMin = this._min;\n    this._min = min;\n    this._isRange ? this._updateMinRange({\n      old: prevMin,\n      new: min\n    }) : this._updateMinNonRange(min);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMinRange(min) {\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    startInput.min = min.new;\n    endInput.min = Math.max(min.new, startInput.value);\n    startInput.max = Math.min(endInput.max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    min.new < min.old ? this._onTranslateXChangeBySideEffect(endInput, startInput) : this._onTranslateXChangeBySideEffect(startInput, endInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMinNonRange(min) {\n    const input = this._getInput(2 /* _MatThumb.END */);\n    if (input) {\n      const oldValue = input.value;\n      input.min = min;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The maximum value that the slider can have. */\n  get max() {\n    return this._max;\n  }\n  set max(v) {\n    const max = coerceNumberProperty(v, this._max);\n    if (this._max !== max) {\n      this._updateMax(max);\n    }\n  }\n  _updateMax(max) {\n    const prevMax = this._max;\n    this._max = max;\n    this._isRange ? this._updateMaxRange({\n      old: prevMax,\n      new: max\n    }) : this._updateMaxNonRange(max);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMaxRange(max) {\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    endInput.max = max.new;\n    startInput.max = Math.min(max.new, endInput.value);\n    endInput.min = startInput.value;\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    max.new > max.old ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMaxNonRange(max) {\n    const input = this._getInput(2 /* _MatThumb.END */);\n    if (input) {\n      const oldValue = input.value;\n      input.max = max;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The values at which the thumb will snap. */\n  get step() {\n    return this._step;\n  }\n  set step(v) {\n    const step = coerceNumberProperty(v, this._step);\n    if (this._step !== step) {\n      this._updateStep(step);\n    }\n  }\n  _updateStep(step) {\n    this._step = step;\n    this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n    this._onMinMaxOrStepChange();\n  }\n  _updateStepRange() {\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    const prevStartValue = startInput.value;\n    endInput.min = this._min;\n    startInput.max = this._max;\n    endInput.step = this._step;\n    startInput.step = this._step;\n    if (this._platform.SAFARI) {\n      endInput.value = endInput.value;\n      startInput.value = startInput.value;\n    }\n    endInput.min = Math.max(this._min, startInput.value);\n    startInput.max = Math.min(this._max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    endInput.value < prevStartValue ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateStepNonRange() {\n    const input = this._getInput(2 /* _MatThumb.END */);\n    if (input) {\n      const oldValue = input.value;\n      input.step = this._step;\n      if (this._platform.SAFARI) {\n        input.value = input.value;\n      }\n      input._updateThumbUIByValue();\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  constructor(_ngZone, _cdr, elementRef, _dir, _globalRippleOptions, animationMode) {\n    super(elementRef);\n    this._ngZone = _ngZone;\n    this._cdr = _cdr;\n    this._dir = _dir;\n    this._globalRippleOptions = _globalRippleOptions;\n    this._disabled = false;\n    this._discrete = false;\n    this._showTickMarks = false;\n    this._min = 0;\n    this._max = 100;\n    this._step = 1;\n    /**\n     * Function that will be used to format the value before it is displayed\n     * in the thumb label. Can be used to format very large number in order\n     * for them to fit into the slider thumb.\n     */\n    this.displayWith = value => `${value}`;\n    this._rippleRadius = 24;\n    // The value indicator tooltip text for the visual slider thumb(s).\n    /** @docs-private */\n    this.startValueIndicatorText = '';\n    /** @docs-private */\n    this.endValueIndicatorText = '';\n    this._isRange = false;\n    /** Whether the slider is rtl. */\n    this._isRtl = false;\n    this._hasViewInitialized = false;\n    /**\n     * The width of the tick mark track.\n     * The tick mark track width is different from full track width\n     */\n    this._tickMarkTrackWidth = 0;\n    this._hasAnimation = false;\n    this._resizeTimer = null;\n    this._platform = inject(Platform);\n    /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n    this._knobRadius = 8;\n    /** Whether or not the slider thumbs overlap. */\n    this._thumbsOverlap = false;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n    this._isRtl = this._dir.value === 'rtl';\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._updateDimensions();\n    }\n    const eInput = this._getInput(2 /* _MatThumb.END */);\n    const sInput = this._getInput(1 /* _MatThumb.START */);\n    this._isRange = !!eInput && !!sInput;\n    this._cdr.detectChanges();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      _validateInputs(this._isRange, this._getInput(2 /* _MatThumb.END */), this._getInput(1 /* _MatThumb.START */));\n    }\n\n    const thumb = this._getThumb(2 /* _MatThumb.END */);\n    this._rippleRadius = thumb._ripple.radius;\n    this._inputPadding = this._rippleRadius - this._knobRadius;\n    this._inputOffset = this._knobRadius;\n    this._isRange ? this._initUIRange(eInput, sInput) : this._initUINonRange(eInput);\n    this._updateTrackUI(eInput);\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._observeHostResize();\n    this._cdr.detectChanges();\n  }\n  _initUINonRange(eInput) {\n    eInput.initProps();\n    eInput.initUI();\n    this._updateValueIndicatorUI(eInput);\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n  }\n  _initUIRange(eInput, sInput) {\n    eInput.initProps();\n    eInput.initUI();\n    sInput.initProps();\n    sInput.initUI();\n    eInput._updateMinMax();\n    sInput._updateMinMax();\n    eInput._updateStaticStyles();\n    sInput._updateStaticStyles();\n    this._updateValueIndicatorUIs();\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n    sInput._updateThumbUIByValue();\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._resizeObserver?.disconnect();\n    this._resizeObserver = null;\n  }\n  /** Handles updating the slider ui after a dir change. */\n  _onDirChange() {\n    this._isRtl = this._dir.value === 'rtl';\n    this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n    this._updateTickMarkUI();\n  }\n  _onDirChangeRange() {\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    endInput._setIsLeftThumb();\n    startInput._setIsLeftThumb();\n    endInput.translateX = endInput._calcTranslateXByValue();\n    startInput.translateX = startInput._calcTranslateXByValue();\n    endInput._updateStaticStyles();\n    startInput._updateStaticStyles();\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    endInput._updateThumbUIByValue();\n    startInput._updateThumbUIByValue();\n  }\n  _onDirChangeNonRange() {\n    const input = this._getInput(2 /* _MatThumb.END */);\n    input._updateThumbUIByValue();\n  }\n  /** Starts observing and updating the slider if the host changes its size. */\n  _observeHostResize() {\n    if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeObserver = new ResizeObserver(() => {\n        if (this._isActive()) {\n          return;\n        }\n        if (this._resizeTimer) {\n          clearTimeout(this._resizeTimer);\n        }\n        this._onResize();\n      });\n      this._resizeObserver.observe(this._elementRef.nativeElement);\n    });\n  }\n  /** Whether any of the thumbs are currently active. */\n  _isActive() {\n    return this._getThumb(1 /* _MatThumb.START */)._isActive || this._getThumb(2 /* _MatThumb.END */)._isActive;\n  }\n  _getValue(thumbPosition = 2 /* _MatThumb.END */) {\n    const input = this._getInput(thumbPosition);\n    if (!input) {\n      return this.min;\n    }\n    return input.value;\n  }\n  _skipUpdate() {\n    return !!(this._getInput(1 /* _MatThumb.START */)?._skipUIUpdate || this._getInput(2 /* _MatThumb.END */)?._skipUIUpdate);\n  }\n  /** Stores the slider dimensions. */\n  _updateDimensions() {\n    this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n    this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n  }\n  /** Sets the styles for the active portion of the track. */\n  _setTrackActiveStyles(styles) {\n    const trackStyle = this._trackActive.nativeElement.style;\n    trackStyle.left = styles.left;\n    trackStyle.right = styles.right;\n    trackStyle.transformOrigin = styles.transformOrigin;\n    trackStyle.transform = styles.transform;\n  }\n  /** Returns the translateX positioning for a tick mark based on it's index. */\n  _calcTickMarkTransform(index) {\n    // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n    const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n    return `translateX(${translateX}px`;\n  }\n  // Handlers for updating the slider ui.\n  _onTranslateXChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateThumbUI(source);\n    this._updateTrackUI(source);\n    this._updateOverlappingThumbUI(source);\n  }\n  _onTranslateXChangeBySideEffect(input1, input2) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    input1._updateThumbUIByValue();\n    input2._updateThumbUIByValue();\n  }\n  _onValueChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateValueIndicatorUI(source);\n    this._updateTickMarkUI();\n    this._cdr.detectChanges();\n  }\n  _onMinMaxOrStepChange() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.markForCheck();\n  }\n  _onResize() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateDimensions();\n    if (this._isRange) {\n      const eInput = this._getInput(2 /* _MatThumb.END */);\n      const sInput = this._getInput(1 /* _MatThumb.START */);\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n      eInput._updateWidthInactive();\n      sInput._updateWidthInactive();\n    } else {\n      const eInput = this._getInput(2 /* _MatThumb.END */);\n      if (eInput) {\n        eInput._updateThumbUIByValue();\n      }\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.detectChanges();\n  }\n  /** Returns true if the slider knobs are overlapping one another. */\n  _areThumbsOverlapping() {\n    const startInput = this._getInput(1 /* _MatThumb.START */);\n    const endInput = this._getInput(2 /* _MatThumb.END */);\n    if (!startInput || !endInput) {\n      return false;\n    }\n    return endInput.translateX - startInput.translateX < 20;\n  }\n  /**\n   * Updates the class names of overlapping slider thumbs so\n   * that the current active thumb is styled to be on \"top\".\n   */\n  _updateOverlappingThumbClassNames(source) {\n    const sibling = source.getSibling();\n    const sourceThumb = this._getThumb(source.thumbPosition);\n    const siblingThumb = this._getThumb(sibling.thumbPosition);\n    siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n    sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n  }\n  /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n  _updateOverlappingThumbUI(source) {\n    if (!this._isRange || this._skipUpdate()) {\n      return;\n    }\n    if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n      this._thumbsOverlap = !this._thumbsOverlap;\n      this._updateOverlappingThumbClassNames(source);\n    }\n  }\n  // _MatThumb styles update conditions\n  //\n  // 1. TranslateX, resize, or dir change\n  //    - Reason: The thumb styles need to be updated according to the new translateX.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the translateX of the given thumb. */\n  _updateThumbUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const thumb = this._getThumb(source.thumbPosition === 2 /* _MatThumb.END */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n    thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n  }\n  // Value indicator text update conditions\n  //\n  // 1. Value\n  //    - Reason: The value displayed needs to be updated.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the value indicator tooltip ui for the given thumb. */\n  _updateValueIndicatorUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const valuetext = this.displayWith(source.value);\n    this._hasViewInitialized ? source._valuetext = valuetext : source._hostElement.setAttribute('aria-valuetext', valuetext);\n    if (this.discrete) {\n      source.thumbPosition === 1 /* _MatThumb.START */ ? this.startValueIndicatorText = valuetext : this.endValueIndicatorText = valuetext;\n      const visualThumb = this._getThumb(source.thumbPosition);\n      valuetext.length < 3 ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value') : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n    }\n  }\n  /** Updates all value indicator UIs in the slider. */\n  _updateValueIndicatorUIs() {\n    const eInput = this._getInput(2 /* _MatThumb.END */);\n    const sInput = this._getInput(1 /* _MatThumb.START */);\n    if (eInput) {\n      this._updateValueIndicatorUI(eInput);\n    }\n    if (sInput) {\n      this._updateValueIndicatorUI(sInput);\n    }\n  }\n  // Update Tick Mark Track Width\n  //\n  // 1. Min, max, or step\n  //    - Reason: The maximum reachable value may have changed.\n  //    - Side note: The maximum reachable value is different from the maximum value set by the\n  //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n  //      reachable value of 95.\n  // 2. Resize\n  //    - Reason: The position for the maximum reachable value needs to be recalculated.\n  /** Updates the width of the tick mark track. */\n  _updateTickMarkTrackUI() {\n    if (!this.showTickMarks || this._skipUpdate()) {\n      return;\n    }\n    const step = this._step && this._step > 0 ? this._step : 1;\n    const maxValue = Math.floor(this.max / step) * step;\n    const percentage = (maxValue - this.min) / (this.max - this.min);\n    this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n  }\n  // Track active update conditions\n  //\n  // 1. TranslateX\n  //    - Reason: The track active should line up with the new thumb position.\n  // 2. Min or max\n  //    - Reason #1: The 'active' percentage needs to be recalculated.\n  //    - Reason #2: The value may have silently changed.\n  // 3. Step\n  //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n  // 4. Dir change\n  //    - Reason: The track active will need to be updated according to the new thumb position(s).\n  // 5. Resize\n  //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    this._isRange ? this._updateTrackUIRange(source) : this._updateTrackUINonRange(source);\n  }\n  _updateTrackUIRange(source) {\n    const sibling = source.getSibling();\n    if (!sibling || !this._cachedWidth) {\n      return;\n    }\n    const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n    if (source._isLeftThumb && this._cachedWidth) {\n      this._setTrackActiveStyles({\n        left: 'auto',\n        right: `${this._cachedWidth - sibling.translateX}px`,\n        transformOrigin: 'right',\n        transform: `scaleX(${activePercentage})`\n      });\n    } else {\n      this._setTrackActiveStyles({\n        left: `${sibling.translateX}px`,\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${activePercentage})`\n      });\n    }\n  }\n  _updateTrackUINonRange(source) {\n    this._isRtl ? this._setTrackActiveStyles({\n      left: 'auto',\n      right: '0px',\n      transformOrigin: 'right',\n      transform: `scaleX(${1 - source.fillPercentage})`\n    }) : this._setTrackActiveStyles({\n      left: '0px',\n      right: 'auto',\n      transformOrigin: 'left',\n      transform: `scaleX(${source.fillPercentage})`\n    });\n  }\n  // Tick mark update conditions\n  //\n  // 1. Value\n  //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n  // 2. Min, max, or step\n  //    - Reason #1: the number of tick marks may have changed.\n  //    - Reason #2: The value may have silently changed.\n  /** Updates the dots along the slider track. */\n  _updateTickMarkUI() {\n    if (!this.showTickMarks || this.step === undefined || this.min === undefined || this.max === undefined) {\n      return;\n    }\n    const step = this.step > 0 ? this.step : 1;\n    this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n    if (this._isRtl) {\n      this._tickMarks.reverse();\n    }\n  }\n  _updateTickMarkUINonRange(step) {\n    const value = this._getValue();\n    let numActive = Math.max(Math.round((value - this.min) / step), 0);\n    let numInactive = Math.max(Math.round((this.max - value) / step), 0);\n    this._isRtl ? numActive++ : numInactive++;\n    this._tickMarks = Array(numActive).fill(0 /* _MatTickMark.ACTIVE */).concat(Array(numInactive).fill(1 /* _MatTickMark.INACTIVE */));\n  }\n\n  _updateTickMarkUIRange(step) {\n    const endValue = this._getValue();\n    const startValue = this._getValue(1 /* _MatThumb.START */);\n    const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n    const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n    const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n    this._tickMarks = Array(numInactiveBeforeStartThumb).fill(1 /* _MatTickMark.INACTIVE */).concat(Array(numActive).fill(0 /* _MatTickMark.ACTIVE */), Array(numInactiveAfterEndThumb).fill(1 /* _MatTickMark.INACTIVE */));\n  }\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition) {\n    if (thumbPosition === 2 /* _MatThumb.END */ && this._input) {\n      return this._input;\n    }\n    if (this._inputs?.length) {\n      return thumbPosition === 1 /* _MatThumb.START */ ? this._inputs.first : this._inputs.last;\n    }\n    return;\n  }\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition) {\n    return thumbPosition === 2 /* _MatThumb.END */ ? this._thumbs?.last : this._thumbs?.first;\n  }\n  _setTransition(withAnimation) {\n    this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n  }\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event, rect) {\n    const radius = rect.width / 2;\n    const centerX = rect.x + radius;\n    const centerY = rect.y + radius;\n    const dx = event.clientX - centerX;\n    const dy = event.clientY - centerY;\n    return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n  }\n  static #_ = this.ɵfac = function MatSlider_Factory(t) {\n    return new (t || MatSlider)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatSlider,\n    selectors: [[\"mat-slider\"]],\n    contentQueries: function MatSlider_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_THUMB, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_RANGE_THUMB, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._input = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputs = _t);\n      }\n    },\n    viewQuery: function MatSlider_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(MAT_SLIDER_VISUAL_THUMB, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._trackActive = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbs = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-slider\", \"mdc-slider\"],\n    hostVars: 10,\n    hostBindings: function MatSlider_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-slider--range\", ctx._isRange)(\"mdc-slider--disabled\", ctx.disabled)(\"mdc-slider--discrete\", ctx.discrete)(\"mdc-slider--tick-marks\", ctx.showTickMarks)(\"_mat-animation-noopable\", ctx._noopAnimations);\n      }\n    },\n    inputs: {\n      color: \"color\",\n      disableRipple: \"disableRipple\",\n      disabled: \"disabled\",\n      discrete: \"discrete\",\n      showTickMarks: \"showTickMarks\",\n      min: \"min\",\n      max: \"max\",\n      step: \"step\",\n      displayWith: \"displayWith\"\n    },\n    exportAs: [\"matSlider\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_SLIDER,\n      useExisting: MatSlider\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c3,\n    decls: 9,\n    vars: 5,\n    consts: [[1, \"mdc-slider__track\"], [1, \"mdc-slider__track--inactive\"], [1, \"mdc-slider__track--active\"], [1, \"mdc-slider__track--active_fill\"], [\"trackActive\", \"\"], [\"class\", \"mdc-slider__tick-marks\", 4, \"ngIf\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\", 4, \"ngIf\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\"], [1, \"mdc-slider__tick-marks\"], [\"tickMarkContainer\", \"\"], [4, \"ngIf\"], [3, \"class\", \"transform\", 4, \"ngFor\", \"ngForOf\"]],\n    template: function MatSlider_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵprojection(0);\n        i0.ɵɵelementStart(1, \"div\", 0);\n        i0.ɵɵelement(2, \"div\", 1);\n        i0.ɵɵelementStart(3, \"div\", 2);\n        i0.ɵɵelement(4, \"div\", 3, 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, MatSlider_div_6_Template, 3, 1, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, MatSlider_mat_slider_visual_thumb_7_Template, 1, 3, \"mat-slider-visual-thumb\", 6);\n        i0.ɵɵelement(8, \"mat-slider-visual-thumb\", 7);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.showTickMarks);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx._isRange);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"discrete\", ctx.discrete)(\"thumbPosition\", 2)(\"valueIndicatorText\", ctx.endValueIndicatorText);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, MatSliderVisualThumb],\n    styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000));border-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color, var(--mdc-theme-primary, #6200ee));opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mdc-theme-on-surface, #000));opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mdc-theme-primary, #6200ee));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mdc-theme-on-surface, #000));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color, #666666);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color, #666666)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height, 6px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 6px)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height, 6px)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size, 2px);width:var(--mdc-slider-with-tick-marks-container-size, 2px)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking, 0.0071428571em);font-size:var(--mdc-slider-label-label-text-size, 0.875rem);font-family:var(--mdc-slider-label-label-text-font, Roboto, sans-serif);font-weight:var(--mdc-slider-label-label-text-weight, 500);line-height:var(--mdc-slider-label-label-text-line-height, 1.375rem)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape, 50%);width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-style:solid;border-width:calc(var(--mdc-slider-handle-height, 20px) / 2) calc(var(--mdc-slider-handle-width, 20px) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape, 50%)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color, #fff);border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator{opacity:var(--mat-mdc-slider-value-indicator-opacity, 1)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-mdc-slider-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-mdc-slider-hover-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-mdc-slider-focus-ripple-color, transparent)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider',\n      host: {\n        'class': 'mat-mdc-slider mdc-slider',\n        '[class.mdc-slider--range]': '_isRange',\n        '[class.mdc-slider--disabled]': 'disabled',\n        '[class.mdc-slider--discrete]': 'discrete',\n        '[class.mdc-slider--tick-marks]': 'showTickMarks',\n        '[class._mat-animation-noopable]': '_noopAnimations'\n      },\n      exportAs: 'matSlider',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      inputs: ['color', 'disableRipple'],\n      providers: [{\n        provide: MAT_SLIDER,\n        useExisting: MatSlider\n      }],\n      template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"showTickMarks\\\" class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n    <ng-container *ngIf=\\\"_cachedWidth\\\">\\n        <div\\n          *ngFor=\\\"let tickMark of _tickMarks; let i = index\\\"\\n          [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n          [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n    </ng-container>\\n  </div>\\n</div>\\n\\n<!-- Thumbs -->\\n<mat-slider-visual-thumb\\n  *ngIf=\\\"_isRange\\\"\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"1\\\"\\n  [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\",\n      styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000));border-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color, var(--mdc-theme-primary, #6200ee));opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mdc-theme-on-surface, #000));opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mdc-theme-primary, #6200ee));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mdc-theme-on-surface, #000));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color, #666666);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color, #666666)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height, 6px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 6px)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height, 6px)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size, 2px);width:var(--mdc-slider-with-tick-marks-container-size, 2px)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking, 0.0071428571em);font-size:var(--mdc-slider-label-label-text-size, 0.875rem);font-family:var(--mdc-slider-label-label-text-font, Roboto, sans-serif);font-weight:var(--mdc-slider-label-label-text-weight, 500);line-height:var(--mdc-slider-label-label-text-line-height, 1.375rem)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape, 50%);width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-style:solid;border-width:calc(var(--mdc-slider-handle-height, 20px) / 2) calc(var(--mdc-slider-handle-width, 20px) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape, 50%)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color, #fff);border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator{opacity:var(--mat-mdc-slider-value-indicator-opacity, 1)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-mdc-slider-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-mdc-slider-hover-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-mdc-slider-focus-ripple-color, transparent)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }];\n  }, {\n    _trackActive: [{\n      type: ViewChild,\n      args: ['trackActive']\n    }],\n    _thumbs: [{\n      type: ViewChildren,\n      args: [MAT_SLIDER_VISUAL_THUMB]\n    }],\n    _input: [{\n      type: ContentChild,\n      args: [MAT_SLIDER_THUMB]\n    }],\n    _inputs: [{\n      type: ContentChildren,\n      args: [MAT_SLIDER_RANGE_THUMB, {\n        descendants: false\n      }]\n    }],\n    disabled: [{\n      type: Input\n    }],\n    discrete: [{\n      type: Input\n    }],\n    showTickMarks: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    displayWith: [{\n      type: Input\n    }]\n  });\n})();\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n  const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\nfunction _throwInvalidInputConfigurationError() {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderThumb),\n  multi: true\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderRangeThumb),\n  multi: true\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nclass MatSliderThumb {\n  get value() {\n    return coerceNumberProperty(this._hostElement.value);\n  }\n  set value(v) {\n    const val = coerceNumberProperty(v).toString();\n    if (!this._hasSetInitialValue) {\n      this._initialValue = val;\n      return;\n    }\n    if (this._isActive) {\n      return;\n    }\n    this._hostElement.value = val;\n    this._updateThumbUIByValue();\n    this._slider._onValueChange(this);\n    this._cdr.detectChanges();\n    this._slider._cdr.markForCheck();\n  }\n  /**\n   * The current translateX in px of the slider visual thumb.\n   * @docs-private\n   */\n  get translateX() {\n    if (this._slider.min >= this._slider.max) {\n      this._translateX = 0;\n      return this._translateX;\n    }\n    if (this._translateX === undefined) {\n      this._translateX = this._calcTranslateXByValue();\n    }\n    return this._translateX;\n  }\n  set translateX(v) {\n    this._translateX = v;\n  }\n  /** @docs-private */\n  get min() {\n    return coerceNumberProperty(this._hostElement.min);\n  }\n  set min(v) {\n    this._hostElement.min = coerceNumberProperty(v).toString();\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get max() {\n    return coerceNumberProperty(this._hostElement.max);\n  }\n  set max(v) {\n    this._hostElement.max = coerceNumberProperty(v).toString();\n    this._cdr.detectChanges();\n  }\n  get step() {\n    return coerceNumberProperty(this._hostElement.step);\n  }\n  set step(v) {\n    this._hostElement.step = coerceNumberProperty(v).toString();\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get disabled() {\n    return coerceBooleanProperty(this._hostElement.disabled);\n  }\n  set disabled(v) {\n    this._hostElement.disabled = coerceBooleanProperty(v);\n    this._cdr.detectChanges();\n    if (this._slider.disabled !== this.disabled) {\n      this._slider.disabled = this.disabled;\n    }\n  }\n  /** The percentage of the slider that coincides with the value. */\n  get percentage() {\n    if (this._slider.min >= this._slider.max) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n  }\n  /** @docs-private */\n  get fillPercentage() {\n    if (!this._slider._cachedWidth) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    if (this._translateX === 0) {\n      return 0;\n    }\n    return this.translateX / this._slider._cachedWidth;\n  }\n  /** Used to relay updates to _isFocused to the slider visual thumbs. */\n  _setIsFocused(v) {\n    this._isFocused = v;\n  }\n  constructor(_ngZone, _elementRef, _cdr, _slider) {\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._cdr = _cdr;\n    this._slider = _slider;\n    /** Event emitted when the `value` is changed. */\n    this.valueChange = new EventEmitter();\n    /** Event emitted when the slider thumb starts being dragged. */\n    this.dragStart = new EventEmitter();\n    /** Event emitted when the slider thumb stops being dragged. */\n    this.dragEnd = new EventEmitter();\n    /**\n     * Indicates whether this thumb is the start or end thumb.\n     * @docs-private\n     */\n    this.thumbPosition = 2 /* _MatThumb.END */;\n    /** The radius of a native html slider's knob. */\n    this._knobRadius = 8;\n    /** Whether user's cursor is currently in a mouse down state on the input. */\n    this._isActive = false;\n    /** Whether the input is currently focused (either by tab or after clicking). */\n    this._isFocused = false;\n    /**\n     * Whether the initial value has been set.\n     * This exists because the initial value cannot be immediately set because the min and max\n     * must first be relayed from the parent MatSlider component, which can only happen later\n     * in the component lifecycle.\n     */\n    this._hasSetInitialValue = false;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Indicates whether UI updates should be skipped.\n     *\n     * This flag is used to avoid flickering\n     * when correcting values on pointer up/down.\n     */\n    this._skipUIUpdate = false;\n    /** Callback called when the slider input has been touched. */\n    this._onTouchedFn = () => {};\n    /**\n     * Whether the NgModel has been initialized.\n     *\n     * This flag is used to ignore ghost null calls to\n     * writeValue which can break slider initialization.\n     *\n     * See https://github.com/angular/angular/issues/14988.\n     */\n    this._isControlInitialized = false;\n    this._platform = inject(Platform);\n    this._hostElement = _elementRef.nativeElement;\n    this._ngZone.runOutsideAngular(() => {\n      this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n      this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n      this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n    });\n  }\n  ngOnDestroy() {\n    this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n    this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n    this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.dragStart.complete();\n    this.dragEnd.complete();\n  }\n  /** @docs-private */\n  initProps() {\n    this._updateWidthInactive();\n    // If this or the parent slider is disabled, just make everything disabled.\n    if (this.disabled !== this._slider.disabled) {\n      // The MatSlider setter for disabled will relay this and disable both inputs.\n      this._slider.disabled = true;\n    }\n    this.step = this._slider.step;\n    this.min = this._slider.min;\n    this.max = this._slider.max;\n    this._initValue();\n  }\n  /** @docs-private */\n  initUI() {\n    this._updateThumbUIByValue();\n  }\n  _initValue() {\n    this._hasSetInitialValue = true;\n    if (this._initialValue === undefined) {\n      this.value = this._getDefaultValue();\n    } else {\n      this._hostElement.value = this._initialValue;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n    }\n  }\n  _getDefaultValue() {\n    return this.min;\n  }\n  _onBlur() {\n    this._setIsFocused(false);\n    this._onTouchedFn();\n  }\n  _onFocus() {\n    this._setIsFocused(true);\n  }\n  _onChange() {\n    this.valueChange.emit(this.value);\n    // only used to handle the edge case where user\n    // mousedown on the slider then uses arrow keys.\n    if (this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n  }\n  _onInput() {\n    this._onChangeFn?.(this.value);\n    // handles arrowing and updating the value when\n    // a step is defined.\n    if (this._slider.step || !this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n    this._slider._onValueChange(this);\n  }\n  _onNgControlValueChange() {\n    // only used to handle when the value change\n    // originates outside of the slider.\n    if (!this._isActive || !this._isFocused) {\n      this._slider._onValueChange(this);\n      this._updateThumbUIByValue();\n    }\n    this._slider.disabled = this._formControl.disabled;\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    // On IOS, dragging only works if the pointer down happens on the\n    // slider thumb and the slider does not receive focus from pointer events.\n    if (this._platform.IOS) {\n      const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n      this._isActive = isCursorOnSliderThumb;\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      return;\n    }\n    this._isActive = true;\n    this._setIsFocused(true);\n    this._updateWidthActive();\n    this._slider._updateDimensions();\n    // Does nothing if a step is defined because we\n    // want the value to snap to the values on input.\n    if (!this._slider.step) {\n      this._updateThumbUIByPointerEvent(event, {\n        withAnimation: true\n      });\n    }\n    if (!this.disabled) {\n      this._handleValueCorrection(event);\n      this.dragStart.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n    }\n  }\n  /**\n   * Corrects the value of the slider on pointer up/down.\n   *\n   * Called on pointer down and up because the value is set based\n   * on the inactive width instead of the active width.\n   */\n  _handleValueCorrection(event) {\n    // Don't update the UI with the current value! The value on pointerdown\n    // and pointerup is calculated in the split second before the input(s)\n    // resize. See _updateWidthInactive() and _updateWidthActive() for more\n    // details.\n    this._skipUIUpdate = true;\n    // Note that this function gets triggered before the actual value of the\n    // slider is updated. This means if we were to set the value here, it\n    // would immediately be overwritten. Using setTimeout ensures the setting\n    // of the value happens after the value has been updated by the\n    // pointerdown event.\n    setTimeout(() => {\n      this._skipUIUpdate = false;\n      this._fixValue(event);\n    }, 0);\n  }\n  /** Corrects the value of the slider based on the pointer event's position. */\n  _fixValue(event) {\n    const xPos = event.clientX - this._slider._cachedLeft;\n    const width = this._slider._cachedWidth;\n    const step = this._slider.step === 0 ? 1 : this._slider.step;\n    const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n    const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n    // To ensure the percentage is rounded to the necessary number of decimals.\n    const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n    const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n    const value = Math.round(impreciseValue / step) * step;\n    const prevValue = this.value;\n    if (value === prevValue) {\n      // Because we prevented UI updates, if it turns out that the race\n      // condition didn't happen and the value is already correct, we\n      // have to apply the ui updates now.\n      this._slider._onValueChange(this);\n      this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n        withAnimation: this._slider._hasAnimation\n      });\n      return;\n    }\n    this.value = value;\n    this.valueChange.emit(this.value);\n    this._onChangeFn?.(this.value);\n    this._slider._onValueChange(this);\n    this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n      withAnimation: this._slider._hasAnimation\n    });\n  }\n  _onPointerMove(event) {\n    // Again, does nothing if a step is defined because\n    // we want the value to snap to the values on input.\n    if (!this._slider.step && this._isActive) {\n      this._updateThumbUIByPointerEvent(event);\n    }\n  }\n  _onPointerUp() {\n    if (this._isActive) {\n      this._isActive = false;\n      this.dragEnd.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n      // This setTimeout is to prevent the pointerup from triggering a value\n      // change on the input based on the inactive width. It's not clear why\n      // but for some reason on IOS this race condition is even more common so\n      // the timeout needs to be increased.\n      setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n    }\n  }\n  _clamp(v) {\n    return Math.max(Math.min(v, this._slider._cachedWidth), 0);\n  }\n  _calcTranslateXByValue() {\n    if (this._slider._isRtl) {\n      return (1 - this.percentage) * this._slider._cachedWidth;\n    }\n    return this.percentage * this._slider._cachedWidth;\n  }\n  _calcTranslateXByPointerEvent(event) {\n    return event.clientX - this._slider._cachedLeft;\n  }\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive() {\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding}px)`;\n  }\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive() {\n    this._hostElement.style.padding = '0px';\n    this._hostElement.style.width = 'calc(100% + 48px)';\n    this._hostElement.style.left = '-24px';\n  }\n  _updateThumbUIByValue(options) {\n    this.translateX = this._clamp(this._calcTranslateXByValue());\n    this._updateThumbUI(options);\n  }\n  _updateThumbUIByPointerEvent(event, options) {\n    this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n    this._updateThumbUI(options);\n  }\n  _updateThumbUI(options) {\n    this._slider._setTransition(!!options?.withAnimation);\n    this._slider._onTranslateXChange(this);\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n    }\n  }\n  /**\n   * Registers a callback to be invoked when the input's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChangeFn = fn;\n    this._isControlInitialized = true;\n  }\n  /**\n   * Registers a callback to be invoked when the input is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouchedFn = fn;\n  }\n  /**\n   * Sets the disabled state of the slider.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  focus() {\n    this._hostElement.focus();\n  }\n  blur() {\n    this._hostElement.blur();\n  }\n  static #_ = this.ɵfac = function MatSliderThumb_Factory(t) {\n    return new (t || MatSliderThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSliderThumb,\n    selectors: [[\"input\", \"matSliderThumb\", \"\"]],\n    hostAttrs: [\"type\", \"range\", 1, \"mdc-slider__input\"],\n    hostVars: 1,\n    hostBindings: function MatSliderThumb_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function MatSliderThumb_change_HostBindingHandler() {\n          return ctx._onChange();\n        })(\"input\", function MatSliderThumb_input_HostBindingHandler() {\n          return ctx._onInput();\n        })(\"blur\", function MatSliderThumb_blur_HostBindingHandler() {\n          return ctx._onBlur();\n        })(\"focus\", function MatSliderThumb_focus_HostBindingHandler() {\n          return ctx._onFocus();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-valuetext\", ctx._valuetext);\n      }\n    },\n    inputs: {\n      value: \"value\"\n    },\n    outputs: {\n      valueChange: \"valueChange\",\n      dragStart: \"dragStart\",\n      dragEnd: \"dragEnd\"\n    },\n    exportAs: [\"matSliderThumb\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n      provide: MAT_SLIDER_THUMB,\n      useExisting: MatSliderThumb\n    }])]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderThumb]',\n      exportAs: 'matSliderThumb',\n      host: {\n        'class': 'mdc-slider__input',\n        'type': 'range',\n        '[attr.aria-valuetext]': '_valuetext',\n        '(change)': '_onChange()',\n        '(input)': '_onInput()',\n        // TODO(wagnermaciel): Consider using a global event listener instead.\n        // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n        '(blur)': '_onBlur()',\n        '(focus)': '_onFocus()'\n      },\n      providers: [MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_THUMB,\n        useExisting: MatSliderThumb\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SLIDER]\n      }]\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSliderRangeThumb extends MatSliderThumb {\n  /** @docs-private */\n  getSibling() {\n    if (!this._sibling) {\n      this._sibling = this._slider._getInput(this._isEndThumb ? 1 /* _MatThumb.START */ : 2 /* _MatThumb.END */);\n    }\n\n    return this._sibling;\n  }\n  /**\n   * Returns the minimum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMinPos() {\n    const sibling = this.getSibling();\n    if (!this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return 0;\n  }\n  /**\n   * Returns the maximum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMaxPos() {\n    const sibling = this.getSibling();\n    if (this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._slider._cachedWidth;\n  }\n  _setIsLeftThumb() {\n    this._isLeftThumb = this._isEndThumb && this._slider._isRtl || !this._isEndThumb && !this._slider._isRtl;\n  }\n  constructor(_ngZone, _slider, _elementRef, _cdr) {\n    super(_ngZone, _elementRef, _cdr, _slider);\n    this._cdr = _cdr;\n    this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n    this._setIsLeftThumb();\n    this.thumbPosition = this._isEndThumb ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */;\n  }\n\n  _getDefaultValue() {\n    return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n  }\n  _onInput() {\n    super._onInput();\n    this._updateSibling();\n    if (!this._isActive) {\n      this._updateWidthInactive();\n    }\n  }\n  _onNgControlValueChange() {\n    super._onNgControlValueChange();\n    this.getSibling()?._updateMinMax();\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    if (this._sibling) {\n      this._sibling._updateWidthActive();\n      this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n    }\n    super._onPointerDown(event);\n  }\n  _onPointerUp() {\n    super._onPointerUp();\n    if (this._sibling) {\n      setTimeout(() => {\n        this._sibling._updateWidthInactive();\n        this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n      });\n    }\n  }\n  _onPointerMove(event) {\n    super._onPointerMove(event);\n    if (!this._slider.step && this._isActive) {\n      this._updateSibling();\n    }\n  }\n  _fixValue(event) {\n    super._fixValue(event);\n    this._sibling?._updateMinMax();\n  }\n  _clamp(v) {\n    return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n  }\n  _updateMinMax() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    if (this._isEndThumb) {\n      this.min = Math.max(this._slider.min, sibling.value);\n      this.max = this._slider.max;\n    } else {\n      this.min = this._slider.min;\n      this.max = Math.min(this._slider.max, sibling.value);\n    }\n  }\n  _updateWidthActive() {\n    const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n    const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth;\n    const percentage = this._slider.min < this._slider.max ? (this.max - this.min) / (this._slider.max - this._slider.min) : 1;\n    const width = maxWidth * percentage + minWidth;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n  }\n  _updateWidthInactive() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    const maxWidth = this._slider._cachedWidth;\n    const midValue = this._isEndThumb ? this.value - (this.value - sibling.value) / 2 : this.value + (sibling.value - this.value) / 2;\n    const _percentage = this._isEndThumb ? (this.max - midValue) / (this._slider.max - this._slider.min) : (midValue - this.min) / (this._slider.max - this._slider.min);\n    const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n    // Extend the native input width by the radius of the ripple\n    let ripplePadding = this._slider._rippleRadius;\n    // If one of the inputs is maximally sized (the value of both thumbs is\n    // equal to the min or max), make that input take up all of the width and\n    // make the other unselectable.\n    if (percentage === 1) {\n      ripplePadding = 48;\n    } else if (percentage === 0) {\n      ripplePadding = 0;\n    }\n    const width = maxWidth * percentage + ripplePadding;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = '0px';\n    if (this._isLeftThumb) {\n      this._hostElement.style.left = '-24px';\n      this._hostElement.style.right = 'auto';\n    } else {\n      this._hostElement.style.left = 'auto';\n      this._hostElement.style.right = '-24px';\n    }\n  }\n  _updateStaticStyles() {\n    this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n  }\n  _updateSibling() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    sibling._updateMinMax();\n    if (this._isActive) {\n      sibling._updateWidthActive();\n    } else {\n      sibling._updateWidthInactive();\n    }\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n      this._updateWidthInactive();\n      this._updateSibling();\n    }\n  }\n  static #_ = this.ɵfac = function MatSliderRangeThumb_Factory(t) {\n    return new (t || MatSliderRangeThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_SLIDER), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatSliderRangeThumb,\n    selectors: [[\"input\", \"matSliderStartThumb\", \"\"], [\"input\", \"matSliderEndThumb\", \"\"]],\n    exportAs: [\"matSliderRangeThumb\"],\n    features: [i0.ɵɵProvidersFeature([MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n      provide: MAT_SLIDER_RANGE_THUMB,\n      useExisting: MatSliderRangeThumb\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderRangeThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n      exportAs: 'matSliderRangeThumb',\n      providers: [MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_RANGE_THUMB,\n        useExisting: MatSliderRangeThumb\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_SLIDER]\n      }]\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\nclass MatSliderModule {\n  static #_ = this.ɵfac = function MatSliderModule_Factory(t) {\n    return new (t || MatSliderModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatSliderModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, CommonModule, MatRippleModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, MatRippleModule],\n      exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb],\n      declarations: [MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };", "map": {"version": 3, "names": ["i1", "coerceBooleanProperty", "coerceNumberProperty", "Platform", "i0", "InjectionToken", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "ViewChild", "inject", "Optional", "ViewChildren", "ContentChild", "ContentChildren", "forwardRef", "EventEmitter", "Directive", "Output", "NgModule", "i2$1", "<PERSON><PERSON><PERSON><PERSON>", "mixinColor", "mixinDisableRipple", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "MatRippleModule", "ANIMATION_MODULE_TYPE", "i2", "CommonModule", "NG_VALUE_ACCESSOR", "Subject", "_c0", "_c1", "MatSliderVisualThumb_div_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "valueIndicatorText", "_c2", "MatSlider_div_6_ng_container_2_div_1_Template", "ɵɵelement", "tickMark_r6", "$implicit", "i_r7", "index", "ctx_r5", "ɵɵclassMap", "ɵɵstyleProp", "_calcTickMarkTransform", "MatSlider_div_6_ng_container_2_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r4", "ɵɵproperty", "_tickMarks", "MatSlider_div_6_Template", "ctx_r1", "_cachedWidth", "Mat<PERSON><PERSON>r_mat_slider_visual_thumb_7_Template", "ctx_r2", "discrete", "startValueIndicatorText", "_c3", "MAT_SLIDER", "MAT_SLIDER_THUMB", "MAT_SLIDER_RANGE_THUMB", "MAT_SLIDER_VISUAL_THUMB", "MatSliderChange", "MatSliderVisualThumb", "constructor", "_cdr", "_ngZone", "_elementRef", "_slider", "_isHovered", "_isActive", "_isValueIndicatorVisible", "_onPointer<PERSON>ove", "event", "_sliderInput", "_isFocused", "rect", "_hostElement", "getBoundingClientRect", "isHovered", "_isCursorOnSliderThumb", "_showHoverRipple", "_hideRipple", "_hoverRippleRef", "_onMouseLeave", "_onFocus", "_showFocusRipple", "classList", "add", "_onBlur", "_focusRippleRef", "remove", "_onDragStart", "button", "_showActiveRipple", "_onDragEnd", "_activeRippleRef", "nativeElement", "ngAfterViewInit", "_ripple", "radius", "_getInput", "thumbPosition", "_sliderInputEl", "input", "runOutsideAngular", "addEventListener", "ngOnDestroy", "removeEventListener", "_isShowingRipple", "_showRipple", "enterDuration", "exitDuration", "element", "rippleRef", "state", "animation", "ignoreGlobalRippleConfig", "disabled", "_showValueIndicator", "_isRange", "sibling", "_getThumb", "_globalRippleOptions", "launch", "_noopAnimations", "centered", "persistent", "fadeOut", "_isShowingAnyRipple", "_hideValueIndicator", "_getSibling", "_getValueIndicatorContainer", "_valueIndicatorContainer", "_getKnob", "_knob", "_", "ɵfac", "MatSliderVisualThumb_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "NgZone", "ElementRef", "_2", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatSliderVisualThumb_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "features", "ɵɵProvidersFeature", "provide", "useExisting", "decls", "vars", "consts", "template", "MatSliderVisualThumb_Template", "dependencies", "NgIf", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "providers", "undefined", "decorators", "_MatSliderMixinBase", "<PERSON><PERSON><PERSON><PERSON>", "_disabled", "v", "endInput", "startInput", "_discrete", "_updateValueIndicatorUIs", "showTickMarks", "_showTickMarks", "min", "_min", "_updateMin", "prevMin", "_updateMinRange", "old", "new", "_updateMinNonRange", "_onMinMaxOrStepChange", "oldEndValue", "value", "oldStartValue", "Math", "max", "_updateWidthInactive", "_onTranslateXChangeBySideEffect", "_onValueChange", "oldValue", "_updateThumbUIByValue", "_updateTrackUI", "_max", "_updateMax", "prevMax", "_updateMaxRange", "_updateMaxNonRange", "step", "_step", "_updateStep", "_updateStepRange", "_updateStepNonRange", "prevStartValue", "_platform", "SAFARI", "elementRef", "_dir", "animationMode", "displayWith", "_rippleRadius", "endValueIndicatorText", "_isRtl", "_hasViewInitialized", "_tickMarkTrackWidth", "_hasAnimation", "_resizeTimer", "_knobRadius", "_thumbsOverlap", "_dirChangeSubscription", "change", "subscribe", "_onDir<PERSON>hange", "<PERSON><PERSON><PERSON><PERSON>", "_updateDimensions", "eInput", "sInput", "detectChanges", "_validateInputs", "thumb", "_inputPadding", "_inputOffset", "_initUIRange", "_initUINonRange", "_updateTickMarkUI", "_updateTickMarkTrackUI", "_observeHostResize", "initProps", "initUI", "_updateValueIndicatorUI", "_updateMinMax", "_updateStaticStyles", "unsubscribe", "_resizeObserver", "disconnect", "_onDirChangeRange", "_onDirChangeNonRange", "_setIsLeftThumb", "translateX", "_calcTranslateXByValue", "ResizeObserver", "clearTimeout", "_onResize", "observe", "_getValue", "_skipUpdate", "_skipUIUpdate", "offsetWidth", "_cachedLeft", "left", "_setTrackActiveStyles", "trackStyle", "_trackActive", "style", "right", "transform<PERSON><PERSON>in", "transform", "length", "_onTranslateXChange", "source", "_updateThumbUI", "_updateOverlappingThumbUI", "input1", "input2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_areThumbsOverlapping", "_updateOverlappingThumbClassNames", "getSibling", "sourceThumb", "siblingThumb", "toggle", "valuetext", "_valuetext", "setAttribute", "visualThumb", "maxValue", "floor", "percentage", "_updateTrackUIRange", "_updateTrackUINonRange", "activePercentage", "abs", "_isLeftThumb", "fillPercentage", "_updateTickMarkUIRange", "_updateTickMarkUINonRange", "reverse", "numActive", "round", "numInactive", "Array", "fill", "concat", "endValue", "startValue", "numInactiveBeforeStartThumb", "numInactiveAfterEndThumb", "_input", "_inputs", "last", "_thumbs", "_setTransition", "withAnimation", "IOS", "width", "centerX", "x", "centerY", "y", "dx", "clientX", "dy", "clientY", "pow", "MatSlider_Factory", "Directionality", "contentQueries", "MatSlider_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatSlider_Query", "hostVars", "hostBindings", "MatSlider_HostBindings", "ɵɵclassProp", "color", "disable<PERSON><PERSON><PERSON>", "exportAs", "ɵɵInheritDefinitionFeature", "ngContentSelectors", "MatSlider_Template", "ɵɵprojectionDef", "ɵɵprojection", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "descendants", "isRange", "endInputElement", "startInputElement", "startValid", "hasAttribute", "endValid", "_throwInvalidInputConfigurationError", "Error", "MAT_SLIDER_THUMB_VALUE_ACCESSOR", "MatSlider<PERSON><PERSON>b", "multi", "MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR", "MatSliderRangeThumb", "val", "toString", "_hasSetInitialValue", "_initialValue", "_translateX", "_setIsFocused", "valueChange", "dragStart", "dragEnd", "_destroyed", "_onTouchedFn", "_isControlInitialized", "_onPointerDown", "bind", "_onPointerUp", "next", "complete", "_initValue", "_getDefaultValue", "_onChange", "emit", "_onInput", "_onChangeFn", "_onNgControlValueChange", "_formControl", "isCursorOnSliderThumb", "_updateWidthActive", "_updateThumbUIByPointerEvent", "_handleValueCorrection", "parent", "setTimeout", "_fixValue", "xPos", "numSteps", "fixedPercentage", "impreciseValue", "prevValue", "_clamp", "_calcTranslateXByPointerEvent", "padding", "options", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "focus", "blur", "MatSliderThumb_Factory", "ɵdir", "ɵɵdefineDirective", "MatSliderThumb_HostBindings", "ɵɵlistener", "MatSliderThumb_change_HostBindingHandler", "MatSliderThumb_input_HostBindingHandler", "MatSliderThumb_blur_HostBindingHandler", "MatSliderThumb_focus_HostBindingHandler", "ɵɵattribute", "outputs", "_sibling", "_isEndThumb", "getMinPos", "getMaxPos", "_updateSibling", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "midValue", "_percentage", "ripplePadding", "MatSliderRangeThumb_Factory", "MatSliderModule", "MatSliderModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["E:/Github/MYDent-FrontEnd/node_modules/@angular/material/fesm2022/slider.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/bidi';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, inject, Optional, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, Directive, Output, NgModule } from '@angular/core';\nimport * as i2$1 from '@angular/material/core';\nimport { MatRipple, mixinColor, mixinDisableRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\n\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER = new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {\n}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nclass MatSliderVisualThumb {\n    constructor(_cdr, _ngZone, _elementRef, _slider) {\n        this._cdr = _cdr;\n        this._ngZone = _ngZone;\n        this._slider = _slider;\n        /** Whether the slider thumb is currently being hovered. */\n        this._isHovered = false;\n        /** Whether the slider thumb is currently being pressed. */\n        this._isActive = false;\n        /** Whether the value indicator tooltip is visible. */\n        this._isValueIndicatorVisible = false;\n        this._onPointerMove = (event) => {\n            if (this._sliderInput._isFocused) {\n                return;\n            }\n            const rect = this._hostElement.getBoundingClientRect();\n            const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n            this._isHovered = isHovered;\n            if (isHovered) {\n                this._showHoverRipple();\n            }\n            else {\n                this._hideRipple(this._hoverRippleRef);\n            }\n        };\n        this._onMouseLeave = () => {\n            this._isHovered = false;\n            this._hideRipple(this._hoverRippleRef);\n        };\n        this._onFocus = () => {\n            // We don't want to show the hover ripple on top of the focus ripple.\n            // Happen when the users cursor is over a thumb and then the user tabs to it.\n            this._hideRipple(this._hoverRippleRef);\n            this._showFocusRipple();\n            this._hostElement.classList.add('mdc-slider__thumb--focused');\n        };\n        this._onBlur = () => {\n            // Happens when the user tabs away while still dragging a thumb.\n            if (!this._isActive) {\n                this._hideRipple(this._focusRippleRef);\n            }\n            // Happens when the user tabs away from a thumb but their cursor is still over it.\n            if (this._isHovered) {\n                this._showHoverRipple();\n            }\n            this._hostElement.classList.remove('mdc-slider__thumb--focused');\n        };\n        this._onDragStart = (event) => {\n            if (event.button !== 0) {\n                return;\n            }\n            this._isActive = true;\n            this._showActiveRipple();\n        };\n        this._onDragEnd = () => {\n            this._isActive = false;\n            this._hideRipple(this._activeRippleRef);\n            // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n            if (!this._sliderInput._isFocused) {\n                this._hideRipple(this._focusRippleRef);\n            }\n        };\n        this._hostElement = _elementRef.nativeElement;\n    }\n    ngAfterViewInit() {\n        this._ripple.radius = 24;\n        this._sliderInput = this._slider._getInput(this.thumbPosition);\n        this._sliderInputEl = this._sliderInput._hostElement;\n        const input = this._sliderInputEl;\n        // These listeners don't update any data bindings so we bind them outside\n        // of the NgZone to prevent Angular from needlessly running change detection.\n        this._ngZone.runOutsideAngular(() => {\n            input.addEventListener('pointermove', this._onPointerMove);\n            input.addEventListener('pointerdown', this._onDragStart);\n            input.addEventListener('pointerup', this._onDragEnd);\n            input.addEventListener('pointerleave', this._onMouseLeave);\n            input.addEventListener('focus', this._onFocus);\n            input.addEventListener('blur', this._onBlur);\n        });\n    }\n    ngOnDestroy() {\n        const input = this._sliderInputEl;\n        input.removeEventListener('pointermove', this._onPointerMove);\n        input.removeEventListener('pointerdown', this._onDragStart);\n        input.removeEventListener('pointerup', this._onDragEnd);\n        input.removeEventListener('pointerleave', this._onMouseLeave);\n        input.removeEventListener('focus', this._onFocus);\n        input.removeEventListener('blur', this._onBlur);\n    }\n    /** Handles displaying the hover ripple. */\n    _showHoverRipple() {\n        if (!this._isShowingRipple(this._hoverRippleRef)) {\n            this._hoverRippleRef = this._showRipple({ enterDuration: 0, exitDuration: 0 });\n            this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n        }\n    }\n    /** Handles displaying the focus ripple. */\n    _showFocusRipple() {\n        // Show the focus ripple event if noop animations are enabled.\n        if (!this._isShowingRipple(this._focusRippleRef)) {\n            this._focusRippleRef = this._showRipple({ enterDuration: 0, exitDuration: 0 }, true);\n            this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n        }\n    }\n    /** Handles displaying the active ripple. */\n    _showActiveRipple() {\n        if (!this._isShowingRipple(this._activeRippleRef)) {\n            this._activeRippleRef = this._showRipple({ enterDuration: 225, exitDuration: 400 });\n            this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n        }\n    }\n    /** Whether the given rippleRef is currently fading in or visible. */\n    _isShowingRipple(rippleRef) {\n        return rippleRef?.state === 0 /* RippleState.FADING_IN */ || rippleRef?.state === 1 /* RippleState.VISIBLE */;\n    }\n    /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n    _showRipple(animation, ignoreGlobalRippleConfig) {\n        if (this._slider.disabled) {\n            return;\n        }\n        this._showValueIndicator();\n        if (this._slider._isRange) {\n            const sibling = this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n            sibling._showValueIndicator();\n        }\n        if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n            return;\n        }\n        return this._ripple.launch({\n            animation: this._slider._noopAnimations ? { enterDuration: 0, exitDuration: 0 } : animation,\n            centered: true,\n            persistent: true,\n        });\n    }\n    /**\n     * Fades out the given ripple.\n     * Also hides the value indicator if no ripple is showing.\n     */\n    _hideRipple(rippleRef) {\n        rippleRef?.fadeOut();\n        if (this._isShowingAnyRipple()) {\n            return;\n        }\n        if (!this._slider._isRange) {\n            this._hideValueIndicator();\n        }\n        const sibling = this._getSibling();\n        if (!sibling._isShowingAnyRipple()) {\n            this._hideValueIndicator();\n            sibling._hideValueIndicator();\n        }\n    }\n    /** Shows the value indicator ui. */\n    _showValueIndicator() {\n        this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n    }\n    /** Hides the value indicator ui. */\n    _hideValueIndicator() {\n        this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n    }\n    _getSibling() {\n        return this._slider._getThumb(this.thumbPosition === 1 /* _MatThumb.START */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n    }\n    /** Gets the value indicator container's native HTML element. */\n    _getValueIndicatorContainer() {\n        return this._valueIndicatorContainer?.nativeElement;\n    }\n    /** Gets the native HTML element of the slider thumb knob. */\n    _getKnob() {\n        return this._knob.nativeElement;\n    }\n    _isShowingAnyRipple() {\n        return (this._isShowingRipple(this._hoverRippleRef) ||\n            this._isShowingRipple(this._focusRippleRef) ||\n            this._isShowingRipple(this._activeRippleRef));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderVisualThumb, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i0.ElementRef }, { token: MAT_SLIDER }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSliderVisualThumb, selector: \"mat-slider-visual-thumb\", inputs: { discrete: \"discrete\", thumbPosition: \"thumbPosition\", valueIndicatorText: \"valueIndicatorText\" }, host: { classAttribute: \"mdc-slider__thumb mat-mdc-slider-visual-thumb\" }, providers: [{ provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb }], viewQueries: [{ propertyName: \"_ripple\", first: true, predicate: MatRipple, descendants: true }, { propertyName: \"_knob\", first: true, predicate: [\"knob\"], descendants: true }, { propertyName: \"_valueIndicatorContainer\", first: true, predicate: [\"valueIndicatorContainer\"], descendants: true }], ngImport: i0, template: \"<div class=\\\"mdc-slider__value-indicator-container\\\" *ngIf=\\\"discrete\\\" #valueIndicatorContainer>\\n  <div class=\\\"mdc-slider__value-indicator\\\">\\n    <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n  </div>\\n</div>\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\", styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"], dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2$1.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderVisualThumb, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slider-visual-thumb', host: {\n                        'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [{ provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb }], template: \"<div class=\\\"mdc-slider__value-indicator-container\\\" *ngIf=\\\"discrete\\\" #valueIndicatorContainer>\\n  <div class=\\\"mdc-slider__value-indicator\\\">\\n    <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n  </div>\\n</div>\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\", styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }]; }, propDecorators: { discrete: [{\n                type: Input\n            }], thumbPosition: [{\n                type: Input\n            }], valueIndicatorText: [{\n                type: Input\n            }], _ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }], _knob: [{\n                type: ViewChild,\n                args: ['knob']\n            }], _valueIndicatorContainer: [{\n                type: ViewChild,\n                args: ['valueIndicatorContainer']\n            }] } });\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n// Boilerplate for applying mixins to MatSlider.\nconst _MatSliderMixinBase = mixinColor(mixinDisableRipple(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}), 'primary');\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nclass MatSlider extends _MatSliderMixinBase {\n    /** Whether the slider is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(v) {\n        this._disabled = coerceBooleanProperty(v);\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        if (endInput) {\n            endInput.disabled = this._disabled;\n        }\n        if (startInput) {\n            startInput.disabled = this._disabled;\n        }\n    }\n    /** Whether the slider displays a numeric value label upon pressing the thumb. */\n    get discrete() {\n        return this._discrete;\n    }\n    set discrete(v) {\n        this._discrete = coerceBooleanProperty(v);\n        this._updateValueIndicatorUIs();\n    }\n    /** Whether the slider displays tick marks along the slider track. */\n    get showTickMarks() {\n        return this._showTickMarks;\n    }\n    set showTickMarks(v) {\n        this._showTickMarks = coerceBooleanProperty(v);\n    }\n    /** The minimum value that the slider can have. */\n    get min() {\n        return this._min;\n    }\n    set min(v) {\n        const min = coerceNumberProperty(v, this._min);\n        if (this._min !== min) {\n            this._updateMin(min);\n        }\n    }\n    _updateMin(min) {\n        const prevMin = this._min;\n        this._min = min;\n        this._isRange ? this._updateMinRange({ old: prevMin, new: min }) : this._updateMinNonRange(min);\n        this._onMinMaxOrStepChange();\n    }\n    _updateMinRange(min) {\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        startInput.min = min.new;\n        endInput.min = Math.max(min.new, startInput.value);\n        startInput.max = Math.min(endInput.max, endInput.value);\n        startInput._updateWidthInactive();\n        endInput._updateWidthInactive();\n        min.new < min.old\n            ? this._onTranslateXChangeBySideEffect(endInput, startInput)\n            : this._onTranslateXChangeBySideEffect(startInput, endInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateMinNonRange(min) {\n        const input = this._getInput(2 /* _MatThumb.END */);\n        if (input) {\n            const oldValue = input.value;\n            input.min = min;\n            input._updateThumbUIByValue();\n            this._updateTrackUI(input);\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    /** The maximum value that the slider can have. */\n    get max() {\n        return this._max;\n    }\n    set max(v) {\n        const max = coerceNumberProperty(v, this._max);\n        if (this._max !== max) {\n            this._updateMax(max);\n        }\n    }\n    _updateMax(max) {\n        const prevMax = this._max;\n        this._max = max;\n        this._isRange ? this._updateMaxRange({ old: prevMax, new: max }) : this._updateMaxNonRange(max);\n        this._onMinMaxOrStepChange();\n    }\n    _updateMaxRange(max) {\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        endInput.max = max.new;\n        startInput.max = Math.min(max.new, endInput.value);\n        endInput.min = startInput.value;\n        endInput._updateWidthInactive();\n        startInput._updateWidthInactive();\n        max.new > max.old\n            ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n            : this._onTranslateXChangeBySideEffect(endInput, startInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateMaxNonRange(max) {\n        const input = this._getInput(2 /* _MatThumb.END */);\n        if (input) {\n            const oldValue = input.value;\n            input.max = max;\n            input._updateThumbUIByValue();\n            this._updateTrackUI(input);\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    /** The values at which the thumb will snap. */\n    get step() {\n        return this._step;\n    }\n    set step(v) {\n        const step = coerceNumberProperty(v, this._step);\n        if (this._step !== step) {\n            this._updateStep(step);\n        }\n    }\n    _updateStep(step) {\n        this._step = step;\n        this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n        this._onMinMaxOrStepChange();\n    }\n    _updateStepRange() {\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        const prevStartValue = startInput.value;\n        endInput.min = this._min;\n        startInput.max = this._max;\n        endInput.step = this._step;\n        startInput.step = this._step;\n        if (this._platform.SAFARI) {\n            endInput.value = endInput.value;\n            startInput.value = startInput.value;\n        }\n        endInput.min = Math.max(this._min, startInput.value);\n        startInput.max = Math.min(this._max, endInput.value);\n        startInput._updateWidthInactive();\n        endInput._updateWidthInactive();\n        endInput.value < prevStartValue\n            ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n            : this._onTranslateXChangeBySideEffect(endInput, startInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateStepNonRange() {\n        const input = this._getInput(2 /* _MatThumb.END */);\n        if (input) {\n            const oldValue = input.value;\n            input.step = this._step;\n            if (this._platform.SAFARI) {\n                input.value = input.value;\n            }\n            input._updateThumbUIByValue();\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    constructor(_ngZone, _cdr, elementRef, _dir, _globalRippleOptions, animationMode) {\n        super(elementRef);\n        this._ngZone = _ngZone;\n        this._cdr = _cdr;\n        this._dir = _dir;\n        this._globalRippleOptions = _globalRippleOptions;\n        this._disabled = false;\n        this._discrete = false;\n        this._showTickMarks = false;\n        this._min = 0;\n        this._max = 100;\n        this._step = 1;\n        /**\n         * Function that will be used to format the value before it is displayed\n         * in the thumb label. Can be used to format very large number in order\n         * for them to fit into the slider thumb.\n         */\n        this.displayWith = (value) => `${value}`;\n        this._rippleRadius = 24;\n        // The value indicator tooltip text for the visual slider thumb(s).\n        /** @docs-private */\n        this.startValueIndicatorText = '';\n        /** @docs-private */\n        this.endValueIndicatorText = '';\n        this._isRange = false;\n        /** Whether the slider is rtl. */\n        this._isRtl = false;\n        this._hasViewInitialized = false;\n        /**\n         * The width of the tick mark track.\n         * The tick mark track width is different from full track width\n         */\n        this._tickMarkTrackWidth = 0;\n        this._hasAnimation = false;\n        this._resizeTimer = null;\n        this._platform = inject(Platform);\n        /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n        this._knobRadius = 8;\n        /** Whether or not the slider thumbs overlap. */\n        this._thumbsOverlap = false;\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n        this._isRtl = this._dir.value === 'rtl';\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._updateDimensions();\n        }\n        const eInput = this._getInput(2 /* _MatThumb.END */);\n        const sInput = this._getInput(1 /* _MatThumb.START */);\n        this._isRange = !!eInput && !!sInput;\n        this._cdr.detectChanges();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            _validateInputs(this._isRange, this._getInput(2 /* _MatThumb.END */), this._getInput(1 /* _MatThumb.START */));\n        }\n        const thumb = this._getThumb(2 /* _MatThumb.END */);\n        this._rippleRadius = thumb._ripple.radius;\n        this._inputPadding = this._rippleRadius - this._knobRadius;\n        this._inputOffset = this._knobRadius;\n        this._isRange\n            ? this._initUIRange(eInput, sInput)\n            : this._initUINonRange(eInput);\n        this._updateTrackUI(eInput);\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._observeHostResize();\n        this._cdr.detectChanges();\n    }\n    _initUINonRange(eInput) {\n        eInput.initProps();\n        eInput.initUI();\n        this._updateValueIndicatorUI(eInput);\n        this._hasViewInitialized = true;\n        eInput._updateThumbUIByValue();\n    }\n    _initUIRange(eInput, sInput) {\n        eInput.initProps();\n        eInput.initUI();\n        sInput.initProps();\n        sInput.initUI();\n        eInput._updateMinMax();\n        sInput._updateMinMax();\n        eInput._updateStaticStyles();\n        sInput._updateStaticStyles();\n        this._updateValueIndicatorUIs();\n        this._hasViewInitialized = true;\n        eInput._updateThumbUIByValue();\n        sInput._updateThumbUIByValue();\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._resizeObserver?.disconnect();\n        this._resizeObserver = null;\n    }\n    /** Handles updating the slider ui after a dir change. */\n    _onDirChange() {\n        this._isRtl = this._dir.value === 'rtl';\n        this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n        this._updateTickMarkUI();\n    }\n    _onDirChangeRange() {\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        endInput._setIsLeftThumb();\n        startInput._setIsLeftThumb();\n        endInput.translateX = endInput._calcTranslateXByValue();\n        startInput.translateX = startInput._calcTranslateXByValue();\n        endInput._updateStaticStyles();\n        startInput._updateStaticStyles();\n        endInput._updateWidthInactive();\n        startInput._updateWidthInactive();\n        endInput._updateThumbUIByValue();\n        startInput._updateThumbUIByValue();\n    }\n    _onDirChangeNonRange() {\n        const input = this._getInput(2 /* _MatThumb.END */);\n        input._updateThumbUIByValue();\n    }\n    /** Starts observing and updating the slider if the host changes its size. */\n    _observeHostResize() {\n        if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._resizeObserver = new ResizeObserver(() => {\n                if (this._isActive()) {\n                    return;\n                }\n                if (this._resizeTimer) {\n                    clearTimeout(this._resizeTimer);\n                }\n                this._onResize();\n            });\n            this._resizeObserver.observe(this._elementRef.nativeElement);\n        });\n    }\n    /** Whether any of the thumbs are currently active. */\n    _isActive() {\n        return this._getThumb(1 /* _MatThumb.START */)._isActive || this._getThumb(2 /* _MatThumb.END */)._isActive;\n    }\n    _getValue(thumbPosition = 2 /* _MatThumb.END */) {\n        const input = this._getInput(thumbPosition);\n        if (!input) {\n            return this.min;\n        }\n        return input.value;\n    }\n    _skipUpdate() {\n        return !!(this._getInput(1 /* _MatThumb.START */)?._skipUIUpdate || this._getInput(2 /* _MatThumb.END */)?._skipUIUpdate);\n    }\n    /** Stores the slider dimensions. */\n    _updateDimensions() {\n        this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n        this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n    }\n    /** Sets the styles for the active portion of the track. */\n    _setTrackActiveStyles(styles) {\n        const trackStyle = this._trackActive.nativeElement.style;\n        trackStyle.left = styles.left;\n        trackStyle.right = styles.right;\n        trackStyle.transformOrigin = styles.transformOrigin;\n        trackStyle.transform = styles.transform;\n    }\n    /** Returns the translateX positioning for a tick mark based on it's index. */\n    _calcTickMarkTransform(index) {\n        // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n        const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n        return `translateX(${translateX}px`;\n    }\n    // Handlers for updating the slider ui.\n    _onTranslateXChange(source) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateThumbUI(source);\n        this._updateTrackUI(source);\n        this._updateOverlappingThumbUI(source);\n    }\n    _onTranslateXChangeBySideEffect(input1, input2) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        input1._updateThumbUIByValue();\n        input2._updateThumbUIByValue();\n    }\n    _onValueChange(source) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateValueIndicatorUI(source);\n        this._updateTickMarkUI();\n        this._cdr.detectChanges();\n    }\n    _onMinMaxOrStepChange() {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._cdr.markForCheck();\n    }\n    _onResize() {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateDimensions();\n        if (this._isRange) {\n            const eInput = this._getInput(2 /* _MatThumb.END */);\n            const sInput = this._getInput(1 /* _MatThumb.START */);\n            eInput._updateThumbUIByValue();\n            sInput._updateThumbUIByValue();\n            eInput._updateStaticStyles();\n            sInput._updateStaticStyles();\n            eInput._updateMinMax();\n            sInput._updateMinMax();\n            eInput._updateWidthInactive();\n            sInput._updateWidthInactive();\n        }\n        else {\n            const eInput = this._getInput(2 /* _MatThumb.END */);\n            if (eInput) {\n                eInput._updateThumbUIByValue();\n            }\n        }\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._cdr.detectChanges();\n    }\n    /** Returns true if the slider knobs are overlapping one another. */\n    _areThumbsOverlapping() {\n        const startInput = this._getInput(1 /* _MatThumb.START */);\n        const endInput = this._getInput(2 /* _MatThumb.END */);\n        if (!startInput || !endInput) {\n            return false;\n        }\n        return endInput.translateX - startInput.translateX < 20;\n    }\n    /**\n     * Updates the class names of overlapping slider thumbs so\n     * that the current active thumb is styled to be on \"top\".\n     */\n    _updateOverlappingThumbClassNames(source) {\n        const sibling = source.getSibling();\n        const sourceThumb = this._getThumb(source.thumbPosition);\n        const siblingThumb = this._getThumb(sibling.thumbPosition);\n        siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n        sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n    }\n    /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n    _updateOverlappingThumbUI(source) {\n        if (!this._isRange || this._skipUpdate()) {\n            return;\n        }\n        if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n            this._thumbsOverlap = !this._thumbsOverlap;\n            this._updateOverlappingThumbClassNames(source);\n        }\n    }\n    // _MatThumb styles update conditions\n    //\n    // 1. TranslateX, resize, or dir change\n    //    - Reason: The thumb styles need to be updated according to the new translateX.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the translateX of the given thumb. */\n    _updateThumbUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        const thumb = this._getThumb(source.thumbPosition === 2 /* _MatThumb.END */ ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */);\n        thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n    }\n    // Value indicator text update conditions\n    //\n    // 1. Value\n    //    - Reason: The value displayed needs to be updated.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the value indicator tooltip ui for the given thumb. */\n    _updateValueIndicatorUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        const valuetext = this.displayWith(source.value);\n        this._hasViewInitialized\n            ? (source._valuetext = valuetext)\n            : source._hostElement.setAttribute('aria-valuetext', valuetext);\n        if (this.discrete) {\n            source.thumbPosition === 1 /* _MatThumb.START */\n                ? (this.startValueIndicatorText = valuetext)\n                : (this.endValueIndicatorText = valuetext);\n            const visualThumb = this._getThumb(source.thumbPosition);\n            valuetext.length < 3\n                ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value')\n                : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n        }\n    }\n    /** Updates all value indicator UIs in the slider. */\n    _updateValueIndicatorUIs() {\n        const eInput = this._getInput(2 /* _MatThumb.END */);\n        const sInput = this._getInput(1 /* _MatThumb.START */);\n        if (eInput) {\n            this._updateValueIndicatorUI(eInput);\n        }\n        if (sInput) {\n            this._updateValueIndicatorUI(sInput);\n        }\n    }\n    // Update Tick Mark Track Width\n    //\n    // 1. Min, max, or step\n    //    - Reason: The maximum reachable value may have changed.\n    //    - Side note: The maximum reachable value is different from the maximum value set by the\n    //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n    //      reachable value of 95.\n    // 2. Resize\n    //    - Reason: The position for the maximum reachable value needs to be recalculated.\n    /** Updates the width of the tick mark track. */\n    _updateTickMarkTrackUI() {\n        if (!this.showTickMarks || this._skipUpdate()) {\n            return;\n        }\n        const step = this._step && this._step > 0 ? this._step : 1;\n        const maxValue = Math.floor(this.max / step) * step;\n        const percentage = (maxValue - this.min) / (this.max - this.min);\n        this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n    }\n    // Track active update conditions\n    //\n    // 1. TranslateX\n    //    - Reason: The track active should line up with the new thumb position.\n    // 2. Min or max\n    //    - Reason #1: The 'active' percentage needs to be recalculated.\n    //    - Reason #2: The value may have silently changed.\n    // 3. Step\n    //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n    // 4. Dir change\n    //    - Reason: The track active will need to be updated according to the new thumb position(s).\n    // 5. Resize\n    //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n    /** Updates the scale on the active portion of the track. */\n    _updateTrackUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        this._isRange\n            ? this._updateTrackUIRange(source)\n            : this._updateTrackUINonRange(source);\n    }\n    _updateTrackUIRange(source) {\n        const sibling = source.getSibling();\n        if (!sibling || !this._cachedWidth) {\n            return;\n        }\n        const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n        if (source._isLeftThumb && this._cachedWidth) {\n            this._setTrackActiveStyles({\n                left: 'auto',\n                right: `${this._cachedWidth - sibling.translateX}px`,\n                transformOrigin: 'right',\n                transform: `scaleX(${activePercentage})`,\n            });\n        }\n        else {\n            this._setTrackActiveStyles({\n                left: `${sibling.translateX}px`,\n                right: 'auto',\n                transformOrigin: 'left',\n                transform: `scaleX(${activePercentage})`,\n            });\n        }\n    }\n    _updateTrackUINonRange(source) {\n        this._isRtl\n            ? this._setTrackActiveStyles({\n                left: 'auto',\n                right: '0px',\n                transformOrigin: 'right',\n                transform: `scaleX(${1 - source.fillPercentage})`,\n            })\n            : this._setTrackActiveStyles({\n                left: '0px',\n                right: 'auto',\n                transformOrigin: 'left',\n                transform: `scaleX(${source.fillPercentage})`,\n            });\n    }\n    // Tick mark update conditions\n    //\n    // 1. Value\n    //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n    // 2. Min, max, or step\n    //    - Reason #1: the number of tick marks may have changed.\n    //    - Reason #2: The value may have silently changed.\n    /** Updates the dots along the slider track. */\n    _updateTickMarkUI() {\n        if (!this.showTickMarks ||\n            this.step === undefined ||\n            this.min === undefined ||\n            this.max === undefined) {\n            return;\n        }\n        const step = this.step > 0 ? this.step : 1;\n        this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n        if (this._isRtl) {\n            this._tickMarks.reverse();\n        }\n    }\n    _updateTickMarkUINonRange(step) {\n        const value = this._getValue();\n        let numActive = Math.max(Math.round((value - this.min) / step), 0);\n        let numInactive = Math.max(Math.round((this.max - value) / step), 0);\n        this._isRtl ? numActive++ : numInactive++;\n        this._tickMarks = Array(numActive)\n            .fill(0 /* _MatTickMark.ACTIVE */)\n            .concat(Array(numInactive).fill(1 /* _MatTickMark.INACTIVE */));\n    }\n    _updateTickMarkUIRange(step) {\n        const endValue = this._getValue();\n        const startValue = this._getValue(1 /* _MatThumb.START */);\n        const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n        const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n        const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n        this._tickMarks = Array(numInactiveBeforeStartThumb)\n            .fill(1 /* _MatTickMark.INACTIVE */)\n            .concat(Array(numActive).fill(0 /* _MatTickMark.ACTIVE */), Array(numInactiveAfterEndThumb).fill(1 /* _MatTickMark.INACTIVE */));\n    }\n    /** Gets the slider thumb input of the given thumb position. */\n    _getInput(thumbPosition) {\n        if (thumbPosition === 2 /* _MatThumb.END */ && this._input) {\n            return this._input;\n        }\n        if (this._inputs?.length) {\n            return thumbPosition === 1 /* _MatThumb.START */ ? this._inputs.first : this._inputs.last;\n        }\n        return;\n    }\n    /** Gets the slider thumb HTML input element of the given thumb position. */\n    _getThumb(thumbPosition) {\n        return thumbPosition === 2 /* _MatThumb.END */ ? this._thumbs?.last : this._thumbs?.first;\n    }\n    _setTransition(withAnimation) {\n        this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n        this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n    }\n    /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n    _isCursorOnSliderThumb(event, rect) {\n        const radius = rect.width / 2;\n        const centerX = rect.x + radius;\n        const centerY = rect.y + radius;\n        const dx = event.clientX - centerX;\n        const dy = event.clientY - centerY;\n        return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSlider, deps: [{ token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSlider, selector: \"mat-slider\", inputs: { color: \"color\", disableRipple: \"disableRipple\", disabled: \"disabled\", discrete: \"discrete\", showTickMarks: \"showTickMarks\", min: \"min\", max: \"max\", step: \"step\", displayWith: \"displayWith\" }, host: { properties: { \"class.mdc-slider--range\": \"_isRange\", \"class.mdc-slider--disabled\": \"disabled\", \"class.mdc-slider--discrete\": \"discrete\", \"class.mdc-slider--tick-marks\": \"showTickMarks\", \"class._mat-animation-noopable\": \"_noopAnimations\" }, classAttribute: \"mat-mdc-slider mdc-slider\" }, providers: [{ provide: MAT_SLIDER, useExisting: MatSlider }], queries: [{ propertyName: \"_input\", first: true, predicate: MAT_SLIDER_THUMB, descendants: true }, { propertyName: \"_inputs\", predicate: MAT_SLIDER_RANGE_THUMB }], viewQueries: [{ propertyName: \"_trackActive\", first: true, predicate: [\"trackActive\"], descendants: true }, { propertyName: \"_thumbs\", predicate: MAT_SLIDER_VISUAL_THUMB, descendants: true }], exportAs: [\"matSlider\"], usesInheritance: true, ngImport: i0, template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"showTickMarks\\\" class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n    <ng-container *ngIf=\\\"_cachedWidth\\\">\\n        <div\\n          *ngFor=\\\"let tickMark of _tickMarks; let i = index\\\"\\n          [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n          [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n    </ng-container>\\n  </div>\\n</div>\\n\\n<!-- Thumbs -->\\n<mat-slider-visual-thumb\\n  *ngIf=\\\"_isRange\\\"\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"1\\\"\\n  [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\", styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000));border-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color, var(--mdc-theme-primary, #6200ee));opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mdc-theme-on-surface, #000));opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mdc-theme-primary, #6200ee));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mdc-theme-on-surface, #000));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color, #666666);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color, #666666)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height, 6px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 6px)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height, 6px)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size, 2px);width:var(--mdc-slider-with-tick-marks-container-size, 2px)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking, 0.0071428571em);font-size:var(--mdc-slider-label-label-text-size, 0.875rem);font-family:var(--mdc-slider-label-label-text-font, Roboto, sans-serif);font-weight:var(--mdc-slider-label-label-text-weight, 500);line-height:var(--mdc-slider-label-label-text-line-height, 1.375rem)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape, 50%);width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-style:solid;border-width:calc(var(--mdc-slider-handle-height, 20px) / 2) calc(var(--mdc-slider-handle-width, 20px) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape, 50%)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color, #fff);border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator{opacity:var(--mat-mdc-slider-value-indicator-opacity, 1)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-mdc-slider-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-mdc-slider-hover-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-mdc-slider-focus-ripple-color, transparent)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: MatSliderVisualThumb, selector: \"mat-slider-visual-thumb\", inputs: [\"discrete\", \"thumbPosition\", \"valueIndicatorText\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSlider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slider', host: {\n                        'class': 'mat-mdc-slider mdc-slider',\n                        '[class.mdc-slider--range]': '_isRange',\n                        '[class.mdc-slider--disabled]': 'disabled',\n                        '[class.mdc-slider--discrete]': 'discrete',\n                        '[class.mdc-slider--tick-marks]': 'showTickMarks',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                    }, exportAs: 'matSlider', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, inputs: ['color', 'disableRipple'], providers: [{ provide: MAT_SLIDER, useExisting: MatSlider }], template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  <div *ngIf=\\\"showTickMarks\\\" class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n    <ng-container *ngIf=\\\"_cachedWidth\\\">\\n        <div\\n          *ngFor=\\\"let tickMark of _tickMarks; let i = index\\\"\\n          [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n          [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n    </ng-container>\\n  </div>\\n</div>\\n\\n<!-- Thumbs -->\\n<mat-slider-visual-thumb\\n  *ngIf=\\\"_isRange\\\"\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"1\\\"\\n  [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\", styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000));border-color:var(--mdc-slider-disabled-handle-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity, 0.04)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity, 0.12)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color, var(--mdc-theme-on-surface, #000))}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color, var(--mdc-theme-primary, #6200ee));opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color, var(--mdc-theme-on-surface, #000));opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color, var(--mdc-theme-on-primary, #fff));opacity:var(--mdc-slider-with-tick-marks-active-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color, var(--mdc-theme-primary, #6200ee));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color, var(--mdc-theme-on-surface, #000));opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity, 0.6)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color, #666666);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color, #666666)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color, var(--mdc-theme-on-primary, #fff))}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height, 6px);top:calc((var(--mdc-slider-inactive-track-height, 4px) - var(--mdc-slider-active-track-height, 6px)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height, 6px)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height, 4px)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size, 2px);width:var(--mdc-slider-with-tick-marks-container-size, 2px)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking, 0.0071428571em);font-size:var(--mdc-slider-label-label-text-size, 0.875rem);font-family:var(--mdc-slider-label-label-text-font, Roboto, sans-serif);font-weight:var(--mdc-slider-label-label-text-weight, 500);line-height:var(--mdc-slider-label-label-text-line-height, 1.375rem)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape, 9999px)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape, 50%);width:var(--mdc-slider-handle-width, 20px);height:var(--mdc-slider-handle-height, 20px);border-style:solid;border-width:calc(var(--mdc-slider-handle-height, 20px) / 2) calc(var(--mdc-slider-handle-width, 20px) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape, 50%)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation, 0px 2px 1px -1px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 1px 3px 0px rgba(0, 0, 0, 0.12))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-hover-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee));border-color:var(--mdc-slider-focus-handle-color, var(--mdc-theme-primary, #6200ee))}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color, #fff);border-width:var(--mdc-slider-with-overlap-handle-outline-width, 1px)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator{opacity:var(--mat-mdc-slider-value-indicator-opacity, 1)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-mdc-slider-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-mdc-slider-hover-ripple-color, transparent)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-mdc-slider-focus-ripple-color, transparent)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }]; }, propDecorators: { _trackActive: [{\n                type: ViewChild,\n                args: ['trackActive']\n            }], _thumbs: [{\n                type: ViewChildren,\n                args: [MAT_SLIDER_VISUAL_THUMB]\n            }], _input: [{\n                type: ContentChild,\n                args: [MAT_SLIDER_THUMB]\n            }], _inputs: [{\n                type: ContentChildren,\n                args: [MAT_SLIDER_RANGE_THUMB, { descendants: false }]\n            }], disabled: [{\n                type: Input\n            }], discrete: [{\n                type: Input\n            }], showTickMarks: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], displayWith: [{\n                type: Input\n            }] } });\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n    const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n    const endValid = endInputElement._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n    if (!startValid || !endValid) {\n        _throwInvalidInputConfigurationError();\n    }\n}\nfunction _throwInvalidInputConfigurationError() {\n    throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSliderThumb),\n    multi: true,\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSliderRangeThumb),\n    multi: true,\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nclass MatSliderThumb {\n    get value() {\n        return coerceNumberProperty(this._hostElement.value);\n    }\n    set value(v) {\n        const val = coerceNumberProperty(v).toString();\n        if (!this._hasSetInitialValue) {\n            this._initialValue = val;\n            return;\n        }\n        if (this._isActive) {\n            return;\n        }\n        this._hostElement.value = val;\n        this._updateThumbUIByValue();\n        this._slider._onValueChange(this);\n        this._cdr.detectChanges();\n        this._slider._cdr.markForCheck();\n    }\n    /**\n     * The current translateX in px of the slider visual thumb.\n     * @docs-private\n     */\n    get translateX() {\n        if (this._slider.min >= this._slider.max) {\n            this._translateX = 0;\n            return this._translateX;\n        }\n        if (this._translateX === undefined) {\n            this._translateX = this._calcTranslateXByValue();\n        }\n        return this._translateX;\n    }\n    set translateX(v) {\n        this._translateX = v;\n    }\n    /** @docs-private */\n    get min() {\n        return coerceNumberProperty(this._hostElement.min);\n    }\n    set min(v) {\n        this._hostElement.min = coerceNumberProperty(v).toString();\n        this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get max() {\n        return coerceNumberProperty(this._hostElement.max);\n    }\n    set max(v) {\n        this._hostElement.max = coerceNumberProperty(v).toString();\n        this._cdr.detectChanges();\n    }\n    get step() {\n        return coerceNumberProperty(this._hostElement.step);\n    }\n    set step(v) {\n        this._hostElement.step = coerceNumberProperty(v).toString();\n        this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get disabled() {\n        return coerceBooleanProperty(this._hostElement.disabled);\n    }\n    set disabled(v) {\n        this._hostElement.disabled = coerceBooleanProperty(v);\n        this._cdr.detectChanges();\n        if (this._slider.disabled !== this.disabled) {\n            this._slider.disabled = this.disabled;\n        }\n    }\n    /** The percentage of the slider that coincides with the value. */\n    get percentage() {\n        if (this._slider.min >= this._slider.max) {\n            return this._slider._isRtl ? 1 : 0;\n        }\n        return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n    }\n    /** @docs-private */\n    get fillPercentage() {\n        if (!this._slider._cachedWidth) {\n            return this._slider._isRtl ? 1 : 0;\n        }\n        if (this._translateX === 0) {\n            return 0;\n        }\n        return this.translateX / this._slider._cachedWidth;\n    }\n    /** Used to relay updates to _isFocused to the slider visual thumbs. */\n    _setIsFocused(v) {\n        this._isFocused = v;\n    }\n    constructor(_ngZone, _elementRef, _cdr, _slider) {\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._cdr = _cdr;\n        this._slider = _slider;\n        /** Event emitted when the `value` is changed. */\n        this.valueChange = new EventEmitter();\n        /** Event emitted when the slider thumb starts being dragged. */\n        this.dragStart = new EventEmitter();\n        /** Event emitted when the slider thumb stops being dragged. */\n        this.dragEnd = new EventEmitter();\n        /**\n         * Indicates whether this thumb is the start or end thumb.\n         * @docs-private\n         */\n        this.thumbPosition = 2 /* _MatThumb.END */;\n        /** The radius of a native html slider's knob. */\n        this._knobRadius = 8;\n        /** Whether user's cursor is currently in a mouse down state on the input. */\n        this._isActive = false;\n        /** Whether the input is currently focused (either by tab or after clicking). */\n        this._isFocused = false;\n        /**\n         * Whether the initial value has been set.\n         * This exists because the initial value cannot be immediately set because the min and max\n         * must first be relayed from the parent MatSlider component, which can only happen later\n         * in the component lifecycle.\n         */\n        this._hasSetInitialValue = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Indicates whether UI updates should be skipped.\n         *\n         * This flag is used to avoid flickering\n         * when correcting values on pointer up/down.\n         */\n        this._skipUIUpdate = false;\n        /** Callback called when the slider input has been touched. */\n        this._onTouchedFn = () => { };\n        /**\n         * Whether the NgModel has been initialized.\n         *\n         * This flag is used to ignore ghost null calls to\n         * writeValue which can break slider initialization.\n         *\n         * See https://github.com/angular/angular/issues/14988.\n         */\n        this._isControlInitialized = false;\n        this._platform = inject(Platform);\n        this._hostElement = _elementRef.nativeElement;\n        this._ngZone.runOutsideAngular(() => {\n            this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n            this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n            this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n        });\n    }\n    ngOnDestroy() {\n        this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n        this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n        this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n        this._destroyed.next();\n        this._destroyed.complete();\n        this.dragStart.complete();\n        this.dragEnd.complete();\n    }\n    /** @docs-private */\n    initProps() {\n        this._updateWidthInactive();\n        // If this or the parent slider is disabled, just make everything disabled.\n        if (this.disabled !== this._slider.disabled) {\n            // The MatSlider setter for disabled will relay this and disable both inputs.\n            this._slider.disabled = true;\n        }\n        this.step = this._slider.step;\n        this.min = this._slider.min;\n        this.max = this._slider.max;\n        this._initValue();\n    }\n    /** @docs-private */\n    initUI() {\n        this._updateThumbUIByValue();\n    }\n    _initValue() {\n        this._hasSetInitialValue = true;\n        if (this._initialValue === undefined) {\n            this.value = this._getDefaultValue();\n        }\n        else {\n            this._hostElement.value = this._initialValue;\n            this._updateThumbUIByValue();\n            this._slider._onValueChange(this);\n            this._cdr.detectChanges();\n        }\n    }\n    _getDefaultValue() {\n        return this.min;\n    }\n    _onBlur() {\n        this._setIsFocused(false);\n        this._onTouchedFn();\n    }\n    _onFocus() {\n        this._setIsFocused(true);\n    }\n    _onChange() {\n        this.valueChange.emit(this.value);\n        // only used to handle the edge case where user\n        // mousedown on the slider then uses arrow keys.\n        if (this._isActive) {\n            this._updateThumbUIByValue({ withAnimation: true });\n        }\n    }\n    _onInput() {\n        this._onChangeFn?.(this.value);\n        // handles arrowing and updating the value when\n        // a step is defined.\n        if (this._slider.step || !this._isActive) {\n            this._updateThumbUIByValue({ withAnimation: true });\n        }\n        this._slider._onValueChange(this);\n    }\n    _onNgControlValueChange() {\n        // only used to handle when the value change\n        // originates outside of the slider.\n        if (!this._isActive || !this._isFocused) {\n            this._slider._onValueChange(this);\n            this._updateThumbUIByValue();\n        }\n        this._slider.disabled = this._formControl.disabled;\n    }\n    _onPointerDown(event) {\n        if (this.disabled || event.button !== 0) {\n            return;\n        }\n        // On IOS, dragging only works if the pointer down happens on the\n        // slider thumb and the slider does not receive focus from pointer events.\n        if (this._platform.IOS) {\n            const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n            this._isActive = isCursorOnSliderThumb;\n            this._updateWidthActive();\n            this._slider._updateDimensions();\n            return;\n        }\n        this._isActive = true;\n        this._setIsFocused(true);\n        this._updateWidthActive();\n        this._slider._updateDimensions();\n        // Does nothing if a step is defined because we\n        // want the value to snap to the values on input.\n        if (!this._slider.step) {\n            this._updateThumbUIByPointerEvent(event, { withAnimation: true });\n        }\n        if (!this.disabled) {\n            this._handleValueCorrection(event);\n            this.dragStart.emit({ source: this, parent: this._slider, value: this.value });\n        }\n    }\n    /**\n     * Corrects the value of the slider on pointer up/down.\n     *\n     * Called on pointer down and up because the value is set based\n     * on the inactive width instead of the active width.\n     */\n    _handleValueCorrection(event) {\n        // Don't update the UI with the current value! The value on pointerdown\n        // and pointerup is calculated in the split second before the input(s)\n        // resize. See _updateWidthInactive() and _updateWidthActive() for more\n        // details.\n        this._skipUIUpdate = true;\n        // Note that this function gets triggered before the actual value of the\n        // slider is updated. This means if we were to set the value here, it\n        // would immediately be overwritten. Using setTimeout ensures the setting\n        // of the value happens after the value has been updated by the\n        // pointerdown event.\n        setTimeout(() => {\n            this._skipUIUpdate = false;\n            this._fixValue(event);\n        }, 0);\n    }\n    /** Corrects the value of the slider based on the pointer event's position. */\n    _fixValue(event) {\n        const xPos = event.clientX - this._slider._cachedLeft;\n        const width = this._slider._cachedWidth;\n        const step = this._slider.step === 0 ? 1 : this._slider.step;\n        const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n        const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n        // To ensure the percentage is rounded to the necessary number of decimals.\n        const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n        const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n        const value = Math.round(impreciseValue / step) * step;\n        const prevValue = this.value;\n        if (value === prevValue) {\n            // Because we prevented UI updates, if it turns out that the race\n            // condition didn't happen and the value is already correct, we\n            // have to apply the ui updates now.\n            this._slider._onValueChange(this);\n            this._slider.step > 0\n                ? this._updateThumbUIByValue()\n                : this._updateThumbUIByPointerEvent(event, { withAnimation: this._slider._hasAnimation });\n            return;\n        }\n        this.value = value;\n        this.valueChange.emit(this.value);\n        this._onChangeFn?.(this.value);\n        this._slider._onValueChange(this);\n        this._slider.step > 0\n            ? this._updateThumbUIByValue()\n            : this._updateThumbUIByPointerEvent(event, { withAnimation: this._slider._hasAnimation });\n    }\n    _onPointerMove(event) {\n        // Again, does nothing if a step is defined because\n        // we want the value to snap to the values on input.\n        if (!this._slider.step && this._isActive) {\n            this._updateThumbUIByPointerEvent(event);\n        }\n    }\n    _onPointerUp() {\n        if (this._isActive) {\n            this._isActive = false;\n            this.dragEnd.emit({ source: this, parent: this._slider, value: this.value });\n            // This setTimeout is to prevent the pointerup from triggering a value\n            // change on the input based on the inactive width. It's not clear why\n            // but for some reason on IOS this race condition is even more common so\n            // the timeout needs to be increased.\n            setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n        }\n    }\n    _clamp(v) {\n        return Math.max(Math.min(v, this._slider._cachedWidth), 0);\n    }\n    _calcTranslateXByValue() {\n        if (this._slider._isRtl) {\n            return (1 - this.percentage) * this._slider._cachedWidth;\n        }\n        return this.percentage * this._slider._cachedWidth;\n    }\n    _calcTranslateXByPointerEvent(event) {\n        return event.clientX - this._slider._cachedLeft;\n    }\n    /**\n     * Used to set the slider width to the correct\n     * dimensions while the user is dragging.\n     */\n    _updateWidthActive() {\n        this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n        this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding}px)`;\n    }\n    /**\n     * Sets the slider input to disproportionate dimensions to allow for touch\n     * events to be captured on touch devices.\n     */\n    _updateWidthInactive() {\n        this._hostElement.style.padding = '0px';\n        this._hostElement.style.width = 'calc(100% + 48px)';\n        this._hostElement.style.left = '-24px';\n    }\n    _updateThumbUIByValue(options) {\n        this.translateX = this._clamp(this._calcTranslateXByValue());\n        this._updateThumbUI(options);\n    }\n    _updateThumbUIByPointerEvent(event, options) {\n        this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n        this._updateThumbUI(options);\n    }\n    _updateThumbUI(options) {\n        this._slider._setTransition(!!options?.withAnimation);\n        this._slider._onTranslateXChange(this);\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n        if (this._isControlInitialized || value !== null) {\n            this.value = value;\n        }\n    }\n    /**\n     * Registers a callback to be invoked when the input's value changes from user input.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChangeFn = fn;\n        this._isControlInitialized = true;\n    }\n    /**\n     * Registers a callback to be invoked when the input is blurred by the user.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouchedFn = fn;\n    }\n    /**\n     * Sets the disabled state of the slider.\n     * @param isDisabled The new disabled state\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    focus() {\n        this._hostElement.focus();\n    }\n    blur() {\n        this._hostElement.blur();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderThumb, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_SLIDER }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSliderThumb, selector: \"input[matSliderThumb]\", inputs: { value: \"value\" }, outputs: { valueChange: \"valueChange\", dragStart: \"dragStart\", dragEnd: \"dragEnd\" }, host: { attributes: { \"type\": \"range\" }, listeners: { \"change\": \"_onChange()\", \"input\": \"_onInput()\", \"blur\": \"_onBlur()\", \"focus\": \"_onFocus()\" }, properties: { \"attr.aria-valuetext\": \"_valuetext\" }, classAttribute: \"mdc-slider__input\" }, providers: [\n            MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n            { provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb },\n        ], exportAs: [\"matSliderThumb\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderThumb, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matSliderThumb]',\n                    exportAs: 'matSliderThumb',\n                    host: {\n                        'class': 'mdc-slider__input',\n                        'type': 'range',\n                        '[attr.aria-valuetext]': '_valuetext',\n                        '(change)': '_onChange()',\n                        '(input)': '_onInput()',\n                        // TODO(wagnermaciel): Consider using a global event listener instead.\n                        // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n                        '(blur)': '_onBlur()',\n                        '(focus)': '_onFocus()',\n                    },\n                    providers: [\n                        MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n                        { provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }]; }, propDecorators: { value: [{\n                type: Input\n            }], valueChange: [{\n                type: Output\n            }], dragStart: [{\n                type: Output\n            }], dragEnd: [{\n                type: Output\n            }] } });\nclass MatSliderRangeThumb extends MatSliderThumb {\n    /** @docs-private */\n    getSibling() {\n        if (!this._sibling) {\n            this._sibling = this._slider._getInput(this._isEndThumb ? 1 /* _MatThumb.START */ : 2 /* _MatThumb.END */);\n        }\n        return this._sibling;\n    }\n    /**\n     * Returns the minimum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMinPos() {\n        const sibling = this.getSibling();\n        if (!this._isLeftThumb && sibling) {\n            return sibling.translateX;\n        }\n        return 0;\n    }\n    /**\n     * Returns the maximum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMaxPos() {\n        const sibling = this.getSibling();\n        if (this._isLeftThumb && sibling) {\n            return sibling.translateX;\n        }\n        return this._slider._cachedWidth;\n    }\n    _setIsLeftThumb() {\n        this._isLeftThumb =\n            (this._isEndThumb && this._slider._isRtl) || (!this._isEndThumb && !this._slider._isRtl);\n    }\n    constructor(_ngZone, _slider, _elementRef, _cdr) {\n        super(_ngZone, _elementRef, _cdr, _slider);\n        this._cdr = _cdr;\n        this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n        this._setIsLeftThumb();\n        this.thumbPosition = this._isEndThumb ? 2 /* _MatThumb.END */ : 1 /* _MatThumb.START */;\n    }\n    _getDefaultValue() {\n        return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n    }\n    _onInput() {\n        super._onInput();\n        this._updateSibling();\n        if (!this._isActive) {\n            this._updateWidthInactive();\n        }\n    }\n    _onNgControlValueChange() {\n        super._onNgControlValueChange();\n        this.getSibling()?._updateMinMax();\n    }\n    _onPointerDown(event) {\n        if (this.disabled || event.button !== 0) {\n            return;\n        }\n        if (this._sibling) {\n            this._sibling._updateWidthActive();\n            this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n        }\n        super._onPointerDown(event);\n    }\n    _onPointerUp() {\n        super._onPointerUp();\n        if (this._sibling) {\n            setTimeout(() => {\n                this._sibling._updateWidthInactive();\n                this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n            });\n        }\n    }\n    _onPointerMove(event) {\n        super._onPointerMove(event);\n        if (!this._slider.step && this._isActive) {\n            this._updateSibling();\n        }\n    }\n    _fixValue(event) {\n        super._fixValue(event);\n        this._sibling?._updateMinMax();\n    }\n    _clamp(v) {\n        return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n    }\n    _updateMinMax() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        if (this._isEndThumb) {\n            this.min = Math.max(this._slider.min, sibling.value);\n            this.max = this._slider.max;\n        }\n        else {\n            this.min = this._slider.min;\n            this.max = Math.min(this._slider.max, sibling.value);\n        }\n    }\n    _updateWidthActive() {\n        const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n        const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth;\n        const percentage = this._slider.min < this._slider.max\n            ? (this.max - this.min) / (this._slider.max - this._slider.min)\n            : 1;\n        const width = maxWidth * percentage + minWidth;\n        this._hostElement.style.width = `${width}px`;\n        this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    }\n    _updateWidthInactive() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        const maxWidth = this._slider._cachedWidth;\n        const midValue = this._isEndThumb\n            ? this.value - (this.value - sibling.value) / 2\n            : this.value + (sibling.value - this.value) / 2;\n        const _percentage = this._isEndThumb\n            ? (this.max - midValue) / (this._slider.max - this._slider.min)\n            : (midValue - this.min) / (this._slider.max - this._slider.min);\n        const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n        // Extend the native input width by the radius of the ripple\n        let ripplePadding = this._slider._rippleRadius;\n        // If one of the inputs is maximally sized (the value of both thumbs is\n        // equal to the min or max), make that input take up all of the width and\n        // make the other unselectable.\n        if (percentage === 1) {\n            ripplePadding = 48;\n        }\n        else if (percentage === 0) {\n            ripplePadding = 0;\n        }\n        const width = maxWidth * percentage + ripplePadding;\n        this._hostElement.style.width = `${width}px`;\n        this._hostElement.style.padding = '0px';\n        if (this._isLeftThumb) {\n            this._hostElement.style.left = '-24px';\n            this._hostElement.style.right = 'auto';\n        }\n        else {\n            this._hostElement.style.left = 'auto';\n            this._hostElement.style.right = '-24px';\n        }\n    }\n    _updateStaticStyles() {\n        this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n    }\n    _updateSibling() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        sibling._updateMinMax();\n        if (this._isActive) {\n            sibling._updateWidthActive();\n        }\n        else {\n            sibling._updateWidthInactive();\n        }\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n        if (this._isControlInitialized || value !== null) {\n            this.value = value;\n            this._updateWidthInactive();\n            this._updateSibling();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderRangeThumb, deps: [{ token: i0.NgZone }, { token: MAT_SLIDER }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatSliderRangeThumb, selector: \"input[matSliderStartThumb], input[matSliderEndThumb]\", providers: [\n            MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n            { provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb },\n        ], exportAs: [\"matSliderRangeThumb\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderRangeThumb, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n                    exportAs: 'matSliderRangeThumb',\n                    providers: [\n                        MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n                        { provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb },\n                    ],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; } });\n\nclass MatSliderModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderModule, declarations: [MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb], imports: [MatCommonModule, CommonModule, MatRippleModule], exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderModule, imports: [MatCommonModule, CommonModule, MatRippleModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatSliderModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule, MatRippleModule],\n                    exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb],\n                    declarations: [MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACrP,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,SAAS,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC/I,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AALA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,oCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAwNoGjC,EAAE,CAAAmC,cAAA,eACquB,CAAC,YAAD,CAAC,aAAD,CAAC;IADxuBnC,EAAE,CAAAoC,MAAA,EACi2B,CAAC;IADp2BpC,EAAE,CAAAqC,YAAA,CACw2B,CAAC,CAAD,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAD32BtC,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAAwC,SAAA,EACi2B,CAAC;IADp2BxC,EAAE,CAAAyC,iBAAA,CAAAH,MAAA,CAAAI,kBACi2B,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,8CAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IADp2BjC,EAAE,CAAA6C,SAAA,SAyqByoD,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAa,WAAA,GAAAZ,GAAA,CAAAa,SAAA;IAAA,MAAAC,IAAA,GAAAd,GAAA,CAAAe,KAAA;IAAA,MAAAC,MAAA,GAzqB5oDlD,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAAmD,UAAA,CAAAL,WAAA,4EAyqBukD,CAAC;IAzqB1kD9C,EAAE,CAAAoD,WAAA,cAAAF,MAAA,CAAAG,sBAAA,CAAAL,IAAA,CAyqBkoD,CAAC;EAAA;AAAA;AAAA,SAAAM,wCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzqBroDjC,EAAE,CAAAuD,uBAAA,EAyqB64C,CAAC;IAzqBh5CvD,EAAE,CAAAwD,UAAA,IAAAZ,6CAAA,iBAyqByoD,CAAC;IAzqB5oD5C,EAAE,CAAAyD,qBAAA,CAyqB8pD,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAyB,MAAA,GAzqBjqD1D,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAAwC,SAAA,EAyqB48C,CAAC;IAzqB/8CxC,EAAE,CAAA2D,UAAA,YAAAD,MAAA,CAAAE,UAyqB48C,CAAC;EAAA;AAAA;AAAA,SAAAC,yBAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzqB/8CjC,EAAE,CAAAmC,cAAA,eAyqBk2C,CAAC;IAzqBr2CnC,EAAE,CAAAwD,UAAA,IAAAF,uCAAA,0BAyqB8pD,CAAC;IAzqBjqDtD,EAAE,CAAAqC,YAAA,CAyqBwqD,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA6B,MAAA,GAzqB3qD9D,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAAwC,SAAA,EAyqB04C,CAAC;IAzqB74CxC,EAAE,CAAA2D,UAAA,SAAAG,MAAA,CAAAC,YAyqB04C,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzqB74CjC,EAAE,CAAA6C,SAAA,gCAyqBw3D,CAAC;EAAA;EAAA,IAAAZ,EAAA;IAAA,MAAAgC,MAAA,GAzqB33DjE,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAA2D,UAAA,aAAAM,MAAA,CAAAC,QAyqB8wD,CAAC,mBAAD,CAAC,uBAAAD,MAAA,CAAAE,uBAAD,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AA33Br3D,MAAMC,UAAU,GAAG,IAAIpE,cAAc,CAAC,YAAY,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,MAAMqE,gBAAgB,GAAG,IAAIrE,cAAc,CAAC,iBAAiB,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,MAAMsE,sBAAsB,GAAG,IAAItE,cAAc,CAAC,sBAAsB,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,MAAMuE,uBAAuB,GAAG,IAAIvE,cAAc,CAAC,uBAAuB,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA,MAAMwE,eAAe,CAAC;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAC7C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,cAAc,GAAIC,KAAK,IAAK;MAC7B,IAAI,IAAI,CAACC,YAAY,CAACC,UAAU,EAAE;QAC9B;MACJ;MACA,MAAMC,IAAI,GAAG,IAAI,CAACC,YAAY,CAACC,qBAAqB,CAAC,CAAC;MACtD,MAAMC,SAAS,GAAG,IAAI,CAACX,OAAO,CAACY,sBAAsB,CAACP,KAAK,EAAEG,IAAI,CAAC;MAClE,IAAI,CAACP,UAAU,GAAGU,SAAS;MAC3B,IAAIA,SAAS,EAAE;QACX,IAAI,CAACE,gBAAgB,CAAC,CAAC;MAC3B,CAAC,MACI;QACD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;MAC1C;IACJ,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAACf,UAAU,GAAG,KAAK;MACvB,IAAI,CAACa,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;IAC1C,CAAC;IACD,IAAI,CAACE,QAAQ,GAAG,MAAM;MAClB;MACA;MACA,IAAI,CAACH,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;MACtC,IAAI,CAACG,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACT,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACjE,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB;MACA,IAAI,CAAC,IAAI,CAACnB,SAAS,EAAE;QACjB,IAAI,CAACY,WAAW,CAAC,IAAI,CAACQ,eAAe,CAAC;MAC1C;MACA;MACA,IAAI,IAAI,CAACrB,UAAU,EAAE;QACjB,IAAI,CAACY,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACJ,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,4BAA4B,CAAC;IACpE,CAAC;IACD,IAAI,CAACC,YAAY,GAAInB,KAAK,IAAK;MAC3B,IAAIA,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;QACpB;MACJ;MACA,IAAI,CAACvB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACwB,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAACC,UAAU,GAAG,MAAM;MACpB,IAAI,CAACzB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACY,WAAW,CAAC,IAAI,CAACc,gBAAgB,CAAC;MACvC;MACA,IAAI,CAAC,IAAI,CAACtB,YAAY,CAACC,UAAU,EAAE;QAC/B,IAAI,CAACO,WAAW,CAAC,IAAI,CAACQ,eAAe,CAAC;MAC1C;IACJ,CAAC;IACD,IAAI,CAACb,YAAY,GAAGV,WAAW,CAAC8B,aAAa;EACjD;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAACC,MAAM,GAAG,EAAE;IACxB,IAAI,CAAC1B,YAAY,GAAG,IAAI,CAACN,OAAO,CAACiC,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC;IAC9D,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC7B,YAAY,CAACG,YAAY;IACpD,MAAM2B,KAAK,GAAG,IAAI,CAACD,cAAc;IACjC;IACA;IACA,IAAI,CAACrC,OAAO,CAACuC,iBAAiB,CAAC,MAAM;MACjCD,KAAK,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAClC,cAAc,CAAC;MAC1DgC,KAAK,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACd,YAAY,CAAC;MACxDY,KAAK,CAACE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACX,UAAU,CAAC;MACpDS,KAAK,CAACE,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACtB,aAAa,CAAC;MAC1DoB,KAAK,CAACE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrB,QAAQ,CAAC;MAC9CmB,KAAK,CAACE,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACjB,OAAO,CAAC;IAChD,CAAC,CAAC;EACN;EACAkB,WAAWA,CAAA,EAAG;IACV,MAAMH,KAAK,GAAG,IAAI,CAACD,cAAc;IACjCC,KAAK,CAACI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACpC,cAAc,CAAC;IAC7DgC,KAAK,CAACI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAChB,YAAY,CAAC;IAC3DY,KAAK,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACb,UAAU,CAAC;IACvDS,KAAK,CAACI,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACxB,aAAa,CAAC;IAC7DoB,KAAK,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACvB,QAAQ,CAAC;IACjDmB,KAAK,CAACI,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACnB,OAAO,CAAC;EACnD;EACA;EACAR,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC4B,gBAAgB,CAAC,IAAI,CAAC1B,eAAe,CAAC,EAAE;MAC9C,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC2B,WAAW,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,CAAC;MAC9E,IAAI,CAAC7B,eAAe,EAAE8B,OAAO,CAAC1B,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC9E;EACJ;EACA;EACAF,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAAC,IAAI,CAACuB,gBAAgB,CAAC,IAAI,CAACnB,eAAe,CAAC,EAAE;MAC9C,IAAI,CAACA,eAAe,GAAG,IAAI,CAACoB,WAAW,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC;MACpF,IAAI,CAACtB,eAAe,EAAEuB,OAAO,CAAC1B,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC9E;EACJ;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACe,gBAAgB,CAAC,IAAI,CAACb,gBAAgB,CAAC,EAAE;MAC/C,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACc,WAAW,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAI,CAAC,CAAC;MACnF,IAAI,CAAChB,gBAAgB,EAAEiB,OAAO,CAAC1B,SAAS,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAChF;EACJ;EACA;EACAqB,gBAAgBA,CAACK,SAAS,EAAE;IACxB,OAAOA,SAAS,EAAEC,KAAK,KAAK,CAAC,CAAC,+BAA+BD,SAAS,EAAEC,KAAK,KAAK,CAAC,CAAC;EACxF;EACA;EACAL,WAAWA,CAACM,SAAS,EAAEC,wBAAwB,EAAE;IAC7C,IAAI,IAAI,CAACjD,OAAO,CAACkD,QAAQ,EAAE;MACvB;IACJ;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACnD,OAAO,CAACoD,QAAQ,EAAE;MACvB,MAAMC,OAAO,GAAG,IAAI,CAACrD,OAAO,CAACsD,SAAS,CAAC,IAAI,CAACpB,aAAa,KAAK,CAAC,CAAC,wBAAwB,CAAC,CAAC,sBAAsB,CAAC,CAAC,qBAAqB,CAAC;MACxImB,OAAO,CAACF,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACnD,OAAO,CAACuD,oBAAoB,EAAEL,QAAQ,IAAI,CAACD,wBAAwB,EAAE;MAC1E;IACJ;IACA,OAAO,IAAI,CAAClB,OAAO,CAACyB,MAAM,CAAC;MACvBR,SAAS,EAAE,IAAI,CAAChD,OAAO,CAACyD,eAAe,GAAG;QAAEd,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,GAAGI,SAAS;MAC3FU,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI7C,WAAWA,CAACgC,SAAS,EAAE;IACnBA,SAAS,EAAEc,OAAO,CAAC,CAAC;IACpB,IAAI,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAC5B;IACJ;IACA,IAAI,CAAC,IAAI,CAAC7D,OAAO,CAACoD,QAAQ,EAAE;MACxB,IAAI,CAACU,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMT,OAAO,GAAG,IAAI,CAACU,WAAW,CAAC,CAAC;IAClC,IAAI,CAACV,OAAO,CAACQ,mBAAmB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1BT,OAAO,CAACS,mBAAmB,CAAC,CAAC;IACjC;EACJ;EACA;EACAX,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC1C,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;EACxE;EACA;EACA0C,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACrD,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,mCAAmC,CAAC;EAC3E;EACAwC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC/D,OAAO,CAACsD,SAAS,CAAC,IAAI,CAACpB,aAAa,KAAK,CAAC,CAAC,wBAAwB,CAAC,CAAC,sBAAsB,CAAC,CAAC,qBAAqB,CAAC;EACnI;EACA;EACA8B,2BAA2BA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACC,wBAAwB,EAAEpC,aAAa;EACvD;EACA;EACAqC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACtC,aAAa;EACnC;EACAgC,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACpB,gBAAgB,CAAC,IAAI,CAAC1B,eAAe,CAAC,IAC/C,IAAI,CAAC0B,gBAAgB,CAAC,IAAI,CAACnB,eAAe,CAAC,IAC3C,IAAI,CAACmB,gBAAgB,CAAC,IAAI,CAACb,gBAAgB,CAAC;EACpD;EAAC,QAAAwC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5E,oBAAoB,EAA9B1E,EAAE,CAAAuJ,iBAAA,CAA8CvJ,EAAE,CAACwJ,iBAAiB,GAApExJ,EAAE,CAAAuJ,iBAAA,CAA+EvJ,EAAE,CAACyJ,MAAM,GAA1FzJ,EAAE,CAAAuJ,iBAAA,CAAqGvJ,EAAE,CAAC0J,UAAU,GAApH1J,EAAE,CAAAuJ,iBAAA,CAA+HlF,UAAU;EAAA,CAA4C;EAAA,QAAAsF,EAAA,GAC9Q,IAAI,CAACC,IAAI,kBAD8E5J,EAAE,CAAA6J,iBAAA;IAAAC,IAAA,EACJpF,oBAAoB;IAAAqF,SAAA;IAAAC,SAAA,WAAAC,2BAAAhI,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADlBjC,EAAE,CAAAkK,WAAA,CACqY/I,SAAS;QADhZnB,EAAE,CAAAkK,WAAA,CAAApI,GAAA;QAAF9B,EAAE,CAAAkK,WAAA,CAAAnI,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAkI,EAAA;QAAFnK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAA4E,OAAA,GAAAqD,EAAA,CAAAG,KAAA;QAAFtK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAAgH,KAAA,GAAAiB,EAAA,CAAAG,KAAA;QAAFtK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAA8G,wBAAA,GAAAmB,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAtG,QAAA;MAAA+C,aAAA;MAAAvE,kBAAA;IAAA;IAAA+H,QAAA,GAAFzK,EAAE,CAAA0K,kBAAA,CACyP,CAAC;MAAEC,OAAO,EAAEnG,uBAAuB;MAAEoG,WAAW,EAAElG;IAAqB,CAAC,CAAC;IAAAmG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAhJ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADpUjC,EAAE,CAAAwD,UAAA,IAAAxB,mCAAA,gBAC03B,CAAC;QAD73BhC,EAAE,CAAA6C,SAAA,eAC86B,CAAC,YAAD,CAAC;MAAA;MAAA,IAAAZ,EAAA;QADj7BjC,EAAE,CAAA2D,UAAA,SAAAzB,GAAA,CAAAgC,QACysB,CAAC;QAD5sBlE,EAAE,CAAAwC,SAAA,EAC6/B,CAAC;QADhgCxC,EAAE,CAAA2D,UAAA,0BAC6/B,CAAC;MAAA;IAAA;IAAAuH,YAAA,GAA6WxJ,EAAE,CAACyJ,IAAI,EAA6FjK,IAAI,CAACC,SAAS;IAAAiK,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA,EAA6T;AACh4D;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvL,EAAE,CAAAwL,iBAAA,CAGX9G,oBAAoB,EAAc,CAAC;IAClHoF,IAAI,EAAE5J,SAAS;IACfuL,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEC,IAAI,EAAE;QACxC,OAAO,EAAE;MACb,CAAC;MAAEL,eAAe,EAAEnL,uBAAuB,CAACyL,MAAM;MAAEP,aAAa,EAAEjL,iBAAiB,CAACyL,IAAI;MAAEC,SAAS,EAAE,CAAC;QAAEnB,OAAO,EAAEnG,uBAAuB;QAAEoG,WAAW,EAAElG;MAAqB,CAAC,CAAC;MAAEsG,QAAQ,EAAE,oYAAoY;MAAEI,MAAM,EAAE,CAAC,2SAA2S;IAAE,CAAC;EACp4B,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtB,IAAI,EAAE9J,EAAE,CAACwJ;IAAkB,CAAC,EAAE;MAAEM,IAAI,EAAE9J,EAAE,CAACyJ;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAE9J,EAAE,CAAC0J;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAEiC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5IlC,IAAI,EAAEzJ,MAAM;QACZoL,IAAI,EAAE,CAACpH,UAAU;MACrB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEH,QAAQ,EAAE,CAAC;MACvC4F,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE2G,aAAa,EAAE,CAAC;MAChB6C,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEoC,kBAAkB,EAAE,CAAC;MACrBoH,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEwG,OAAO,EAAE,CAAC;MACVgD,IAAI,EAAEvJ,SAAS;MACfkL,IAAI,EAAE,CAACtK,SAAS;IACpB,CAAC,CAAC;IAAE+H,KAAK,EAAE,CAAC;MACRY,IAAI,EAAEvJ,SAAS;MACfkL,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAEzC,wBAAwB,EAAE,CAAC;MAC3Bc,IAAI,EAAEvJ,SAAS;MACfkL,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMQ,mBAAmB,GAAG7K,UAAU,CAACC,kBAAkB,CAAC,MAAM;EAC5DsD,WAAWA,CAACG,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC,EAAE,SAAS,CAAC;AACd;AACA;AACA;AACA;AACA,MAAMoH,SAAS,SAASD,mBAAmB,CAAC;EACxC;EACA,IAAIhE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkE,SAAS;EACzB;EACA,IAAIlE,QAAQA,CAACmE,CAAC,EAAE;IACZ,IAAI,CAACD,SAAS,GAAGtM,qBAAqB,CAACuM,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,MAAMsF,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,IAAIqF,QAAQ,EAAE;MACVA,QAAQ,CAACpE,QAAQ,GAAG,IAAI,CAACkE,SAAS;IACtC;IACA,IAAIG,UAAU,EAAE;MACZA,UAAU,CAACrE,QAAQ,GAAG,IAAI,CAACkE,SAAS;IACxC;EACJ;EACA;EACA,IAAIjI,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACqI,SAAS;EACzB;EACA,IAAIrI,QAAQA,CAACkI,CAAC,EAAE;IACZ,IAAI,CAACG,SAAS,GAAG1M,qBAAqB,CAACuM,CAAC,CAAC;IACzC,IAAI,CAACI,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACL,CAAC,EAAE;IACjB,IAAI,CAACM,cAAc,GAAG7M,qBAAqB,CAACuM,CAAC,CAAC;EAClD;EACA;EACA,IAAIO,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,GAAGA,CAACP,CAAC,EAAE;IACP,MAAMO,GAAG,GAAG7M,oBAAoB,CAACsM,CAAC,EAAE,IAAI,CAACQ,IAAI,CAAC;IAC9C,IAAI,IAAI,CAACA,IAAI,KAAKD,GAAG,EAAE;MACnB,IAAI,CAACE,UAAU,CAACF,GAAG,CAAC;IACxB;EACJ;EACAE,UAAUA,CAACF,GAAG,EAAE;IACZ,MAAMG,OAAO,GAAG,IAAI,CAACF,IAAI;IACzB,IAAI,CAACA,IAAI,GAAGD,GAAG;IACf,IAAI,CAACxE,QAAQ,GAAG,IAAI,CAAC4E,eAAe,CAAC;MAAEC,GAAG,EAAEF,OAAO;MAAEG,GAAG,EAAEN;IAAI,CAAC,CAAC,GAAG,IAAI,CAACO,kBAAkB,CAACP,GAAG,CAAC;IAC/F,IAAI,CAACQ,qBAAqB,CAAC,CAAC;EAChC;EACAJ,eAAeA,CAACJ,GAAG,EAAE;IACjB,MAAMN,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,MAAMsF,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,MAAMoG,WAAW,GAAGf,QAAQ,CAACgB,KAAK;IAClC,MAAMC,aAAa,GAAGhB,UAAU,CAACe,KAAK;IACtCf,UAAU,CAACK,GAAG,GAAGA,GAAG,CAACM,GAAG;IACxBZ,QAAQ,CAACM,GAAG,GAAGY,IAAI,CAACC,GAAG,CAACb,GAAG,CAACM,GAAG,EAAEX,UAAU,CAACe,KAAK,CAAC;IAClDf,UAAU,CAACkB,GAAG,GAAGD,IAAI,CAACZ,GAAG,CAACN,QAAQ,CAACmB,GAAG,EAAEnB,QAAQ,CAACgB,KAAK,CAAC;IACvDf,UAAU,CAACmB,oBAAoB,CAAC,CAAC;IACjCpB,QAAQ,CAACoB,oBAAoB,CAAC,CAAC;IAC/Bd,GAAG,CAACM,GAAG,GAAGN,GAAG,CAACK,GAAG,GACX,IAAI,CAACU,+BAA+B,CAACrB,QAAQ,EAAEC,UAAU,CAAC,GAC1D,IAAI,CAACoB,+BAA+B,CAACpB,UAAU,EAAED,QAAQ,CAAC;IAChE,IAAIe,WAAW,KAAKf,QAAQ,CAACgB,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACtB,QAAQ,CAAC;IACjC;IACA,IAAIiB,aAAa,KAAKhB,UAAU,CAACe,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACrB,UAAU,CAAC;IACnC;EACJ;EACAY,kBAAkBA,CAACP,GAAG,EAAE;IACpB,MAAMxF,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnD,IAAIG,KAAK,EAAE;MACP,MAAMyG,QAAQ,GAAGzG,KAAK,CAACkG,KAAK;MAC5BlG,KAAK,CAACwF,GAAG,GAAGA,GAAG;MACfxF,KAAK,CAAC0G,qBAAqB,CAAC,CAAC;MAC7B,IAAI,CAACC,cAAc,CAAC3G,KAAK,CAAC;MAC1B,IAAIyG,QAAQ,KAAKzG,KAAK,CAACkG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAACxG,KAAK,CAAC;MAC9B;IACJ;EACJ;EACA;EACA,IAAIqG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACO,IAAI;EACpB;EACA,IAAIP,GAAGA,CAACpB,CAAC,EAAE;IACP,MAAMoB,GAAG,GAAG1N,oBAAoB,CAACsM,CAAC,EAAE,IAAI,CAAC2B,IAAI,CAAC;IAC9C,IAAI,IAAI,CAACA,IAAI,KAAKP,GAAG,EAAE;MACnB,IAAI,CAACQ,UAAU,CAACR,GAAG,CAAC;IACxB;EACJ;EACAQ,UAAUA,CAACR,GAAG,EAAE;IACZ,MAAMS,OAAO,GAAG,IAAI,CAACF,IAAI;IACzB,IAAI,CAACA,IAAI,GAAGP,GAAG;IACf,IAAI,CAACrF,QAAQ,GAAG,IAAI,CAAC+F,eAAe,CAAC;MAAElB,GAAG,EAAEiB,OAAO;MAAEhB,GAAG,EAAEO;IAAI,CAAC,CAAC,GAAG,IAAI,CAACW,kBAAkB,CAACX,GAAG,CAAC;IAC/F,IAAI,CAACL,qBAAqB,CAAC,CAAC;EAChC;EACAe,eAAeA,CAACV,GAAG,EAAE;IACjB,MAAMnB,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,MAAMsF,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,MAAMoG,WAAW,GAAGf,QAAQ,CAACgB,KAAK;IAClC,MAAMC,aAAa,GAAGhB,UAAU,CAACe,KAAK;IACtChB,QAAQ,CAACmB,GAAG,GAAGA,GAAG,CAACP,GAAG;IACtBX,UAAU,CAACkB,GAAG,GAAGD,IAAI,CAACZ,GAAG,CAACa,GAAG,CAACP,GAAG,EAAEZ,QAAQ,CAACgB,KAAK,CAAC;IAClDhB,QAAQ,CAACM,GAAG,GAAGL,UAAU,CAACe,KAAK;IAC/BhB,QAAQ,CAACoB,oBAAoB,CAAC,CAAC;IAC/BnB,UAAU,CAACmB,oBAAoB,CAAC,CAAC;IACjCD,GAAG,CAACP,GAAG,GAAGO,GAAG,CAACR,GAAG,GACX,IAAI,CAACU,+BAA+B,CAACpB,UAAU,EAAED,QAAQ,CAAC,GAC1D,IAAI,CAACqB,+BAA+B,CAACrB,QAAQ,EAAEC,UAAU,CAAC;IAChE,IAAIc,WAAW,KAAKf,QAAQ,CAACgB,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACtB,QAAQ,CAAC;IACjC;IACA,IAAIiB,aAAa,KAAKhB,UAAU,CAACe,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACrB,UAAU,CAAC;IACnC;EACJ;EACA6B,kBAAkBA,CAACX,GAAG,EAAE;IACpB,MAAMrG,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnD,IAAIG,KAAK,EAAE;MACP,MAAMyG,QAAQ,GAAGzG,KAAK,CAACkG,KAAK;MAC5BlG,KAAK,CAACqG,GAAG,GAAGA,GAAG;MACfrG,KAAK,CAAC0G,qBAAqB,CAAC,CAAC;MAC7B,IAAI,CAACC,cAAc,CAAC3G,KAAK,CAAC;MAC1B,IAAIyG,QAAQ,KAAKzG,KAAK,CAACkG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAACxG,KAAK,CAAC;MAC9B;IACJ;EACJ;EACA;EACA,IAAIiH,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAAChC,CAAC,EAAE;IACR,MAAMgC,IAAI,GAAGtO,oBAAoB,CAACsM,CAAC,EAAE,IAAI,CAACiC,KAAK,CAAC;IAChD,IAAI,IAAI,CAACA,KAAK,KAAKD,IAAI,EAAE;MACrB,IAAI,CAACE,WAAW,CAACF,IAAI,CAAC;IAC1B;EACJ;EACAE,WAAWA,CAACF,IAAI,EAAE;IACd,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACjG,QAAQ,GAAG,IAAI,CAACoG,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACpE,IAAI,CAACrB,qBAAqB,CAAC,CAAC;EAChC;EACAoB,gBAAgBA,CAAA,EAAG;IACf,MAAMlC,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,MAAMsF,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,MAAMoG,WAAW,GAAGf,QAAQ,CAACgB,KAAK;IAClC,MAAMC,aAAa,GAAGhB,UAAU,CAACe,KAAK;IACtC,MAAMoB,cAAc,GAAGnC,UAAU,CAACe,KAAK;IACvChB,QAAQ,CAACM,GAAG,GAAG,IAAI,CAACC,IAAI;IACxBN,UAAU,CAACkB,GAAG,GAAG,IAAI,CAACO,IAAI;IAC1B1B,QAAQ,CAAC+B,IAAI,GAAG,IAAI,CAACC,KAAK;IAC1B/B,UAAU,CAAC8B,IAAI,GAAG,IAAI,CAACC,KAAK;IAC5B,IAAI,IAAI,CAACK,SAAS,CAACC,MAAM,EAAE;MACvBtC,QAAQ,CAACgB,KAAK,GAAGhB,QAAQ,CAACgB,KAAK;MAC/Bf,UAAU,CAACe,KAAK,GAAGf,UAAU,CAACe,KAAK;IACvC;IACAhB,QAAQ,CAACM,GAAG,GAAGY,IAAI,CAACC,GAAG,CAAC,IAAI,CAACZ,IAAI,EAAEN,UAAU,CAACe,KAAK,CAAC;IACpDf,UAAU,CAACkB,GAAG,GAAGD,IAAI,CAACZ,GAAG,CAAC,IAAI,CAACoB,IAAI,EAAE1B,QAAQ,CAACgB,KAAK,CAAC;IACpDf,UAAU,CAACmB,oBAAoB,CAAC,CAAC;IACjCpB,QAAQ,CAACoB,oBAAoB,CAAC,CAAC;IAC/BpB,QAAQ,CAACgB,KAAK,GAAGoB,cAAc,GACzB,IAAI,CAACf,+BAA+B,CAACpB,UAAU,EAAED,QAAQ,CAAC,GAC1D,IAAI,CAACqB,+BAA+B,CAACrB,QAAQ,EAAEC,UAAU,CAAC;IAChE,IAAIc,WAAW,KAAKf,QAAQ,CAACgB,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACtB,QAAQ,CAAC;IACjC;IACA,IAAIiB,aAAa,KAAKhB,UAAU,CAACe,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACrB,UAAU,CAAC;IACnC;EACJ;EACAkC,mBAAmBA,CAAA,EAAG;IAClB,MAAMrH,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnD,IAAIG,KAAK,EAAE;MACP,MAAMyG,QAAQ,GAAGzG,KAAK,CAACkG,KAAK;MAC5BlG,KAAK,CAACiH,IAAI,GAAG,IAAI,CAACC,KAAK;MACvB,IAAI,IAAI,CAACK,SAAS,CAACC,MAAM,EAAE;QACvBxH,KAAK,CAACkG,KAAK,GAAGlG,KAAK,CAACkG,KAAK;MAC7B;MACAlG,KAAK,CAAC0G,qBAAqB,CAAC,CAAC;MAC7B,IAAID,QAAQ,KAAKzG,KAAK,CAACkG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAACxG,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAxC,WAAWA,CAACE,OAAO,EAAED,IAAI,EAAEgK,UAAU,EAAEC,IAAI,EAAEvG,oBAAoB,EAAEwG,aAAa,EAAE;IAC9E,KAAK,CAACF,UAAU,CAAC;IACjB,IAAI,CAAC/J,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiK,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACvG,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAAC6D,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACE,IAAI,GAAG,CAAC;IACb,IAAI,CAACmB,IAAI,GAAG,GAAG;IACf,IAAI,CAACM,KAAK,GAAG,CAAC;IACd;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACU,WAAW,GAAI1B,KAAK,IAAM,GAAEA,KAAM,EAAC;IACxC,IAAI,CAAC2B,aAAa,GAAG,EAAE;IACvB;IACA;IACA,IAAI,CAAC7K,uBAAuB,GAAG,EAAE;IACjC;IACA,IAAI,CAAC8K,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAAC9G,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC+G,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACZ,SAAS,GAAGlO,MAAM,CAACT,QAAQ,CAAC;IACjC;IACA,IAAI,CAACwP,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAChH,eAAe,GAAGsG,aAAa,KAAK,gBAAgB;IACzD,IAAI,CAACW,sBAAsB,GAAG,IAAI,CAACZ,IAAI,CAACa,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;IACnF,IAAI,CAACV,MAAM,GAAG,IAAI,CAACL,IAAI,CAACxB,KAAK,KAAK,KAAK;EAC3C;EACAxG,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC6H,SAAS,CAACmB,SAAS,EAAE;MAC1B,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B;IACA,MAAMC,MAAM,GAAG,IAAI,CAAC/I,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACpD,MAAMgJ,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IACtD,IAAI,CAACmB,QAAQ,GAAG,CAAC,CAAC4H,MAAM,IAAI,CAAC,CAACC,MAAM;IACpC,IAAI,CAACpL,IAAI,CAACqL,aAAa,CAAC,CAAC;IACzB,IAAI,OAAO1E,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C2E,eAAe,CAAC,IAAI,CAAC/H,QAAQ,EAAE,IAAI,CAACnB,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC;IAClH;;IACA,MAAMmJ,KAAK,GAAG,IAAI,CAAC9H,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnD,IAAI,CAAC2G,aAAa,GAAGmB,KAAK,CAACrJ,OAAO,CAACC,MAAM;IACzC,IAAI,CAACqJ,aAAa,GAAG,IAAI,CAACpB,aAAa,GAAG,IAAI,CAACO,WAAW;IAC1D,IAAI,CAACc,YAAY,GAAG,IAAI,CAACd,WAAW;IACpC,IAAI,CAACpH,QAAQ,GACP,IAAI,CAACmI,YAAY,CAACP,MAAM,EAAEC,MAAM,CAAC,GACjC,IAAI,CAACO,eAAe,CAACR,MAAM,CAAC;IAClC,IAAI,CAACjC,cAAc,CAACiC,MAAM,CAAC;IAC3B,IAAI,CAACS,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC9L,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACAM,eAAeA,CAACR,MAAM,EAAE;IACpBA,MAAM,CAACY,SAAS,CAAC,CAAC;IAClBZ,MAAM,CAACa,MAAM,CAAC,CAAC;IACf,IAAI,CAACC,uBAAuB,CAACd,MAAM,CAAC;IACpC,IAAI,CAACZ,mBAAmB,GAAG,IAAI;IAC/BY,MAAM,CAAClC,qBAAqB,CAAC,CAAC;EAClC;EACAyC,YAAYA,CAACP,MAAM,EAAEC,MAAM,EAAE;IACzBD,MAAM,CAACY,SAAS,CAAC,CAAC;IAClBZ,MAAM,CAACa,MAAM,CAAC,CAAC;IACfZ,MAAM,CAACW,SAAS,CAAC,CAAC;IAClBX,MAAM,CAACY,MAAM,CAAC,CAAC;IACfb,MAAM,CAACe,aAAa,CAAC,CAAC;IACtBd,MAAM,CAACc,aAAa,CAAC,CAAC;IACtBf,MAAM,CAACgB,mBAAmB,CAAC,CAAC;IAC5Bf,MAAM,CAACe,mBAAmB,CAAC,CAAC;IAC5B,IAAI,CAACvE,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC2C,mBAAmB,GAAG,IAAI;IAC/BY,MAAM,CAAClC,qBAAqB,CAAC,CAAC;IAC9BmC,MAAM,CAACnC,qBAAqB,CAAC,CAAC;EAClC;EACAvG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmI,sBAAsB,CAACuB,WAAW,CAAC,CAAC;IACzC,IAAI,CAACC,eAAe,EAAEC,UAAU,CAAC,CAAC;IAClC,IAAI,CAACD,eAAe,GAAG,IAAI;EAC/B;EACA;EACArB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACV,MAAM,GAAG,IAAI,CAACL,IAAI,CAACxB,KAAK,KAAK,KAAK;IACvC,IAAI,CAAClF,QAAQ,GAAG,IAAI,CAACgJ,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACtE,IAAI,CAACZ,iBAAiB,CAAC,CAAC;EAC5B;EACAW,iBAAiBA,CAAA,EAAG;IAChB,MAAM9E,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,MAAMsF,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1DqF,QAAQ,CAACgF,eAAe,CAAC,CAAC;IAC1B/E,UAAU,CAAC+E,eAAe,CAAC,CAAC;IAC5BhF,QAAQ,CAACiF,UAAU,GAAGjF,QAAQ,CAACkF,sBAAsB,CAAC,CAAC;IACvDjF,UAAU,CAACgF,UAAU,GAAGhF,UAAU,CAACiF,sBAAsB,CAAC,CAAC;IAC3DlF,QAAQ,CAAC0E,mBAAmB,CAAC,CAAC;IAC9BzE,UAAU,CAACyE,mBAAmB,CAAC,CAAC;IAChC1E,QAAQ,CAACoB,oBAAoB,CAAC,CAAC;IAC/BnB,UAAU,CAACmB,oBAAoB,CAAC,CAAC;IACjCpB,QAAQ,CAACwB,qBAAqB,CAAC,CAAC;IAChCvB,UAAU,CAACuB,qBAAqB,CAAC,CAAC;EACtC;EACAuD,oBAAoBA,CAAA,EAAG;IACnB,MAAMjK,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnDG,KAAK,CAAC0G,qBAAqB,CAAC,CAAC;EACjC;EACA;EACA6C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAOc,cAAc,KAAK,WAAW,IAAI,CAACA,cAAc,EAAE;MAC1D;IACJ;IACA,IAAI,CAAC3M,OAAO,CAACuC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC6J,eAAe,GAAG,IAAIO,cAAc,CAAC,MAAM;QAC5C,IAAI,IAAI,CAACvM,SAAS,CAAC,CAAC,EAAE;UAClB;QACJ;QACA,IAAI,IAAI,CAACqK,YAAY,EAAE;UACnBmC,YAAY,CAAC,IAAI,CAACnC,YAAY,CAAC;QACnC;QACA,IAAI,CAACoC,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACT,eAAe,CAACU,OAAO,CAAC,IAAI,CAAC7M,WAAW,CAAC8B,aAAa,CAAC;IAChE,CAAC,CAAC;EACN;EACA;EACA3B,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACoD,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAACpD,SAAS,IAAI,IAAI,CAACoD,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAACpD,SAAS;EAC/G;EACA2M,SAASA,CAAC3K,aAAa,GAAG,CAAC,CAAC,qBAAqB;IAC7C,MAAME,KAAK,GAAG,IAAI,CAACH,SAAS,CAACC,aAAa,CAAC;IAC3C,IAAI,CAACE,KAAK,EAAE;MACR,OAAO,IAAI,CAACwF,GAAG;IACnB;IACA,OAAOxF,KAAK,CAACkG,KAAK;EACtB;EACAwE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,EAAE,IAAI,CAAC7K,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC,EAAE8K,aAAa,IAAI,IAAI,CAAC9K,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC,EAAE8K,aAAa,CAAC;EAC7H;EACA;EACAhC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC/L,YAAY,GAAG,IAAI,CAACe,WAAW,CAAC8B,aAAa,CAACmL,WAAW;IAC9D,IAAI,CAACC,WAAW,GAAG,IAAI,CAAClN,WAAW,CAAC8B,aAAa,CAACnB,qBAAqB,CAAC,CAAC,CAACwM,IAAI;EAClF;EACA;EACAC,qBAAqBA,CAAC9G,MAAM,EAAE;IAC1B,MAAM+G,UAAU,GAAG,IAAI,CAACC,YAAY,CAACxL,aAAa,CAACyL,KAAK;IACxDF,UAAU,CAACF,IAAI,GAAG7G,MAAM,CAAC6G,IAAI;IAC7BE,UAAU,CAACG,KAAK,GAAGlH,MAAM,CAACkH,KAAK;IAC/BH,UAAU,CAACI,eAAe,GAAGnH,MAAM,CAACmH,eAAe;IACnDJ,UAAU,CAACK,SAAS,GAAGpH,MAAM,CAACoH,SAAS;EAC3C;EACA;EACAnP,sBAAsBA,CAACJ,KAAK,EAAE;IAC1B;IACA,MAAMqO,UAAU,GAAGrO,KAAK,IAAI,IAAI,CAACmM,mBAAmB,IAAI,IAAI,CAACxL,UAAU,CAAC6O,MAAM,GAAG,CAAC,CAAC,CAAC;IACpF,OAAQ,cAAanB,UAAW,IAAG;EACvC;EACA;EACAoB,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,CAAC,IAAI,CAACxD,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACyD,cAAc,CAACD,MAAM,CAAC;IAC3B,IAAI,CAAC7E,cAAc,CAAC6E,MAAM,CAAC;IAC3B,IAAI,CAACE,yBAAyB,CAACF,MAAM,CAAC;EAC1C;EACAjF,+BAA+BA,CAACoF,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAAC,IAAI,CAAC5D,mBAAmB,EAAE;MAC3B;IACJ;IACA2D,MAAM,CAACjF,qBAAqB,CAAC,CAAC;IAC9BkF,MAAM,CAAClF,qBAAqB,CAAC,CAAC;EAClC;EACAF,cAAcA,CAACgF,MAAM,EAAE;IACnB,IAAI,CAAC,IAAI,CAACxD,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAAC0B,uBAAuB,CAAC8B,MAAM,CAAC;IACpC,IAAI,CAACnC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC5L,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACA9C,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACgC,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACqB,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC7L,IAAI,CAACoO,YAAY,CAAC,CAAC;EAC5B;EACAtB,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACvC,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACW,iBAAiB,CAAC,CAAC;IACxB,IAAI,IAAI,CAAC3H,QAAQ,EAAE;MACf,MAAM4H,MAAM,GAAG,IAAI,CAAC/I,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;MACpD,MAAMgJ,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;MACtD+I,MAAM,CAAClC,qBAAqB,CAAC,CAAC;MAC9BmC,MAAM,CAACnC,qBAAqB,CAAC,CAAC;MAC9BkC,MAAM,CAACgB,mBAAmB,CAAC,CAAC;MAC5Bf,MAAM,CAACe,mBAAmB,CAAC,CAAC;MAC5BhB,MAAM,CAACe,aAAa,CAAC,CAAC;MACtBd,MAAM,CAACc,aAAa,CAAC,CAAC;MACtBf,MAAM,CAACtC,oBAAoB,CAAC,CAAC;MAC7BuC,MAAM,CAACvC,oBAAoB,CAAC,CAAC;IACjC,CAAC,MACI;MACD,MAAMsC,MAAM,GAAG,IAAI,CAAC/I,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;MACpD,IAAI+I,MAAM,EAAE;QACRA,MAAM,CAAClC,qBAAqB,CAAC,CAAC;MAClC;IACJ;IACA,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC7L,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACA;EACAgD,qBAAqBA,CAAA,EAAG;IACpB,MAAM3G,UAAU,GAAG,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,MAAMqF,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACtD,IAAI,CAACsF,UAAU,IAAI,CAACD,QAAQ,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,OAAOA,QAAQ,CAACiF,UAAU,GAAGhF,UAAU,CAACgF,UAAU,GAAG,EAAE;EAC3D;EACA;AACJ;AACA;AACA;EACI4B,iCAAiCA,CAACP,MAAM,EAAE;IACtC,MAAMvK,OAAO,GAAGuK,MAAM,CAACQ,UAAU,CAAC,CAAC;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAC/K,SAAS,CAACsK,MAAM,CAAC1L,aAAa,CAAC;IACxD,MAAMoM,YAAY,GAAG,IAAI,CAAChL,SAAS,CAACD,OAAO,CAACnB,aAAa,CAAC;IAC1DoM,YAAY,CAAC7N,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,wBAAwB,CAAC;IACpE8M,WAAW,CAAC5N,YAAY,CAACU,SAAS,CAACoN,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC9D,cAAc,CAAC;EAC5F;EACA;EACAqD,yBAAyBA,CAACF,MAAM,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACxK,QAAQ,IAAI,IAAI,CAAC0J,WAAW,CAAC,CAAC,EAAE;MACtC;IACJ;IACA,IAAI,IAAI,CAACrC,cAAc,KAAK,IAAI,CAACyD,qBAAqB,CAAC,CAAC,EAAE;MACtD,IAAI,CAACzD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;MAC1C,IAAI,CAAC0D,iCAAiC,CAACP,MAAM,CAAC;IAClD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,cAAcA,CAACD,MAAM,EAAE;IACnB,IAAI,IAAI,CAACd,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,MAAM1B,KAAK,GAAG,IAAI,CAAC9H,SAAS,CAACsK,MAAM,CAAC1L,aAAa,KAAK,CAAC,CAAC,sBAAsB,CAAC,CAAC,sBAAsB,CAAC,CAAC,qBAAqB,CAAC;IAC9HkJ,KAAK,CAAC3K,YAAY,CAAC6M,KAAK,CAACG,SAAS,GAAI,cAAaG,MAAM,CAACrB,UAAW,KAAI;EAC7E;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAT,uBAAuBA,CAAC8B,MAAM,EAAE;IAC5B,IAAI,IAAI,CAACd,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,MAAM0B,SAAS,GAAG,IAAI,CAACxE,WAAW,CAAC4D,MAAM,CAACtF,KAAK,CAAC;IAChD,IAAI,CAAC8B,mBAAmB,GACjBwD,MAAM,CAACa,UAAU,GAAGD,SAAS,GAC9BZ,MAAM,CAACnN,YAAY,CAACiO,YAAY,CAAC,gBAAgB,EAAEF,SAAS,CAAC;IACnE,IAAI,IAAI,CAACrP,QAAQ,EAAE;MACfyO,MAAM,CAAC1L,aAAa,KAAK,CAAC,CAAC,wBACpB,IAAI,CAAC9C,uBAAuB,GAAGoP,SAAS,GACxC,IAAI,CAACtE,qBAAqB,GAAGsE,SAAU;MAC9C,MAAMG,WAAW,GAAG,IAAI,CAACrL,SAAS,CAACsK,MAAM,CAAC1L,aAAa,CAAC;MACxDsM,SAAS,CAACd,MAAM,GAAG,CAAC,GACdiB,WAAW,CAAClO,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC,GACxEuN,WAAW,CAAClO,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,gCAAgC,CAAC;IACrF;EACJ;EACA;EACAkG,wBAAwBA,CAAA,EAAG;IACvB,MAAMuD,MAAM,GAAG,IAAI,CAAC/I,SAAS,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACpD,MAAMgJ,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IACtD,IAAI+I,MAAM,EAAE;MACR,IAAI,CAACc,uBAAuB,CAACd,MAAM,CAAC;IACxC;IACA,IAAIC,MAAM,EAAE;MACR,IAAI,CAACa,uBAAuB,CAACb,MAAM,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAS,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAAChE,aAAa,IAAI,IAAI,CAACoF,WAAW,CAAC,CAAC,EAAE;MAC3C;IACJ;IACA,MAAMzD,IAAI,GAAG,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;IAC1D,MAAMsF,QAAQ,GAAGpG,IAAI,CAACqG,KAAK,CAAC,IAAI,CAACpG,GAAG,GAAGY,IAAI,CAAC,GAAGA,IAAI;IACnD,MAAMyF,UAAU,GAAG,CAACF,QAAQ,GAAG,IAAI,CAAChH,GAAG,KAAK,IAAI,CAACa,GAAG,GAAG,IAAI,CAACb,GAAG,CAAC;IAChE,IAAI,CAACyC,mBAAmB,GAAG,IAAI,CAACrL,YAAY,GAAG8P,UAAU,GAAG,CAAC;EACjE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA/F,cAAcA,CAAC6E,MAAM,EAAE;IACnB,IAAI,IAAI,CAACd,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,IAAI,CAAC1J,QAAQ,GACP,IAAI,CAAC2L,mBAAmB,CAACnB,MAAM,CAAC,GAChC,IAAI,CAACoB,sBAAsB,CAACpB,MAAM,CAAC;EAC7C;EACAmB,mBAAmBA,CAACnB,MAAM,EAAE;IACxB,MAAMvK,OAAO,GAAGuK,MAAM,CAACQ,UAAU,CAAC,CAAC;IACnC,IAAI,CAAC/K,OAAO,IAAI,CAAC,IAAI,CAACrE,YAAY,EAAE;MAChC;IACJ;IACA,MAAMiQ,gBAAgB,GAAGzG,IAAI,CAAC0G,GAAG,CAAC7L,OAAO,CAACkJ,UAAU,GAAGqB,MAAM,CAACrB,UAAU,CAAC,GAAG,IAAI,CAACvN,YAAY;IAC7F,IAAI4O,MAAM,CAACuB,YAAY,IAAI,IAAI,CAACnQ,YAAY,EAAE;MAC1C,IAAI,CAACmO,qBAAqB,CAAC;QACvBD,IAAI,EAAE,MAAM;QACZK,KAAK,EAAG,GAAE,IAAI,CAACvO,YAAY,GAAGqE,OAAO,CAACkJ,UAAW,IAAG;QACpDiB,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAG,UAASwB,gBAAiB;MAC1C,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAC9B,qBAAqB,CAAC;QACvBD,IAAI,EAAG,GAAE7J,OAAO,CAACkJ,UAAW,IAAG;QAC/BgB,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAG,UAASwB,gBAAiB;MAC1C,CAAC,CAAC;IACN;EACJ;EACAD,sBAAsBA,CAACpB,MAAM,EAAE;IAC3B,IAAI,CAACzD,MAAM,GACL,IAAI,CAACgD,qBAAqB,CAAC;MACzBD,IAAI,EAAE,MAAM;MACZK,KAAK,EAAE,KAAK;MACZC,eAAe,EAAE,OAAO;MACxBC,SAAS,EAAG,UAAS,CAAC,GAAGG,MAAM,CAACwB,cAAe;IACnD,CAAC,CAAC,GACA,IAAI,CAACjC,qBAAqB,CAAC;MACzBD,IAAI,EAAE,KAAK;MACXK,KAAK,EAAE,MAAM;MACbC,eAAe,EAAE,MAAM;MACvBC,SAAS,EAAG,UAASG,MAAM,CAACwB,cAAe;IAC/C,CAAC,CAAC;EACV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA3D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC/D,aAAa,IACnB,IAAI,CAAC2B,IAAI,KAAKrC,SAAS,IACvB,IAAI,CAACY,GAAG,KAAKZ,SAAS,IACtB,IAAI,CAACyB,GAAG,KAAKzB,SAAS,EAAE;MACxB;IACJ;IACA,MAAMqC,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC;IAC1C,IAAI,CAACjG,QAAQ,GAAG,IAAI,CAACiM,sBAAsB,CAAChG,IAAI,CAAC,GAAG,IAAI,CAACiG,yBAAyB,CAACjG,IAAI,CAAC;IACxF,IAAI,IAAI,CAACc,MAAM,EAAE;MACb,IAAI,CAACtL,UAAU,CAAC0Q,OAAO,CAAC,CAAC;IAC7B;EACJ;EACAD,yBAAyBA,CAACjG,IAAI,EAAE;IAC5B,MAAMf,KAAK,GAAG,IAAI,CAACuE,SAAS,CAAC,CAAC;IAC9B,IAAI2C,SAAS,GAAGhH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACiH,KAAK,CAAC,CAACnH,KAAK,GAAG,IAAI,CAACV,GAAG,IAAIyB,IAAI,CAAC,EAAE,CAAC,CAAC;IAClE,IAAIqG,WAAW,GAAGlH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACiH,KAAK,CAAC,CAAC,IAAI,CAAChH,GAAG,GAAGH,KAAK,IAAIe,IAAI,CAAC,EAAE,CAAC,CAAC;IACpE,IAAI,CAACc,MAAM,GAAGqF,SAAS,EAAE,GAAGE,WAAW,EAAE;IACzC,IAAI,CAAC7Q,UAAU,GAAG8Q,KAAK,CAACH,SAAS,CAAC,CAC7BI,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,CACjCC,MAAM,CAACF,KAAK,CAACD,WAAW,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACvE;;EACAP,sBAAsBA,CAAChG,IAAI,EAAE;IACzB,MAAMyG,QAAQ,GAAG,IAAI,CAACjD,SAAS,CAAC,CAAC;IACjC,MAAMkD,UAAU,GAAG,IAAI,CAAClD,SAAS,CAAC,CAAC,CAAC,qBAAqB,CAAC;IAC1D,MAAMmD,2BAA2B,GAAGxH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAACkB,UAAU,GAAG,IAAI,CAACnI,GAAG,IAAIyB,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3F,MAAMmG,SAAS,GAAGhH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAACiB,QAAQ,GAAGC,UAAU,IAAI1G,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC7E,MAAM4G,wBAAwB,GAAGzH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAAC,IAAI,CAACpG,GAAG,GAAGqH,QAAQ,IAAIzG,IAAI,CAAC,EAAE,CAAC,CAAC;IACtF,IAAI,CAACxK,UAAU,GAAG8Q,KAAK,CAACK,2BAA2B,CAAC,CAC/CJ,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC,CACnCC,MAAM,CAACF,KAAK,CAACH,SAAS,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAED,KAAK,CAACM,wBAAwB,CAAC,CAACL,IAAI,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC;EACxI;EACA;EACA3N,SAASA,CAACC,aAAa,EAAE;IACrB,IAAIA,aAAa,KAAK,CAAC,CAAC,uBAAuB,IAAI,CAACgO,MAAM,EAAE;MACxD,OAAO,IAAI,CAACA,MAAM;IACtB;IACA,IAAI,IAAI,CAACC,OAAO,EAAEzC,MAAM,EAAE;MACtB,OAAOxL,aAAa,KAAK,CAAC,CAAC,wBAAwB,IAAI,CAACiO,OAAO,CAAC5K,KAAK,GAAG,IAAI,CAAC4K,OAAO,CAACC,IAAI;IAC7F;IACA;EACJ;EACA;EACA9M,SAASA,CAACpB,aAAa,EAAE;IACrB,OAAOA,aAAa,KAAK,CAAC,CAAC,sBAAsB,IAAI,CAACmO,OAAO,EAAED,IAAI,GAAG,IAAI,CAACC,OAAO,EAAE9K,KAAK;EAC7F;EACA+K,cAAcA,CAACC,aAAa,EAAE;IAC1B,IAAI,CAACjG,aAAa,GAAG,CAAC,IAAI,CAACX,SAAS,CAAC6G,GAAG,IAAID,aAAa,IAAI,CAAC,IAAI,CAAC9M,eAAe;IAClF,IAAI,CAAC1D,WAAW,CAAC8B,aAAa,CAACV,SAAS,CAACoN,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAACjE,aAAa,CAAC;EACxG;EACA;EACA1J,sBAAsBA,CAACP,KAAK,EAAEG,IAAI,EAAE;IAChC,MAAMwB,MAAM,GAAGxB,IAAI,CAACiQ,KAAK,GAAG,CAAC;IAC7B,MAAMC,OAAO,GAAGlQ,IAAI,CAACmQ,CAAC,GAAG3O,MAAM;IAC/B,MAAM4O,OAAO,GAAGpQ,IAAI,CAACqQ,CAAC,GAAG7O,MAAM;IAC/B,MAAM8O,EAAE,GAAGzQ,KAAK,CAAC0Q,OAAO,GAAGL,OAAO;IAClC,MAAMM,EAAE,GAAG3Q,KAAK,CAAC4Q,OAAO,GAAGL,OAAO;IAClC,OAAOpI,IAAI,CAAC0I,GAAG,CAACJ,EAAE,EAAE,CAAC,CAAC,GAAGtI,IAAI,CAAC0I,GAAG,CAACF,EAAE,EAAE,CAAC,CAAC,GAAGxI,IAAI,CAAC0I,GAAG,CAAClP,MAAM,EAAE,CAAC,CAAC;EAClE;EAAC,QAAAoC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA8M,kBAAA5M,CAAA;IAAA,YAAAA,CAAA,IAAwF4C,SAAS,EAxqBnBlM,EAAE,CAAAuJ,iBAAA,CAwqBmCvJ,EAAE,CAACyJ,MAAM,GAxqB9CzJ,EAAE,CAAAuJ,iBAAA,CAwqByDvJ,EAAE,CAACwJ,iBAAiB,GAxqB/ExJ,EAAE,CAAAuJ,iBAAA,CAwqB0FvJ,EAAE,CAAC0J,UAAU,GAxqBzG1J,EAAE,CAAAuJ,iBAAA,CAwqBoH3J,EAAE,CAACuW,cAAc,MAxqBvInW,EAAE,CAAAuJ,iBAAA,CAwqBkKjI,yBAAyB,MAxqB7LtB,EAAE,CAAAuJ,iBAAA,CAwqBwN9H,qBAAqB;EAAA,CAA4D;EAAA,QAAAkI,EAAA,GAClY,IAAI,CAACC,IAAI,kBAzqB8E5J,EAAE,CAAA6J,iBAAA;IAAAC,IAAA,EAyqBJoC,SAAS;IAAAnC,SAAA;IAAAqM,cAAA,WAAAC,yBAAApU,EAAA,EAAAC,GAAA,EAAAoU,QAAA;MAAA,IAAArU,EAAA;QAzqBPjC,EAAE,CAAAuW,cAAA,CAAAD,QAAA,EAyqB0oBhS,gBAAgB;QAzqB5pBtE,EAAE,CAAAuW,cAAA,CAAAD,QAAA,EAyqButB/R,sBAAsB;MAAA;MAAA,IAAAtC,EAAA;QAAA,IAAAkI,EAAA;QAzqB/uBnK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAA+S,MAAA,GAAA9K,EAAA,CAAAG,KAAA;QAAFtK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAAgT,OAAA,GAAA/K,EAAA;MAAA;IAAA;IAAAH,SAAA,WAAAwM,gBAAAvU,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAAkK,WAAA,CAAAvH,GAAA;QAAF3C,EAAE,CAAAkK,WAAA,CAyqBo4B1F,uBAAuB;MAAA;MAAA,IAAAvC,EAAA;QAAA,IAAAkI,EAAA;QAzqB75BnK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAAkQ,YAAA,GAAAjI,EAAA,CAAAG,KAAA;QAAFtK,EAAE,CAAAoK,cAAA,CAAAD,EAAA,GAAFnK,EAAE,CAAAqK,WAAA,QAAAnI,GAAA,CAAAkT,OAAA,GAAAjL,EAAA;MAAA;IAAA;IAAAI,SAAA;IAAAkM,QAAA;IAAAC,YAAA,WAAAC,uBAAA1U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAA4W,WAAA,sBAAA1U,GAAA,CAAAiG,QAAA,0BAAAjG,GAAA,CAAA+F,QAAA,0BAAA/F,GAAA,CAAAgC,QAAA,4BAAAhC,GAAA,CAAAuK,aAAA,6BAAAvK,GAAA,CAAAsG,eAAA;MAAA;IAAA;IAAAgC,MAAA;MAAAqM,KAAA;MAAAC,aAAA;MAAA7O,QAAA;MAAA/D,QAAA;MAAAuI,aAAA;MAAAE,GAAA;MAAAa,GAAA;MAAAY,IAAA;MAAAW,WAAA;IAAA;IAAAgI,QAAA;IAAAtM,QAAA,GAAFzK,EAAE,CAAA0K,kBAAA,CAyqB2hB,CAAC;MAAEC,OAAO,EAAEtG,UAAU;MAAEuG,WAAW,EAAEsB;IAAU,CAAC,CAAC,GAzqB9kBlM,EAAE,CAAAgX,0BAAA;IAAAC,kBAAA,EAAA7S,GAAA;IAAAyG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAkM,mBAAAjV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjC,EAAE,CAAAmX,eAAA;QAAFnX,EAAE,CAAAoX,YAAA,EAyqBqiC,CAAC;QAzqBxiCpX,EAAE,CAAAmC,cAAA,YAyqB0lC,CAAC;QAzqB7lCnC,EAAE,CAAA6C,SAAA,YAyqB+oC,CAAC;QAzqBlpC7C,EAAE,CAAAmC,cAAA,YAyqB4rC,CAAC;QAzqB/rCnC,EAAE,CAAA6C,SAAA,eAyqBmwC,CAAC;QAzqBtwC7C,EAAE,CAAAqC,YAAA,CAyqB6wC,CAAC;QAzqBhxCrC,EAAE,CAAAwD,UAAA,IAAAK,wBAAA,gBAyqBwqD,CAAC;QAzqB3qD7D,EAAE,CAAAqC,YAAA,CAyqBgrD,CAAC;QAzqBnrDrC,EAAE,CAAAwD,UAAA,IAAAQ,4CAAA,oCAyqBw3D,CAAC;QAzqB33DhE,EAAE,CAAA6C,SAAA,gCAyqBuhE,CAAC;MAAA;MAAA,IAAAZ,EAAA;QAzqB1hEjC,EAAE,CAAAwC,SAAA,EAyqB2yC,CAAC;QAzqB9yCxC,EAAE,CAAA2D,UAAA,SAAAzB,GAAA,CAAAuK,aAyqB2yC,CAAC;QAzqB9yCzM,EAAE,CAAAwC,SAAA,EAyqBivD,CAAC;QAzqBpvDxC,EAAE,CAAA2D,UAAA,SAAAzB,GAAA,CAAAiG,QAyqBivD,CAAC;QAzqBpvDnI,EAAE,CAAAwC,SAAA,EAyqB+6D,CAAC;QAzqBl7DxC,EAAE,CAAA2D,UAAA,aAAAzB,GAAA,CAAAgC,QAyqB+6D,CAAC,mBAAD,CAAC,uBAAAhC,GAAA,CAAA+M,qBAAD,CAAC;MAAA;IAAA;IAAA/D,YAAA,GAAy0bxJ,EAAE,CAAC2V,OAAO,EAAmH3V,EAAE,CAACyJ,IAAI,EAA6FzG,oBAAoB;IAAA0G,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA,EAAsM;AAC1xgB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3qBoGvL,EAAE,CAAAwL,iBAAA,CA2qBXU,SAAS,EAAc,CAAC;IACvGpC,IAAI,EAAE5J,SAAS;IACfuL,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEC,IAAI,EAAE;QAC3B,OAAO,EAAE,2BAA2B;QACpC,2BAA2B,EAAE,UAAU;QACvC,8BAA8B,EAAE,UAAU;QAC1C,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,eAAe;QACjD,iCAAiC,EAAE;MACvC,CAAC;MAAEoL,QAAQ,EAAE,WAAW;MAAEzL,eAAe,EAAEnL,uBAAuB,CAACyL,MAAM;MAAEP,aAAa,EAAEjL,iBAAiB,CAACyL,IAAI;MAAErB,MAAM,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC;MAAEsB,SAAS,EAAE,CAAC;QAAEnB,OAAO,EAAEtG,UAAU;QAAEuG,WAAW,EAAEsB;MAAU,CAAC,CAAC;MAAElB,QAAQ,EAAE,giCAAgiC;MAAEI,MAAM,EAAE,CAAC,sqbAAsqb;IAAE,CAAC;EAC97d,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtB,IAAI,EAAE9J,EAAE,CAACyJ;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAE9J,EAAE,CAACwJ;IAAkB,CAAC,EAAE;MAAEM,IAAI,EAAE9J,EAAE,CAAC0J;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAElK,EAAE,CAACuW,cAAc;MAAEnK,UAAU,EAAE,CAAC;QACpJlC,IAAI,EAAErJ;MACV,CAAC;IAAE,CAAC,EAAE;MAAEqJ,IAAI,EAAEiC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClClC,IAAI,EAAErJ;MACV,CAAC,EAAE;QACCqJ,IAAI,EAAEzJ,MAAM;QACZoL,IAAI,EAAE,CAACnK,yBAAyB;MACpC,CAAC;IAAE,CAAC,EAAE;MAAEwI,IAAI,EAAEiC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClClC,IAAI,EAAErJ;MACV,CAAC,EAAE;QACCqJ,IAAI,EAAEzJ,MAAM;QACZoL,IAAI,EAAE,CAAChK,qBAAqB;MAChC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE2Q,YAAY,EAAE,CAAC;MAC3CtI,IAAI,EAAEvJ,SAAS;MACfkL,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAE2J,OAAO,EAAE,CAAC;MACVtL,IAAI,EAAEpJ,YAAY;MAClB+K,IAAI,EAAE,CAACjH,uBAAuB;IAClC,CAAC,CAAC;IAAEyQ,MAAM,EAAE,CAAC;MACTnL,IAAI,EAAEnJ,YAAY;MAClB8K,IAAI,EAAE,CAACnH,gBAAgB;IAC3B,CAAC,CAAC;IAAE4Q,OAAO,EAAE,CAAC;MACVpL,IAAI,EAAElJ,eAAe;MACrB6K,IAAI,EAAE,CAAClH,sBAAsB,EAAE;QAAE+S,WAAW,EAAE;MAAM,CAAC;IACzD,CAAC,CAAC;IAAErP,QAAQ,EAAE,CAAC;MACX6B,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE4D,QAAQ,EAAE,CAAC;MACX4F,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChB3C,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEqM,GAAG,EAAE,CAAC;MACN7C,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEkN,GAAG,EAAE,CAAC;MACN1D,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAE8N,IAAI,EAAE,CAAC;MACPtE,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEyO,WAAW,EAAE,CAAC;MACdjF,IAAI,EAAExJ;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS4P,eAAeA,CAACqH,OAAO,EAAEC,eAAe,EAAEC,iBAAiB,EAAE;EAClE,MAAMC,UAAU,GAAG,CAACH,OAAO,IAAIE,iBAAiB,EAAEjS,YAAY,CAACmS,YAAY,CAAC,qBAAqB,CAAC;EAClG,MAAMC,QAAQ,GAAGJ,eAAe,CAAChS,YAAY,CAACmS,YAAY,CAACJ,OAAO,GAAG,mBAAmB,GAAG,gBAAgB,CAAC;EAC5G,IAAI,CAACG,UAAU,IAAI,CAACE,QAAQ,EAAE;IAC1BC,oCAAoC,CAAC,CAAC;EAC1C;AACJ;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,MAAMC,KAAK,CAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCpN,OAAO,EAAE/I,iBAAiB;EAC1BgJ,WAAW,EAAE/J,UAAU,CAAC,MAAMmX,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,qCAAqC,GAAG;EAC1CvN,OAAO,EAAE/I,iBAAiB;EAC1BgJ,WAAW,EAAE/J,UAAU,CAAC,MAAMsX,mBAAmB,CAAC;EAClDF,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,cAAc,CAAC;EACjB,IAAI3K,KAAKA,CAAA,EAAG;IACR,OAAOvN,oBAAoB,CAAC,IAAI,CAAC0F,YAAY,CAAC6H,KAAK,CAAC;EACxD;EACA,IAAIA,KAAKA,CAACjB,CAAC,EAAE;IACT,MAAMgM,GAAG,GAAGtY,oBAAoB,CAACsM,CAAC,CAAC,CAACiM,QAAQ,CAAC,CAAC;IAC9C,IAAI,CAAC,IAAI,CAACC,mBAAmB,EAAE;MAC3B,IAAI,CAACC,aAAa,GAAGH,GAAG;MACxB;IACJ;IACA,IAAI,IAAI,CAACnT,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACO,YAAY,CAAC6H,KAAK,GAAG+K,GAAG;IAC7B,IAAI,CAACvK,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAC9I,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;IACjC,IAAI,CAAC/I,IAAI,CAACqL,aAAa,CAAC,CAAC;IACzB,IAAI,CAAClL,OAAO,CAACH,IAAI,CAACoO,YAAY,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACI,IAAI1B,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAACvM,OAAO,CAAC4H,GAAG,IAAI,IAAI,CAAC5H,OAAO,CAACyI,GAAG,EAAE;MACtC,IAAI,CAACgL,WAAW,GAAG,CAAC;MACpB,OAAO,IAAI,CAACA,WAAW;IAC3B;IACA,IAAI,IAAI,CAACA,WAAW,KAAKzM,SAAS,EAAE;MAChC,IAAI,CAACyM,WAAW,GAAG,IAAI,CAACjH,sBAAsB,CAAC,CAAC;IACpD;IACA,OAAO,IAAI,CAACiH,WAAW;EAC3B;EACA,IAAIlH,UAAUA,CAAClF,CAAC,EAAE;IACd,IAAI,CAACoM,WAAW,GAAGpM,CAAC;EACxB;EACA;EACA,IAAIO,GAAGA,CAAA,EAAG;IACN,OAAO7M,oBAAoB,CAAC,IAAI,CAAC0F,YAAY,CAACmH,GAAG,CAAC;EACtD;EACA,IAAIA,GAAGA,CAACP,CAAC,EAAE;IACP,IAAI,CAAC5G,YAAY,CAACmH,GAAG,GAAG7M,oBAAoB,CAACsM,CAAC,CAAC,CAACiM,QAAQ,CAAC,CAAC;IAC1D,IAAI,CAACzT,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACA;EACA,IAAIzC,GAAGA,CAAA,EAAG;IACN,OAAO1N,oBAAoB,CAAC,IAAI,CAAC0F,YAAY,CAACgI,GAAG,CAAC;EACtD;EACA,IAAIA,GAAGA,CAACpB,CAAC,EAAE;IACP,IAAI,CAAC5G,YAAY,CAACgI,GAAG,GAAG1N,oBAAoB,CAACsM,CAAC,CAAC,CAACiM,QAAQ,CAAC,CAAC;IAC1D,IAAI,CAACzT,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACA,IAAI7B,IAAIA,CAAA,EAAG;IACP,OAAOtO,oBAAoB,CAAC,IAAI,CAAC0F,YAAY,CAAC4I,IAAI,CAAC;EACvD;EACA,IAAIA,IAAIA,CAAChC,CAAC,EAAE;IACR,IAAI,CAAC5G,YAAY,CAAC4I,IAAI,GAAGtO,oBAAoB,CAACsM,CAAC,CAAC,CAACiM,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACzT,IAAI,CAACqL,aAAa,CAAC,CAAC;EAC7B;EACA;EACA,IAAIhI,QAAQA,CAAA,EAAG;IACX,OAAOpI,qBAAqB,CAAC,IAAI,CAAC2F,YAAY,CAACyC,QAAQ,CAAC;EAC5D;EACA,IAAIA,QAAQA,CAACmE,CAAC,EAAE;IACZ,IAAI,CAAC5G,YAAY,CAACyC,QAAQ,GAAGpI,qBAAqB,CAACuM,CAAC,CAAC;IACrD,IAAI,CAACxH,IAAI,CAACqL,aAAa,CAAC,CAAC;IACzB,IAAI,IAAI,CAAClL,OAAO,CAACkD,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;MACzC,IAAI,CAAClD,OAAO,CAACkD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACzC;EACJ;EACA;EACA,IAAI4L,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC9O,OAAO,CAAC4H,GAAG,IAAI,IAAI,CAAC5H,OAAO,CAACyI,GAAG,EAAE;MACtC,OAAO,IAAI,CAACzI,OAAO,CAACmK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,OAAO,CAAC,IAAI,CAAC7B,KAAK,GAAG,IAAI,CAACtI,OAAO,CAAC4H,GAAG,KAAK,IAAI,CAAC5H,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,CAAC;EAClF;EACA;EACA,IAAIwH,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACpP,OAAO,CAAChB,YAAY,EAAE;MAC5B,OAAO,IAAI,CAACgB,OAAO,CAACmK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,IAAI,IAAI,CAACsJ,WAAW,KAAK,CAAC,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAAClH,UAAU,GAAG,IAAI,CAACvM,OAAO,CAAChB,YAAY;EACtD;EACA;EACA0U,aAAaA,CAACrM,CAAC,EAAE;IACb,IAAI,CAAC9G,UAAU,GAAG8G,CAAC;EACvB;EACAzH,WAAWA,CAACE,OAAO,EAAEC,WAAW,EAAEF,IAAI,EAAEG,OAAO,EAAE;IAC7C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAAC2T,WAAW,GAAG,IAAI5X,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAAC6X,SAAS,GAAG,IAAI7X,YAAY,CAAC,CAAC;IACnC;IACA,IAAI,CAAC8X,OAAO,GAAG,IAAI9X,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACmG,aAAa,GAAG,CAAC,CAAC;IACvB;IACA,IAAI,CAACsI,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACtK,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACK,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgT,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACO,UAAU,GAAG,IAAIhX,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiQ,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACgH,YAAY,GAAG,MAAM,CAAE,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACrK,SAAS,GAAGlO,MAAM,CAACT,QAAQ,CAAC;IACjC,IAAI,CAACyF,YAAY,GAAGV,WAAW,CAAC8B,aAAa;IAC7C,IAAI,CAAC/B,OAAO,CAACuC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC5B,YAAY,CAAC6B,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC2R,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAACzT,YAAY,CAAC6B,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAClC,cAAc,CAAC8T,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAACzT,YAAY,CAAC6B,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC6R,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA3R,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,YAAY,CAAC+B,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACyR,cAAc,CAAC;IACzE,IAAI,CAACxT,YAAY,CAAC+B,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACpC,cAAc,CAAC;IACzE,IAAI,CAACK,YAAY,CAAC+B,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC2R,YAAY,CAAC;IACrE,IAAI,CAACL,UAAU,CAACM,IAAI,CAAC,CAAC;IACtB,IAAI,CAACN,UAAU,CAACO,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACT,SAAS,CAACS,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACR,OAAO,CAACQ,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAzI,SAASA,CAAA,EAAG;IACR,IAAI,CAAClD,oBAAoB,CAAC,CAAC;IAC3B;IACA,IAAI,IAAI,CAACxF,QAAQ,KAAK,IAAI,CAAClD,OAAO,CAACkD,QAAQ,EAAE;MACzC;MACA,IAAI,CAAClD,OAAO,CAACkD,QAAQ,GAAG,IAAI;IAChC;IACA,IAAI,CAACmG,IAAI,GAAG,IAAI,CAACrJ,OAAO,CAACqJ,IAAI;IAC7B,IAAI,CAACzB,GAAG,GAAG,IAAI,CAAC5H,OAAO,CAAC4H,GAAG;IAC3B,IAAI,CAACa,GAAG,GAAG,IAAI,CAACzI,OAAO,CAACyI,GAAG;IAC3B,IAAI,CAAC6L,UAAU,CAAC,CAAC;EACrB;EACA;EACAzI,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/C,qBAAqB,CAAC,CAAC;EAChC;EACAwL,UAAUA,CAAA,EAAG;IACT,IAAI,CAACf,mBAAmB,GAAG,IAAI;IAC/B,IAAI,IAAI,CAACC,aAAa,KAAKxM,SAAS,EAAE;MAClC,IAAI,CAACsB,KAAK,GAAG,IAAI,CAACiM,gBAAgB,CAAC,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAAC9T,YAAY,CAAC6H,KAAK,GAAG,IAAI,CAACkL,aAAa;MAC5C,IAAI,CAAC1K,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAC9I,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAAC/I,IAAI,CAACqL,aAAa,CAAC,CAAC;IAC7B;EACJ;EACAqJ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3M,GAAG;EACnB;EACAvG,OAAOA,CAAA,EAAG;IACN,IAAI,CAACqS,aAAa,CAAC,KAAK,CAAC;IACzB,IAAI,CAACK,YAAY,CAAC,CAAC;EACvB;EACA9S,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyS,aAAa,CAAC,IAAI,CAAC;EAC5B;EACAc,SAASA,CAAA,EAAG;IACR,IAAI,CAACb,WAAW,CAACc,IAAI,CAAC,IAAI,CAACnM,KAAK,CAAC;IACjC;IACA;IACA,IAAI,IAAI,CAACpI,SAAS,EAAE;MAChB,IAAI,CAAC4I,qBAAqB,CAAC;QAAEyH,aAAa,EAAE;MAAK,CAAC,CAAC;IACvD;EACJ;EACAmE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,WAAW,GAAG,IAAI,CAACrM,KAAK,CAAC;IAC9B;IACA;IACA,IAAI,IAAI,CAACtI,OAAO,CAACqJ,IAAI,IAAI,CAAC,IAAI,CAACnJ,SAAS,EAAE;MACtC,IAAI,CAAC4I,qBAAqB,CAAC;QAAEyH,aAAa,EAAE;MAAK,CAAC,CAAC;IACvD;IACA,IAAI,CAACvQ,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;EACrC;EACAgM,uBAAuBA,CAAA,EAAG;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAAC1U,SAAS,IAAI,CAAC,IAAI,CAACK,UAAU,EAAE;MACrC,IAAI,CAACP,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAAC9I,OAAO,CAACkD,QAAQ,GAAG,IAAI,CAAC2R,YAAY,CAAC3R,QAAQ;EACtD;EACA+Q,cAAcA,CAAC5T,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC6C,QAAQ,IAAI7C,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;MACrC;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACkI,SAAS,CAAC6G,GAAG,EAAE;MACpB,MAAMsE,qBAAqB,GAAG,IAAI,CAAC9U,OAAO,CAACY,sBAAsB,CAACP,KAAK,EAAE,IAAI,CAACL,OAAO,CAACsD,SAAS,CAAC,IAAI,CAACpB,aAAa,CAAC,CAACzB,YAAY,CAACC,qBAAqB,CAAC,CAAC,CAAC;MACzJ,IAAI,CAACR,SAAS,GAAG4U,qBAAqB;MACtC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC/U,OAAO,CAAC+K,iBAAiB,CAAC,CAAC;MAChC;IACJ;IACA,IAAI,CAAC7K,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwT,aAAa,CAAC,IAAI,CAAC;IACxB,IAAI,CAACqB,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC/U,OAAO,CAAC+K,iBAAiB,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAAC,IAAI,CAAC/K,OAAO,CAACqJ,IAAI,EAAE;MACpB,IAAI,CAAC2L,4BAA4B,CAAC3U,KAAK,EAAE;QAAEkQ,aAAa,EAAE;MAAK,CAAC,CAAC;IACrE;IACA,IAAI,CAAC,IAAI,CAACrN,QAAQ,EAAE;MAChB,IAAI,CAAC+R,sBAAsB,CAAC5U,KAAK,CAAC;MAClC,IAAI,CAACuT,SAAS,CAACa,IAAI,CAAC;QAAE7G,MAAM,EAAE,IAAI;QAAEsH,MAAM,EAAE,IAAI,CAAClV,OAAO;QAAEsI,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAClF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI2M,sBAAsBA,CAAC5U,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAI,CAAC0M,aAAa,GAAG,IAAI;IACzB;IACA;IACA;IACA;IACA;IACAoI,UAAU,CAAC,MAAM;MACb,IAAI,CAACpI,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACqI,SAAS,CAAC/U,KAAK,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC;EACT;EACA;EACA+U,SAASA,CAAC/U,KAAK,EAAE;IACb,MAAMgV,IAAI,GAAGhV,KAAK,CAAC0Q,OAAO,GAAG,IAAI,CAAC/Q,OAAO,CAACiN,WAAW;IACrD,MAAMwD,KAAK,GAAG,IAAI,CAACzQ,OAAO,CAAChB,YAAY;IACvC,MAAMqK,IAAI,GAAG,IAAI,CAACrJ,OAAO,CAACqJ,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAACrJ,OAAO,CAACqJ,IAAI;IAC5D,MAAMiM,QAAQ,GAAG9M,IAAI,CAACqG,KAAK,CAAC,CAAC,IAAI,CAAC7O,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,IAAIyB,IAAI,CAAC;IACzE,MAAMyF,UAAU,GAAG,IAAI,CAAC9O,OAAO,CAACmK,MAAM,GAAG,CAAC,GAAGkL,IAAI,GAAG5E,KAAK,GAAG4E,IAAI,GAAG5E,KAAK;IACxE;IACA,MAAM8E,eAAe,GAAG/M,IAAI,CAACiH,KAAK,CAACX,UAAU,GAAGwG,QAAQ,CAAC,GAAGA,QAAQ;IACpE,MAAME,cAAc,GAAGD,eAAe,IAAI,IAAI,CAACvV,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,CAAC,GAAG,IAAI,CAAC5H,OAAO,CAAC4H,GAAG;IACjG,MAAMU,KAAK,GAAGE,IAAI,CAACiH,KAAK,CAAC+F,cAAc,GAAGnM,IAAI,CAAC,GAAGA,IAAI;IACtD,MAAMoM,SAAS,GAAG,IAAI,CAACnN,KAAK;IAC5B,IAAIA,KAAK,KAAKmN,SAAS,EAAE;MACrB;MACA;MACA;MACA,IAAI,CAACzV,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAAC5I,OAAO,CAACqJ,IAAI,GAAG,CAAC,GACf,IAAI,CAACP,qBAAqB,CAAC,CAAC,GAC5B,IAAI,CAACkM,4BAA4B,CAAC3U,KAAK,EAAE;QAAEkQ,aAAa,EAAE,IAAI,CAACvQ,OAAO,CAACsK;MAAc,CAAC,CAAC;MAC7F;IACJ;IACA,IAAI,CAAChC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACqL,WAAW,CAACc,IAAI,CAAC,IAAI,CAACnM,KAAK,CAAC;IACjC,IAAI,CAACqM,WAAW,GAAG,IAAI,CAACrM,KAAK,CAAC;IAC9B,IAAI,CAACtI,OAAO,CAAC4I,cAAc,CAAC,IAAI,CAAC;IACjC,IAAI,CAAC5I,OAAO,CAACqJ,IAAI,GAAG,CAAC,GACf,IAAI,CAACP,qBAAqB,CAAC,CAAC,GAC5B,IAAI,CAACkM,4BAA4B,CAAC3U,KAAK,EAAE;MAAEkQ,aAAa,EAAE,IAAI,CAACvQ,OAAO,CAACsK;IAAc,CAAC,CAAC;EACjG;EACAlK,cAAcA,CAACC,KAAK,EAAE;IAClB;IACA;IACA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACqJ,IAAI,IAAI,IAAI,CAACnJ,SAAS,EAAE;MACtC,IAAI,CAAC8U,4BAA4B,CAAC3U,KAAK,CAAC;IAC5C;EACJ;EACA8T,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACjU,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC2T,OAAO,CAACY,IAAI,CAAC;QAAE7G,MAAM,EAAE,IAAI;QAAEsH,MAAM,EAAE,IAAI,CAAClV,OAAO;QAAEsI,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;MAC5E;MACA;MACA;MACA;MACA6M,UAAU,CAAC,MAAM,IAAI,CAACzM,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAACiB,SAAS,CAAC6G,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9E;EACJ;EACAkF,MAAMA,CAACrO,CAAC,EAAE;IACN,OAAOmB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACZ,GAAG,CAACP,CAAC,EAAE,IAAI,CAACrH,OAAO,CAAChB,YAAY,CAAC,EAAE,CAAC,CAAC;EAC9D;EACAwN,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACxM,OAAO,CAACmK,MAAM,EAAE;MACrB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC2E,UAAU,IAAI,IAAI,CAAC9O,OAAO,CAAChB,YAAY;IAC5D;IACA,OAAO,IAAI,CAAC8P,UAAU,GAAG,IAAI,CAAC9O,OAAO,CAAChB,YAAY;EACtD;EACA2W,6BAA6BA,CAACtV,KAAK,EAAE;IACjC,OAAOA,KAAK,CAAC0Q,OAAO,GAAG,IAAI,CAAC/Q,OAAO,CAACiN,WAAW;EACnD;EACA;AACJ;AACA;AACA;EACI8H,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACtU,YAAY,CAAC6M,KAAK,CAACsI,OAAO,GAAI,KAAI,IAAI,CAAC5V,OAAO,CAACqL,aAAc,IAAG;IACrE,IAAI,CAAC5K,YAAY,CAAC6M,KAAK,CAACmD,KAAK,GAAI,eAAc,IAAI,CAACzQ,OAAO,CAACqL,aAAc,KAAI;EAClF;EACA;AACJ;AACA;AACA;EACI3C,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACjI,YAAY,CAAC6M,KAAK,CAACsI,OAAO,GAAG,KAAK;IACvC,IAAI,CAACnV,YAAY,CAAC6M,KAAK,CAACmD,KAAK,GAAG,mBAAmB;IACnD,IAAI,CAAChQ,YAAY,CAAC6M,KAAK,CAACJ,IAAI,GAAG,OAAO;EAC1C;EACApE,qBAAqBA,CAAC+M,OAAO,EAAE;IAC3B,IAAI,CAACtJ,UAAU,GAAG,IAAI,CAACmJ,MAAM,CAAC,IAAI,CAAClJ,sBAAsB,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACqB,cAAc,CAACgI,OAAO,CAAC;EAChC;EACAb,4BAA4BA,CAAC3U,KAAK,EAAEwV,OAAO,EAAE;IACzC,IAAI,CAACtJ,UAAU,GAAG,IAAI,CAACmJ,MAAM,CAAC,IAAI,CAACC,6BAA6B,CAACtV,KAAK,CAAC,CAAC;IACxE,IAAI,CAACwN,cAAc,CAACgI,OAAO,CAAC;EAChC;EACAhI,cAAcA,CAACgI,OAAO,EAAE;IACpB,IAAI,CAAC7V,OAAO,CAACsQ,cAAc,CAAC,CAAC,CAACuF,OAAO,EAAEtF,aAAa,CAAC;IACrD,IAAI,CAACvQ,OAAO,CAAC2N,mBAAmB,CAAC,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;EACImI,UAAUA,CAACxN,KAAK,EAAE;IACd,IAAI,IAAI,CAAC0L,qBAAqB,IAAI1L,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIyN,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACrB,WAAW,GAAGqB,EAAE;IACrB,IAAI,CAAChC,qBAAqB,GAAG,IAAI;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIiC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACjC,YAAY,GAAGiC,EAAE;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACjT,QAAQ,GAAGiT,UAAU;EAC9B;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC3V,YAAY,CAAC2V,KAAK,CAAC,CAAC;EAC7B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC5V,YAAY,CAAC4V,IAAI,CAAC,CAAC;EAC5B;EAAC,QAAAjS,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAiS,uBAAA/R,CAAA;IAAA,YAAAA,CAAA,IAAwF0O,cAAc,EAjqCxBhY,EAAE,CAAAuJ,iBAAA,CAiqCwCvJ,EAAE,CAACyJ,MAAM,GAjqCnDzJ,EAAE,CAAAuJ,iBAAA,CAiqC8DvJ,EAAE,CAAC0J,UAAU,GAjqC7E1J,EAAE,CAAAuJ,iBAAA,CAiqCwFvJ,EAAE,CAACwJ,iBAAiB,GAjqC9GxJ,EAAE,CAAAuJ,iBAAA,CAiqCyHlF,UAAU;EAAA,CAA4C;EAAA,QAAAsF,EAAA,GACxQ,IAAI,CAAC2R,IAAI,kBAlqC8Etb,EAAE,CAAAub,iBAAA;IAAAzR,IAAA,EAkqCJkO,cAAc;IAAAjO,SAAA;IAAAQ,SAAA,WAAoL,OAAO;IAAAkM,QAAA;IAAAC,YAAA,WAAA8E,4BAAAvZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAlqCvMjC,EAAE,CAAAyb,UAAA,oBAAAC,yCAAA;UAAA,OAkqCJxZ,GAAA,CAAAqX,SAAA,CAAU,CAAC;QAAA,qBAAAoC,wCAAA;UAAA,OAAXzZ,GAAA,CAAAuX,QAAA,CAAS,CAAC;QAAA,oBAAAmC,uCAAA;UAAA,OAAV1Z,GAAA,CAAAkE,OAAA,CAAQ,CAAC;QAAA,qBAAAyV,wCAAA;UAAA,OAAT3Z,GAAA,CAAA8D,QAAA,CAAS,CAAC;QAAA;MAAA;MAAA,IAAA/D,EAAA;QAlqCRjC,EAAE,CAAA8b,WAAA,mBAAA5Z,GAAA,CAAAsR,UAAA;MAAA;IAAA;IAAAhJ,MAAA;MAAA6C,KAAA;IAAA;IAAA0O,OAAA;MAAArD,WAAA;MAAAC,SAAA;MAAAC,OAAA;IAAA;IAAA7B,QAAA;IAAAtM,QAAA,GAAFzK,EAAE,CAAA0K,kBAAA,CAkqC2Z,CACrfqN,+BAA+B,EAC/B;MAAEpN,OAAO,EAAErG,gBAAgB;MAAEsG,WAAW,EAAEoN;IAAe,CAAC,CAC7D;EAAA,EAA+C;AACxD;AACA;EAAA,QAAAzM,SAAA,oBAAAA,SAAA,KAvqCoGvL,EAAE,CAAAwL,iBAAA,CAuqCXwM,cAAc,EAAc,CAAC;IAC5GlO,IAAI,EAAE/I,SAAS;IACf0K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCqL,QAAQ,EAAE,gBAAgB;MAC1BpL,IAAI,EAAE;QACF,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE,OAAO;QACf,uBAAuB,EAAE,YAAY;QACrC,UAAU,EAAE,aAAa;QACzB,SAAS,EAAE,YAAY;QACvB;QACA;QACA,QAAQ,EAAE,WAAW;QACrB,SAAS,EAAE;MACf,CAAC;MACDG,SAAS,EAAE,CACPiM,+BAA+B,EAC/B;QAAEpN,OAAO,EAAErG,gBAAgB;QAAEsG,WAAW,EAAEoN;MAAe,CAAC;IAElE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElO,IAAI,EAAE9J,EAAE,CAACyJ;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAE9J,EAAE,CAAC0J;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAE9J,EAAE,CAACwJ;IAAkB,CAAC,EAAE;MAAEM,IAAI,EAAEiC,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC5IlC,IAAI,EAAEzJ,MAAM;QACZoL,IAAI,EAAE,CAACpH,UAAU;MACrB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgJ,KAAK,EAAE,CAAC;MACpCvD,IAAI,EAAExJ;IACV,CAAC,CAAC;IAAEoY,WAAW,EAAE,CAAC;MACd5O,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAE2X,SAAS,EAAE,CAAC;MACZ7O,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAE4X,OAAO,EAAE,CAAC;MACV9O,IAAI,EAAE9I;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMmX,mBAAmB,SAASH,cAAc,CAAC;EAC7C;EACA7E,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC6I,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACjX,OAAO,CAACiC,SAAS,CAAC,IAAI,CAACiV,WAAW,GAAG,CAAC,CAAC,wBAAwB,CAAC,CAAC,mBAAmB,CAAC;IAC9G;;IACA,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACIE,SAASA,CAAA,EAAG;IACR,MAAM9T,OAAO,GAAG,IAAI,CAAC+K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC,IAAI,CAACe,YAAY,IAAI9L,OAAO,EAAE;MAC/B,OAAOA,OAAO,CAACkJ,UAAU;IAC7B;IACA,OAAO,CAAC;EACZ;EACA;AACJ;AACA;AACA;EACI6K,SAASA,CAAA,EAAG;IACR,MAAM/T,OAAO,GAAG,IAAI,CAAC+K,UAAU,CAAC,CAAC;IACjC,IAAI,IAAI,CAACe,YAAY,IAAI9L,OAAO,EAAE;MAC9B,OAAOA,OAAO,CAACkJ,UAAU;IAC7B;IACA,OAAO,IAAI,CAACvM,OAAO,CAAChB,YAAY;EACpC;EACAsN,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC6C,YAAY,GACZ,IAAI,CAAC+H,WAAW,IAAI,IAAI,CAAClX,OAAO,CAACmK,MAAM,IAAM,CAAC,IAAI,CAAC+M,WAAW,IAAI,CAAC,IAAI,CAAClX,OAAO,CAACmK,MAAO;EAChG;EACAvK,WAAWA,CAACE,OAAO,EAAEE,OAAO,EAAED,WAAW,EAAEF,IAAI,EAAE;IAC7C,KAAK,CAACC,OAAO,EAAEC,WAAW,EAAEF,IAAI,EAAEG,OAAO,CAAC;IAC1C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqX,WAAW,GAAG,IAAI,CAACzW,YAAY,CAACmS,YAAY,CAAC,mBAAmB,CAAC;IACtE,IAAI,CAACtG,eAAe,CAAC,CAAC;IACtB,IAAI,CAACpK,aAAa,GAAG,IAAI,CAACgV,WAAW,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC;EACtE;;EACA3C,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC2C,WAAW,IAAI,IAAI,CAAClX,OAAO,CAACoD,QAAQ,GAAG,IAAI,CAACqF,GAAG,GAAG,IAAI,CAACb,GAAG;EAC1E;EACA8M,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAAC2C,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAACnX,SAAS,EAAE;MACjB,IAAI,CAACwI,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACAkM,uBAAuBA,CAAA,EAAG;IACtB,KAAK,CAACA,uBAAuB,CAAC,CAAC;IAC/B,IAAI,CAACxG,UAAU,CAAC,CAAC,EAAErC,aAAa,CAAC,CAAC;EACtC;EACAkI,cAAcA,CAAC5T,KAAK,EAAE;IAClB,IAAI,IAAI,CAAC6C,QAAQ,IAAI7C,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAACwV,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAAClC,kBAAkB,CAAC,CAAC;MAClC,IAAI,CAACkC,QAAQ,CAACxW,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACtF;IACA,KAAK,CAAC6S,cAAc,CAAC5T,KAAK,CAAC;EAC/B;EACA8T,YAAYA,CAAA,EAAG;IACX,KAAK,CAACA,YAAY,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC8C,QAAQ,EAAE;MACf9B,UAAU,CAAC,MAAM;QACb,IAAI,CAAC8B,QAAQ,CAACvO,oBAAoB,CAAC,CAAC;QACpC,IAAI,CAACuO,QAAQ,CAACxW,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,wCAAwC,CAAC;MACzF,CAAC,CAAC;IACN;EACJ;EACAnB,cAAcA,CAACC,KAAK,EAAE;IAClB,KAAK,CAACD,cAAc,CAACC,KAAK,CAAC;IAC3B,IAAI,CAAC,IAAI,CAACL,OAAO,CAACqJ,IAAI,IAAI,IAAI,CAACnJ,SAAS,EAAE;MACtC,IAAI,CAACmX,cAAc,CAAC,CAAC;IACzB;EACJ;EACAjC,SAASA,CAAC/U,KAAK,EAAE;IACb,KAAK,CAAC+U,SAAS,CAAC/U,KAAK,CAAC;IACtB,IAAI,CAAC4W,QAAQ,EAAElL,aAAa,CAAC,CAAC;EAClC;EACA2J,MAAMA,CAACrO,CAAC,EAAE;IACN,OAAOmB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACZ,GAAG,CAACP,CAAC,EAAE,IAAI,CAAC+P,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC;EACpE;EACApL,aAAaA,CAAA,EAAG;IACZ,MAAM1I,OAAO,GAAG,IAAI,CAAC+K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC/K,OAAO,EAAE;MACV;IACJ;IACA,IAAI,IAAI,CAAC6T,WAAW,EAAE;MAClB,IAAI,CAACtP,GAAG,GAAGY,IAAI,CAACC,GAAG,CAAC,IAAI,CAACzI,OAAO,CAAC4H,GAAG,EAAEvE,OAAO,CAACiF,KAAK,CAAC;MACpD,IAAI,CAACG,GAAG,GAAG,IAAI,CAACzI,OAAO,CAACyI,GAAG;IAC/B,CAAC,MACI;MACD,IAAI,CAACb,GAAG,GAAG,IAAI,CAAC5H,OAAO,CAAC4H,GAAG;MAC3B,IAAI,CAACa,GAAG,GAAGD,IAAI,CAACZ,GAAG,CAAC,IAAI,CAAC5H,OAAO,CAACyI,GAAG,EAAEpF,OAAO,CAACiF,KAAK,CAAC;IACxD;EACJ;EACAyM,kBAAkBA,CAAA,EAAG;IACjB,MAAMuC,QAAQ,GAAG,IAAI,CAACtX,OAAO,CAACiK,aAAa,GAAG,CAAC,GAAG,IAAI,CAACjK,OAAO,CAACqL,aAAa,GAAG,CAAC;IAChF,MAAMkM,QAAQ,GAAG,IAAI,CAACvX,OAAO,CAAChB,YAAY,GAAG,IAAI,CAACgB,OAAO,CAACqL,aAAa,GAAGiM,QAAQ;IAClF,MAAMxI,UAAU,GAAG,IAAI,CAAC9O,OAAO,CAAC4H,GAAG,GAAG,IAAI,CAAC5H,OAAO,CAACyI,GAAG,GAChD,CAAC,IAAI,CAACA,GAAG,GAAG,IAAI,CAACb,GAAG,KAAK,IAAI,CAAC5H,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,CAAC,GAC7D,CAAC;IACP,MAAM6I,KAAK,GAAG8G,QAAQ,GAAGzI,UAAU,GAAGwI,QAAQ;IAC9C,IAAI,CAAC7W,YAAY,CAAC6M,KAAK,CAACmD,KAAK,GAAI,GAAEA,KAAM,IAAG;IAC5C,IAAI,CAAChQ,YAAY,CAAC6M,KAAK,CAACsI,OAAO,GAAI,KAAI,IAAI,CAAC5V,OAAO,CAACqL,aAAc,IAAG;EACzE;EACA3C,oBAAoBA,CAAA,EAAG;IACnB,MAAMrF,OAAO,GAAG,IAAI,CAAC+K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC/K,OAAO,EAAE;MACV;IACJ;IACA,MAAMkU,QAAQ,GAAG,IAAI,CAACvX,OAAO,CAAChB,YAAY;IAC1C,MAAMwY,QAAQ,GAAG,IAAI,CAACN,WAAW,GAC3B,IAAI,CAAC5O,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,GAAGjF,OAAO,CAACiF,KAAK,IAAI,CAAC,GAC7C,IAAI,CAACA,KAAK,GAAG,CAACjF,OAAO,CAACiF,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC;IACnD,MAAMmP,WAAW,GAAG,IAAI,CAACP,WAAW,GAC9B,CAAC,IAAI,CAACzO,GAAG,GAAG+O,QAAQ,KAAK,IAAI,CAACxX,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,CAAC,GAC7D,CAAC4P,QAAQ,GAAG,IAAI,CAAC5P,GAAG,KAAK,IAAI,CAAC5H,OAAO,CAACyI,GAAG,GAAG,IAAI,CAACzI,OAAO,CAAC4H,GAAG,CAAC;IACnE,MAAMkH,UAAU,GAAG,IAAI,CAAC9O,OAAO,CAAC4H,GAAG,GAAG,IAAI,CAAC5H,OAAO,CAACyI,GAAG,GAAGgP,WAAW,GAAG,CAAC;IACxE;IACA,IAAIC,aAAa,GAAG,IAAI,CAAC1X,OAAO,CAACiK,aAAa;IAC9C;IACA;IACA;IACA,IAAI6E,UAAU,KAAK,CAAC,EAAE;MAClB4I,aAAa,GAAG,EAAE;IACtB,CAAC,MACI,IAAI5I,UAAU,KAAK,CAAC,EAAE;MACvB4I,aAAa,GAAG,CAAC;IACrB;IACA,MAAMjH,KAAK,GAAG8G,QAAQ,GAAGzI,UAAU,GAAG4I,aAAa;IACnD,IAAI,CAACjX,YAAY,CAAC6M,KAAK,CAACmD,KAAK,GAAI,GAAEA,KAAM,IAAG;IAC5C,IAAI,CAAChQ,YAAY,CAAC6M,KAAK,CAACsI,OAAO,GAAG,KAAK;IACvC,IAAI,IAAI,CAACzG,YAAY,EAAE;MACnB,IAAI,CAAC1O,YAAY,CAAC6M,KAAK,CAACJ,IAAI,GAAG,OAAO;MACtC,IAAI,CAACzM,YAAY,CAAC6M,KAAK,CAACC,KAAK,GAAG,MAAM;IAC1C,CAAC,MACI;MACD,IAAI,CAAC9M,YAAY,CAAC6M,KAAK,CAACJ,IAAI,GAAG,MAAM;MACrC,IAAI,CAACzM,YAAY,CAAC6M,KAAK,CAACC,KAAK,GAAG,OAAO;IAC3C;EACJ;EACAvB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACvL,YAAY,CAACU,SAAS,CAACoN,MAAM,CAAC,yBAAyB,EAAE,CAAC,IAAI,CAACY,YAAY,CAAC;EACrF;EACAkI,cAAcA,CAAA,EAAG;IACb,MAAMhU,OAAO,GAAG,IAAI,CAAC+K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC/K,OAAO,EAAE;MACV;IACJ;IACAA,OAAO,CAAC0I,aAAa,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC7L,SAAS,EAAE;MAChBmD,OAAO,CAAC0R,kBAAkB,CAAC,CAAC;IAChC,CAAC,MACI;MACD1R,OAAO,CAACqF,oBAAoB,CAAC,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIoN,UAAUA,CAACxN,KAAK,EAAE;IACd,IAAI,IAAI,CAAC0L,qBAAqB,IAAI1L,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC2O,cAAc,CAAC,CAAC;IACzB;EACJ;EAAC,QAAAjT,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAsT,4BAAApT,CAAA;IAAA,YAAAA,CAAA,IAAwF6O,mBAAmB,EAv3C7BnY,EAAE,CAAAuJ,iBAAA,CAu3C6CvJ,EAAE,CAACyJ,MAAM,GAv3CxDzJ,EAAE,CAAAuJ,iBAAA,CAu3CmElF,UAAU,GAv3C/ErE,EAAE,CAAAuJ,iBAAA,CAu3C0FvJ,EAAE,CAAC0J,UAAU,GAv3CzG1J,EAAE,CAAAuJ,iBAAA,CAu3CoHvJ,EAAE,CAACwJ,iBAAiB;EAAA,CAA4C;EAAA,QAAAG,EAAA,GAC7Q,IAAI,CAAC2R,IAAI,kBAx3C8Etb,EAAE,CAAAub,iBAAA;IAAAzR,IAAA,EAw3CJqO,mBAAmB;IAAApO,SAAA;IAAAgN,QAAA;IAAAtM,QAAA,GAx3CjBzK,EAAE,CAAA0K,kBAAA,CAw3C8F,CACxLwN,qCAAqC,EACrC;MAAEvN,OAAO,EAAEpG,sBAAsB;MAAEqG,WAAW,EAAEuN;IAAoB,CAAC,CACxE,GA33C2FnY,EAAE,CAAAgX,0BAAA;EAAA,EA23ClB;AACpF;AACA;EAAA,QAAAzL,SAAA,oBAAAA,SAAA,KA73CoGvL,EAAE,CAAAwL,iBAAA,CA63CX2M,mBAAmB,EAAc,CAAC;IACjHrO,IAAI,EAAE/I,SAAS;IACf0K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sDAAsD;MAChEqL,QAAQ,EAAE,qBAAqB;MAC/BjL,SAAS,EAAE,CACPoM,qCAAqC,EACrC;QAAEvN,OAAO,EAAEpG,sBAAsB;QAAEqG,WAAW,EAAEuN;MAAoB,CAAC;IAE7E,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErO,IAAI,EAAE9J,EAAE,CAACyJ;IAAO,CAAC,EAAE;MAAEK,IAAI,EAAEiC,SAAS;MAAEC,UAAU,EAAE,CAAC;QACnFlC,IAAI,EAAEzJ,MAAM;QACZoL,IAAI,EAAE,CAACpH,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEyF,IAAI,EAAE9J,EAAE,CAAC0J;IAAW,CAAC,EAAE;MAAEI,IAAI,EAAE9J,EAAE,CAACwJ;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;AAEjF,MAAMmT,eAAe,CAAC;EAAA,QAAAxT,CAAA,GACT,IAAI,CAACC,IAAI,YAAAwT,wBAAAtT,CAAA;IAAA,YAAAA,CAAA,IAAwFqT,eAAe;EAAA,CAAkD;EAAA,QAAAhT,EAAA,GAClK,IAAI,CAACkT,IAAI,kBA94C8E7c,EAAE,CAAA8c,gBAAA;IAAAhT,IAAA,EA84CS6S;EAAe,EAA+M;EAAA,QAAAI,EAAA,GAChU,IAAI,CAACC,IAAI,kBA/4C8Ehd,EAAE,CAAAid,gBAAA;IAAAC,OAAA,GA+4CoC3b,eAAe,EAAEI,YAAY,EAAEH,eAAe;EAAA,EAAI;AAC5L;AACA;EAAA,QAAA+J,SAAA,oBAAAA,SAAA,KAj5CoGvL,EAAE,CAAAwL,iBAAA,CAi5CXmR,eAAe,EAAc,CAAC;IAC7G7S,IAAI,EAAE7I,QAAQ;IACdwK,IAAI,EAAE,CAAC;MACCyR,OAAO,EAAE,CAAC3b,eAAe,EAAEI,YAAY,EAAEH,eAAe,CAAC;MACzD2b,OAAO,EAAE,CAACjR,SAAS,EAAE8L,cAAc,EAAEG,mBAAmB,CAAC;MACzDiF,YAAY,EAAE,CAAClR,SAAS,EAAE8L,cAAc,EAAEG,mBAAmB,EAAEzT,oBAAoB;IACvF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASwH,SAAS,EAAEzH,eAAe,EAAEkY,eAAe,EAAExE,mBAAmB,EAAEH,cAAc,EAAEtT,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}