package com.navitsa.mydent.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.entity.Customer;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface AppointmentsRepository extends JpaRepository<Appointments, Integer> {
    @Query("SELECT a FROM Appointments a WHERE a.clinics.userId.userId = :userId")
    Optional<List<Appointments>> getAllPendingAppointmentsByUserId(@Param("userId") Integer userId);
    
    List<Appointments> findByCustomer(Customer customerId);

    List<Appointments> findByClinicsClinicId(int clinicId);

    List<Appointments> findByClinicsClinicIdAndFromDate(Integer clinicId, String fromDate);

//    List<Appointments> findByFromDate(String fromDate);
}
