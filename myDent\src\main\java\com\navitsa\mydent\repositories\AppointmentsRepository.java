package com.navitsa.mydent.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.entity.Customer;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;


public interface AppointmentsRepository extends JpaRepository<Appointments, Integer> {
    @Query("SELECT a FROM Appointments a WHERE a.clinics.userId.userId = :userId")
    Optional<List<Appointments>> getAllPendingAppointmentsByUserId(@Param("userId") Integer userId);
    
    List<Appointments> findByCustomer(Customer customerId);

    List<Appointments> findByClinicsClinicId(int clinicId);

    List<Appointments> findByClinicsClinicIdAndFromDate(Integer clinicId, String fromDate);

    Page<Appointments> findByClinicsClinicIdAndFromDate(int clinicId, String fromDate, Pageable pageable);

    @Query("SELECT CASE WHEN COUNT(a) > 0 THEN true ELSE false END " +
            "FROM Appointments a " +
            "WHERE a.clinics.clinicId = :clinicId AND a.preferredservice = :preferredService")
    boolean existsByClinicIdAndPreferredService(@Param("clinicId") int clinicId,
                                                @Param("preferredService") int preferredService);

    @Query("SELECT a FROM Appointments a WHERE a.clinics.clinicId = :clinicId AND a.fromDate = :date AND a.fromTime = :fromTime AND a.toTime = :toTime")
    List<Appointments> findByClinicIdAndDateAndTime(@Param("clinicId") Integer clinicId,
                                                   @Param("date") String date,
                                                   @Param("fromTime") String fromTime,
                                                   @Param("toTime") String toTime);


//    List<Appointments> findByFromDate(String fromDate);
}
