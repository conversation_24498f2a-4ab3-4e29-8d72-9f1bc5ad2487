{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../http.service\";\nclass DoctorService {\n  searchDoctorsByName(searchTerm) {\n    throw new Error('Method not implemented.');\n  }\n  constructor(httpService) {\n    this.httpService = httpService;\n  }\n  saveDoctor(doctor) {\n    return this.httpService.request('POST', '/saveDoctor', doctor);\n  }\n  getDoctorList() {\n    return this.httpService.request('GET', '/doctorList', {});\n  }\n  getDoctorById(id) {\n    return this.httpService.request('GET', `/getDoctorById/${id}`, {});\n  }\n  updateDoctor(id, doctor) {\n    return this.httpService.request('PUT', `/updateDoctor/${id}`, doctor);\n  }\n  deleteDoctor(id) {\n    return this.httpService.request('DELETE', `/deleteDoctor/${id}`, {});\n  }\n  checkSlmcNumber(regNo) {\n    const params = {\n      regNo\n    };\n    return this.httpService.request('GET', `/checkSlmcNumber`, null, params);\n  }\n  getDoctorsByUserId(userId) {\n    const url = `/api/doctors/${userId}`;\n    return this.httpService.request('GET', url, null);\n  }\n  deleteClinicDoctor(doctorId) {\n    const userId = localStorage.getItem('userid');\n    const url = `/api/remove/${userId}/${doctorId}`;\n    return this.httpService.request('DELETE', url, null);\n  }\n  assignDoctorToClinic(userId, doctorId) {\n    const url = `/api/assignDoctor/${userId}/${doctorId}`;\n    return this.httpService.request('POST', url, null);\n  }\n  getAllDoctorsFilter(filterInput, clinicId) {\n    const url = `/api/getAllDoctorsAssignAndNot/${filterInput}/${clinicId}`;\n    return this.httpService.request('GET', url, null);\n  }\n  getClinicListByUserId() {\n    const userId = localStorage.getItem('userid');\n    if (userId != null) {\n      return this.httpService.request('GET', `/getClinicsListByDoctorId/${userId}`, null);\n    } else {\n      return of(null);\n    }\n  }\n  getMyAppointmentList() {\n    const userId = localStorage.getItem('userid');\n    if (userId != null) {\n      return this.httpService.request('GET', `/getAppointmentListByDoctor_UserId/${userId}`, null);\n    } else {\n      return of(null);\n    }\n  }\n  static #_ = this.ɵfac = function DoctorService_Factory(t) {\n    return new (t || DoctorService)(i0.ɵɵinject(i1.HttpService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DoctorService,\n    factory: DoctorService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { DoctorService };", "map": {"version": 3, "names": ["of", "DoctorService", "searchDoctorsByName", "searchTerm", "Error", "constructor", "httpService", "saveDoctor", "doctor", "request", "getDoctorList", "getDoctorById", "id", "updateDoctor", "deleteDoctor", "checkSlmcNumber", "regNo", "params", "getDoctorsByUserId", "userId", "url", "deleteClinicDoctor", "doctorId", "localStorage", "getItem", "assignDoctorToClinic", "getAllDoctorsFilter", "filterInput", "clinicId", "getClinicListByUserId", "getMyAppointmentList", "_", "i0", "ɵɵinject", "i1", "HttpService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\doctor\\doctor.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, of } from 'rxjs';\r\nimport { Doctor } from './doctor';\r\nimport { DoctorList } from '../clinic/doctor-list/doctorList';\r\nimport { HttpService } from '../http.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class DoctorService {\r\n  searchDoctorsByName(searchTerm: string) {\r\n    throw new Error('Method not implemented.');\r\n  }\r\n  constructor(private httpService: HttpService) {}\r\n\r\n  saveDoctor(doctor: Doctor): Observable<any> {\r\n    return this.httpService.request('POST', '/saveDoctor', doctor);\r\n  }\r\n\r\n  getDoctorList(): Observable<DoctorList[]> {\r\n    return this.httpService.request('GET', '/doctorList', {});\r\n  }\r\n\r\n  getDoctorById(id: number): Observable<Doctor> {\r\n    return this.httpService.request('GET', `/getDoctorById/${id}`, {});\r\n  }\r\n\r\n  updateDoctor(id: number, doctor: Doctor): Observable<object> {\r\n    return this.httpService.request('PUT', `/updateDoctor/${id}`, doctor);\r\n  }\r\n\r\n  deleteDoctor(id: number): Observable<any> {\r\n    return this.httpService.request('DELETE', `/deleteDoctor/${id}`, {});\r\n  }\r\n\r\n  checkSlmcNumber(regNo: string): Observable<any> {\r\n    const params = { regNo };\r\n    return this.httpService.request('GET', `/checkSlmcNumber`, null, params);\r\n  }\r\n\r\n  getDoctorsByUserId(userId: string): Observable<any> {\r\n    const url = `/api/doctors/${userId}`;\r\n    return this.httpService.request('GET', url, null);\r\n  }\r\n\r\n  deleteClinicDoctor(doctorId: number): Observable<void> {\r\n    const userId = localStorage.getItem('userid');\r\n    const url = `/api/remove/${userId}/${doctorId}`;\r\n    return this.httpService.request('DELETE', url, null);\r\n  }\r\n\r\n  assignDoctorToClinic(userId: string, doctorId: number): Observable<any> {\r\n    const url = `/api/assignDoctor/${userId}/${doctorId}`;\r\n    return this.httpService.request('POST', url, null);\r\n  }\r\n\r\ngetAllDoctorsFilter(filterInput: string,clinicId: number): Observable<any> {\r\n    const url = `/api/getAllDoctorsAssignAndNot/${filterInput}/${clinicId}`;\r\n    return this.httpService.request('GET', url, null);\r\n  }\r\n\r\n  getClinicListByUserId() : Observable<any> {\r\n    const userId = localStorage.getItem('userid');\r\n    if (userId != null) {\r\n      return this.httpService.request('GET',`/getClinicsListByDoctorId/${userId}`, null);\r\n    }else{\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n  getMyAppointmentList(){\r\n    const userId = localStorage.getItem('userid');\r\n    if (userId != null) {\r\n      return this.httpService.request('GET',`/getAppointmentListByDoctor_UserId/${userId}`, null);\r\n    }else{\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAqBA,EAAE,QAAQ,MAAM;;;AAKrC,MAGaC,aAAa;EACxBC,mBAAmBA,CAACC,UAAkB;IACpC,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EACAC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,UAAUA,CAACC,MAAc;IACvB,OAAO,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,aAAa,EAAED,MAAM,CAAC;EAChE;EAEAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACJ,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC;EAC3D;EAEAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,kBAAkBG,EAAE,EAAE,EAAE,EAAE,CAAC;EACpE;EAEAC,YAAYA,CAACD,EAAU,EAAEJ,MAAc;IACrC,OAAO,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,iBAAiBG,EAAE,EAAE,EAAEJ,MAAM,CAAC;EACvE;EAEAM,YAAYA,CAACF,EAAU;IACrB,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,QAAQ,EAAE,iBAAiBG,EAAE,EAAE,EAAE,EAAE,CAAC;EACtE;EAEAG,eAAeA,CAACC,KAAa;IAC3B,MAAMC,MAAM,GAAG;MAAED;IAAK,CAAE;IACxB,OAAO,IAAI,CAACV,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,kBAAkB,EAAE,IAAI,EAAEQ,MAAM,CAAC;EAC1E;EAEAC,kBAAkBA,CAACC,MAAc;IAC/B,MAAMC,GAAG,GAAG,gBAAgBD,MAAM,EAAE;IACpC,OAAO,IAAI,CAACb,WAAW,CAACG,OAAO,CAAC,KAAK,EAAEW,GAAG,EAAE,IAAI,CAAC;EACnD;EAEAC,kBAAkBA,CAACC,QAAgB;IACjC,MAAMH,MAAM,GAAGI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,MAAMJ,GAAG,GAAG,eAAeD,MAAM,IAAIG,QAAQ,EAAE;IAC/C,OAAO,IAAI,CAAChB,WAAW,CAACG,OAAO,CAAC,QAAQ,EAAEW,GAAG,EAAE,IAAI,CAAC;EACtD;EAEAK,oBAAoBA,CAACN,MAAc,EAAEG,QAAgB;IACnD,MAAMF,GAAG,GAAG,qBAAqBD,MAAM,IAAIG,QAAQ,EAAE;IACrD,OAAO,IAAI,CAAChB,WAAW,CAACG,OAAO,CAAC,MAAM,EAAEW,GAAG,EAAE,IAAI,CAAC;EACpD;EAEFM,mBAAmBA,CAACC,WAAmB,EAACC,QAAgB;IACpD,MAAMR,GAAG,GAAG,kCAAkCO,WAAW,IAAIC,QAAQ,EAAE;IACvE,OAAO,IAAI,CAACtB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAEW,GAAG,EAAE,IAAI,CAAC;EACnD;EAEAS,qBAAqBA,CAAA;IACnB,MAAMV,MAAM,GAAGI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIL,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI,CAACb,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,6BAA6BU,MAAM,EAAE,EAAE,IAAI,CAAC;KACnF,MAAI;MACH,OAAOnB,EAAE,CAAC,IAAI,CAAC;;EAEnB;EAEA8B,oBAAoBA,CAAA;IAClB,MAAMX,MAAM,GAAGI,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIL,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI,CAACb,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,sCAAsCU,MAAM,EAAE,EAAE,IAAI,CAAC;KAC5F,MAAI;MACH,OAAOnB,EAAE,CAAC,IAAI,CAAC;;EAEnB;EAAC,QAAA+B,CAAA,G;qBApEU9B,aAAa,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbnC,aAAa;IAAAoC,OAAA,EAAbpC,aAAa,CAAAqC,IAAA;IAAAC,UAAA,EAFZ;EAAM;;SAEPtC,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}