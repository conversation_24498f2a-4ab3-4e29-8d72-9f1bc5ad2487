package com.navitsa.mydent.services;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import com.navitsa.mydent.entity.Laboratory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.Doctor;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.DoctorRepository;

@Service
public class DoctorService {
	
    @Autowired
    private DoctorRepository doctorRepository;
    
	@Autowired
    private EmailService emailService;

    public Doctor saveDoctor(Doctor doctor) {
        try {
            if (doctorRepository.findByRegNo(doctor.getRegNo()).isPresent()) {
                throw new RuntimeException("SLMC number with " + doctor.getRegNo() + " already exists.");
            }
            
Doctor savedDoctor = doctorRepository.save(doctor);
            
           	User user = savedDoctor.getUserId();
            String verificationToken = user.getVerificationToken();
            String verificationTokenWithUserType = verificationToken + "&userType=Doctor";
            System.out.println("token: " + verificationTokenWithUserType);

            CompletableFuture.runAsync(() ->
                    emailService.sendRegistrationEmail(
                    	savedDoctor.getEmail(),
                    	savedDoctor.getFirstName(),
                        "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType,
                        "Doctor"
                    ));
            
            return savedDoctor;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the doctor.");
        }
    }

    public Doctor save(Doctor doctor){
        if (doctorRepository.findByRegNo(doctor.getRegNo()).isPresent()) {
            throw new RuntimeException("Doctor with " + doctor.getRegNo() + " already exists.");
        }
        return doctorRepository.save(doctor);
    }

    public List<Doctor> findAllDoctors() {
        try {
            return (List<Doctor>) doctorRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of doctors.");
        }
    }

    public Doctor getDoctorById(int id) {
        return doctorRepository.findById(id).orElseThrow(() -> 
            new RuntimeException("Doctor not found with id: " + id));
    }

    public Doctor updateDoctor(int id, Doctor doctorDetails) {
        try {
            Doctor doctor = doctorRepository.findById(id).orElse(null);
            if (doctor == null) {
                throw new RuntimeException("Doctor not found with id: " + id);
            }

            doctor.setFirstName(doctorDetails.getFirstName());
            doctor.setLastName(doctorDetails.getLastName());
            doctor.setTitle(doctorDetails.getTitle());
            doctor.setQualifications(doctorDetails.getQualifications());
            doctor.setTelephone(doctorDetails.getTelephone());
            doctor.setEmail(doctorDetails.getEmail());
            doctor.setRegNo(doctorDetails.getRegNo());

            return doctorRepository.save(doctor);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the doctor.");
        }
    }

    public void deleteDoctor(int id) {
        try {
            doctorRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the doctor.");
        }
    }

    public boolean slmcNumberExists(String regNo) {
        Optional<Doctor> existingNumber = doctorRepository.findByRegNo(regNo);
        return existingNumber.isPresent();
    }
}
