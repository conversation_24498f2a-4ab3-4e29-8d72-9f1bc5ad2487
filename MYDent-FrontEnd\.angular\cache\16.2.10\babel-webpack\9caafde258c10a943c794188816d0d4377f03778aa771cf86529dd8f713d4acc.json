{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass UserService {\n  constructor(http) {\n    this.http = http;\n    this.baseURL = environment.apiUrl;\n  }\n  getAuthToken() {\n    return window.localStorage.getItem('auth_token');\n  }\n  setAuthToken(token) {\n    if (token !== null) {\n      window.localStorage.setItem('auth_token', token);\n    } else {\n      window.localStorage.removeItem('auth_token');\n    }\n  }\n  request(method, url, data, params, responseType = 'json') {\n    let headers = new HttpHeaders();\n    if (this.getAuthToken() !== null) {\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\n    }\n    const options = {\n      headers: headers,\n      params: new HttpParams({\n        fromObject: params\n      }),\n      responseType: responseType\n    };\n    switch (method.toUpperCase()) {\n      case 'GET':\n        return this.http.get(this.baseURL + url, options);\n      case 'POST':\n        return this.http.post(this.baseURL + url, data, options);\n      case 'PUT':\n        return this.http.put(this.baseURL + url, data, options);\n      case 'DELETE':\n        return this.http.delete(this.baseURL + url, options);\n      default:\n        throw new Error('Unsupported HTTP method');\n    }\n  }\n  register(signUpDto) {\n    return this.request('POST', `/register`, signUpDto);\n  }\n  login(user) {\n    return this.request('POST', `/login`, user);\n  }\n  checkUser(username) {\n    const params = {\n      username\n    };\n    return this.request('GET', `/check-username`, null, params);\n  }\n  //User Categories\n  getUserCategoryList() {\n    return this.request('GET', '/userCategoryList', {});\n  }\n  getUserCategoryById(id) {\n    return this.request('GET', `/getUserCategoryById/${id}`, {});\n  }\n  verifyEmail(token, userType) {\n    return this.request('GET', `/verify`, null, {\n      token,\n      userType\n    });\n  }\n  verifyEmailForget(email) {\n    return this.request('POST', `/forget_password`, null, {\n      email\n    });\n  }\n  verifyEmailAllUsers(token) {\n    return this.request('GET', `/verify/forgetPassword`, null, {\n      token\n    });\n  }\n  changePassword(password, token) {\n    return this.request('POST', `/ChangePassword`, null, {\n      password,\n      token\n    });\n  }\n  static #_ = this.ɵfac = function UserService_Factory(t) {\n    return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UserService,\n    factory: UserService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { UserService };", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "environment", "UserService", "constructor", "http", "baseURL", "apiUrl", "getAuthToken", "window", "localStorage", "getItem", "setAuthToken", "token", "setItem", "removeItem", "request", "method", "url", "data", "params", "responseType", "headers", "set", "options", "fromObject", "toUpperCase", "get", "post", "put", "delete", "Error", "register", "signUpDto", "login", "user", "checkUser", "username", "getUserCategoryList", "getUserCategoryById", "id", "verifyEmail", "userType", "verifyEmailForget", "email", "verifyEmailAllUsers", "changePassword", "password", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { SignUpDto, User, UserCategory } from './user';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class UserService {\r\n  private readonly baseURL = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getAuthToken(): string | null {\r\n    return window.localStorage.getItem('auth_token');\r\n  }\r\n\r\n  setAuthToken(token: string | null): void {\r\n    if (token !== null) {\r\n      window.localStorage.setItem('auth_token', token);\r\n    } else {\r\n      window.localStorage.removeItem('auth_token');\r\n    }\r\n  }\r\n\r\n  request(\r\n    method: string,\r\n    url: string,\r\n    data: any,\r\n    params?: any,\r\n    responseType: 'json' | 'text' = 'json'\r\n  ): Observable<any> {\r\n    let headers = new HttpHeaders();\r\n\r\n    if (this.getAuthToken() !== null) {\r\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\r\n    }\r\n\r\n    const options: any = {\r\n      headers: headers,\r\n      params: new HttpParams({ fromObject: params }),\r\n      responseType: responseType as 'json' | 'text',\r\n    };\r\n\r\n    switch (method.toUpperCase()) {\r\n      case 'GET':\r\n        return this.http.get(this.baseURL + url, options);\r\n      case 'POST':\r\n        return this.http.post(this.baseURL + url, data, options);\r\n      case 'PUT':\r\n        return this.http.put(this.baseURL + url, data, options);\r\n      case 'DELETE':\r\n        return this.http.delete(this.baseURL + url, options);\r\n      default:\r\n        throw new Error('Unsupported HTTP method');\r\n    }\r\n  }\r\n\r\n  register(signUpDto: SignUpDto): Observable<any> {\r\n    return this.request('POST', `/register`, signUpDto);\r\n  }\r\n\r\n  login(user: User): Observable<any> {\r\n    return this.request('POST', `/login`, user);\r\n  }\r\n\r\n  checkUser(username: string): Observable<any> {\r\n    const params = { username };\r\n    return this.request('GET', `/check-username`, null, params);\r\n  }\r\n\r\n  //User Categories\r\n  getUserCategoryList(): Observable<UserCategory[]> {\r\n    return this.request('GET', '/userCategoryList', {});\r\n  }\r\n\r\n  getUserCategoryById(id: number): Observable<UserCategory> {\r\n    return this.request('GET', `/getUserCategoryById/${id}`, {});\r\n  }\r\n\r\n  verifyEmail(token: string, userType: String): Observable<string> {\r\n    return this.request('GET', `/verify`, null, { token, userType });\r\n  }\r\n\r\n  verifyEmailForget(email: string): Observable<string> {\r\n    return this.request('POST', `/forget_password`, null, {email});\r\n  }\r\n\r\n   verifyEmailAllUsers(token: string): Observable<string> {\r\n    return this.request('GET', `/verify/forgetPassword`, null, { token });\r\n  }\r\n\r\n  changePassword(password: string,token : string): Observable<string> {\r\n    return this.request('POST', `/ChangePassword`, null, {password, token});\r\n  }\r\n\r\n}\r\n\r\n"], "mappings": "AAAA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAG1E,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;EAEN;EAEvCC,YAAYA,CAAA;IACV,OAAOC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAClD;EAEAC,YAAYA,CAACC,KAAoB;IAC/B,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBJ,MAAM,CAACC,YAAY,CAACI,OAAO,CAAC,YAAY,EAAED,KAAK,CAAC;KACjD,MAAM;MACLJ,MAAM,CAACC,YAAY,CAACK,UAAU,CAAC,YAAY,CAAC;;EAEhD;EAEAC,OAAOA,CACLC,MAAc,EACdC,GAAW,EACXC,IAAS,EACTC,MAAY,EACZC,YAAA,GAAgC,MAAM;IAEtC,IAAIC,OAAO,GAAG,IAAItB,WAAW,EAAE;IAE/B,IAAI,IAAI,CAACQ,YAAY,EAAE,KAAK,IAAI,EAAE;MAChCc,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,SAAS,GAAG,IAAI,CAACf,YAAY,EAAE,CAAC;;IAGzE,MAAMgB,OAAO,GAAQ;MACnBF,OAAO,EAAEA,OAAO;MAChBF,MAAM,EAAE,IAAInB,UAAU,CAAC;QAAEwB,UAAU,EAAEL;MAAM,CAAE,CAAC;MAC9CC,YAAY,EAAEA;KACf;IAED,QAAQJ,MAAM,CAACS,WAAW,EAAE;MAC1B,KAAK,KAAK;QACR,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAC,IAAI,CAACrB,OAAO,GAAGY,GAAG,EAAEM,OAAO,CAAC;MACnD,KAAK,MAAM;QACT,OAAO,IAAI,CAACnB,IAAI,CAACuB,IAAI,CAAC,IAAI,CAACtB,OAAO,GAAGY,GAAG,EAAEC,IAAI,EAAEK,OAAO,CAAC;MAC1D,KAAK,KAAK;QACR,OAAO,IAAI,CAACnB,IAAI,CAACwB,GAAG,CAAC,IAAI,CAACvB,OAAO,GAAGY,GAAG,EAAEC,IAAI,EAAEK,OAAO,CAAC;MACzD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACnB,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACxB,OAAO,GAAGY,GAAG,EAAEM,OAAO,CAAC;MACtD;QACE,MAAM,IAAIO,KAAK,CAAC,yBAAyB,CAAC;;EAEhD;EAEAC,QAAQA,CAACC,SAAoB;IAC3B,OAAO,IAAI,CAACjB,OAAO,CAAC,MAAM,EAAE,WAAW,EAAEiB,SAAS,CAAC;EACrD;EAEAC,KAAKA,CAACC,IAAU;IACd,OAAO,IAAI,CAACnB,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAEmB,IAAI,CAAC;EAC7C;EAEAC,SAASA,CAACC,QAAgB;IACxB,MAAMjB,MAAM,GAAG;MAAEiB;IAAQ,CAAE;IAC3B,OAAO,IAAI,CAACrB,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAEI,MAAM,CAAC;EAC7D;EAEA;EACAkB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACtB,OAAO,CAAC,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC;EACrD;EAEAuB,mBAAmBA,CAACC,EAAU;IAC5B,OAAO,IAAI,CAACxB,OAAO,CAAC,KAAK,EAAE,wBAAwBwB,EAAE,EAAE,EAAE,EAAE,CAAC;EAC9D;EAEAC,WAAWA,CAAC5B,KAAa,EAAE6B,QAAgB;IACzC,OAAO,IAAI,CAAC1B,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE;MAAEH,KAAK;MAAE6B;IAAQ,CAAE,CAAC;EAClE;EAEAC,iBAAiBA,CAACC,KAAa;IAC7B,OAAO,IAAI,CAAC5B,OAAO,CAAC,MAAM,EAAE,kBAAkB,EAAE,IAAI,EAAE;MAAC4B;IAAK,CAAC,CAAC;EAChE;EAECC,mBAAmBA,CAAChC,KAAa;IAChC,OAAO,IAAI,CAACG,OAAO,CAAC,KAAK,EAAE,wBAAwB,EAAE,IAAI,EAAE;MAAEH;IAAK,CAAE,CAAC;EACvE;EAEAiC,cAAcA,CAACC,QAAgB,EAAClC,KAAc;IAC5C,OAAO,IAAI,CAACG,OAAO,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE;MAAC+B,QAAQ;MAAElC;IAAK,CAAC,CAAC;EACzE;EAAC,QAAAmC,CAAA,G;qBAtFU7C,WAAW,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXlD,WAAW;IAAAmD,OAAA,EAAXnD,WAAW,CAAAoD,IAAA;IAAAC,UAAA,EAFV;EAAM;;SAEPrD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}