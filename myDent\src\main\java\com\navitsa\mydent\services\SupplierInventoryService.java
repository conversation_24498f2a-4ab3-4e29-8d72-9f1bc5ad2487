package com.navitsa.mydent.services;

import java.util.Collections;
import java.util.List;
import java.util.Base64;
import java.nio.charset.StandardCharsets;

import com.navitsa.mydent.dtos.ClinicSupplierInventoryItemDto;
import com.navitsa.mydent.entity.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.navitsa.mydent.entity.SupplierInventory;
import com.navitsa.mydent.repositories.SupplierInventoryRepository;


@Service
public class SupplierInventoryService {
	
	@Autowired
	private SupplierInventoryRepository supplierInventoryRepository;


	public SupplierInventory saveSupplierInventory(SupplierInventory supplierInventory) {
		try {
			return supplierInventoryRepository.save(supplierInventory);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("An error occurred while saving the Inventory item.");
		}
	}

//	public SupplierInventory saveSupplierInventory(SupplierInventory supplierInventory) {
//		try {
//			// Check if an image is provided
//			if (supplierInventory.getItemImage() != null && supplierInventory.getItemImage().length > 0) {
//				// Convert byte array to String (assuming the byte array represents Base64 encoded data)
//				String base64Image = new String(supplierInventory.getItemImage(), StandardCharsets.UTF_8);
//
//				// Check if the string contains the MIME type prefix (data:image/png;base64,)
//				if (base64Image.startsWith("data:image")) {
//					base64Image = base64Image.split(",")[1]; // Remove metadata part
//				}
//
//				// Now we have pure Base64 string without the MIME part
//				byte[] decodedImage = decodeBase64(base64Image);
//				if (decodedImage != null) {
//					supplierInventory.setItemImage(decodedImage);  // Store the decoded byte[] as image
//				} else {
//					throw new IllegalArgumentException("Invalid Base64 string for image");
//				}
//			}
//
//			// Save the entity
//			return supplierInventoryRepository.save(supplierInventory);
//		} catch (IllegalArgumentException e) {
//			// Print error message and stack trace for invalid image data
//			System.err.println("Error: " + e.getMessage());
//			e.printStackTrace();
//			throw new RuntimeException("Invalid image data: " + e.getMessage(), e);
//		} catch (Exception e) {
//			// Print error message and stack trace for any other errors
//			System.err.println("An error occurred while saving the Inventory item: " + e.getMessage());
//			e.printStackTrace();
//			throw new RuntimeException("An error occurred while saving the Inventory item: " + e.getMessage(), e);
//		}
//	}
//
//	/**
//	 * Utility method to decode Base64 string into byte array.
//	 * It returns null if the string is invalid.
//	 */
//	private byte[] decodeBase64(String base64String) {
//		try {
//			// Try decoding the Base64 string
//			return Base64.getDecoder().decode(base64String);
//		} catch (IllegalArgumentException e) {
//			// Return null in case of an invalid Base64 string
//			System.err.println("Base64 decoding failed: " + e.getMessage());
//			return null;
//		}
//	}
//
//	/**
//	 * Utility method to check if a string is a valid Base64 encoded string.
//	 * This method assumes that the string should be a valid Base64 string without the MIME type prefix.
//	 */
//	private boolean isBase64(String str) {
//		try {
//			Base64.getDecoder().decode(str);
//			return true;
//		} catch (IllegalArgumentException e) {
//			// Return false if decoding fails
//			return false;
//		}
//	}
//
//






//	public SupplierInventory saveSupplierInventory(SupplierInventory supplierInventory) {
//		try {
//			return supplierInventoryRepository.save(supplierInventory);
//		} catch (Exception e) {
//			e.printStackTrace();
//			throw new RuntimeException("An error occurred while saving the Inventory item.");
//		}
//	}

	public List<SupplierInventory> findAllSupplierInventories() {

		try {
			return (List<SupplierInventory>) supplierInventoryRepository.findAll();
		} catch (Exception e) {
			e.printStackTrace(); // Logging the exception stack trace (replace with your logging framework)
			throw new RuntimeException("An error occurred while retrieving the list of Inventory items."); // Example: Throwing a custom exception
		}
	}
	
	
	public SupplierInventory getSupplierInventoryById(int id) {
		return supplierInventoryRepository.findById(id).orElse(null);
	}

	public List<Supplier> getSupplierByCategoryName(String categoryName) {
        return supplierInventoryRepository.getSupplierInventoriesByCategory(categoryName).orElse(Collections.emptyList());
	}

	public List<ClinicSupplierInventoryItemDto> getInventoryItemsByCategoryNameAndSupplierId(String categoryName, Integer supplierId) {
		return supplierInventoryRepository.getInventoriesByCategoryNameAndSupplierId(categoryName,supplierId).orElse(Collections.emptyList());
	}

}
