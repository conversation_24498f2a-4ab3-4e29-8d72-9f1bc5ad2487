<app-default-navbar loggedUser="Test Appointment"/>

<div style="padding-bottom: 50px;">
  <div class="appointment-container">
    <div class="header py-1" style="display: flex; justify-content: center; align-items: center; width: 100%;">
      <span style="text-align: center;">MyDent Dental Consultations With Certified Professionals! Book Your
        Appointment Now!</span>
    </div>

    <div class="content py-1">
      <div class="navigation mb-4">
        <h2 class="mb-3 header1">Make Appointment</h2>
        <div class="navigation-bar">
          <div class="navigation-step" *ngFor="let step of steps; let i = index"
            [ngClass]="{'active': currentStep === i + 1}">
            <div class="dot"></div>
            <span>{{ step }}</span>
          </div>
        </div>
      </div>
      <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()" style="padding-inline: 20px;">
        <div *ngIf="currentStep === 1">
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="firstName" class="form-label">First Name</label>
              <input type="text" formControlName="firstName" id="firstName" class="form-control">
              <div
                *ngIf="appointmentForm.get('firstName')?.invalid && (appointmentForm.get('firstName')?.dirty || appointmentForm.get('firstName')?.touched)"
                class="text-danger">
                <div *ngIf="appointmentForm.get('firstName')?.errors?.['required']">First Name is required.
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <label for="lastName" class="form-label">Last Name</label>
              <input type="text" formControlName="lastName" id="lastName" class="form-control">
              <div
                *ngIf="appointmentForm.get('lastName')?.invalid && (appointmentForm.get('lastName')?.dirty || appointmentForm.get('lastName')?.touched)"
                class="text-danger">
                <div *ngIf="appointmentForm.get('lastName')?.errors?.['required']">Last Name is required.
                </div>
              </div>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="address" class="form-label">Address</label>
              <input type="text" formControlName="address" id="address" class="form-control">
              <div
                *ngIf="appointmentForm.get('address')?.invalid && (appointmentForm.get('address')?.dirty || appointmentForm.get('address')?.touched)"
                class="text-danger">
                <div *ngIf="appointmentForm.get('address')?.errors?.['required']">Address is required.</div>
                <div *ngIf="appointmentForm.get('address')?.errors?.['minlength']">Address must be at least
                  10 characters long.</div>
              </div>
            </div>
            <div class="col-md-6">
              <label for="email" class="form-label">Email</label>
              <input type="email" formControlName="email" id="email" class="form-control">
              <div
                *ngIf="appointmentForm.get('email')?.invalid && (appointmentForm.get('email')?.dirty || appointmentForm.get('email')?.touched)"
                class="text-danger">
                <div *ngIf="appointmentForm.get('email')?.errors?.['required']">Email is required.</div>
                <div *ngIf="appointmentForm.get('email')?.errors?.['email']">Invalid email format.</div>
                <div *ngIf="appointmentForm.get('email')?.errors?.['emailExists']">{{ userEmailExistsMessage
                  }}</div>
              </div>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="district" class="form-label">District</label>
              <div class="custom-arrow">
                <select name="district" id="district" formControlName="district" [(ngModel)]="appointments.state"
                  (change)="onDistrictChange($event)">
                  <option value="" disabled selected>Select</option>
                  <option *ngFor="let district of districts" [value]="district">
                    {{ district }}
                  </option>
                </select>
              </div>
              <div *ngIf="
                            appointmentForm.get('district')?.invalid &&
                            (appointmentForm.get('district')?.dirty || appointmentForm.get('district')?.touched)
                        ">
                <small class="text-danger" *ngIf="appointmentForm.get('district')?.errors?.['required']">
                  District is required.
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <label for="city" class="form-label">City</label>
              <div class="custom-arrow">
                <select id="city" class="form-control" formControlName="city" [(ngModel)]="selectedCity"
                  (change)="onCitySelect()">
                  <option value="" disabled selected>Select</option>
                  <option *ngFor="let city of cities" [value]="city">
                    {{ city }}
                  </option>
                </select>
              </div>
              <div *ngIf="
                            appointmentForm.get('city')?.invalid &&
                            (appointmentForm.get('city')?.dirty || appointmentForm.get('city')?.touched)
                            ">
                <small class="text-danger" *ngIf="appointmentForm.get('city')?.errors?.['required']">
                  City is required.
                </small>
              </div>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="telephone" class="form-label">Telephone</label>
              <input type="tel" formControlName="telephone" id="telephone" class="form-control">
              <div
                *ngIf="appointmentForm.get('telephone')?.invalid && (appointmentForm.get('telephone')?.dirty || appointmentForm.get('telephone')?.touched)"
                class="text-danger">
                <div *ngIf="appointmentForm.get('telephone')?.errors?.['required']">Telephone is required.
                </div>
                <div *ngIf="appointmentForm.get('telephone')?.errors?.['pattern']">Invalid telephone number
                  format.</div>
              </div>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="password" class="form-label">Password</label>
              <div class="form-group position-relative" style="position: relative;">
                <input
                  type="{{ passwordVisible ? 'text' : 'password' }}"
                  style="width: 125%;"
                  formControlName="password"
                  id="password"
                  class="form-control"
                  [ngClass]="{'is-invalid': appointmentForm.get('password')?.invalid && (appointmentForm.get('password')?.dirty || appointmentForm.get('password')?.touched)}">

                <!-- Password visibility toggle button -->
                <button
                  type="button"
                  class="show-password"
                  (click)="togglePasswordVisibility()"
                  style="
                  position: absolute;
                  right: -35px;
                  top: 50%;
                  transform: translateY(-50%);
                  padding: 0;
                  border: none;
                  background: none;
                  color: #6c757d;
                  cursor: pointer;
                  ">
                  <i [ngClass]="passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'"></i>
                </button>

                <!-- Error messages for validation -->
                <div *ngIf="appointmentForm.get('password')?.invalid && (appointmentForm.get('password')?.dirty || appointmentForm.get('password')?.touched)" class="text-danger warnings">
                  <div *ngIf="appointmentForm.get('password')?.errors?.['required']">
                    Password is required.
                  </div>
                  <div *ngIf="appointmentForm.get('password')?.errors?.['minlength']">
                    Password must be at least {{ appointmentForm.get('password')?.errors?.['minlength'].requiredLength }} characters long.
                  </div>
                  <div *ngIf="appointmentForm.get('password')?.errors?.['pattern']">
                    Password must include at least one uppercase letter, one lowercase letter, and one number.
                  </div>
                </div>
              </div>
            </div>


            <div class="col-md-6">
              <label for="confirm-password" class="form-label">Confirm Password</label>
              <input type="password"
                     formControlName="confirmPassword"
                     id="confirmPassword"
                     class="form-control"
                     placeholder="Re-enter your password"/>
              <div *ngIf="appointmentForm.get('confirmPassword')?.invalid &&
                (appointmentForm.get('confirmPassword')?.dirty ||
                 appointmentForm.get('confirmPassword')?.touched)"
                   class="text-danger">
                <div *ngIf="appointmentForm.get('confirmPassword')?.errors?.['required']">
                  Re-enter the password.
                </div>
                <div *ngIf="appointmentForm.get('confirmPassword')?.errors?.['mismatch'] &&
                  appointmentForm.get('confirmPassword')?.dirty">
                  Passwords do not match.
                </div>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-between btn-field">
            <!-- <button type="button" class="btn btn-secondary" [disabled]="currentStep === 1" (click)="prevStep()">Previous</button> -->
            <!-- Use (click) event to trigger nextStep() -->
            <button type="button" class="btn btn-primary first-next" (click)="nextStep()"
              [disabled]="isStepInvalid()">Next</button>
          </div>
        </div>
        <div *ngIf="currentStep === 2">
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="preferredservice" class="form-label">Preferred Service</label>
              <div class="custom-arrow">
                <select formControlName="preferredservice" id="preferredservice" class="form-control" required>
                  <option value="" selected disabled>Select</option>
                  <option value="1">Dental Bonding</option>
                  <option value="2">Cosmetic Fillings</option>
                  <option value="3">Invisalign</option>
                  <option value="4">Teeth Cleanings</option>
                  <option value="5">Root Canal Therapy</option>
                  <option value="6">Dental Sealants</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <label for="city" class="form-label">City</label>
              <div class="custom-arrow">
                <select id="nearestCity" class="form-control" formControlName="city">
                  <option value="" disabled selected>Select</option>
                  <option *ngFor="let city of cities" [value]="city">
                    {{ city }}
                  </option>
                </select>
              </div>
              <div *ngIf="
                            appointmentForm.get('city')?.invalid &&
                            (appointmentForm.get('city')?.dirty || appointmentForm.get('city')?.touched)
                            ">
                <small class="text-danger" *ngIf="appointmentForm.get('city')?.errors?.['required']">
                  City is required.
                </small>
              </div>
            </div>

          </div>

          <div class="row mb-3">
            <label for="preferredDate" class="form-label">Preferred Date</label>
            <div class="row">
              <div class="col-md-6">
                <input type="date" formControlName="fromDate" id="fromDate" class="form-control" [min]="currentDate"
                  required>
                <div
                  *ngIf="appointmentForm.get('fromDate')?.invalid && (appointmentForm.get('fromDate')?.dirty || appointmentForm.get('fromDate')?.touched)"
                  class="text-danger">
                  <div *ngIf="appointmentForm.get('fromDate')?.errors?.['required']">Date From is
                    required.</div>
                  <div *ngIf="appointmentForm.get('fromDate')?.errors?.['pastDate']">Date From cannot be
                    in the past.</div>
                </div>
              </div>
            </div>
          </div>

          <div class="row mb-3">
            <label for="preferredTime" class="form-label">Preferred Time</label>
            <div class="row">
              <div class="col-md-6">
                <label for="fromTime" class="form-label">From</label>
                <input type="time" formControlName="fromTime" id="fromTime" class="form-control"
                  [disabled]="isImmediateBooking" required>
                <div
                  *ngIf="appointmentForm.get('fromTime')?.invalid && (appointmentForm.get('fromTime')?.dirty || appointmentForm.get('fromTime')?.touched)"
                  class="text-danger">
                  <div *ngIf="appointmentForm.get('fromTime')?.errors?.['required']">Preferred Time From
                    is required.</div>
                </div>
              </div>
              <div class="col-md-6">
                <label for="toTime" class="form-label">To</label>
                <input type="time" formControlName="toTime" id="toTime" class="form-control"
                  [disabled]="isImmediateBooking" required>
                <div
                  *ngIf="appointmentForm.get('toTime')?.invalid && (appointmentForm.get('toTime')?.dirty || appointmentForm.get('toTime')?.touched)"
                  class="text-danger">
                  <div *ngIf="appointmentForm.get('toTime')?.errors?.['required']">Preferred Time To is
                    required.</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Immediate Booking Checkbox -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-check">
                <input type="checkbox" formControlName="immediate" id="immediate" class="form-check-input">
                <label for="immediate" class="form-check-label">Immediate Booking</label>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-between btn-field">
            <button type="button" class="btn btn-secondary" (click)="prevStep()">Previous</button>
            <button type="button" class="btn btn-primary second-next" (click)="cliniclistload(); nextStep()"
              [disabled]="isStepInvalid()">Find Your Nearest Dental Clinic</button>
          </div>
        </div>

        <div *ngIf="currentStep === 3">
          <h4 class="mb-4">Please select nearest clinic:</h4>
          <div class="clinic-list mb-4">
            <table class="table">
              <tbody>
                <tr *ngFor="let clinic of clinics" [ngClass]="{'selected-clinic': selectedClinic === clinic.clinicId}"
                  (click)="selectClinic(clinic.clinicId)">
                  <td>{{ clinic.name }}</td>
                  <td>{{ clinic.city}}</td>
                  <td class="text-end">
                    <span class="text-primary" *ngIf="selectedClinic !== clinic.clinicId">Book Now</span>
                    <span class="badge bg-success" *ngIf="selectedClinic === clinic.clinicId">Selected</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="d-flex justify-content-between">
            <button type="button" class="btn btn-secondary" (click)="prevStep()">Previous</button>
            <button type="submit" class="btn btn-primary" [disabled]="!selectedClinic">Request For
              The Appointment</button>
          </div>
        </div>

        <div *ngIf="currentStep === 4">
          <div class="text-center">
            <h4 class="mb-4">Appointment Submission</h4>
            <div class="appointment-success mb-4">
              <i class="bi bi-check-circle-fill text-success display-4"></i>
            </div>
            <p class="mb-4">Your Appointment Submission is Sent to Approval Now. <br> Please Check Your
              Notifications or Bookings.</p>
            <div class="d-flex justify-content-center d-none">
              <button type="button" class="btn btn-secondary me-3" (click)="cancelAppointment()">Cancel</button>
              <button type="button" class="btn btn-primary" (click)="saveAppointment()">Done</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

</div>
