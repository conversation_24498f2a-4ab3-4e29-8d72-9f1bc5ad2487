{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { User } from '../user';\nimport Swal from 'sweetalert2'; // Import SweetAlert\nlet UserLoginComponent = class UserLoginComponent {\n  constructor(router, userService, cookieService) {\n    this.router = router;\n    this.userService = userService;\n    this.cookieService = cookieService;\n    this.username = '';\n    this.password = '';\n    this.user = new User();\n    this.remember = false;\n    this.passwordVisible = false;\n  }\n  ngOnInit() {\n    const encodedData = this.cookieService.get('authData');\n    if (encodedData) {\n      try {\n        const decoded = atob(encodedData);\n        const [token, username, routerPath] = decoded.split('::');\n        console.log('Decoded Cookie Data:', {\n          token,\n          username,\n          routerPath\n        });\n        if (username) {\n          this.username = username;\n          this.remember = true; // checked only if cookie exists\n        }\n\n        if (token && routerPath) {\n          this.router.navigate(['/' + routerPath]);\n        }\n      } catch (e) {\n        console.error('Invalid cookie format');\n        this.remember = false; // reset if cookie invalid\n      }\n    } else {\n      this.remember = false; // no cookie → checkbox unchecked\n    }\n  }\n\n  togglePasswordVisibility() {\n    this.passwordVisible = !this.passwordVisible;\n    const passwordField = document.getElementById('password');\n    passwordField.type = this.passwordVisible ? 'text' : 'password';\n  }\n  onSubmitLogin() {\n    localStorage.clear();\n    this.onLogin();\n  }\n  onLogin() {\n    this.userService.login(this.user).subscribe(response => {\n      console.log('Login Successful', response); // Log the successful response\n      // Set the auth token after successful login\n      this.userService.setAuthToken(response.token);\n      // Log the complete response to check for clinicId\n      console.log('API Response:', response.username);\n      // Store the full user object and other relevant data\n      localStorage.setItem('userid', response.id.toString());\n      localStorage.setItem('firstName', response.firstName || '');\n      localStorage.setItem('lastName', response.lastName || '');\n      localStorage.setItem('companyId', response.companyId || '');\n      localStorage.setItem('username', response.username || '');\n      console.table(response);\n      // ✅ Combine token, username, and routerPath into one string\n      const combined = `${response.token}::${response.username}::${response.userCategoryId?.routerPath || ''}`;\n      // ✅ Encode it using Base64 to hide content\n      const encoded = btoa(combined);\n      // ✅ Save cookie (persistent if Remember Me checked)\n      if (this.remember) {\n        this.cookieService.set('authData', encoded, 30, '/'); // 30 days, path = /\n        console.log('Cookie Set:', this.cookieService.get('authData'));\n      }\n      if (response.userCategoryId != null && response.userCategoryId.routerPath != null) {\n        this.router.navigate(['/' + response.userCategoryId.routerPath]);\n      }\n    }, error => {\n      // Handle login failure\n      this.userService.setAuthToken(null);\n      // Display a user-friendly error message based on the error response\n      const errorMessage = error.error?.message || 'Login Failed: Invalid username or password.';\n      // Use SweetAlert instead of alert\n      Swal.fire({\n        icon: 'error',\n        title: 'Login Failed',\n        text: errorMessage,\n        confirmButtonText: 'OK'\n      });\n      console.log('Login Failed', error); // Log the full error details for further debugging\n    });\n  }\n\n  logout() {\n    localStorage.removeItem('authToken');\n    this.cookieService.delete('authData', '/');\n    // Reset Remember Me checkbox\n    this.remember = false;\n    localStorage.clear();\n    this.router.navigate(['/home-page']);\n  }\n  navigateToUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n};\nUserLoginComponent = __decorate([Component({\n  selector: 'app-user-login',\n  templateUrl: './user-login.component.html',\n  styleUrls: ['./user-login.component.css']\n})], UserLoginComponent);\nexport { UserLoginComponent };", "map": {"version": 3, "names": ["Component", "User", "<PERSON><PERSON>", "UserLoginComponent", "constructor", "router", "userService", "cookieService", "username", "password", "user", "remember", "passwordVisible", "ngOnInit", "encodedData", "get", "decoded", "atob", "token", "routerPath", "split", "console", "log", "navigate", "e", "error", "togglePasswordVisibility", "passwordField", "document", "getElementById", "type", "onSubmitLogin", "localStorage", "clear", "onLogin", "login", "subscribe", "response", "setAuthToken", "setItem", "id", "toString", "firstName", "lastName", "companyId", "table", "combined", "userCategoryId", "encoded", "btoa", "set", "errorMessage", "message", "fire", "icon", "title", "text", "confirmButtonText", "logout", "removeItem", "delete", "navigateToUserSelection", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-login\\user-login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from '../user.service';\r\nimport { User } from '../user';\r\nimport Swal from 'sweetalert2'; // Import SweetAlert\r\nimport { CookieService } from 'ngx-cookie-service';\r\n\r\n@Component({\r\n  selector: 'app-user-login',\r\n  templateUrl: './user-login.component.html',\r\n  styleUrls: ['./user-login.component.css'],\r\n})\r\nexport class UserLoginComponent {\r\n  username: string = '';\r\n  password: string = '';\r\n  user: User = new User();\r\n  remember: boolean = false;\r\n  passwordVisible: boolean = false;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private cookieService: CookieService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const encodedData = this.cookieService.get('authData');\r\n\r\n    if (encodedData) {\r\n      try {\r\n        const decoded = atob(encodedData);\r\n        const [token, username, routerPath] = decoded.split('::');\r\nconsole.log('Decoded Cookie Data:', { token, username, routerPath });\r\n        if (username) {\r\n          this.username = username;\r\n          this.remember = true; // checked only if cookie exists\r\n        }\r\n\r\n        if (token && routerPath) {\r\n          this.router.navigate(['/' + routerPath]);\r\n        }\r\n      } catch (e) {\r\n        console.error('Invalid cookie format');\r\n        this.remember = false; // reset if cookie invalid\r\n      }\r\n    } else {\r\n      this.remember = false; // no cookie → checkbox unchecked\r\n    }\r\n  }\r\n\r\n  togglePasswordVisibility() {\r\n    this.passwordVisible = !this.passwordVisible;\r\n    const passwordField = document.getElementById(\r\n      'password'\r\n    ) as HTMLInputElement;\r\n    passwordField.type = this.passwordVisible ? 'text' : 'password';\r\n  }\r\n\r\n  onSubmitLogin() {\r\n    localStorage.clear();\r\n    this.onLogin();\r\n  }\r\n\r\n  onLogin() {\r\n    this.userService.login(this.user).subscribe(\r\n      (response) => {\r\n        console.log('Login Successful', response); // Log the successful response\r\n        // Set the auth token after successful login\r\n        this.userService.setAuthToken(response.token);\r\n\r\n        // Log the complete response to check for clinicId\r\n        console.log('API Response:', response.username);\r\n\r\n        // Store the full user object and other relevant data\r\n        localStorage.setItem('userid', response.id.toString());\r\n        localStorage.setItem('firstName', response.firstName || '');\r\n        localStorage.setItem('lastName', response.lastName || '');\r\n        localStorage.setItem('companyId', response.companyId || '');\r\n        localStorage.setItem('username', response.username || '');\r\n        console.table(response);\r\n\r\n        // ✅ Combine token, username, and routerPath into one string\r\n        const combined = `${response.token}::${response.username}::${\r\n          response.userCategoryId?.routerPath || ''\r\n        }`;\r\n\r\n        // ✅ Encode it using Base64 to hide content\r\n        const encoded = btoa(combined);\r\n\r\n        // ✅ Save cookie (persistent if Remember Me checked)\r\n        if (this.remember) {\r\n          this.cookieService.set('authData', encoded, 30, '/'); // 30 days, path = /\r\n          console.log('Cookie Set:', this.cookieService.get('authData'));\r\n        }\r\n\r\n        if (\r\n          response.userCategoryId != null &&\r\n          response.userCategoryId.routerPath != null\r\n        ) {\r\n          this.router.navigate(['/' + response.userCategoryId.routerPath]);\r\n        }\r\n      },\r\n      (error) => {\r\n        // Handle login failure\r\n        this.userService.setAuthToken(null);\r\n\r\n        // Display a user-friendly error message based on the error response\r\n        const errorMessage =\r\n          error.error?.message || 'Login Failed: Invalid username or password.';\r\n\r\n        // Use SweetAlert instead of alert\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Login Failed',\r\n          text: errorMessage,\r\n          confirmButtonText: 'OK',\r\n        });\r\n\r\n        console.log('Login Failed', error); // Log the full error details for further debugging\r\n      }\r\n    );\r\n  }\r\n\r\n  public logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    this.cookieService.delete('authData', '/');\r\n    \r\n    // Reset Remember Me checkbox\r\n    this.remember = false;\r\n\r\n    localStorage.clear();\r\n    this.router.navigate(['/home-page']);\r\n  }\r\n\r\n  navigateToUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAGzC,SAASC,IAAI,QAAQ,SAAS;AAC9B,OAAOC,IAAI,MAAM,aAAa,CAAC,CAAC;AAQhC,IAAaC,kBAAkB,GAA/B,MAAaA,kBAAkB;EAO7BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,aAA4B;IAF5B,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,IAAI,GAAS,IAAIT,IAAI,EAAE;IACvB,KAAAU,QAAQ,GAAY,KAAK;IACzB,KAAAC,eAAe,GAAY,KAAK;EAM7B;EAEHC,QAAQA,CAAA;IACN,MAAMC,WAAW,GAAG,IAAI,CAACP,aAAa,CAACQ,GAAG,CAAC,UAAU,CAAC;IAEtD,IAAID,WAAW,EAAE;MACf,IAAI;QACF,MAAME,OAAO,GAAGC,IAAI,CAACH,WAAW,CAAC;QACjC,MAAM,CAACI,KAAK,EAAEV,QAAQ,EAAEW,UAAU,CAAC,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;QACjEC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAEJ,KAAK;UAAEV,QAAQ;UAAEW;QAAU,CAAE,CAAC;QAC5D,IAAIX,QAAQ,EAAE;UACZ,IAAI,CAACA,QAAQ,GAAGA,QAAQ;UACxB,IAAI,CAACG,QAAQ,GAAG,IAAI,CAAC,CAAC;;;QAGxB,IAAIO,KAAK,IAAIC,UAAU,EAAE;UACvB,IAAI,CAACd,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAGJ,UAAU,CAAC,CAAC;;OAE3C,CAAC,OAAOK,CAAC,EAAE;QACVH,OAAO,CAACI,KAAK,CAAC,uBAAuB,CAAC;QACtC,IAAI,CAACd,QAAQ,GAAG,KAAK,CAAC,CAAC;;KAE1B,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,KAAK,CAAC,CAAC;;EAE3B;;EAEAe,wBAAwBA,CAAA;IACtB,IAAI,CAACd,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,MAAMe,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAC3C,UAAU,CACS;IACrBF,aAAa,CAACG,IAAI,GAAG,IAAI,CAAClB,eAAe,GAAG,MAAM,GAAG,UAAU;EACjE;EAEAmB,aAAaA,CAAA;IACXC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,OAAO,EAAE;EAChB;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC5B,WAAW,CAAC6B,KAAK,CAAC,IAAI,CAACzB,IAAI,CAAC,CAAC0B,SAAS,CACxCC,QAAQ,IAAI;MACXhB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEe,QAAQ,CAAC,CAAC,CAAC;MAC3C;MACA,IAAI,CAAC/B,WAAW,CAACgC,YAAY,CAACD,QAAQ,CAACnB,KAAK,CAAC;MAE7C;MACAG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,QAAQ,CAAC7B,QAAQ,CAAC;MAE/C;MACAwB,YAAY,CAACO,OAAO,CAAC,QAAQ,EAAEF,QAAQ,CAACG,EAAE,CAACC,QAAQ,EAAE,CAAC;MACtDT,YAAY,CAACO,OAAO,CAAC,WAAW,EAAEF,QAAQ,CAACK,SAAS,IAAI,EAAE,CAAC;MAC3DV,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACM,QAAQ,IAAI,EAAE,CAAC;MACzDX,YAAY,CAACO,OAAO,CAAC,WAAW,EAAEF,QAAQ,CAACO,SAAS,IAAI,EAAE,CAAC;MAC3DZ,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAAC7B,QAAQ,IAAI,EAAE,CAAC;MACzDa,OAAO,CAACwB,KAAK,CAACR,QAAQ,CAAC;MAEvB;MACA,MAAMS,QAAQ,GAAG,GAAGT,QAAQ,CAACnB,KAAK,KAAKmB,QAAQ,CAAC7B,QAAQ,KACtD6B,QAAQ,CAACU,cAAc,EAAE5B,UAAU,IAAI,EACzC,EAAE;MAEF;MACA,MAAM6B,OAAO,GAAGC,IAAI,CAACH,QAAQ,CAAC;MAE9B;MACA,IAAI,IAAI,CAACnC,QAAQ,EAAE;QACjB,IAAI,CAACJ,aAAa,CAAC2C,GAAG,CAAC,UAAU,EAAEF,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACtD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACf,aAAa,CAACQ,GAAG,CAAC,UAAU,CAAC,CAAC;;MAGhE,IACEsB,QAAQ,CAACU,cAAc,IAAI,IAAI,IAC/BV,QAAQ,CAACU,cAAc,CAAC5B,UAAU,IAAI,IAAI,EAC1C;QACA,IAAI,CAACd,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAGc,QAAQ,CAACU,cAAc,CAAC5B,UAAU,CAAC,CAAC;;IAEpE,CAAC,EACAM,KAAK,IAAI;MACR;MACA,IAAI,CAACnB,WAAW,CAACgC,YAAY,CAAC,IAAI,CAAC;MAEnC;MACA,MAAMa,YAAY,GAChB1B,KAAK,CAACA,KAAK,EAAE2B,OAAO,IAAI,6CAA6C;MAEvE;MACAlD,IAAI,CAACmD,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAEL,YAAY;QAClBM,iBAAiB,EAAE;OACpB,CAAC;MAEFpC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC,CACF;EACH;;EAEOiC,MAAMA,CAAA;IACX1B,YAAY,CAAC2B,UAAU,CAAC,WAAW,CAAC;IACpC,IAAI,CAACpD,aAAa,CAACqD,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;IAE1C;IACA,IAAI,CAACjD,QAAQ,GAAG,KAAK;IAErBqB,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC5B,MAAM,CAACkB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAsC,uBAAuBA,CAAA;IACrB,IAAI,CAACxD,MAAM,CAACkB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;CACD;AA7HYpB,kBAAkB,GAAA2D,UAAA,EAL9B9D,SAAS,CAAC;EACT+D,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACW9D,kBAAkB,CA6H9B;SA7HYA,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}