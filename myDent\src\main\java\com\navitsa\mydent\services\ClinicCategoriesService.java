//package com.navitsa.mydent.services;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import com.navitsa.mydent.entity.Company;
//import com.navitsa.mydent.repositories.CompanyRepository;
//
//@Service
//public class ClinicCategoriesService {
//	
//	@Autowired
//	private CompanyRepository companyRepository;
//	
//	
//	public Company saveCompany(Company company) {
//		 try {
//		       // Get the current date and time
//				SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
//				SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm:ss");
//
//				Date currentDate = new Date();
//				String formattedDate = dateFormatter.format(currentDate);
//				String formattedTime = timeFormatter.format(currentDate);
//
//				company.setStatus("ACTIVE");
//				
//				company.setCreatedDate(formattedDate);
//				company.setCreatedTime(formattedTime);
//
//				company.setUpdatedUser(0);
//				company.setUpdatedDate("Not Yet Updated");
//				company.setUpdatedTime("Not Yet Updated");
//				
//				
//		        return companyRepository.save(company);
//		        
//		 } catch (Exception e) {
//		        // Handle the exception here
//		        // You can log the exception, return a specific error response, or take other appropriate actions
//		        e.printStackTrace(); // Logging the exception stack trace (replace with your logging framework)
//		        throw new RuntimeException("An error occurred while saving the company."); // Example: Throwing a custom exception
//		    }	
//	}
//
//
//	public List<Company> findAllCompanies() {
//		try {
//			return (List<Company>) companyRepository.findAll();
//		 } catch (Exception e) {
//		        // Handle the exception here
//		        // You can log the exception, return an empty list, return a specific error response, or take other appropriate actions
//		        e.printStackTrace(); // Logging the exception stack trace (replace with your logging framework)
//		        throw new RuntimeException("An error occurred while retrieving the list of Companies."); // Example: Throwing a custom exception
//		    }
//	}
//	
//	public Company getCompanyById(int id) {
//
//		return companyRepository.findById(id).get();
//		
//	}
//	
//	public Company updateCompany(int id, Company companyDetails) {
//	    try {
//	    	Company company = companyRepository.findById(id).orElse(null);
//
//	        if (company == null) {
//	            // Handle the case where the vehicle type with the given id is not found
//	            throw new RuntimeException("Company not found with id: " + id);
//	        }
//
//	        SimpleDateFormat dateFormatter = new SimpleDateFormat("dd-MM-yyyy");
//	        SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm:ss");
//
//	        Date currentDate = new Date();
//
//	        // Format the date and time separately
//	        String formattedDate = dateFormatter.format(currentDate);
//	        String formattedTime = timeFormatter.format(currentDate);
//
//	        // Update the properties with details from the request
//	        company.setCompanyName(companyDetails.getCompanyName());
//	        company.setAddress(companyDetails.getAddress());
//	        company.setCity(companyDetails.getCity());
//	        company.setCountry(companyDetails.getCountry());
//	        company.setState(companyDetails.getState());
//	        company.setPostalCode(companyDetails.getPostalCode());
//	        company.setTelephone(companyDetails.getTelephone());
//	        company.setEmail(companyDetails.getEmail());
//	        company.setWeb(companyDetails.getWeb());
//	        company.setStatus(companyDetails.getStatus());
//
//	        company.setUpdatedUser(companyDetails.getUpdatedUser());
//	        company.setUpdatedDate(formattedDate);
//	        company.setUpdatedTime(formattedTime);
//
//	        return companyRepository.save(company);
//	    } catch (Exception e) {
//	        // Handle the exception here
//	        // You can log the exception, return a specific error response, or take other appropriate actions
//	        e.printStackTrace(); // Logging the exception stack trace (replace with your logging framework)
//	        throw new RuntimeException("An error occurred while updating the company."); // Example: Throwing a custom exception
//	    }
//	}
//
//}
