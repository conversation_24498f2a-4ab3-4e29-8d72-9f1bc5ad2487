{"ast": null, "code": "import { ClinicRoutingModule } from './clinic-routing.module';\nimport { ClinicLayoutComponent } from './clinic-layout/clinic-layout.component';\nimport { ClinicSideBarComponent } from './components/clinic-side-bar/clinic-side-bar.component';\nimport { ClinicNavbarComponent } from './components/clinic-navbar/clinic-navbar.component';\nimport { ClinicOrdersComponent } from './clinic-orders/clinic-orders.component';\nimport { OrderDetailsPopupComponent } from './components/order-details-popup/order-details-popup.component';\nimport { AcceptPopupComponent } from './components/accept-popup/accept-popup.component';\nimport { ClinicAddLaboratorOrderComponent } from './clinic-add-laborator-order/clinic-add-laborator-order.component';\nimport { ClinicLaboratoryOrderListComponent } from './clinic-laboratory-order-list/clinic-laboratory-order-list.component';\nimport { AddClinicServiceComponent } from './add-clinic-service/add-clinic-service.component';\nimport { ClinicSelectLaboratorySetupComponent } from './clinic-select-laboratory-setup/clinic-select-laboratory-setup.component';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatTableModule } from '@angular/material/table';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { ClinicDashboardComponent } from './clinic-dashboard/clinic-dashboard.component';\nimport { ClinicSupplierOrderComponent } from './clinic-supplier-order/clinic-supplier-order.component';\nimport { ClinicSetupClinicComponent } from './clinic-setup-clinic/clinic-setup-clinic.component';\nimport { ClinicSupplierOrderListComponent } from './clinic-supplier-order-list/clinic-supplier-order-list.component';\nimport { CoreModule } from \"../core/core.module\";\nimport { AppointmentDetailsDialogComponent } from './clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';\nimport { ClinicNewAppointmentComponent } from './clinic-new-appointment/clinic-new-appointment.component';\nimport { DoctorSelectComponent } from './clinic-dashboard/appointment-popup/doctor-select/doctor-select.component';\nimport * as i0 from \"@angular/core\";\nclass ClinicModule {\n  static #_ = this.ɵfac = function ClinicModule_Factory(t) {\n    return new (t || ClinicModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ClinicModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ClinicRoutingModule, MatPaginatorModule, MatProgressSpinnerModule, MatTableModule, FormsModule, ReactiveFormsModule, CoreModule]\n  });\n}\nexport { ClinicModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ClinicModule, {\n    declarations: [ClinicLayoutComponent, ClinicSideBarComponent, ClinicNavbarComponent, ClinicOrdersComponent, ClinicAddLaboratorOrderComponent, OrderDetailsPopupComponent, AcceptPopupComponent, ClinicLaboratoryOrderListComponent, AddClinicServiceComponent, ClinicSelectLaboratorySetupComponent, ClinicDashboardComponent, ClinicSupplierOrderComponent, ClinicSetupClinicComponent, ClinicSupplierOrderListComponent, AppointmentDetailsDialogComponent, ClinicNewAppointmentComponent, DoctorSelectComponent],\n    imports: [CommonModule, ClinicRoutingModule, MatPaginatorModule, MatProgressSpinnerModule, MatTableModule, FormsModule, ReactiveFormsModule, CoreModule]\n  });\n})();", "map": {"version": 3, "names": ["ClinicRoutingModule", "ClinicLayoutComponent", "ClinicSideBarComponent", "ClinicNavbarComponent", "ClinicOrdersComponent", "OrderDetailsPopupComponent", "AcceptPopupComponent", "ClinicAddLaboratorOrderComponent", "ClinicLaboratoryOrderListComponent", "AddClinicServiceComponent", "ClinicSelectLaboratorySetupComponent", "MatPaginatorModule", "MatProgressSpinnerModule", "MatTableModule", "FormsModule", "ReactiveFormsModule", "CommonModule", "ClinicDashboardComponent", "ClinicSupplierOrderComponent", "ClinicSetupClinicComponent", "ClinicSupplierOrderListComponent", "CoreModule", "AppointmentDetailsDialogComponent", "ClinicNewAppointmentComponent", "DoctorSelectComponent", "ClinicModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\n\r\nimport { ClinicRoutingModule } from './clinic-routing.module';\r\nimport { ClinicLayoutComponent } from './clinic-layout/clinic-layout.component';\r\nimport { ClinicSideBarComponent } from './components/clinic-side-bar/clinic-side-bar.component';\r\nimport { ClinicNavbarComponent } from './components/clinic-navbar/clinic-navbar.component';\r\nimport { ClinicOrdersComponent } from './clinic-orders/clinic-orders.component';\r\nimport { OrderDetailsPopupComponent } from './components/order-details-popup/order-details-popup.component';\r\nimport { AcceptPopupComponent } from './components/accept-popup/accept-popup.component';\r\nimport { ClinicAddLaboratorOrderComponent } from './clinic-add-laborator-order/clinic-add-laborator-order.component';\r\nimport { ClinicLaboratoryOrderListComponent } from './clinic-laboratory-order-list/clinic-laboratory-order-list.component';\r\nimport { AddClinicServiceComponent } from './add-clinic-service/add-clinic-service.component';\r\nimport { ClinicSelectLaboratorySetupComponent } from './clinic-select-laboratory-setup/clinic-select-laboratory-setup.component';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ClinicDashboardComponent } from './clinic-dashboard/clinic-dashboard.component';\r\nimport { ClinicSupplierOrderComponent } from './clinic-supplier-order/clinic-supplier-order.component';\r\nimport { ClinicSetupClinicComponent } from './clinic-setup-clinic/clinic-setup-clinic.component';\r\nimport { ClinicSupplierOrderListComponent } from './clinic-supplier-order-list/clinic-supplier-order-list.component';\r\nimport { CoreModule } from \"../core/core.module\";\r\nimport { AppointmentDetailsDialogComponent } from './clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';\r\nimport { ClinicNewAppointmentComponent } from './clinic-new-appointment/clinic-new-appointment.component';\r\nimport { DoctorSelectComponent } from './clinic-dashboard/appointment-popup/doctor-select/doctor-select.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ClinicLayoutComponent,\r\n    ClinicSideBarComponent,\r\n    ClinicNavbarComponent,\r\n    ClinicOrdersComponent,\r\n    ClinicAddLaboratorOrderComponent,\r\n    OrderDetailsPopupComponent,\r\n    AcceptPopupComponent,\r\n    ClinicLaboratoryOrderListComponent,\r\n    AddClinicServiceComponent,\r\n    ClinicSelectLaboratorySetupComponent,\r\n    ClinicDashboardComponent,\r\n    ClinicSupplierOrderComponent,\r\n    ClinicSetupClinicComponent,\r\n    ClinicSupplierOrderListComponent,\r\n    AppointmentDetailsDialogComponent,\r\n    ClinicNewAppointmentComponent,\r\n    DoctorSelectComponent\r\n\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ClinicRoutingModule,\r\n    MatPaginatorModule,\r\n    MatProgressSpinnerModule,\r\n    MatTableModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CoreModule\r\n],\r\n})\r\nexport class ClinicModule {}\r\n"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,0BAA0B,QAAQ,gEAAgE;AAC3G,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,gCAAgC,QAAQ,mEAAmE;AACpH,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,oCAAoC,QAAQ,2EAA2E;AAChI,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,gCAAgC,QAAQ,mEAAmE;AACpH,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,iCAAiC,QAAQ,sGAAsG;AACxJ,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,qBAAqB,QAAQ,4EAA4E;;AAGlH,MAgCaC,YAAY;EAAA,QAAAC,CAAA,G;qBAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA,G;UAAZF;EAAY;EAAA,QAAAG,EAAA,G;cAVrBZ,YAAY,EACZhB,mBAAmB,EACnBW,kBAAkB,EAClBC,wBAAwB,EACxBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBM,UAAU;EAAA;;SAGDI,YAAY;;2EAAZA,YAAY;IAAAI,YAAA,GA9BrB5B,qBAAqB,EACrBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBG,gCAAgC,EAChCF,0BAA0B,EAC1BC,oBAAoB,EACpBE,kCAAkC,EAClCC,yBAAyB,EACzBC,oCAAoC,EACpCO,wBAAwB,EACxBC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gCAAgC,EAChCE,iCAAiC,EACjCC,6BAA6B,EAC7BC,qBAAqB;IAAAM,OAAA,GAIrBd,YAAY,EACZhB,mBAAmB,EACnBW,kBAAkB,EAClBC,wBAAwB,EACxBC,cAAc,EACdC,WAAW,EACXC,mBAAmB,EACnBM,UAAU;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}