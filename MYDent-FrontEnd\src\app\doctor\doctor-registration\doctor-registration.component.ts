import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { map, mapTo, Observable, of, tap } from 'rxjs';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { UserService } from 'src/app/user/user.service';
import { User, UserCategory } from 'src/app/user/user';
import { Doctor } from 'src/app/doctor/doctor';
import { DoctorService } from 'src/app/doctor/doctor.service';
import Swal from 'sweetalert2';
import { AuthService } from 'src/app/auth/auth.service';
import { UserTemp, UserTempType } from 'src/app/auth/auth';

@Component({
  selector: 'app-doctor-registration',
  templateUrl: './doctor-registration.component.html',
  styleUrls: ['./doctor-registration.component.css'],
})
export class DoctorRegistrationComponent implements OnInit,AfterViewInit {
  doctor: Doctor = new Doctor();
  user: User = new User();
  userCategory: UserCategory = new UserCategory();
  doctorForm: FormGroup;
  isEmailRegistered: boolean = false;
  isSLMCRegistered: boolean = false;
  userEmailExistsMessage: string = '';
  slmcNumberExistsMessage: string = '';

  // User temp
  protected userTemp: UserTemp = new UserTemp();
  @ViewChild('RegisterButton') registerButton!: ElementRef<HTMLButtonElement>;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private doctorService: DoctorService,
    private authService: AuthService,
    private userService: UserService
  ) {
    this.doctorForm = this.fb.group(
      {
        title: ['', Validators.required],
        firstName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z]*$')],
        ],
        lastName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z]*$')],
        ],
        regNo: ['', Validators.required],
        telephone: [
          '',
          [Validators.required, Validators.pattern('^[0-9]{10}$')],
        ],
        email: ['', [Validators.required, Validators.email]],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.pattern('^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\W).*$'),
          ],
        ],
        confirmPassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  ngOnInit(): void {
    localStorage.clear();
    this.getUserCategoryFromDB();
    this.userTemp.userTempType = UserTempType.DOCTOR;
  }

  ngAfterViewInit(): void {
  }

  private getUserCategoryFromDB() {
    this.userService.getUserCategoryById(2).subscribe((response) => {
      this.userCategory = response;
    });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (
      password?.value &&
      confirmPassword?.value &&
      (confirmPassword.dirty || confirmPassword.touched)
    ) {
      return password.value === confirmPassword.value
        ? null
        : { mismatch: true };
    }
    return null;
  }

  updateEmail() {
    this.user.username = this.doctor.email;
  }

  setFirstName() {
    this.user.firstName = this.doctor.firstName;
  }

  setLastName() {
    this.user.lastName = this.doctor.lastName;
  }

  onUserRegister(): Observable<void> {
    if (this.userCategory == null) {
      this.getUserCategoryFromDB();
    }
    this.user.userCategoryId = this.userCategory;
    console.table(this.user);

    return this.userService.register(this.user).pipe(
      tap(
        (response) => {
          this.user.userId = response.id;
          this.doctor.userId = this.user;
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }

  onDoctorRegister(): Observable<void> {
    return this.doctorService.saveDoctor(this.doctor).pipe(
      tap(
        () => {
          Swal.fire({
            title: 'Registration Successful!',
            text: 'Thank you for registering! Please verify your email to complete the login process.',
            icon: 'success',
            confirmButtonText: 'Continue',
          }).then((confirmation) => {
            if (confirmation.isConfirmed) {
              this.router.navigate(['/user-login']);
            }
          });
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }

  // onSubmitRegister() {
  //   if (this.doctorForm.invalid) {
  //     this.doctorForm.markAllAsTouched();
  //     return;
  //   }
  //   this.checkRegisterNumber().subscribe((isSLMCRegistered) => {
  //     if (!isSLMCRegistered) {
  //       this.checkUserEmail().subscribe((isEmailRegistered) => {
  //         if (!isEmailRegistered) {
  //           this.onUserRegister().subscribe(() => {
  //             this.onDoctorRegister().subscribe(() => {
  //               console.log('Doctor registered successfully');
  //             });
  //           });
  //         }
  //       });
  //     }
  //   });
  // }

  onSubmitRegister() {
    Swal.fire({
      title: "Wait until approval!",
      text: "Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.",
      icon: 'success',
      confirmButtonText: 'OK',
    });
    // if (this.doctorForm.invalid) {
    //   this.doctorForm.markAllAsTouched();
    //   return;
    // }
    // this.checkUserEmail().subscribe((isEmailRegistered) => {
    //   if (!isEmailRegistered) {
    //     this.onUserRegister().subscribe(() => {
    //       this.onDoctorRegister().subscribe(() => {
    //         console.log('Doctor registered successfully');
    //       });
    //     });
    //   }
    // });
  }

    // UserTemp Saving
    onUserTempRegister() {
      console.log("Working............");

      if (this.doctorForm.invalid) {
        this.doctorForm.markAllAsTouched();
        return;
      }

      console.log("Working Here............");


      // Disable the register button and show a loading indicator
      this.registerButton.nativeElement.disabled = true;
      this.registerButton.nativeElement.innerHTML = `<img src="/assets/icons/more-30.png" />`;

      // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times
      // this.authService
      //   .checkUserTempAvailability(this.userTemp.userEmail)
      //   .subscribe((resp) => {

      //     if (resp !=null) {
      //       Swal.fire({
      //         title: 'Registration Already Exists!',
      //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',
      //         icon: 'info',
      //         confirmButtonText: 'OK',
      //       });

      //       // Reset the button state
      //       this.registerButton.nativeElement.disabled = false;
      //       this.registerButton.nativeElement.innerHTML = 'Register';
      //       return;
      //     }

          this.authService.saveUserTemp(this.userTemp).subscribe(
            (userTempSaved: UserTemp) => {
              console.log('Full userTempSaved object:', userTempSaved);

              const receivedUserTemp: UserTemp = userTempSaved;
              let title = 'Registration Completed!';
              let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';
              let iconName:
                | 'success'
                | 'info'
                | 'error'
                | 'warning'
                | 'question' = 'success';

              if (!receivedUserTemp) {
                title = 'Registration Failed!';
                message ='An error occurred while registering. Please try again.';
                iconName = 'error';
              }

              Swal.fire({
                title: title,
                text: message,
                icon: iconName,
                confirmButtonText: 'OK',
              });

              // Reset button state
              this.registerButton.nativeElement.disabled = false;
              this.registerButton.nativeElement.innerHTML = 'Register';
            },
            (error) => {
              Swal.fire({
                title: 'Registration Failed!',
                text: 'An error occurred during registration. Please try again later.',
                icon: 'error',
                confirmButtonText: 'OK',
              });

              this.registerButton.nativeElement.disabled = false;
              this.registerButton.nativeElement.innerHTML = 'Register';
            }
          );
        // }); // COMMENTED OUT FOR TESTING
    }


  checkUserEmail(): Observable<boolean> {
    if (this.doctorForm.get('email')?.valid) {
      const userEmail = this.doctorForm.get('email')?.value;
      return this.userService.checkUser(userEmail).pipe(
        map((data) => {
          if (data) {
            this.isEmailRegistered = true;
            this.userEmailExistsMessage =
              'Email already registered. Try another.';
          } else {
            this.isEmailRegistered = false;
            this.userEmailExistsMessage = '';
          }
          return this.isEmailRegistered;
        })
      );
    } else {
      this.isEmailRegistered = false;
      this.userEmailExistsMessage = '';
      return of(this.isEmailRegistered);
    }
  }

  checkRegisterNumber(): Observable<boolean> {
    if (this.doctorForm.get('regNo')?.valid) {
      const regNo = this.doctorForm.get('regNo')?.value;
      return this.doctorService.checkSlmcNumber(regNo).pipe(
        map((data) => {
          if (data) {
            this.isSLMCRegistered = true;
            this.slmcNumberExistsMessage =
              'That number already registered. Try another.';
          } else {
            this.isSLMCRegistered = false;
            this.slmcNumberExistsMessage = '';
          }
          return this.isSLMCRegistered;
        })
      );
    } else {
      this.isSLMCRegistered = false;
      this.slmcNumberExistsMessage = '';
      return of(this.isSLMCRegistered);
    }
  }

  navigateUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}

