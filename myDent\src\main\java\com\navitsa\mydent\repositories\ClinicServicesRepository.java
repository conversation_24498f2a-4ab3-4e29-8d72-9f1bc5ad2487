package com.navitsa.mydent.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.navitsa.mydent.entity.ClinicServices;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;


public interface ClinicServicesRepository extends JpaRepository<ClinicServices, Integer>  {
	
	List<ClinicServices> findByClinics_ClinicId(Integer clinicId);

    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END " +
            "FROM ClinicServices c " +
            "WHERE c.clinics.clinicId = :clinicId " +
            "AND c.services.clinicServiceCategoryId = :serviceId")
    boolean existsByClinicIdAndServiceId(@Param("clinicId") int clinicId,
                                         @Param("serviceId") int serviceId);

}
