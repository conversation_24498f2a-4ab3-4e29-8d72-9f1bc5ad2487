package com.navitsa.mydent.entity;

import com.navitsa.mydent.enums.UserTempStatus;
import com.navitsa.mydent.enums.UserTempType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_temp")
public class UserTemp implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_temp_id")
    private Long userTempId;

    @NotNull
    @Column(name = "main_name")
    private String mainName;

    @Column(name = "additional_name")
    private String additionalName;

    @Column(name = "user_email")
    private String userEmail;

    @Column(name = "user_password")
    private String userPassword;

    @Column(name = "user_type")
    private UserTempType userTempType;

    @Column(name = "user_temp_status")
    private UserTempStatus userTempStatus;

    @Column(name = "title")
    private String userTitle;

    @Column(name = "contact_number")
    private String contactNumber;

    @Column(name = "contact_person")
    private String contactPerson;

    @Column(name = "contact_person_designation")
    private String contactPersonDesignation;

    @Column(name = "registration_number")
    private String registrationNumber;

    @Column(name = "address")
    private String address;

    @Column(name = "district")
    private String district;

    @Column(name = "city")
    private String city;

    @Column(name = "state")
    private String state;

    @Column(name = "create_date_time")
    private LocalDateTime createDateTime;

    @Column(name = "verification_token")
    private String verificationToken;

    public UserTemp() {
    }

    public UserTemp(String mainName, String additionalName, String userEmail, String userPassword, UserTempType userTempType, UserTempStatus userTempStatus, String userTitle, String contactNumber, String contactPerson, String contactPersonDesignation, String registrationNumber, String address, String district, String city, String state, LocalDateTime createDateTime, String verificationToken) {
        this.mainName = mainName;
        this.additionalName = additionalName;
        this.userEmail = userEmail;
        this.userPassword = userPassword;
        this.userTempType = userTempType;
        this.userTempStatus = userTempStatus;
        this.userTitle = userTitle;
        this.contactNumber = contactNumber;
        this.contactPerson = contactPerson;
        this.contactPersonDesignation = contactPersonDesignation;
        this.registrationNumber = registrationNumber;
        this.address = address;
        this.district = district;
        this.city = city;
        this.state = state;
        this.createDateTime = createDateTime;
        this.verificationToken = verificationToken;
    }

    public UserTemp(Long userTempId, String mainName, String additionalName, String userEmail, String userPassword, UserTempType userTempType, UserTempStatus userTempStatus, String userTitle, String contactNumber, String contactPerson, String contactPersonDesignation, String registrationNumber, String address, String district, String city, String state, LocalDateTime createDateTime, String verificationToken) {
        this.userTempId = userTempId;
        this.mainName = mainName;
        this.additionalName = additionalName;
        this.userEmail = userEmail;
        this.userPassword = userPassword;
        this.userTempType = userTempType;
        this.userTempStatus = userTempStatus;
        this.userTitle = userTitle;
        this.contactNumber = contactNumber;
        this.contactPerson = contactPerson;
        this.contactPersonDesignation = contactPersonDesignation;
        this.registrationNumber = registrationNumber;
        this.address = address;
        this.district = district;
        this.city = city;
        this.state = state;
        this.createDateTime = createDateTime;
        this.verificationToken = verificationToken;
    }

    public Long getUserTempId() {
        return userTempId;
    }

    public void setUserTempId(Long userTempId) {
        this.userTempId = userTempId;
    }

    public String getMainName() {
        return mainName;
    }

    public void setMainName(String mainName) {
        this.mainName = mainName;
    }

    public String getAdditionalName() {
        return additionalName;
    }

    public void setAdditionalName(String additionalName) {
        this.additionalName = additionalName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPassword() {
        return userPassword;
    }

    public void setUserPassword(String userPassword) {
        this.userPassword = userPassword;
    }

    public UserTempType getUserTempType() {
        return userTempType;
    }

    public void setUserTempType(UserTempType userTempType) {
        this.userTempType = userTempType;
    }

    public UserTempStatus getUserTempStatus() {
        return userTempStatus;
    }

    public void setUserTempStatus(UserTempStatus userTempStatus) {
        this.userTempStatus = userTempStatus;
    }

    public String getUserTitle() {
        return userTitle;
    }

    public void setUserTitle(String userTitle) {
        this.userTitle = userTitle;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPersonDesignation() {
        return contactPersonDesignation;
    }

    public void setContactPersonDesignation(String contactPersonDesignation) {
        this.contactPersonDesignation = contactPersonDesignation;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public String getVerificationToken() {
        return verificationToken;
    }

    public void setVerificationToken(String verificationToken) {
        this.verificationToken = verificationToken;
    }
}
