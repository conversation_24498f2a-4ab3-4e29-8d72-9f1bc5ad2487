package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.AppointmentsRepository;
import com.navitsa.mydent.repositories.ClinicRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class ClinicService {


    private final ClinicRepository clinicRepository;
    private final AppointmentsRepository appointmentsRepository;

    @Autowired
    public ClinicService(ClinicRepository clinicRepository,AppointmentsRepository appointmentsRepository) {
        this.clinicRepository = clinicRepository;
        this.appointmentsRepository = appointmentsRepository;
    }
    
	@Autowired
    private EmailService emailService;

    // Save Clinic
    public Clinic saveClinic(Clinic clinic) {
        try {

            if (clinicRepository.findByName(clinic.getName()).isPresent()) {
                throw new RuntimeException("Clinic with '" + clinic.getName() + "' already exists.");
            }

            SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
            Date currentDate = new Date();
            String formattedDate = dateFormatter.format(currentDate);

            clinic.setRegisteredDate(formattedDate);
                       
            Clinic savedClinic = clinicRepository.save(clinic);
            
           	User user = savedClinic.getUserId();
            String verificationToken = user.getVerificationToken();
            String verificationTokenWithUserType = verificationToken + "&userType=Clinic"; 
            System.out.println("token: " + verificationTokenWithUserType);
            
            CompletableFuture.runAsync(() -> 
                    emailService.sendRegistrationEmail(
                        clinic.getEmail(),
                        clinic.getName(),
                        "http://localhost:4200/user/verify?token=" + verificationTokenWithUserType,
                        "Clinic"
                    ));
            
            return savedClinic;
            
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the Clinic.");
        }
    }

    public Clinic save(Clinic clinic){
        if (clinicRepository.findByName(clinic.getName()).isPresent()) {
            throw new RuntimeException("Clinic with " + clinic.getName() + " already exists.");
        }
        return clinicRepository.save(clinic);
    }

    // Get all Clinics
    public List<Clinic> findAllClinics() {
        try {
            return clinicRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of clinics.");
        }
    }

    // Get Clinic by ID
    public Clinic getClinicById(int id) {
        try {
            return clinicRepository.findById(id).orElse(null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the clinic.");
        }
    }

    // Update Clinic
    public Clinic updateClinic(int id, Clinic clinicDetails) {
        try {
            Clinic clinic = clinicRepository.findById(id).orElse(null);
            if (clinic == null) {
                throw new RuntimeException("Clinic not found with id: " + id);
            }
            clinic.setName(clinicDetails.getName());
            clinic.setAddress(clinicDetails.getAddress());
            clinic.setCity(clinicDetails.getCity());
            clinic.setState(clinicDetails.getState());
            clinic.setCountry(clinicDetails.getCountry());
            clinic.setContactPerson(clinicDetails.getContactPerson());
            clinic.setTele(clinicDetails.getTele());
            clinic.setEmail(clinicDetails.getEmail());
            clinic.setWeb(clinicDetails.getWeb());
            clinic.setRegisteredDate(clinicDetails.getRegisteredDate());
            clinic.setLatitude(clinicDetails.getLatitude());
            clinic.setLongitude(clinicDetails.getLongitude());

            return clinicRepository.save(clinic);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the clinic.");
        }
    }

    // Delete Clinic
    public void deleteClinic(int id) {
        try {
            clinicRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the clinic.");
        }
    }

    public boolean clinicNameExists(String clinicName){
        Optional<Clinic> existingClinic = clinicRepository.findByName(clinicName);
        return existingClinic.isPresent();
    }

    public List<Appointments> getPendingAppointments(Integer userId){
        Optional<List<Appointments>> allPendingAppointmentsByUserId = appointmentsRepository.getAllPendingAppointmentsByUserId(userId);
        return allPendingAppointmentsByUserId.orElse(null);
    }

    public Integer findClinicIdByUserId(Integer userId) {
        return clinicRepository.findClinicIdByUserId(userId);
    }
    
    public List<Clinic> findClinics(String date, String time, String city, Integer serviceId) {
        return clinicRepository.findClinicsByTimeDateCityAndService(date, time, city, serviceId);
    }

}
