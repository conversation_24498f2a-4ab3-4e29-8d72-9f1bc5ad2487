package com.navitsa.mydent.controller;

import java.util.List;

import com.navitsa.mydent.dtos.DoctorClinicDto;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.services.ClinicDoctorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.navitsa.mydent.entity.Doctor;
import com.navitsa.mydent.services.DoctorService;

@RestController
public class DoctorController {

	private final DoctorService doctorService;
	private final ClinicDoctorService clinicDoctorService;

	@Autowired
	public DoctorController(DoctorService doctorService, ClinicDoctorService clinicDoctorService) {
		this.doctorService = doctorService;
		this.clinicDoctorService = clinicDoctorService;
	}

	@PostMapping("/saveDoctor")
	public Doctor saveDoctor(@RequestBody Doctor doctor) {
		return doctorService.saveDoctor(doctor);
	}

	@GetMapping("/doctorList")
	public List<Doctor> getAllDoctors() {
		return doctorService.findAllDoctors();
	}

	@GetMapping("/getDoctorById/{id}")
	public Doctor getDoctorById(@PathVariable int id) {
		return doctorService.getDoctorById(id);
	}

	@PutMapping("/updateDoctor/{id}")
	public ResponseEntity<Doctor> updateDoctor(@PathVariable int id, @RequestBody Doctor doctorDetails) {
		return ResponseEntity.ok(doctorService.updateDoctor(id, doctorDetails));
	}

	@DeleteMapping("/deleteDoctor/{id}")
	public ResponseEntity<Void> deleteDoctor(@PathVariable int id) {
		doctorService.deleteDoctor(id);
		return ResponseEntity.noContent().build();
	}

	@GetMapping("/checkSlmcNumber")
	public ResponseEntity<Boolean> checkSlmcNumberExists(@RequestParam String regNo) {
		boolean isSlmcNumberExists = doctorService.slmcNumberExists(regNo);
		return ResponseEntity.ok(isSlmcNumberExists);
	}

	@GetMapping("/getClinicsListByDoctorId/{userId}")
	public ResponseEntity<List<DoctorClinicDto>> getClinicList(@PathVariable Integer userId) {
		System.out.println("Request Came : "+userId);
		List<DoctorClinicDto> assignedClinics = clinicDoctorService.getAssignedClinics(userId);
		return ResponseEntity.ok(assignedClinics);
	}
}
