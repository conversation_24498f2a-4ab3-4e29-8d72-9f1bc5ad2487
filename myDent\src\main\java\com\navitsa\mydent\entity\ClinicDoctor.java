package com.navitsa.mydent.entity;
import jakarta.persistence.*;

import java.io.Serializable;

@Entity
@Table(name = "clinic_doctor")
public class ClinicDoctor implements Serializable {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "clinic_doctor_id")
    private Integer id;

    @ManyToOne
    @JoinColumn(referencedColumnName ="clinic_id", name = "clinic_id")
    private Clinic clinic;

    @ManyToOne
    @JoinColumn(referencedColumnName ="doctor_id", name = "doctor_id")
    private Doctor doctor;

    public ClinicDoctor() {
    }

    public ClinicDoctor(Clinic clinicId, Doctor doctorId) {
        this.clinic = clinicId;
        this.doctor = doctorId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Clinic getClinic() {
        return clinic;
    }

    public void setClinic(Clinic clinic) {
        this.clinic = clinic;
    }

    public Doctor getDoctor() {
        return doctor;
    }

    public void setDoctor(Doctor doctor) {
        this.doctor = doctor;
    }
}
