{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SupplierRoutingModule } from './supplier-routing.module';\nimport { SupplierLayoutComponent } from './supplier-layout/supplier-layout.component';\nimport { SupplierDashboardComponent } from './supplier-dashboard/supplier-dashboard.component';\nimport { SupplierSidebarComponent } from './components/supplier-sidebar/supplier-sidebar.component';\nimport { SupplierNavbarComponent } from './components/supplier-navbar/supplier-navbar.component';\nimport { InventoryItemsComponent } from './supplier-inventory-items/supplier-inventory-items.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SupplierClinicOrdersComponent } from './supplier-clinic-orders/supplier-clinic-orders.component';\nimport { SupplierClinicOrderSingleViewComponent } from './supplier-clinic-order-single-view/supplier-clinic-order-single-view.component';\nimport { CoreModule } from '../core/core.module';\nimport * as i0 from \"@angular/core\";\nclass SupplierModule {\n  static #_ = this.ɵfac = function SupplierModule_Factory(t) {\n    return new (t || SupplierModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SupplierModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, SupplierRoutingModule, ReactiveFormsModule, CoreModule]\n  });\n}\nexport { SupplierModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SupplierModule, {\n    declarations: [SupplierLayoutComponent, SupplierDashboardComponent, SupplierSidebarComponent, SupplierNavbarComponent, InventoryItemsComponent, SupplierClinicOrdersComponent, SupplierClinicOrderSingleViewComponent],\n    imports: [CommonModule, FormsModule, SupplierRoutingModule, ReactiveFormsModule, CoreModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SupplierRoutingModule", "SupplierLayoutComponent", "SupplierDashboardComponent", "SupplierSidebarComponent", "SupplierNavbarComponent", "InventoryItemsComponent", "FormsModule", "ReactiveFormsModule", "SupplierClinicOrdersComponent", "SupplierClinicOrderSingleViewComponent", "CoreModule", "SupplierModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\supplier\\supplier.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SupplierRoutingModule } from './supplier-routing.module';\r\nimport { SupplierLayoutComponent } from './supplier-layout/supplier-layout.component';\r\nimport { SupplierDashboardComponent } from './supplier-dashboard/supplier-dashboard.component';\r\nimport { SupplierSidebarComponent } from './components/supplier-sidebar/supplier-sidebar.component';\r\nimport { SupplierNavbarComponent } from './components/supplier-navbar/supplier-navbar.component';\r\nimport { InventoryItemsComponent } from './supplier-inventory-items/supplier-inventory-items.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { SupplierClinicOrdersComponent } from './supplier-clinic-orders/supplier-clinic-orders.component';\r\nimport { SupplierClinicOrderSingleViewComponent } from './supplier-clinic-order-single-view/supplier-clinic-order-single-view.component';\r\nimport { CoreModule } from '../core/core.module';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SupplierLayoutComponent,\r\n    SupplierDashboardComponent,\r\n    SupplierSidebarComponent,\r\n    SupplierNavbarComponent,\r\n    InventoryItemsComponent,\r\n    SupplierClinicOrdersComponent,\r\n    SupplierClinicOrderSingleViewComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    SupplierRoutingModule,\r\n    ReactiveFormsModule,\r\n    CoreModule\r\n  ]\r\n})\r\nexport class SupplierModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,uBAAuB,QAAQ,+DAA+D;AACvG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,sCAAsC,QAAQ,iFAAiF;AACxI,SAASC,UAAU,QAAQ,qBAAqB;;AAEhD,MAkBaC,cAAc;EAAA,QAAAC,CAAA,G;qBAAdD,cAAc;EAAA;EAAA,QAAAE,EAAA,G;UAAdF;EAAc;EAAA,QAAAG,EAAA,G;cAPvBf,YAAY,EACZO,WAAW,EACXN,qBAAqB,EACrBO,mBAAmB,EACnBG,UAAU;EAAA;;SAGDC,cAAc;;2EAAdA,cAAc;IAAAI,YAAA,GAhBvBd,uBAAuB,EACvBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,uBAAuB,EACvBC,uBAAuB,EACvBG,6BAA6B,EAC7BC,sCAAsC;IAAAO,OAAA,GAGtCjB,YAAY,EACZO,WAAW,EACXN,qBAAqB,EACrBO,mBAAmB,EACnBG,UAAU;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}