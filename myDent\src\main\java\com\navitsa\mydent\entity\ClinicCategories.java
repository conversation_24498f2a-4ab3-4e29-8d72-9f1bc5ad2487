package com.navitsa.mydent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name= "clinic_categories")
public class ClinicCategories{
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "clinic_category_id")
	private Integer clinicCategoryId;
	
	@Column(name= "clinic_category")
	private String clinicCategory;
	
    
    public ClinicCategories() {
    	
    }

	public ClinicCategories(Integer clinicCategoryId, String clinicCategory) {
		super();
		this.clinicCategoryId = clinicCategoryId;
		this.clinicCategory = clinicCategory;
	}


	public Integer getClinicCategoryId() {
		return clinicCategoryId;
	}


	public void setClinicCategoryId(Integer clinicCategoryId) {
		this.clinicCategoryId = clinicCategoryId;
	}


	public String getClinicCategory() {
		return clinicCategory;
	}


	public void setClinicCategory(String clinicCategory) {
		this.clinicCategory = clinicCategory;
	}
	
	
}

