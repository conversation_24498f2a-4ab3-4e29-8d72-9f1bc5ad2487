{"ast": null, "code": "import { User } from \"src/app/user/user\";\nexport class Customer {\n  constructor() {\n    this.customerId = 0;\n    this.firstName = '';\n    this.lastName = '';\n    this.address = '';\n    this.city = '';\n    this.state = '';\n    this.country = '';\n    this.telephone = '';\n    this.email = '';\n    this.user = new User();\n  }\n}", "map": {"version": 3, "names": ["User", "Customer", "constructor", "customerId", "firstName", "lastName", "address", "city", "state", "country", "telephone", "email", "user"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\customer.ts"], "sourcesContent": ["import { User } from \"src/app/user/user\";\r\n\r\nexport class Customer{\r\ncustomerId:number = 0;\r\nfirstName: string = '';\r\nlastName: string = '';\r\naddress: string = '';\r\ncity: string = '';\r\nstate: string = '';\r\ncountry: string = '';\r\ntelephone: string = '';\r\nemail: string = '';\r\n  user: User = new User();\r\n}\r\n\r\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,mBAAmB;AAExC,OAAM,MAAOC,QAAQ;EAArBC,YAAA;IACA,KAAAC,UAAU,GAAU,CAAC;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,KAAK,GAAW,EAAE;IAChB,KAAAC,IAAI,GAAS,IAAIZ,IAAI,EAAE;EACzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}