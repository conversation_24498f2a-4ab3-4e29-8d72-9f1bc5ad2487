package com.navitsa.mydent.entity;

import com.navitsa.mydent.enums.UserStatus;
import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Table(name = "user_master")
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "first_name", nullable = false)
    private String firstName;

    @Column(name = "last_name", nullable = true)
    private String lastName;

    @Column(name = "user_name", nullable = false)
    private String username;

    @Column(name = "email")
    private String email;

    @Column(name = "user_type")
    private String userType;

    @Column(name = "password", nullable = false)
    private String password;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "company_id", referencedColumnName = "company_id")
    private Company companyId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "user_category_id", referencedColumnName = "user_category_id", nullable = true)
    private UserCategory userCategoryId;

    @Column(name = "user_verification")
    private UserStatus userVerified;
    
    @Column(name = "verification_token")
    private String verificationToken;

    public User() {

    }

    public User(String firstName, String lastName, String username, String email,
                String userType, String password, Company companyId, UserCategory userCategoryId,
                UserStatus userVerified, String verificationToken) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.username = username;
        this.email = email;
        this.userType = userType;
        this.password = password;
        this.companyId = companyId;
        this.userCategoryId = userCategoryId;
        this.userVerified = userVerified;
        this.verificationToken = verificationToken;
    }

    public User(String firstName, String lastName, String username, String password, Company companyId, UserCategory userCategoryId, UserStatus userVerified,String verificationToken) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.username = username;
        this.password = password;
        this.companyId = companyId;
        this.userCategoryId = userCategoryId;
        this.userVerified = userVerified;
        this.verificationToken = verificationToken;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Company getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Company companyId) {
        this.companyId = companyId;
    }

    public UserCategory getUserCategoryId() {
        return userCategoryId;
    }

    public void setUserCategoryId(UserCategory userCategoryId) {
        this.userCategoryId = userCategoryId;
    }

    public UserStatus getUserVerified() {
        return userVerified;
    }

    public void setUserVerified(UserStatus userVerified) {
        this.userVerified = userVerified;
    }
    
    public String getVerificationToken() {
        return verificationToken;
    }

    public void setVerificationToken(String verificationToken) {
        this.verificationToken = verificationToken;
    }

    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", userType='" + userType + '\'' +
                ", password='" + password + '\'' +
                ", companyId=" + companyId +
                ", userCategoryId=" + userCategoryId +
                ", userVerified='" + userVerified + '\'' +
                '}';
    }
}












