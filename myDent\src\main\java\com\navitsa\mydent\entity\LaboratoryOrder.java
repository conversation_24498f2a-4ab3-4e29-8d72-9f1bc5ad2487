package com.navitsa.mydent.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "laboratory_order")
public class LaboratoryOrder {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "laboratory_order_id")
    private Integer laboratoryOrderId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "clinic_id", referencedColumnName = "clinic_id")
    private Clinic clinicId;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "laboratory_setup_id", referencedColumnName = "laboratory_setup_id")
    private LaboratorySetup laboratorySetupId;

    @Column(name = "status")
    private String status;

    @Column(name = "patient_name")
    private String patientName;

    @Column(name = "date_of_birth")
    private String dateOfBirth;

    @Column(name = "contact_number")
    private String contactNumber;

    @Column(name = "expected_date")
    private String expectedDate;

    @Column(name = "tooth_number")
    private Integer toothNumber;

    @Column(name = "tooth_surface")
    private String toothSurface;

    @Column(name = "type_of_crown")
    private String typeOfCrown;

    @Column(name = "shade")
    private String shade;

    @Column(name = "material_specifications")
    private String materialSpecifications;

    @Column(name = "occlusion")
    private String occlusion;

    @Column(name = "margin_type")
    private String marginType;

    @Column(name = "crown_design")
    private String crownDesign;

    @Column(name = "additional_instructions")
    private String additionalInstructions;

    @Column(name = "laboratory_order_date")
    private String laboratoryOrderDate;

    @Column(name = "laboratory_order_time")
    private String laboratoryOrderTime;

    public LaboratoryOrder() {}

    public LaboratoryOrder(Integer laboratoryOrderId, Clinic clinicId, LaboratorySetup laboratorySetupId, String status, String patientName,
                           String dateOfBirth, String contactNumber, String expectedDate, Integer toothNumber, String toothSurface,
                           String typeOfCrown, String shade, String materialSpecifications, String occlusion, String marginType,
                           String crownDesign, String additionalInstructions, String laboratoryOrderDate, String laboratoryOrderTime) {
        super();
        this.laboratoryOrderId = laboratoryOrderId;
        this.clinicId = clinicId;
        this.laboratorySetupId = laboratorySetupId;
        this.status = status;
        this.patientName = patientName;
        this.dateOfBirth = dateOfBirth;
        this.contactNumber = contactNumber;
        this.expectedDate = expectedDate;
        this.toothNumber = toothNumber;
        this.toothSurface = toothSurface;
        this.typeOfCrown = typeOfCrown;
        this.shade = shade;
        this.materialSpecifications = materialSpecifications;
        this.occlusion = occlusion;
        this.marginType = marginType;
        this.crownDesign = crownDesign;
        this.additionalInstructions = additionalInstructions;
        this.laboratoryOrderDate = laboratoryOrderDate;
        this.laboratoryOrderTime = laboratoryOrderTime;
    }

    public Integer getLaboratoryOrderId() {
        return laboratoryOrderId;
    }

    public void setLaboratoryOrderId(Integer laboratoryOrderId) {
        this.laboratoryOrderId = laboratoryOrderId;
    }

    public Clinic getClinicId() {
        return clinicId;
    }

    public void setClinicId(Clinic clinicId) {
        this.clinicId = clinicId;
    }

    public LaboratorySetup getLaboratorySetupId() {
        return laboratorySetupId;
    }

    public void setLaboratorySetupId(LaboratorySetup laboratorySetupId) {
        this.laboratorySetupId = laboratorySetupId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getExpectedDate() {
        return expectedDate;
    }

    public void setExpectedDate(String expectedDate) {
        this.expectedDate = expectedDate;
    }

    public Integer getToothNumber() {
        return toothNumber;
    }

    public void setToothNumber(Integer toothNumber) {
        this.toothNumber = toothNumber;
    }

    public String getToothSurface() {
        return toothSurface;
    }

    public void setToothSurface(String toothSurface) {
        this.toothSurface = toothSurface;
    }

    public String getTypeOfCrown() {
        return typeOfCrown;
    }

    public void setTypeOfCrown(String typeOfCrown) {
        this.typeOfCrown = typeOfCrown;
    }

    public String getShade() {
        return shade;
    }

    public void setShade(String shade) {
        this.shade = shade;
    }

    public String getMaterialSpecifications() {
        return materialSpecifications;
    }

    public void setMaterialSpecifications(String materialSpecifications) {
        this.materialSpecifications = materialSpecifications;
    }

    public String getOcclusion() {
        return occlusion;
    }

    public void setOcclusion(String occlusion) {
        this.occlusion = occlusion;
    }

    public String getMarginType() {
        return marginType;
    }

    public void setMarginType(String marginType) {
        this.marginType = marginType;
    }

    public String getCrownDesign() {
        return crownDesign;
    }

    public void setCrownDesign(String crownDesign) {
        this.crownDesign = crownDesign;
    }

    public String getAdditionalInstructions() {
        return additionalInstructions;
    }

    public void setAdditionalInstructions(String additionalInstructions) {
        this.additionalInstructions = additionalInstructions;
    }

    public String getLaboratoryOrderDate() {
        return laboratoryOrderDate;
    }

    public void setLaboratoryOrderDate(String laboratoryOrderDate) {
        this.laboratoryOrderDate = laboratoryOrderDate;
    }

    public String getLaboratoryOrderTime() {
        return laboratoryOrderTime;
    }

    public void setLaboratoryOrderTime(String laboratoryOrderTime) {
        this.laboratoryOrderTime = laboratoryOrderTime;
    }
}
