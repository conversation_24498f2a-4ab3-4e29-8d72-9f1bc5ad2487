{"ast": null, "code": "export class UserTemp {\n  constructor() {\n    this.userTempId = 0;\n    this.mainName = '';\n    this.userEmail = '';\n    this.userPassword = '';\n    this.userTempStatus = UserTempStatus.CREATED;\n    this.createDateTime = new Date();\n    this.verificationToken = \"0\";\n  }\n}\nexport var UserTempType;\n(function (UserTempType) {\n  UserTempType[UserTempType[\"DOCTOR\"] = 0] = \"DOCTOR\";\n  UserTempType[UserTempType[\"CLINIC\"] = 1] = \"CLINIC\";\n  UserTempType[UserTempType[\"LABORATORY\"] = 2] = \"LABORATORY\";\n  UserTempType[UserTempType[\"SUPPLIER\"] = 3] = \"SUPPLIER\";\n  UserTempType[UserTempType[\"FUTURE_DENTIST\"] = 4] = \"FUTURE_DENTIST\";\n  UserTempType[UserTempType[\"ADMIN\"] = 5] = \"ADMIN\";\n  UserTempType[UserTempType[\"CUSTOMER\"] = 6] = \"CUSTOMER\";\n})(UserTempType || (UserTempType = {}));\nexport var UserTempStatus;\n(function (UserTempStatus) {\n  UserTempStatus[UserTempStatus[\"CREATED\"] = 0] = \"CREATED\";\n  UserTempStatus[UserTempStatus[\"SENT_USER_VERIFICATION\"] = 1] = \"SENT_USER_VERIFICATION\";\n  UserTempStatus[UserTempStatus[\"USER_VERIFIED\"] = 2] = \"USER_VERIFIED\";\n  UserTempStatus[UserTempStatus[\"ADMIN_APPROVED\"] = 3] = \"ADMIN_APPROVED\";\n  UserTempStatus[UserTempStatus[\"ADMIN_REJECTED\"] = 4] = \"ADMIN_REJECTED\";\n})(UserTempStatus || (UserTempStatus = {}));", "map": {"version": 3, "names": ["UserTemp", "constructor", "userTempId", "mainName", "userEmail", "userPassword", "userTempStatus", "UserTempStatus", "CREATED", "createDateTime", "Date", "verificationToken", "UserTempType"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\auth\\auth.ts"], "sourcesContent": ["export class UserTemp {\r\n  userTempId: number =0;\r\n  mainName: string = '';\r\n  additionalName?: string;\r\n  userEmail: string = '';\r\n  userPassword: string = '';\r\n  userTempType?: UserTempType\r\n  userTempStatus?: UserTempStatus = UserTempStatus.CREATED;\r\n  userTitle?: string;\r\n  contactNumber?: string;\r\n  contactPerson?: string;\r\n  contactPersonDesignation?: string;\r\n  registrationNumber?: string;\r\n  address?: string;\r\n  district?: string;\r\n  city?: string;\r\n  state?: string;\r\n  createDateTime?: Date = new Date();\r\n  verificationToken?:string = \"0\"\r\n}\r\n\r\nexport enum UserTempType {\r\n  DOCTOR,\r\n  CLINIC,\r\n  LABORATORY,\r\n  SUPPLIER,\r\n  FUTURE_DENTIST,\r\n  ADMIN,\r\n  CUSTOMER\r\n}\r\n\r\nexport enum UserTempStatus {\r\n  CREATED,\r\n  SENT_USER_VERIFICATION,\r\n  USER_VERIFIED,\r\n  ADMIN_APPROVED,\r\n  ADMIN_REJECTED\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,QAAQ;EAArBC,YAAA;IACE,KAAAC,UAAU,GAAU,CAAC;IACrB,KAAAC,QAAQ,GAAW,EAAE;IAErB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,YAAY,GAAW,EAAE;IAEzB,KAAAC,cAAc,GAAoBC,cAAc,CAACC,OAAO;IAUxD,KAAAC,cAAc,GAAU,IAAIC,IAAI,EAAE;IAClC,KAAAC,iBAAiB,GAAW,GAAG;EACjC;;AAEA,WAAYC,YAQX;AARD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,0BAAM;EACNA,YAAA,CAAAA,YAAA,0BAAM;EACNA,YAAA,CAAAA,YAAA,kCAAU;EACVA,YAAA,CAAAA,YAAA,8BAAQ;EACRA,YAAA,CAAAA,YAAA,0CAAc;EACdA,YAAA,CAAAA,YAAA,wBAAK;EACLA,YAAA,CAAAA,YAAA,8BAAQ;AACV,CAAC,EARWA,YAAY,KAAZA,YAAY;AAUxB,WAAYL,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,4BAAO;EACPA,cAAA,CAAAA,cAAA,0DAAsB;EACtBA,cAAA,CAAAA,cAAA,wCAAa;EACbA,cAAA,CAAAA,cAAA,0CAAc;EACdA,cAAA,CAAAA,cAAA,0CAAc;AAChB,CAAC,EANWA,cAAc,KAAdA,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}