{"ast": null, "code": "import { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../http.service\";\nclass ClinicService {\n  constructor(httpService) {\n    this.httpService = httpService;\n  }\n  //Clinic APIs\n  saveClinic(clinic) {\n    return this.httpService.request('POST', '/saveClinic', clinic);\n  }\n  getClinicList() {\n    return this.httpService.request('GET', '/clinicList', {});\n  }\n  getClinicById(id) {\n    return this.httpService.request('GET', `/getClinicById/${id}`, {});\n  }\n  updateClinic(id, clinic) {\n    return this.httpService.request('PUT', `/updateClinic/${id}`, clinic);\n  }\n  deleteClinic(id) {\n    return this.httpService.request('DELETE', `/deleteClinic/${id}`, {});\n  }\n  checkClinicName(clinicName) {\n    const params = {\n      clinicName\n    };\n    return this.httpService.request('GET', `/check-clinicName`, null, params);\n  }\n  // Clinic Services APIs\n  saveClinicServicesCategory(clinicService) {\n    return this.httpService.request('POST', '/saveClinicServicesCategory', clinicService);\n  }\n  getAllClinicServicesCategory() {\n    return this.httpService.request('GET', '/AllClinicServicesCategoryList', {});\n  }\n  getLaboratorySetupById(id) {\n    return this.httpService.request('GET', `/getClinicServicesCategoryById/${id}`, {});\n  }\n  // Clinic - Appoinemnt APIs\n  getPendingAppointmentList() {\n    const userId = localStorage.getItem('userid');\n    if (userId != null) {\n      return this.httpService.request('GET', `/getPendingAppointmentList/${userId}`, null);\n    } else {\n      return of(null);\n    }\n  }\n  // Clinic Laboratory Order APIs\n  saveLaboratoryOrder(orderData, userId) {\n    return this.httpService.request('POST', `/saveLaboratoryOrder/${userId}`, orderData);\n  }\n  getAllLaboratoryOrders() {\n    return this.httpService.request('GET', '/LaboratoryOrderlist', {});\n  }\n  getLaboratoryOrderById(id) {\n    return this.httpService.request('GET', `/getLaboratoryOrderById/${id}`, {});\n  }\n  updateLaboratoryOrder(id, clinicLaboratoryOrder) {\n    return this.httpService.request('PUT', `/updateLaboratoryOrder/${id}`, clinicLaboratoryOrder);\n  }\n  deleteLaboratoryOrder(id) {\n    return this.httpService.request('DELETE', `/deleteLaboratoryOrder/${id}`, {});\n  }\n  getLaboratoryOrderByClinicId(id) {\n    return this.httpService.request('GET', `/getLaboratoryOrdersByClinicId/${id}`, {});\n  }\n  // Clinic-Supplier Order APIs\n  getSupplierItemCategories() {\n    return this.httpService.request('GET', '/getSupplierItemCategoryList', null);\n  }\n  getSupplierListByCategoryName(categoryName) {\n    return this.httpService.request('GET', `/getSuppliersByItemCategoryName/${categoryName}`, null);\n  }\n  getSupplierItemListByCategoryNameAndSupplierId(categoryName, supplierId) {\n    const params = {\n      categoryName,\n      supplierId\n    };\n    return this.httpService.request('GET', `/getSuppliersByItemCategoryNameAndSupplierId`, null, params);\n  }\n  getSupplierItemsBySupplierId(supplierId) {\n    return this.httpService.request('GET', `/getSupplierItemsBySupplierId/${supplierId}`, null);\n  }\n  saveInventoryOrderRequest(inventoryOrderRequestDto) {\n    return this.httpService.request('POST', `/saveInventoryOrderRequest`, inventoryOrderRequestDto);\n  }\n  // Supplier-Clinic APIs\n  getOrderRequestByClinicUserId(clinicId) {\n    return this.httpService.request('GET', `/getClinicOrdersHeadersFromClinicUserId/${clinicId}`, null);\n  }\n  getOrderDetailsByClinicUserId(headerId) {\n    return this.httpService.request('GET', `/getClinicOrdersDetailsFromSupplierId/${headerId}`, null);\n  }\n  // Fetch appointments based on clinicId\n  getAppointmentsByClinicId(clinicId) {\n    return this.httpService.request('GET', `/clinic/${clinicId}`, {}); // Adjust the endpoint as needed\n  }\n  // Update appointment status\n  updateAppointmentStatus(id, status) {\n    return this.httpService.request('PUT', `/updateAppointmentStatus/${id}`, status);\n  }\n  static #_ = this.ɵfac = function ClinicService_Factory(t) {\n    return new (t || ClinicService)(i0.ɵɵinject(i1.HttpService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClinicService,\n    factory: ClinicService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { ClinicService };", "map": {"version": 3, "names": ["of", "ClinicService", "constructor", "httpService", "saveClinic", "clinic", "request", "getClinicList", "getClinicById", "id", "updateClinic", "deleteClinic", "checkClinicName", "clinicName", "params", "saveClinicServicesCategory", "clinicService", "getAllClinicServicesCategory", "getLaboratorySetupById", "getPendingAppointmentList", "userId", "localStorage", "getItem", "saveLaboratoryOrder", "orderData", "getAllLaboratoryOrders", "getLaboratoryOrderById", "updateLaboratoryOrder", "clinicLaboratoryOrder", "deleteLaboratoryOrder", "getLaboratoryOrderByClinicId", "getSupplierItemCategories", "getSupplierListByCategoryName", "categoryName", "getSupplierItemListByCategoryNameAndSupplierId", "supplierId", "getSupplierItemsBySupplierId", "saveInventoryOrderRequest", "inventoryOrderRequestDto", "getOrderRequestByClinicUserId", "clinicId", "getOrderDetailsByClinicUserId", "headerId", "getAppointmentsByClinicId", "updateAppointmentStatus", "status", "_", "i0", "ɵɵinject", "i1", "HttpService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Observable, of } from 'rxjs';\r\nimport { Clinic, ClinicLaboratoryOrder, ClinicServices, InventoryOrderRequestDto } from './clinic';\r\nimport { HttpService } from '../http.service';\r\nimport { Appointments } from '../modules/appointments/appointments';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\n\r\nexport class ClinicService {\r\n\r\n  constructor(private httpService:HttpService) {}\r\n\r\n  //Clinic APIs\r\n\r\n  saveClinic(clinic: Clinic): Observable<any> {\r\n    return this.httpService.request('POST', '/saveClinic', clinic);\r\n  }\r\n\r\n  getClinicList(): Observable<Clinic[]> {\r\n    return this.httpService.request('GET', '/clinicList', {});\r\n  }\r\n\r\n  getClinicById(id: number): Observable<Clinic> {\r\n    return this.httpService.request('GET', `/getClinicById/${id}`, {});\r\n  }\r\n\r\n  updateClinic(id: number, clinic: Clinic): Observable<object> {\r\n    return this.httpService.request('PUT', `/updateClinic/${id}`, clinic);\r\n  }\r\n\r\n  deleteClinic(id: number): Observable<any> {\r\n    return this.httpService.request('DELETE', `/deleteClinic/${id}`, {});\r\n  }\r\n\r\n  checkClinicName(clinicName: string): Observable<any> {\r\n    const params = { clinicName };\r\n    return this.httpService.request('GET', `/check-clinicName`, null, params);\r\n  }\r\n\r\n  // Clinic Services APIs\r\n\r\n  saveClinicServicesCategory(clinicService: ClinicServices): Observable<any> {\r\n    return this.httpService.request('POST', '/saveClinicServicesCategory', clinicService);\r\n  }\r\n\r\n  getAllClinicServicesCategory(): Observable< ClinicServices[]> {\r\n    return this.httpService.request('GET', '/AllClinicServicesCategoryList', {});\r\n  }\r\n\r\n  getLaboratorySetupById(id: number): Observable< ClinicServices> {\r\n    return this.httpService.request('GET', `/getClinicServicesCategoryById/${id}`, {});\r\n  }\r\n\r\n\r\n  // Clinic - Appoinemnt APIs\r\n\r\n  getPendingAppointmentList() : Observable<any> {\r\n    const userId = localStorage.getItem('userid');\r\n    if (userId != null) {\r\n      return this.httpService.request('GET',`/getPendingAppointmentList/${userId}`, null);\r\n    }else{\r\n      return of(null);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  // Clinic Laboratory Order APIs\r\n\r\n  saveLaboratoryOrder(orderData: ClinicLaboratoryOrder, userId: number): Observable<ClinicLaboratoryOrder> {\r\n    return this.httpService.request('POST', `/saveLaboratoryOrder/${userId}`, orderData);\r\n  }\r\n\r\n  getAllLaboratoryOrders(): Observable<ClinicLaboratoryOrder[]> {\r\n    return this.httpService.request('GET', '/LaboratoryOrderlist', {});\r\n  }\r\n\r\n  getLaboratoryOrderById(id: number): Observable<ClinicLaboratoryOrder> {\r\n    return this.httpService.request('GET', `/getLaboratoryOrderById/${id}`, {});\r\n  }\r\n\r\n  updateLaboratoryOrder(id: number, clinicLaboratoryOrder: ClinicLaboratoryOrder): Observable<ClinicLaboratoryOrder> {\r\n    return this.httpService.request('PUT', `/updateLaboratoryOrder/${id}`, clinicLaboratoryOrder);\r\n  }\r\n\r\n  deleteLaboratoryOrder(id: number): Observable<void> {\r\n    return this.httpService.request('DELETE', `/deleteLaboratoryOrder/${id}`, {});\r\n  }\r\n\r\n  getLaboratoryOrderByClinicId(id: number): Observable<ClinicLaboratoryOrder[]> {\r\n    return this.httpService.request('GET', `/getLaboratoryOrdersByClinicId/${id}`, {});\r\n  }\r\n\r\n\r\n  // Clinic-Supplier Order APIs\r\n  getSupplierItemCategories(){\r\n    return this.httpService.request('GET','/getSupplierItemCategoryList',null)\r\n  }\r\n\r\n  getSupplierListByCategoryName(categoryName:String){\r\n    return this.httpService.request('GET',`/getSuppliersByItemCategoryName/${categoryName}`,null)\r\n  }\r\n\r\n  getSupplierItemListByCategoryNameAndSupplierId(categoryName:String,supplierId:number){\r\n    const params = {categoryName,supplierId}\r\n    return this.httpService.request('GET',`/getSuppliersByItemCategoryNameAndSupplierId`,null,params)\r\n  }\r\n\r\n  getSupplierItemsBySupplierId(supplierId:number){\r\n    return this.httpService.request('GET',`/getSupplierItemsBySupplierId/${supplierId}`,null)\r\n  }\r\n\r\n  saveInventoryOrderRequest(inventoryOrderRequestDto:InventoryOrderRequestDto){\r\n    return this.httpService.request('POST',`/saveInventoryOrderRequest`,inventoryOrderRequestDto)\r\n  }\r\n\r\n  // Supplier-Clinic APIs\r\n  getOrderRequestByClinicUserId(clinicId:number) {\r\n    return this.httpService.request('GET',`/getClinicOrdersHeadersFromClinicUserId/${clinicId}`,null)\r\n  }\r\n\r\n  getOrderDetailsByClinicUserId(headerId:number) {\r\n    return this.httpService.request('GET',`/getClinicOrdersDetailsFromSupplierId/${headerId}`,null)\r\n  }\r\n  // Fetch appointments based on clinicId\r\n  getAppointmentsByClinicId(clinicId: number): Observable<Appointments[]> {\r\n    return this.httpService.request('GET', `/clinic/${clinicId}`, {}); // Adjust the endpoint as needed\r\n  }\r\n\r\n   // Update appointment status\r\n   updateAppointmentStatus(id: number, status: String): Observable<any> {\r\n    return this.httpService.request('PUT', `/updateAppointmentStatus/${id}`,  status );\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAqBA,EAAE,QAAQ,MAAM;;;AAKrC,MAIaC,aAAa;EAExBC,YAAoBC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;EAAe;EAE9C;EAEAC,UAAUA,CAACC,MAAc;IACvB,OAAO,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,aAAa,EAAED,MAAM,CAAC;EAChE;EAEAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACJ,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC;EAC3D;EAEAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,kBAAkBG,EAAE,EAAE,EAAE,EAAE,CAAC;EACpE;EAEAC,YAAYA,CAACD,EAAU,EAAEJ,MAAc;IACrC,OAAO,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,iBAAiBG,EAAE,EAAE,EAAEJ,MAAM,CAAC;EACvE;EAEAM,YAAYA,CAACF,EAAU;IACrB,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,QAAQ,EAAE,iBAAiBG,EAAE,EAAE,EAAE,EAAE,CAAC;EACtE;EAEAG,eAAeA,CAACC,UAAkB;IAChC,MAAMC,MAAM,GAAG;MAAED;IAAU,CAAE;IAC7B,OAAO,IAAI,CAACV,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,mBAAmB,EAAE,IAAI,EAAEQ,MAAM,CAAC;EAC3E;EAEA;EAEAC,0BAA0BA,CAACC,aAA6B;IACtD,OAAO,IAAI,CAACb,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,6BAA6B,EAAEU,aAAa,CAAC;EACvF;EAEAC,4BAA4BA,CAAA;IAC1B,OAAO,IAAI,CAACd,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,gCAAgC,EAAE,EAAE,CAAC;EAC9E;EAEAY,sBAAsBA,CAACT,EAAU;IAC/B,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,kCAAkCG,EAAE,EAAE,EAAE,EAAE,CAAC;EACpF;EAGA;EAEAU,yBAAyBA,CAAA;IACvB,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IAC7C,IAAIF,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI,CAACjB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,8BAA8Bc,MAAM,EAAE,EAAE,IAAI,CAAC;KACpF,MAAI;MACH,OAAOpB,EAAE,CAAC,IAAI,CAAC;;EAEnB;EAIA;EAEAuB,mBAAmBA,CAACC,SAAgC,EAAEJ,MAAc;IAClE,OAAO,IAAI,CAACjB,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,wBAAwBc,MAAM,EAAE,EAAEI,SAAS,CAAC;EACtF;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACtB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,sBAAsB,EAAE,EAAE,CAAC;EACpE;EAEAoB,sBAAsBA,CAACjB,EAAU;IAC/B,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,2BAA2BG,EAAE,EAAE,EAAE,EAAE,CAAC;EAC7E;EAEAkB,qBAAqBA,CAAClB,EAAU,EAAEmB,qBAA4C;IAC5E,OAAO,IAAI,CAACzB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,0BAA0BG,EAAE,EAAE,EAAEmB,qBAAqB,CAAC;EAC/F;EAEAC,qBAAqBA,CAACpB,EAAU;IAC9B,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,QAAQ,EAAE,0BAA0BG,EAAE,EAAE,EAAE,EAAE,CAAC;EAC/E;EAEAqB,4BAA4BA,CAACrB,EAAU;IACrC,OAAO,IAAI,CAACN,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,kCAAkCG,EAAE,EAAE,EAAE,EAAE,CAAC;EACpF;EAGA;EACAsB,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAAC5B,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,8BAA8B,EAAC,IAAI,CAAC;EAC5E;EAEA0B,6BAA6BA,CAACC,YAAmB;IAC/C,OAAO,IAAI,CAAC9B,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,mCAAmC2B,YAAY,EAAE,EAAC,IAAI,CAAC;EAC/F;EAEAC,8CAA8CA,CAACD,YAAmB,EAACE,UAAiB;IAClF,MAAMrB,MAAM,GAAG;MAACmB,YAAY;MAACE;IAAU,CAAC;IACxC,OAAO,IAAI,CAAChC,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,8CAA8C,EAAC,IAAI,EAACQ,MAAM,CAAC;EACnG;EAEAsB,4BAA4BA,CAACD,UAAiB;IAC5C,OAAO,IAAI,CAAChC,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,iCAAiC6B,UAAU,EAAE,EAAC,IAAI,CAAC;EAC3F;EAEAE,yBAAyBA,CAACC,wBAAiD;IACzE,OAAO,IAAI,CAACnC,WAAW,CAACG,OAAO,CAAC,MAAM,EAAC,4BAA4B,EAACgC,wBAAwB,CAAC;EAC/F;EAEA;EACAC,6BAA6BA,CAACC,QAAe;IAC3C,OAAO,IAAI,CAACrC,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,2CAA2CkC,QAAQ,EAAE,EAAC,IAAI,CAAC;EACnG;EAEAC,6BAA6BA,CAACC,QAAe;IAC3C,OAAO,IAAI,CAACvC,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,yCAAyCoC,QAAQ,EAAE,EAAC,IAAI,CAAC;EACjG;EACA;EACAC,yBAAyBA,CAACH,QAAgB;IACxC,OAAO,IAAI,CAACrC,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,WAAWkC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACrE;EAEC;EACAI,uBAAuBA,CAACnC,EAAU,EAAEoC,MAAc;IACjD,OAAO,IAAI,CAAC1C,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,4BAA4BG,EAAE,EAAE,EAAGoC,MAAM,CAAE;EACpF;EAAC,QAAAC,CAAA,G;qBA5HU7C,aAAa,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAblD,aAAa;IAAAmD,OAAA,EAAbnD,aAAa,CAAAoD,IAAA;IAAAC,UAAA,EAHZ;EAAM;;SAGPrD,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}