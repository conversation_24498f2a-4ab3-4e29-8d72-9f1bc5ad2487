{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../reject-reason-popup/reject-reason-popup.component\";\nfunction RejectPopupComponent_app_reject_reason_popup_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-reject-reason-popup\", 13);\n    i0.ɵɵlistener(\"closePopup\", function RejectPopupComponent_app_reject_reason_popup_16_Template_app_reject_reason_popup_closePopup_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onReasonPopupClose());\n    })(\"reasonSubmitted\", function RejectPopupComponent_app_reject_reason_popup_16_Template_app_reject_reason_popup_reasonSubmitted_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onReasonSubmitted($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nclass RejectPopupComponent {\n  constructor() {\n    this.closePopup = new EventEmitter(); // Emit event to close the popup\n    this.confirmReject = new EventEmitter(); // Emit event to confirm rejection\n    this.showRejectReasonPopup = false; // Control showing of the new rejection reason popup\n  }\n  // Function to close the popup\n  onClose() {\n    this.closePopup.emit();\n  }\n  // Function to confirm rejection and show the reason popup\n  onConfirmReject() {\n    this.showRejectReasonPopup = true; // Show the new popup\n  }\n  // Handle closing the reason popup\n  onReasonPopupClose() {\n    this.showRejectReasonPopup = false;\n  }\n  // Handle receiving the submitted rejection reason\n  onReasonSubmitted(reason) {\n    console.log('Rejection Reason:', reason);\n    this.showRejectReasonPopup = false;\n    // You can emit the reason to the parent or handle it as needed\n  }\n  static #_ = this.ɵfac = function RejectPopupComponent_Factory(t) {\n    return new (t || RejectPopupComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RejectPopupComponent,\n    selectors: [[\"app-reject-popup\"]],\n    outputs: {\n      closePopup: \"closePopup\",\n      confirmReject: \"confirmReject\"\n    },\n    decls: 17,\n    vars: 1,\n    consts: [[1, \"popup-overlay\"], [1, \"popup-box\"], [2, \"width\", \"100%\", \"display\", \"flex\", \"justify-content\", \"flex-end\"], [2, \"background\", \"none\", \"border\", \"none\", \"font-size\", \"20px\", \"cursor\", \"pointer\", 3, \"click\"], [2, \"padding\", \"20px\", \"display\", \"flex\", \"flex-direction\", \"column\", \"align-content\", \"center\", \"width\", \"100%\"], [2, \"display\", \"flex\", \"flex-direction\", \"row\", \"width\", \"100%\", \"justify-content\", \"center\", \"margin-bottom\", \"40px\"], [\"src\", \"../../../assets/images/close.png\", \"alt\", \"\", \"width\", \"80px\", \"height\", \"80px\"], [2, \"display\", \"flex\", \"flex-direction\", \"row\", \"width\", \"100%\", \"justify-content\", \"center\", \"padding-top\", \"20px\"], [2, \"font-size\", \"25px\", \"font-weight\", \"500\", \"width\", \"80%\", \"text-align\", \"center\"], [2, \"display\", \"flex\", \"justify-content\", \"center\", \"gap\", \"60px\", \"margin-top\", \"20px\", \"flex-direction\", \"row\", \"margin-top\", \"80px\"], [2, \"width\", \"120px\", \"padding-block\", \"5px\", \"background\", \"gray\", \"color\", \"black\", \"border-radius\", \"30px\", 3, \"click\"], [2, \"width\", \"120px\", \"background\", \"linear-gradient(to right, #FB751E , #B93426)\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"30px\", 3, \"click\"], [3, \"closePopup\", \"reasonSubmitted\", 4, \"ngIf\"], [3, \"closePopup\", \"reasonSubmitted\"]],\n    template: function RejectPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function RejectPopupComponent_Template_button_click_3_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵtext(4, \" \\u2715 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"div\", 5);\n        i0.ɵɵelement(7, \"img\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n        i0.ɵɵtext(10, \" Do you want to send reasons for rejections to the clinic? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 9)(12, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function RejectPopupComponent_Template_button_click_12_listener() {\n          return ctx.onClose();\n        });\n        i0.ɵɵtext(13, \" No \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 11);\n        i0.ɵɵlistener(\"click\", function RejectPopupComponent_Template_button_click_14_listener() {\n          return ctx.onConfirmReject();\n        });\n        i0.ɵɵtext(15, \" Yes \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(16, RejectPopupComponent_app_reject_reason_popup_16_Template, 1, 0, \"app-reject-reason-popup\", 12);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(16);\n        i0.ɵɵproperty(\"ngIf\", ctx.showRejectReasonPopup);\n      }\n    },\n    dependencies: [i1.NgIf, i2.RejectReasonPopupComponent],\n    styles: [\".popup-overlay[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 100%;\\n    background-color: rgba(0, 0, 0, 0.5);\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n}\\n\\n.popup-box[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding: 20px;\\n    border-radius: 8px;\\n    width: 700px;\\n    height: 520px;\\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGFib3JhdG9yeS9jb21wb25lbnRzL3JlamVjdC1wb3B1cC9yZWplY3QtcG9wdXAuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGVBQWU7SUFDZixNQUFNO0lBQ04sT0FBTztJQUNQLFdBQVc7SUFDWCxZQUFZO0lBQ1osb0NBQW9DO0lBQ3BDLGFBQWE7SUFDYix1QkFBdUI7SUFDdkIsbUJBQW1CO0FBQ3ZCOztBQUVBO0lBQ0ksdUJBQXVCO0lBQ3ZCLGFBQWE7SUFDYixrQkFBa0I7SUFDbEIsWUFBWTtJQUNaLGFBQWE7SUFDYix3Q0FBd0M7QUFDNUMiLCJzb3VyY2VzQ29udGVudCI6WyIucG9wdXAtb3ZlcmxheSB7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNSk7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG59XHJcblxyXG4ucG9wdXAtYm94IHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgcGFkZGluZzogMjBweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuICAgIHdpZHRoOiA3MDBweDtcclxuICAgIGhlaWdodDogNTIwcHg7XHJcbiAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { RejectPopupComponent };", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "RejectPopupComponent_app_reject_reason_popup_16_Template_app_reject_reason_popup_closePopup_0_listener", "ɵɵrestoreView", "_r2", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onReasonPopupClose", "RejectPopupComponent_app_reject_reason_popup_16_Template_app_reject_reason_popup_reasonSubmitted_0_listener", "$event", "ctx_r3", "onReasonSubmitted", "ɵɵelementEnd", "RejectPopupComponent", "constructor", "closePopup", "confirmReject", "showRejectReasonPopup", "onClose", "emit", "onConfirmReject", "reason", "console", "log", "_", "_2", "selectors", "outputs", "decls", "vars", "consts", "template", "RejectPopupComponent_Template", "rf", "ctx", "RejectPopupComponent_Template_button_click_3_listener", "ɵɵtext", "ɵɵelement", "RejectPopupComponent_Template_button_click_12_listener", "RejectPopupComponent_Template_button_click_14_listener", "ɵɵtemplate", "RejectPopupComponent_app_reject_reason_popup_16_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\components\\reject-popup\\reject-popup.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\components\\reject-popup\\reject-popup.component.html"], "sourcesContent": ["import { Component, Output, EventEmitter } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-reject-popup',\r\n  templateUrl: './reject-popup.component.html',\r\n  styleUrls: ['./reject-popup.component.css']\r\n})\r\nexport class RejectPopupComponent {\r\n  @Output() closePopup = new EventEmitter<void>(); // Emit event to close the popup\r\n  @Output() confirmReject = new EventEmitter<void>(); // Emit event to confirm rejection\r\n\r\n  showRejectReasonPopup: boolean = false; // Control showing of the new rejection reason popup\r\n\r\n  // Function to close the popup\r\n  onClose() {\r\n    this.closePopup.emit();\r\n  }\r\n\r\n  // Function to confirm rejection and show the reason popup\r\n  onConfirmReject() {\r\n    this.showRejectReasonPopup = true; // Show the new popup\r\n  }\r\n\r\n  // Handle closing the reason popup\r\n  onReasonPopupClose() {\r\n    this.showRejectReasonPopup = false;\r\n  }\r\n\r\n  // <PERSON>le receiving the submitted rejection reason\r\n  onReasonSubmitted(reason: string) {\r\n    console.log('Rejection Reason:', reason);\r\n    this.showRejectReasonPopup = false;\r\n    // You can emit the reason to the parent or handle it as needed\r\n  }\r\n}\r\n", "<div class=\"popup-overlay\">\r\n    <div class=\"popup-box\">\r\n        <!-- Existing content -->\r\n        <div style=\"width: 100%; display: flex; justify-content: flex-end;\">\r\n            <button style=\"background: none; border: none; font-size: 20px; cursor: pointer;\" (click)=\"onClose()\">\r\n                &#10005;\r\n            </button>\r\n        </div>\r\n        <div style=\"padding: 20px; display: flex; flex-direction: column; align-content: center; width: 100%;\">\r\n            <div style=\"display: flex; flex-direction: row; width: 100%; justify-content: center; margin-bottom: 40px;\">\r\n                <img src=\"../../../assets/images/close.png\" alt=\"\" width=\"80px\" height=\"80px\" />\r\n            </div>\r\n            <div style=\"display: flex; flex-direction: row; width: 100%; justify-content: center; padding-top: 20px;\">\r\n                <div style=\"font-size: 25px; font-weight: 500; width: 80%; text-align: center;\">\r\n                    Do you want to send reasons for rejections to the clinic?\r\n                </div>\r\n            </div>\r\n            <div\r\n                style=\"display: flex; justify-content: center; gap: 60px; margin-top: 20px; flex-direction: row; margin-top: 80px;\">\r\n                <button (click)=\"onClose()\"\r\n                    style=\"width: 120px; padding-block: 5px; background: gray; color: black; border-radius: 30px;\">\r\n                    No\r\n                </button>\r\n                <button (click)=\"onConfirmReject()\"\r\n                    style=\"width: 120px; background: linear-gradient(to right, #FB751E , #B93426); color: white; border: none; border-radius: 30px;\">\r\n                    Yes\r\n                </button>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Conditionally render the new rejection reason popup -->\r\n        <app-reject-reason-popup *ngIf=\"showRejectReasonPopup\" (closePopup)=\"onReasonPopupClose()\"\r\n            (reasonSubmitted)=\"onReasonSubmitted($event)\"></app-reject-reason-popup>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAA4BA,YAAY,QAAQ,eAAe;;;;;;;IC+BvDC,EAAA,CAAAC,cAAA,kCACkD;IADKD,EAAA,CAAAE,UAAA,wBAAAC,uGAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAcP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC,6BAAAC,4GAAAC,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAZ,EAAA,CAAAO,aAAA;MAAA,OACnEP,EAAA,CAAAQ,WAAA,CAAAI,MAAA,CAAAC,iBAAA,CAAAF,MAAA,CAAyB;IAAA,EAD0C;IACxCX,EAAA,CAAAc,YAAA,EAA0B;;;AD9BpF,MAKaC,oBAAoB;EALjCC,YAAA;IAMY,KAAAC,UAAU,GAAG,IAAIlB,YAAY,EAAQ,CAAC,CAAC;IACvC,KAAAmB,aAAa,GAAG,IAAInB,YAAY,EAAQ,CAAC,CAAC;IAEpD,KAAAoB,qBAAqB,GAAY,KAAK,CAAC,CAAC;;EAExC;EACAC,OAAOA,CAAA;IACL,IAAI,CAACH,UAAU,CAACI,IAAI,EAAE;EACxB;EAEA;EACAC,eAAeA,CAAA;IACb,IAAI,CAACH,qBAAqB,GAAG,IAAI,CAAC,CAAC;EACrC;EAEA;EACAV,kBAAkBA,CAAA;IAChB,IAAI,CAACU,qBAAqB,GAAG,KAAK;EACpC;EAEA;EACAN,iBAAiBA,CAACU,MAAc;IAC9BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,MAAM,CAAC;IACxC,IAAI,CAACJ,qBAAqB,GAAG,KAAK;IAClC;EACF;EAAC,QAAAO,CAAA,G;qBA1BUX,oBAAoB;EAAA;EAAA,QAAAY,EAAA,G;UAApBZ,oBAAoB;IAAAa,SAAA;IAAAC,OAAA;MAAAZ,UAAA;MAAAC,aAAA;IAAA;IAAAY,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPjCnC,EAAA,CAAAC,cAAA,aAA2B;QAImED,EAAA,CAAAE,UAAA,mBAAAmC,sDAAA;UAAA,OAASD,GAAA,CAAAhB,OAAA,EAAS;QAAA,EAAC;QACjGpB,EAAA,CAAAsC,MAAA,eACJ;QAAAtC,EAAA,CAAAc,YAAA,EAAS;QAEbd,EAAA,CAAAC,cAAA,aAAuG;QAE/FD,EAAA,CAAAuC,SAAA,aAAgF;QACpFvC,EAAA,CAAAc,YAAA,EAAM;QACNd,EAAA,CAAAC,cAAA,aAA0G;QAElGD,EAAA,CAAAsC,MAAA,mEACJ;QAAAtC,EAAA,CAAAc,YAAA,EAAM;QAEVd,EAAA,CAAAC,cAAA,cACwH;QAC5GD,EAAA,CAAAE,UAAA,mBAAAsC,uDAAA;UAAA,OAASJ,GAAA,CAAAhB,OAAA,EAAS;QAAA,EAAC;QAEvBpB,EAAA,CAAAsC,MAAA,YACJ;QAAAtC,EAAA,CAAAc,YAAA,EAAS;QACTd,EAAA,CAAAC,cAAA,kBACqI;QAD7HD,EAAA,CAAAE,UAAA,mBAAAuC,uDAAA;UAAA,OAASL,GAAA,CAAAd,eAAA,EAAiB;QAAA,EAAC;QAE/BtB,EAAA,CAAAsC,MAAA,aACJ;QAAAtC,EAAA,CAAAc,YAAA,EAAS;QAKjBd,EAAA,CAAA0C,UAAA,KAAAC,wDAAA,sCAC4E;QAChF3C,EAAA,CAAAc,YAAA,EAAM;;;QAFwBd,EAAA,CAAA4C,SAAA,IAA2B;QAA3B5C,EAAA,CAAA6C,UAAA,SAAAT,GAAA,CAAAjB,qBAAA,CAA2B;;;;;;;SDxBhDJ,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}