import { HttpClientModule } from '@angular/common/http';
import { NgModule, isDevMode } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { MatNativeDateModule, MatRippleModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { AppointmentPageComponent } from './modules/appointments/appointment-page/appointment-page.component';
import { AppointmentsComponent } from './modules/appointments/appointments/appointments.component';
import { AppointmentComponent } from './modules/appointments/create-appoinment/appointment.component';
import { OrderRequestsComponent } from './modules/order-requests/order-requests.component';
import { OrdersComponent } from './modules/orders/orders.component';
import { QuotationComponent } from './modules/quotation/quotation.component';
import { SalesQuotesComponent } from './modules/sales-quotes/sales-quotes.component';
import { SalesquotesComponent } from './modules/salesquotes/salesquotes.component';
import { ServicesComponent } from './modules/services/services.component';
import { UserAppointmentDashboardComponent } from './user/user-appointment-dashboard/user-appointment-dashboard.component';
import { UserAppointmentComponent } from './user/user-appointment/user-appointment.component';
import { UserNotificationComponent } from './user/user-notification/user-notification.component';
import { UserSalesQuotesComponent } from './user/user-sales-quotes/user-sales-quotes.component';
import { ClinicRegistrationComponent } from './clinic/clinic-registration/clinic-registration.component';
import { FutureDentistRegistrationComponent } from './modules/future-dentist-registration/future-dentist-registration.component';
import { LaboratoryRegistrationComponent } from './laboratory/laboratory-registration/laboratory-registration.component';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatBadgeModule } from '@angular/material/badge';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSliderModule } from '@angular/material/slider';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTreeModule } from '@angular/material/tree';
import { SalesquoteComponent } from './modules/salesquote/salesquote.component';
import { UserLoginComponent } from './user/user-login/user-login.component';
import { CreateOrderComponent } from './modules/create-order/create-order.component';
import { MyAppointmentsComponent } from './modules/my-appointments/my-appointments.component';
import { NotificationsComponent } from './modules/notifications/notifications.component';
import { PurchaseComponent } from './modules/purchase/purchase.component';
import { PurchasingDashboardComponent } from './modules/purchasing-dashboard/purchasing-dashboard.component';
import { UserSelectionComponent } from './user/user-selection/user-selection.component';
import { HomePageComponent } from './modules/home-page/home-page.component';
import { SupplierRegistrationComponent } from './supplier/supplier-registration/supplier-registration.component';
import { DoctorRegistrationComponent } from './doctor/doctor-registration/doctor-registration.component';
import { SharedModule } from './shared/shared.module';
import { ContactUsComponent } from './modules/contact-us/contact-us.component';
import { CoreModule } from "./core/core.module";

@NgModule({

  declarations: [
    AppComponent,
    SalesquoteComponent,
    ServicesComponent,
    AppointmentComponent,
    UserAppointmentComponent,
    AppointmentsComponent,
    AppointmentPageComponent,
    NotificationsComponent,
    UserNotificationComponent,
    PurchaseComponent,
    PurchasingDashboardComponent,
    UserAppointmentDashboardComponent,
    SalesQuotesComponent,
    UserSalesQuotesComponent,
    MyAppointmentsComponent,
    QuotationComponent,
    OrderRequestsComponent,
    SalesquotesComponent,
    OrdersComponent,
    CreateOrderComponent,

    // Required Components
    UserLoginComponent,
    UserSelectionComponent,
    ClinicRegistrationComponent,
    FutureDentistRegistrationComponent,
    LaboratoryRegistrationComponent,
    SupplierRegistrationComponent,
    DoctorRegistrationComponent,
    ContactUsComponent,
    HomePageComponent,

  ],

  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    HttpClientModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    MatSidenavModule,
    NgSelectModule,
    NgxMatSelectSearchModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatTableModule,
    MatSortModule,
    MatInputModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatListModule,
    BrowserModule,
    BrowserAnimationsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatPaginatorModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
    MatTabsModule,
    MatToolbarModule,
    MatTooltipModule,
    MatFormFieldModule,
    ScrollingModule,
    MatInputModule,
    MatAutocompleteModule,
    MatBadgeModule,
    MatBottomSheetModule,
    MatButtonModule,
    MatButtonToggleModule,
    MatCardModule,
    MatCheckboxModule,
    MatChipsModule,
    MatStepperModule,
    MatDatepickerModule,
    MatDialogModule,
    MatDividerModule,
    MatExpansionModule,
    MatGridListModule,
    MatIconModule,
    MatListModule,
    MatMenuModule,
    MatNativeDateModule,
    MatRippleModule,
    MatRadioModule,
    MatSelectModule,
    MatSidenavModule,
    MatSliderModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTreeModule,
    NgxMatSelectSearchModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    CoreModule
],
  providers: [],
  bootstrap: [AppComponent],
})
export class AppModule {}
