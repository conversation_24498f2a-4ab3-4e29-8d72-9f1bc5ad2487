<div class="page-background">
  <div class="rec1"></div>
  <div class="rec2"></div>
  <div class="verification-container">
    <div class="verification-content">
      <h2> Email Verification</h2>

<form [formGroup]="loginForm" (ngSubmit)="verifyUrlGenarate()">
  <div class="col-12">
    <label class="input-label , mb-3"  for="email-address">Email Address</label>
    <input
      type="email"
      id="email-address"
      name="email-address"
      formControlName="email"
      class="form-control"
    />

    <div class="px-1" style="font-weight: 500;">
      <!-- Required validation -->
      <small
        class="text-danger"
        *ngIf="loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched"
      >
        Email is required.
      </small>

      <!-- Invalid email format validation -->
      <small
        class="text-danger"
        *ngIf="loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched"
      >
        Invalid email format.
      </small>
    </div>
  </div>

  <button type="submit" >Submit</button>
</form>


    
     
    </div>
  </div>
</div>
