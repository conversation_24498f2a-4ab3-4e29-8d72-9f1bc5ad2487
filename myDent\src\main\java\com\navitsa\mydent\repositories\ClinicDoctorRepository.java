package com.navitsa.mydent.repositories;
import com.navitsa.mydent.dtos.DoctorClinicDto;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.Doctor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import java.util.Optional;
import com.navitsa.mydent.entity.ClinicDoctor;


import javax.print.Doc;
import java.util.List; // Use this import instead of AssertFalse.List

public interface ClinicDoctorRepository extends JpaRepository<ClinicDoctor, Integer> {
    
    @Query("SELECT c.clinicId FROM Clinic c WHERE c.userId.userId = :userId")
    Integer findClinicIdByUserId(@Param("userId") Integer userId);
    
    
    @Modifying
    @Query("DELETE FROM ClinicDoctor cd WHERE cd.clinic.clinicId = :clinicId AND cd.doctor.doctorId = :doctorId")
    void deleteByClinicIdAndDoctorId(@Param("clinicId") Integer clinicId, @Param("doctorId") Integer doctorId);
    
    @Query("SELECT new com.navitsa.mydent.dtos.DoctorClinicDto(c.clinic.clinicId, c.clinic.name, c.clinic.address, c.clinic.city) FROM ClinicDoctor c WHERE c.doctor.userId.userId = :userId")
    Optional<List<DoctorClinicDto>> findClinicsByDoctorId(@Param("userId") Integer userId);
    
    @Query("SELECT cd.doctor FROM ClinicDoctor cd WHERE cd.clinic.userId.userId = :userId")
    Optional<List<Doctor>> findDoctorsByClinicId(@Param("userId") Integer userId);


    @Query("""
        SELECT d FROM Doctor d
        WHERE d NOT IN (
            SELECT cd.doctor FROM ClinicDoctor cd
            WHERE cd.clinic.clinicId = :clinicId
        )
    """)
    List<Doctor> findDoctorsNotAssignedToClinic(@Param("clinicId") Integer clinicId);

    @Query("""
        SELECT cd.doctor FROM ClinicDoctor cd
        WHERE cd.clinic.clinicId = :clinicId
    """)
    List<Doctor> findDoctorsAssignByClinicId(@Param("clinicId") Integer clinicId);

}
