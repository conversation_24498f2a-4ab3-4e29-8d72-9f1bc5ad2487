{"ast": null, "code": "import Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/schedule.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/doctor/doctor.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ClinicSetupClinicComponent_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_button_12_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r6);\n      const date_r4 = restoredCtx.$implicit;\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.selectDay(date_r4));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.selectedDay === date_r4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", date_r4, \" \");\n  }\n}\nfunction ClinicSetupClinicComponent_tr_60_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const schedule_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(schedule_r7.fromTime);\n  }\n}\nfunction ClinicSetupClinicComponent_tr_60_ng_template_7_Template(rf, ctx) {}\nfunction ClinicSetupClinicComponent_tr_60_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const schedule_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(schedule_r7.toTime);\n  }\n}\nfunction ClinicSetupClinicComponent_tr_60_ng_template_11_Template(rf, ctx) {}\nfunction ClinicSetupClinicComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 60);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 61);\n    i0.ɵɵtemplate(6, ClinicSetupClinicComponent_tr_60_ng_container_6_Template, 2, 1, \"ng-container\", 62);\n    i0.ɵɵtemplate(7, ClinicSetupClinicComponent_tr_60_ng_template_7_Template, 0, 0, \"ng-template\", null, 63, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 61);\n    i0.ɵɵtemplate(10, ClinicSetupClinicComponent_tr_60_ng_container_10_Template, 2, 1, \"ng-container\", 62);\n    i0.ɵɵtemplate(11, ClinicSetupClinicComponent_tr_60_ng_template_11_Template, 0, 0, \"ng-template\", null, 63, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 64);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\", 65)(16, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_tr_60_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const schedule_r7 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.updateSchedule(schedule_r7));\n    });\n    i0.ɵɵtext(17, \"Edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_tr_60_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const schedule_r7 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(schedule_r7.scheduleId ? ctx_r18.deleteSchedule(schedule_r7.scheduleId) : null);\n    });\n    i0.ɵɵtext(19, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const schedule_r7 = ctx.$implicit;\n    const _r9 = i0.ɵɵreference(8);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(schedule_r7.clinics.clinicId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(schedule_r7.date);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !schedule_r7.holidayDate)(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !schedule_r7.holidayDate)(\"ngIfElse\", _r9);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(schedule_r7.holidayDate || schedule_r7.isClosed ? \"Closed\" : \"Open\");\n  }\n}\nfunction ClinicSetupClinicComponent_tr_94_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 69)(4, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_tr_94_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r21);\n      const service_r19 = restoredCtx.$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.deleteService(service_r19.clinicServiceId));\n    });\n    i0.ɵɵtext(5, \"Delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const service_r19 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r19.services.clinicServiceCategoryName);\n  }\n}\nfunction ClinicSetupClinicComponent_tr_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 72);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 65)(8, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_tr_130_Template_button_click_8_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const doctor_r22 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.deleteClinicDoctor(doctor_r22.doctorId));\n    });\n    i0.ɵɵelement(9, \"i\", 74);\n    i0.ɵɵtext(10, \" Delete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const doctor_r22 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r22.doctorId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r22.firstName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r22.regNo);\n  }\n}\n// Day mapping constant to map day abbreviations to full names\nconst DAY_MAPPING = {\n  Su: 'Sunday',\n  M: 'Monday',\n  TU: 'Tuesday',\n  W: 'Wednesday',\n  TH: 'Thursday',\n  F: 'Friday',\n  Sa: 'Saturday'\n};\n// Reverse Day mapping to map full names to abbreviations\nconst REVERSE_DAY_MAPPING = {\n  Sunday: 'Su',\n  Monday: 'M',\n  Tuesday: 'TU',\n  Wednesday: 'W',\n  Thursday: 'TH',\n  Friday: 'F',\n  Saturday: 'Sa'\n};\nclass ClinicSetupClinicComponent {\n  constructor(scheduleService, router, doctorService) {\n    this.scheduleService = scheduleService;\n    this.router = router;\n    this.doctorService = doctorService;\n    this.dates = ['Su', 'M', 'TU', 'W', 'TH', 'F', 'Sa']; // Days of the week\n    this.disabledDays = [];\n    this.selectedDay = ''; // Track the selected day\n    this.fromTime = ''; // Time input for \"from\"\n    this.toTime = ''; // Time input for \"to\"\n    this.selectedValue1 = 'AM'; // Track AM/PM for 'from' time\n    this.selectedValue2 = 'AM'; // Track AM/PM for 'to' time\n    this.showHolidayDateInput = false; // Toggle holiday date input visibility\n    this.isClosed = false; // Status for \"Mark as Closed\"\n    this.holidayDate = false; // Store the selected holiday date\n    this.isUpdateMode = false;\n    this.schedules = []; // Array to store fetched schedules\n    this.clinicId = 0; // Initialize to a default value\n    this.editingScheduleId = null;\n    this.doctors = []; // Array to store fetched doctors\n    this.selectedServiceId = null; // Bind to ngModel\n    this.existingServices = []; // To store existing services\n  }\n\n  ngOnInit() {\n    const userIdString = localStorage.getItem('userid'); // Retrieve userId from localStorage\n    const clinicId = localStorage.getItem('clinicId'); // Retrieve clinicId from localStorage\n    if (userIdString) {\n      console.log('Component initialized. Fetching schedules...');\n      this.loadClinicId(); // Load clinicId from localStorage\n      this.getSchedules(); // Fetch schedules when component initializes\n      this.getDoctors(); // Fetch doctors when the component initializes\n      this.loadExistingServices(); // Load existing services on component initialization\n    } else {\n      alert('User ID not found. Please log in or select a clinic.');\n      this.router.navigate(['/login']); // Redirect to login if userId is not found\n    }\n  }\n\n  validateTimeRange(fromTime, toTime) {\n    // Convert strings (e.g. \"09:00\", \"17:00\") to Date objects\n    const from = new Date(`1970-01-01T${fromTime}:00`);\n    const to = new Date(`1970-01-01T${toTime}:00`);\n    if (this.isClosed && this.holidayDate) {\n      // Check if times are valid\n      if (isNaN(from.getTime()) || isNaN(to.getTime())) {\n        Swal.fire('Error', 'Invalid time format!', 'error');\n        return false;\n      }\n    }\n    // fromTime == toTime\n    if (from.getTime() === to.getTime()) {\n      Swal.fire('Invalid Time', 'From Time and To Time cannot be the same.', 'warning');\n      return false;\n    }\n    // toTime < fromTime\n    if (to.getTime() < from.getTime()) {\n      Swal.fire('Invalid Time', 'To Time must be later than From Time.', 'warning');\n      return false;\n    }\n    return true; // valid\n  }\n  // Load the clinicId from localStorage\n  loadClinicId() {\n    const clinicIdString = localStorage.getItem('clinicId'); // Retrieve the clinicId from localStorage\n    if (clinicIdString) {\n      this.clinicId = parseInt(clinicIdString, 10); // Convert it to a number\n      console.log('Loaded Clinic ID from localStorage:', this.clinicId); // Log the loaded clinicId\n    } else {\n      console.log('No Clinic ID found in localStorage.'); // Log if no clinicId was found\n    }\n  }\n  // Fetch all doctors from the backend\n  getDoctors() {\n    const userId = localStorage.getItem('userid'); // Retrieve userId from localStorage\n    if (userId) {\n      console.log('Fetching doctors for userId:', userId);\n      this.doctorService.getAllDoctorsFilter('Assign', Number(localStorage.getItem('clinicId'))).subscribe(data => {\n        this.doctors = data; // Populate the doctors array\n        console.log('Doctors fetched successfully:', data);\n      }, error => {\n        console.error('Error fetching doctors:', error);\n      });\n    } else {\n      console.warn('No userId found in localStorage.');\n    }\n  }\n  redirectToDoctorList() {\n    this.router.navigate(['./list-doctor']);\n  }\n  // Toggle the sidebar visibility\n  toggleSidebar() {\n    const sidebarElement = document.getElementById('sidebar');\n    if (sidebarElement) {\n      sidebarElement.classList.toggle('open');\n      console.log('Sidebar toggled:', sidebarElement.classList.contains('open') ? 'opened' : 'closed');\n    } else {\n      console.error('Sidebar element not found.');\n    }\n  }\n  // Function to select a day and keep the button \"pressed\"\n  selectDay(day) {\n    if (this.disabledDays.includes(day) && (!this.isUpdateMode || this.selectedDay !== day)) {\n      // Use SweetAlert dialog\n      Swal.fire('Info!', `${this.getFullDayName(day)} is already scheduled.`, 'info');\n      return;\n    }\n    this.selectedDay = this.selectedDay === day ? '' : day;\n  }\n  // Toggle between AM/PM for both From and To time\n  toggleAmPm(timeField) {\n    if (timeField === 'from') {\n      this.selectedValue1 = this.selectedValue1 === 'AM' ? 'PM' : 'AM';\n      console.log('From time AM/PM toggled to:', this.selectedValue1);\n    } else {\n      this.selectedValue2 = this.selectedValue2 === 'AM' ? 'PM' : 'AM';\n      console.log('To time AM/PM toggled to:', this.selectedValue2);\n    }\n  }\n  // Validate the form before saving\n  validateForm() {\n    console.log('Selected Day:', this.selectedDay);\n    console.log('From Time:', this.fromTime);\n    console.log('To Time:', this.toTime);\n    console.log('Clinic ID:', this.clinicId);\n    console.log('Closed:', this.isClosed);\n    if (!this.fromTime || !this.toTime || !this.selectedDay) {\n      if (this.holidayDate == true && this.selectedDay) {\n        return true; // Assuming no time validation is needed for holidays/closed days\n      }\n\n      if (this.isClosed == true) {\n        return true;\n      }\n      // Use SweetAlertdialog\n      Swal.fire('Missing Information', 'Time fields and Date cannot be empty.', 'info');\n      // Swal.fire({\n      //   title: 'Error!',\n      //   text: 'Time fields and Date cannot be empty.',\n      //   icon: 'error',\n      //   confirmButtonText: 'Try Again',\n      // });\n      return false; // times or date not choose then get error message\n    } else if (!this.clinicId) {\n      return false;\n    }\n    return true;\n  }\n  // Get all schedules from the backend\n  getSchedules() {\n    this.scheduleService.getSchedules().subscribe(data => {\n      const storedUserId = localStorage.getItem('clinicId'); // Use userId\n      if (storedUserId) {\n        const userIdNumber = Number(storedUserId);\n        this.schedules = data.filter(schedule => schedule.clinics?.clinicId === userIdNumber);\n        // Convert full day names in schedules to abbreviations for comparison\n        this.disabledDays = this.schedules.map(schedule => REVERSE_DAY_MAPPING[schedule.date]);\n      }\n    }, error => {\n      console.error('Error fetching schedules:', error);\n    });\n  }\n  // Save the selected schedule\n  saveSchedule() {\n    // Validate the form inputs before proceeding\n    if (!this.validateForm()) return;\n    // Retrieve clinicId from localStorage\n    const clinicIdString = localStorage.getItem('clinicId');\n    const clinicId = clinicIdString ? parseInt(clinicIdString, 10) : null;\n    // Handle the case where clinicId is not found or invalid\n    if (clinicId === null) {\n      this.handleError('Clinic ID not found or invalid');\n      return;\n    }\n    const fullDayName = this.getFullDayName(this.selectedDay);\n    const schedule = {\n      clinics: {\n        clinicId: clinicId\n      },\n      date: fullDayName,\n      fromTime: this.fromTime,\n      toTime: this.toTime,\n      // isClosed: this.isClosed,\n      holidayDate: this.holidayDate || this.isClosed,\n      scheduleId: this.editingScheduleId\n    };\n    let x = this.validateTimeRange(this.fromTime, this.toTime);\n    console.log(x);\n    if (!x) {\n      return;\n    }\n    // Check if we're in update mode\n    if (this.isUpdateMode && this.editingScheduleId !== null) {\n      this.scheduleService.updateSchedule(this.editingScheduleId, schedule).subscribe(updatedSchedule => {\n        // Update the schedules list by removing the old schedule and adding the updated one\n        this.schedules = this.schedules.filter(s => s.scheduleId !== this.editingScheduleId);\n        this.schedules.push(updatedSchedule);\n        this.disabledDays.push(this.selectedDay);\n        this.getSchedules(); // Refresh the schedules list\n        this.resetForm(); // Reset the form inputs\n      }, error => this.handleError(error) // Handle any errors\n      );\n    } else {\n      // In create mode, save a new schedule\n      this.scheduleService.saveSchedule(schedule).subscribe(data => {\n        this.disabledDays.push(this.selectedDay);\n        this.getSchedules(); // Refresh the schedules list\n        this.resetForm(); // Reset the form inputs\n      }, error => this.handleError(error) // Handle any errors\n      );\n    }\n  }\n  // Update a specific schedule\n  updateSchedule(schedule) {\n    this.isUpdateMode = true;\n    this.editingScheduleId = schedule.scheduleId || null;\n    this.selectedDay = REVERSE_DAY_MAPPING[schedule.date];\n    this.disabledDays = this.dates.filter(date => date !== this.selectedDay);\n    this.fromTime = schedule.fromTime.split(' ')[0];\n    this.selectedValue1 = schedule.fromTime.split(' ')[1];\n    this.toTime = schedule.toTime.split(' ')[0];\n    this.selectedValue2 = schedule.toTime.split(' ')[1];\n  }\n  deleteClinicDoctor(doctorId) {\n    // Use SweetAlert2 for confirmation dialog\n    Swal.fire({\n      title: 'Are you sure?',\n      text: \"You won't be able to revert this!\",\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#3085d6',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, delete it!'\n    }).then(result => {\n      if (result.isConfirmed) {\n        // Call the deleteDoctor method with doctorId\n        this.doctorService.deleteClinicDoctor(doctorId).subscribe(() => {\n          // Remove the doctor from the list after successful deletion\n          this.doctors = this.doctors.filter(doctor => doctor.doctorId !== doctorId);\n          console.log(`Doctor with ID ${doctorId} deleted successfully.`);\n          Swal.fire('Deleted!', 'Doctor has been deleted.', 'success');\n        }, error => {\n          console.log(error);\n          console.error(`Error deleting doctor with ID ${doctorId}.`, error);\n          Swal.fire('Error!', 'There was an error deleting the doctor. Please try again.', 'error');\n        });\n      }\n    });\n  }\n  // Delete a schedule\n  deleteSchedule(scheduleId) {\n    if (!scheduleId) {\n      console.error('Invalid scheduleId:', scheduleId);\n      return;\n    }\n    // Use SweetAlert2 for confirmation dialog\n    Swal.fire({\n      title: 'Are you sure?',\n      text: \"You won't be able to revert this!\",\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#f39c12',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, delete it!',\n      cancelButtonText: 'Cancel'\n    }).then(result => {\n      if (result.isConfirmed) {\n        // Call your delete API\n        this.scheduleService.deleteSchedule(scheduleId).subscribe({\n          next: () => {\n            Swal.fire('Deleted!', 'The schedule has been deleted.', 'success');\n            // refresh list\n            this.getSchedules();\n          },\n          error: err => {\n            Swal.fire('Error!', 'Failed to delete the schedule.', 'error');\n          }\n        });\n      }\n    });\n  }\n  // Helper method to map the selected day abbreviation to the full day name\n  getFullDayName(day) {\n    return DAY_MAPPING[day] || day;\n  }\n  // Reset the form after saving or updating a schedule\n  resetForm() {\n    console.log('Resetting form...');\n    this.selectedDay = '';\n    this.fromTime = '';\n    this.toTime = '';\n    this.selectedValue1 = 'AM';\n    this.selectedValue2 = 'AM';\n    this.isClosed = false;\n    this.holidayDate = false;\n  }\n  // General error handler\n  handleError(error) {\n    console.error('Error occurred:', error);\n    alert('An error occurred. Please try again.');\n  }\n  loadExistingServices() {\n    this.clinicId = Number(localStorage.getItem('clinicId')); // Retrieve clinicId from local storage\n    console.log(this.clinicId + 'hello');\n    this.scheduleService.getClinicServicesByClinicId(this.clinicId).subscribe(services => {\n      this.existingServices = services; // Store the existing services\n    }, error => {\n      console.error('Error loading existing services', error);\n      // Handle error response, e.g., show an error message\n    });\n  }\n\n  saveService() {\n    if (this.selectedServiceId !== null && this.clinicId !== null) {\n      // Debugging logs\n      console.log('Selected Service ID:', this.selectedServiceId);\n      console.log('Existing Services:', this.existingServices);\n      // Check if the service is already added\n      const isServiceAlreadyAdded = this.existingServices.some(service => service.services.clinicServiceCategoryId === Number(this.selectedServiceId) // Access the service ID correctly\n      );\n\n      console.log('Is Service Already Added:', isServiceAlreadyAdded); // Debugging log\n      if (isServiceAlreadyAdded) {\n        Swal.fire({\n          icon: 'warning',\n          title: 'Service Already Added',\n          text: 'This service has already been added for the selected clinic.'\n        });\n        return; // Exit the function early\n      }\n      // Create the clinic service object\n      const clinicService = {\n        clinics: {\n          clinicId: this.clinicId\n        },\n        services: {\n          clinicServiceCategoryId: this.selectedServiceId\n        } // Ensure this matches your interface\n      };\n      // Save the new clinic service\n      this.scheduleService.saveClinicService(clinicService).subscribe(response => {\n        // if(response.status == false){\n        // }\n        console.log('Service saved successfully', response);\n        this.loadExistingServices(); // Reload existing services to update the list\n        Swal.fire({\n          icon: 'success',\n          title: 'Service Added',\n          text: 'The service has been successfully added.'\n        });\n      }, error => {\n        console.error('Error saving service', error);\n        Swal.fire({\n          icon: 'error',\n          title: 'Error Saving Service',\n          text: 'There was an error saving the service. Please try again.'\n        });\n      });\n    } else {\n      console.warn('Selected service or clinic ID is missing');\n      Swal.fire({\n        icon: 'info',\n        title: 'Missing Information',\n        text: 'Please select a service before saving.'\n      });\n    }\n  }\n  deleteService(clinicServiceId) {\n    if (clinicServiceId === null) {\n      console.warn('Service ID is missing');\n      Swal.fire({\n        icon: 'info',\n        title: 'Missing Information',\n        text: 'Unable to delete the service. Please try again.'\n      });\n      return;\n    }\n    // Show confirmation dialog\n    Swal.fire({\n      title: 'Are you sure?',\n      text: \"You won't be able to revert this!\",\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#f39c12',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, delete it!',\n      cancelButtonText: 'Cancel'\n    }).then(result => {\n      if (result.isConfirmed) {\n        // Call the delete API\n        this.scheduleService.deleteClinicService(clinicServiceId, Number(this.clinicId)).subscribe({\n          next: response => {\n            if (response.status === 'true') {\n              Swal.fire({\n                icon: 'success',\n                title: 'Service Deleted',\n                text: response.message\n              });\n            } else {\n              Swal.fire({\n                icon: 'error',\n                title: 'Error Deleting Service',\n                text: response.message\n              });\n            }\n            // Refresh the list\n            this.loadExistingServices();\n          },\n          error: err => {\n            console.error('Error deleting service', err);\n            Swal.fire({\n              icon: 'error',\n              title: 'Error Deleting Service',\n              text: 'There was an error deleting the service. Please try again.'\n            });\n          }\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ClinicSetupClinicComponent_Factory(t) {\n    return new (t || ClinicSetupClinicComponent)(i0.ɵɵdirectiveInject(i1.ScheduleService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DoctorService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClinicSetupClinicComponent,\n    selectors: [[\"app-clinic-setup-clinic\"]],\n    decls: 131,\n    vars: 9,\n    consts: [[1, \"mt-3\", \"container\", 2, \"width\", \"100%\"], [1, \"mb-3\", \"first-row\"], [1, \"d-flex\", \"flex-column\", \"first-row-right\"], [\"id\", \"text\", 1, \"set-date-time\"], [\"id\", \"date-time-container\", 1, \"d-flex\"], [1, \"d-flex\"], [1, \"day-col\", \"d-flex\", \"flex-row\", \"flex-wrap\", \"overflow-auto\", 2, \"gap\", \"15px\", \"justify-content\", \"center\", \"align-items\", \"center\"], [\"id\", \"day-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"mb-2\", \"mark-holidays\", 2, \"margin-top\", \"50px\", \"justify-content\", \"space-between\"], [1, \"\", 2, \"gap\", \"4px\", \"display\", \"flex\", \"cursor\", \"pointer\"], [\"type\", \"checkbox\", \"id\", \"holidayDate\", 2, \"color\", \"#FB751E\", \"background-color\", \"#FB751E\", \"cursor\", \"pointer\", 3, \"ngModel\", \"ngModelChange\"], [\"href\", \"#\", \"id\", \"markAsHolidayLink\", 1, \"disabled-link\"], [1, \"bi\", \"bi-calendar-event\"], [1, \"checkbox\", 2, \"display\", \"flex\"], [\"type\", \"checkbox\", \"name\", \"closedStatus\", \"value\", \"closed\", 3, \"ngModel\", \"ngModelChange\"], [1, \"d-flex\", \"flex-column\", \"first-row-left\"], [\"id\", \"text\", 1, \"open-hours\"], [\"id\", \"open-hours\", 1, \"flex-grow-1\", \"d-flex\", \"flex-column\"], [1, \"mb-5\"], [1, \"d-flex\", \"flex-row\", \"align-items-center\"], [\"type\", \"time\", 1, \"time\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"d-flex\", \"flex-row\", \"align-items-center\"], [1, \"col-12\"], [\"id\", \"save-btn\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"row\", \"mb-10\"], [1, \"col-md-12\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"container\", \"rounded-container\", \"p-4\", \"mt-3\", \"col-12\"], [1, \"text-dark\"], [1, \"col-12\", \"align-items\"], [1, \"form-group\", \"row\", \"d-flex\", \"col-12\"], [1, \"mb-4\", \"col-12\", 2, \"width\", \"600px\"], [\"for\", \"serviceIds\", 1, \"form-label\"], [\"id\", \"serviceIds\", \"name\", \"serviceIds\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [\"value\", \"6\"], [1, \"form-group\", \"mb-4\", \"col-12\"], [\"id\", \"save-btn-service\", 1, \"btn\", \"btn-custom\", 3, \"click\"], [1, \"table\", \"table-bordered\"], [1, \"table-light\"], [1, \"mb-3\", 2, \"width\", \"100%\"], [1, \"d-flex\", \"flex-column\"], [\"id\", \"set-date-time\", 1, \"flex-grow-1\", \"d-flex\", \"flex-column\"], [1, \"mb-2\"], [1, \"mb-11\", \"d-flex\", 2, \"flex-wrap\", \"wrap\", \"align-items\", \"center\", \"justify-content\", \"space-between\"], [1, \"\", 2, \"padding-right\", \"40px\"], [1, \"\", 2, \"margin-top\", \"-10px\"], [\"routerLink\", \"/clinic/list-doctor\", \"id\", \"markAsHolidayLink\"], [1, \"\", 2, \"margin-top\", \"10px\", \"margin-bottom\", \"10px\", \"width\", \"100%\"], [\"id\", \"add-Doctors-btn\", \"routerLink\", \"/clinic/list-doctor\", 1, \"btn\", \"btn-primary\"], [1, \"fa\", \"fa-arrow-circle-right\", \"icon\"], [1, \"row\", \"mt-3\"], [\"id\", \"day-btn\", 3, \"click\"], [\"hidden\", \"\", \"data-label\", \"Clinic Id\"], [\"data-label\", \"Day\"], [\"data-label\", \"From\"], [4, \"ngIf\", \"ngIfElse\"], [\"holidayPlaceholder\", \"\"], [\"data-label\", \"Holiday Date\"], [\"data-label\", \"Actions\"], [1, \"btn\", \"btn-warning\", \"btn-sm\", 2, \"margin-right\", \"10px\", 3, \"click\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [\"data-label\", \"Added Services\"], [\"data-label\", \"Action\"], [1, \"btn\", \"btn-danger\", \"btn-rounded\", 3, \"click\"], [\"data-label\", \"Doctor ID\"], [\"data-label\", \"Name\"], [\"data-label\", \"Reg No\"], [1, \"fa\", \"fa-trash\"]],\n    template: function ClinicSetupClinicComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\")(4, \"p\", 3)(5, \"b\");\n        i0.ɵɵtext(6, \"Set Date and Time\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5)(9, \"p\");\n        i0.ɵɵtext(10, \"Days\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 6);\n        i0.ɵɵtemplate(12, ClinicSetupClinicComponent_button_12_Template, 2, 3, \"button\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 8)(14, \"label\", 9)(15, \"input\", 10);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicSetupClinicComponent_Template_input_ngModelChange_15_listener($event) {\n          return ctx.holidayDate = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"a\", 11);\n        i0.ɵɵtext(17, \"Mark Holidays \");\n        i0.ɵɵelement(18, \"i\", 12);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 13)(20, \"label\")(21, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicSetupClinicComponent_Template_input_ngModelChange_21_listener($event) {\n          return ctx.isClosed = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22, \" Mark as Closed\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(23, \"div\", 15)(24, \"p\", 16)(25, \"b\");\n        i0.ɵɵtext(26, \"Open Hours\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 17)(28, \"div\", 18)(29, \"p\");\n        i0.ɵɵtext(30, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"div\", 19)(32, \"input\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicSetupClinicComponent_Template_input_ngModelChange_32_listener($event) {\n          return ctx.fromTime = $event;\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 18)(34, \"p\");\n        i0.ɵɵtext(35, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 21)(37, \"input\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicSetupClinicComponent_Template_input_ngModelChange_37_listener($event) {\n          return ctx.toTime = $event;\n        });\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(38, \"div\", 22)(39, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_Template_button_click_39_listener() {\n          return ctx.saveSchedule();\n        });\n        i0.ɵɵtext(40, \"Save\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(41, \"div\", 24)(42, \"div\", 25)(43, \"h4\");\n        i0.ɵɵtext(44, \"Schedule List\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"div\", 26)(46, \"table\", 27)(47, \"thead\")(48, \"tr\")(49, \"th\");\n        i0.ɵɵtext(50, \"Day\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"th\");\n        i0.ɵɵtext(52, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"th\");\n        i0.ɵɵtext(54, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"th\");\n        i0.ɵɵtext(56, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"th\");\n        i0.ɵɵtext(58, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(59, \"tbody\");\n        i0.ɵɵtemplate(60, ClinicSetupClinicComponent_tr_60_Template, 20, 7, \"tr\", 28);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(61, \"div\", 29)(62, \"h3\", 30);\n        i0.ɵɵtext(63, \"Add services\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(64, \"div\", 31)(65, \"div\", 32)(66, \"div\", 33)(67, \"label\", 34);\n        i0.ɵɵtext(68, \"Select Services:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"select\", 35);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicSetupClinicComponent_Template_select_ngModelChange_69_listener($event) {\n          return ctx.selectedServiceId = $event;\n        });\n        i0.ɵɵelementStart(70, \"option\", 36);\n        i0.ɵɵtext(71, \"Dental Bonding\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(72, \"option\", 37);\n        i0.ɵɵtext(73, \"Cosmetic Fillings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"option\", 38);\n        i0.ɵɵtext(75, \"Invisalign\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"option\", 39);\n        i0.ɵɵtext(77, \"Teeth Cleanings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(78, \"option\", 40);\n        i0.ɵɵtext(79, \"Root Canal Therapy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"option\", 41);\n        i0.ɵɵtext(81, \"Dental Sealants\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(82, \"div\", 42)(83, \"button\", 43);\n        i0.ɵɵlistener(\"click\", function ClinicSetupClinicComponent_Template_button_click_83_listener() {\n          return ctx.saveService();\n        });\n        i0.ɵɵtext(84, \"Save\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(85, \"div\", 26)(86, \"table\", 44)(87, \"thead\", 45)(88, \"tr\")(89, \"th\");\n        i0.ɵɵtext(90, \"Added Services\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(91, \"th\");\n        i0.ɵɵtext(92, \"Action\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(93, \"tbody\");\n        i0.ɵɵtemplate(94, ClinicSetupClinicComponent_tr_94_Template, 6, 1, \"tr\", 28);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(95, \"div\");\n        i0.ɵɵelement(96, \"br\")(97, \"br\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(98, \"div\", 46)(99, \"div\", 47)(100, \"div\", 48)(101, \"div\", 49)(102, \"div\", 50)(103, \"div\", 51)(104, \"p\", 3);\n        i0.ɵɵtext(105, \"Add Doctors\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(106, \"div\", 52)(107, \"a\", 53);\n        i0.ɵɵtext(108, \"View All Doctors\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(109, \"div\", 54)(110, \"a\", 55);\n        i0.ɵɵelement(111, \"i\", 56);\n        i0.ɵɵtext(112, \" Add Doctors \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(113, \"div\", 57)(114, \"div\", 22)(115, \"h5\");\n        i0.ɵɵtext(116, \"Doctor List\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"div\", 26)(118, \"table\", 27)(119, \"thead\")(120, \"tr\")(121, \"th\");\n        i0.ɵɵtext(122, \"Doctor ID\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(123, \"th\");\n        i0.ɵɵtext(124, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(125, \"th\");\n        i0.ɵɵtext(126, \"Reg No\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(127, \"th\");\n        i0.ɵɵtext(128, \"Actions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(129, \"tbody\");\n        i0.ɵɵtemplate(130, ClinicSetupClinicComponent_tr_130_Template, 11, 3, \"tr\", 28);\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngForOf\", ctx.dates);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.holidayDate);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngModel\", ctx.isClosed);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngModel\", ctx.fromTime);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.toTime);\n        i0.ɵɵadvance(23);\n        i0.ɵɵproperty(\"ngForOf\", ctx.schedules);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngModel\", ctx.selectedServiceId);\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"ngForOf\", ctx.existingServices);\n        i0.ɵɵadvance(36);\n        i0.ɵɵproperty(\"ngForOf\", ctx.doctors);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i2.RouterLink, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n    styles: [\"\\n\\n\\nbody[_ngcontent-%COMP%] {\\n    font-family: Arial, sans-serif;\\n    background-color: #f9f9f9;\\n    color: #333;\\n \\n}\\n\\n.border-orange[_ngcontent-%COMP%] {\\n    border: 2px solid orange !important;\\n}\\n\\n\\n\\n\\n\\n\\n.row[_ngcontent-%COMP%] {\\n    margin-bottom: 20px;\\n}\\n\\n#set-date-time[_ngcontent-%COMP%] {\\n    border: 2px solid #00000040;\\n    border-radius: 30px;\\n    padding: 20px;\\n    background-color: #fff;\\n}\\n\\n#date-time-container[_ngcontent-%COMP%] {\\n    border: 2px solid #00000040;\\n    border-radius: 30px;\\n    padding: 20px;\\n    background-color: #fff;\\n    width: 90%;\\n    flex-direction: column;\\n    \\n}\\n\\n.first-row-right[_ngcontent-%COMP%]{\\n    margin-bottom: 30px;\\n    justify-content: center;\\n    width: 50%;\\n}\\n\\n.first-row-left[_ngcontent-%COMP%]{\\n    margin-bottom: 30px;\\n    padding-left: 20px;\\n    width: 50%;\\n}\\n\\n#open-hours[_ngcontent-%COMP%] {\\n    \\n    background-color: #fff;\\n}\\n\\n#from-hours[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    border: 2px solid #807a78;\\n    border-radius: 5px;\\n    padding: 5px;\\n    display: inline-block;\\n    margin-top: 10px;\\n}\\n\\n#to-hours[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    border: 2px solid #807a78;\\n    border-radius: 5px;\\n    padding: 5px;\\n    display: inline-block;\\n    margin-top: 10px;\\n}\\n\\n.dropdown[_ngcontent-%COMP%] {\\n    padding: 5px;\\n    display: inline-block;\\n    margin-top: 10px;\\n}\\n\\n#save-btn[_ngcontent-%COMP%] {\\n    width: 121px;\\n    height: 35px;\\n    font-size: 16px;\\n    color: white;\\n    background: linear-gradient(to right, #B93426, #FB751E);\\n    border: none;\\n    border-radius: 40px;\\n    margin-top: 15px;\\n    float: right;\\n}\\n\\n#save-btn-service[_ngcontent-%COMP%] {\\n    width: 121px;\\n    height: 35px;\\n    font-size: 16px;\\n    color: white;\\n    background: linear-gradient(to right, #B93426, #FB751E);\\n    border: none;\\n    border-radius: 40px;\\n    margin-top: 15px;\\n    \\n}\\n\\n\\n\\n\\n\\n\\n.set-service[_ngcontent-%COMP%] {\\n    margin-right: 100px;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n#make-appointment-btn[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    background: linear-gradient(to right, #FF512F, #F09819);\\n    border: none;\\n    border-radius: 40px;\\n    color: white;\\n    padding: 10px 20px;\\n}\\n\\n#make-appointment-btn[_ngcontent-%COMP%]:hover {\\n    background: linear-gradient(to right, #FF512F, #F09819);\\n    color: white;\\n}\\n\\n.day-col[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-direction: row;\\n    max-width: 595px;\\n    overflow-x: auto;\\n}\\n\\n.day-col[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-direction: row;\\n    white-space: nowrap;\\n}\\n\\n.time[_ngcontent-%COMP%]{\\n    width: 100%;\\n    height: 40px;\\n    border-radius: 10px;\\n}\\n\\n#dropdownMenuButton1[_ngcontent-%COMP%] {\\n    color: #000;\\n    background-color: #fff;\\n    border: none;\\n}\\n\\n#day-btn[_ngcontent-%COMP%] {\\n    width: 59.57px;\\n    height: 58.66px;\\n    font-size: 20px;\\n    background: #fff;\\n    color: #D85322;\\n    border: 1px solid #D85322;\\n    border-radius: 10px;\\n}\\n\\n#day-btn[_ngcontent-%COMP%]:hover {\\n    background: #FB751E;\\n    color: white;\\n    border: none;\\n}\\n#day-btn.active[_ngcontent-%COMP%] {\\n    background: #FB751E;  \\n\\n    color: white;  \\n\\n    border: none;  \\n\\n}\\n\\n#markAsHolidayLink[_ngcontent-%COMP%] {\\n    color: #FB751E;\\n    font-style: italic;\\n}\\n\\n.checkbox[_ngcontent-%COMP%] {\\n    text-align: right;\\n}\\n\\n#add-Doctors-btn[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    width: 232px;\\n    height: 54px;\\n    font-size: 16px;\\n    background: linear-gradient(to right, #FB751E, #B93426);\\n    border: none;\\n    border-radius: 40px;\\n    color: white;\\n    \\n\\n}\\n\\n.icon[_ngcontent-%COMP%] {\\n    font-size: 40px;\\n    margin-right: 40px;\\n}\\n\\n\\n\\n\\n\\n\\n\\n#text[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    font-weight: bold;\\n}\\n\\n.sidebar-container[_ngcontent-%COMP%] {\\n    height: calc(100vh - 80px);\\n    \\n\\n    width: 20%;\\n    overflow-y: auto;\\n    display: inline-block;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 80px);\\n    \\n\\n\\n    overflow-y: auto;\\n    padding: 16px;\\n    display: inline-block;\\n}\\n\\n\\n.col-5[_ngcontent-%COMP%]{\\n    margin-top: 80%;\\n}\\n.row-mb-3[_ngcontent-%COMP%]{\\n    margin-top: 2%;\\n}\\n\\n\\n@media (max-width: 768px) {\\n    .sidebar-container[_ngcontent-%COMP%], .main-content[_ngcontent-%COMP%] {\\n        width: 100%;\\n        display: block;\\n    }\\n}\\n.rounded-container[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    border-radius: 20px;\\n    padding: 20px;\\n    border: 2px solid #00000040;\\n  \\n}\\n\\n.text-dark[_ngcontent-%COMP%] {\\n    color: #000;\\n    font-weight: bold;\\n    font-size: 1.25rem;\\n}\\n\\n\\n\\n.btn-custom[_ngcontent-%COMP%] {\\n\\n    color: #fff;\\n    border-radius: 20px;\\n    padding: 0.5rem 2rem;\\n    transition: background-color 0.3s ease;\\n    \\n}\\n\\n.btn-custom[_ngcontent-%COMP%]:hover {\\n    background-color: #ff3d3d;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n    background-color: #fff;\\n    border-radius: 10px;\\n    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);\\n\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    vertical-align: middle;\\n    text-align: center;\\n}\\n\\n.btn-rounded[_ngcontent-%COMP%] {\\n    border-radius: 20px;\\n  \\n    color: #fff;\\n    transition: background-color 0.3s ease;\\n    padding: 0.25rem 1rem;\\n}\\n\\n.btn-rounded[_ngcontent-%COMP%]:hover {\\n    background-color: #ff3d3d;\\n}\\n.form-select[_ngcontent-%COMP%]{\\n    width: 50%;\\n    border: 2px solid #00000040;\\n    border-radius: 20px;\\n}\\n\\n\\n\\n.disabled-link[_ngcontent-%COMP%] {\\n    pointer-events: none;\\n    color: gray;\\n    text-decoration: none;\\n}\\n@media only screen and (max-width: 768px) {\\n    .col-md-7[_ngcontent-%COMP%]   .col-md-5[_ngcontent-%COMP%] {\\n        flex: 100%;\\n        max-width: 100%;\\n        margin-bottom: 20px;\\n        \\n    }\\n\\n    \\n\\n\\n\\n\\n\\n\\n\\n button#day-btn[_ngcontent-%COMP%] {\\n        width: 50px;\\n        height: 50px;\\n        margin: 5px;\\n    }\\n    .set-date-time[_ngcontent-%COMP%], .open-hours[_ngcontent-%COMP%] {\\n        text-align: center;\\n    }\\n\\n    .row[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n    }\\n\\n    .checkbox[_ngcontent-%COMP%] {\\n        display: flex;\\n        justify-content: center;\\n        \\n    }\\n\\n    .mark-holidays[_ngcontent-%COMP%]{\\n        display: flex;\\n        flex-wrap: wrap; gap: 30px;\\n        justify-content: center;\\n      \\n    }\\n    #markAsHolidayLink[_ngcontent-%COMP%] {\\n        font-size: 14px;\\n    }\\n\\n    #save-btn[_ngcontent-%COMP%] {\\n        width: 100%;\\n        margin-top: 10px;\\n    }\\n}\\n\\n@media (max-width: 768px) {\\n    .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n        display: none;\\n    }\\n    #add-Doctors-btn[_ngcontent-%COMP%] {\\n       max-width: 100%;\\n        margin: 35px auto;\\n        font-size: 16px;\\n    }\\n    .table[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        display: block;\\n        width: 100%;\\n    }\\n    .table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n        margin-bottom: 15px;\\n    }\\n    .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        text-align: right;\\n        padding-left: 50%;\\n        position: relative;\\n    }\\n    .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]::before {\\n        content: attr(data-label);\\n        position: absolute;\\n        left: 0;\\n        width: 50%;\\n        padding-left: 15px;\\n        font-weight: bold;\\n        text-align: left;\\n    }\\n    .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n        display: none;\\n    }\\n    .table[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        display: block;\\n        width: 100%;\\n    }\\n    .table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n        margin-bottom: 15px;\\n    }\\n    .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n        text-align: right;\\n        padding-left: 50%;\\n        position: relative;\\n    }\\n    .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]::before {\\n        content: attr(data-label);\\n        position: absolute;\\n        left: 0;\\n        width: 50%;\\n        padding-left: 15px;\\n        font-weight: bold;\\n        text-align: left;\\n    }\\n    .form-group[_ngcontent-%COMP%]{\\n        margin-top: 15px;\\n       }\\n  \\n}\\n\\n@media (max-width: 1000px) {\\n    .first-row[_ngcontent-%COMP%]{\\n        display: flex;\\n        flex-direction: column;\\n        width: 100%;\\n    }\\n    .first-row-right[_ngcontent-%COMP%]{\\n        width: 100%;\\n        justify-content: center;\\n    }\\n    .first-row-left[_ngcontent-%COMP%]{\\n        width: 100%;\\n        justify-content: center;\\n        margin-top: 40px;\\n    }\\n    .first-row-left[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{\\n        max-width: 100%;\\n    }\\n    #date-time-container[_ngcontent-%COMP%] {\\n        border: 2px solid #00000040;\\n        border-radius: 30px;\\n        padding: 20px;\\n        background-color: #fff;\\n        width: 100%;\\n        \\n        \\n    }\\n    #save-btn[_ngcontent-%COMP%] {\\n        margin-top: -10px;\\n        margin-bottom: 20px;\\n    }\\n    .form-select[_ngcontent-%COMP%]{\\n        width: 60%;\\n    }\\n}\\n\\n\\n\\n   @media only screen and (min-width: 769px) {\\n    .align-items[_ngcontent-%COMP%]{\\n        margin-left: 30%;\\n       \\n       }\\n      \\n   }\\n\\n   .first-row[_ngcontent-%COMP%]{\\n    display: flex;\\nalign-items: center;\\n    width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************** */\"]\n  });\n}\nexport { ClinicSetupClinicComponent };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "ClinicSetupClinicComponent_button_12_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r6", "date_r4", "$implicit", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "selectDay", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "ctx_r0", "selected<PERSON>ay", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵtextInterpolate", "schedule_r7", "fromTime", "toTime", "ɵɵtemplate", "ClinicSetupClinicComponent_tr_60_ng_container_6_Template", "ClinicSetupClinicComponent_tr_60_ng_template_7_Template", "ɵɵtemplateRefExtractor", "ClinicSetupClinicComponent_tr_60_ng_container_10_Template", "ClinicSetupClinicComponent_tr_60_ng_template_11_Template", "ClinicSetupClinicComponent_tr_60_Template_button_click_16_listener", "_r17", "ctx_r16", "updateSchedule", "ClinicSetupClinicComponent_tr_60_Template_button_click_18_listener", "ctx_r18", "scheduleId", "deleteSchedule", "clinics", "clinicId", "date", "ɵɵproperty", "holidayDate", "_r9", "isClosed", "ClinicSetupClinicComponent_tr_94_Template_button_click_4_listener", "_r21", "service_r19", "ctx_r20", "deleteService", "clinicServiceId", "services", "clinicServiceCategoryName", "ClinicSetupClinicComponent_tr_130_Template_button_click_8_listener", "_r24", "doctor_r22", "ctx_r23", "deleteClinicDoctor", "doctorId", "ɵɵelement", "firstName", "regNo", "DAY_MAPPING", "Su", "M", "TU", "W", "TH", "F", "Sa", "REVERSE_DAY_MAPPING", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "ClinicSetupClinicComponent", "constructor", "scheduleService", "router", "doctorService", "dates", "disabledDays", "selectedValue1", "selectedValue2", "showHolidayDateInput", "isUpdateMode", "schedules", "editingScheduleId", "doctors", "selectedServiceId", "existingServices", "ngOnInit", "userIdString", "localStorage", "getItem", "console", "log", "loadClinicId", "getSchedules", "getDoctors", "loadExistingServices", "alert", "navigate", "validateTimeRange", "from", "Date", "to", "isNaN", "getTime", "fire", "clinicIdString", "parseInt", "userId", "getAllDoctorsFilter", "Number", "subscribe", "data", "error", "warn", "redirectToDoctorList", "toggleSidebar", "sidebarElement", "document", "getElementById", "classList", "toggle", "contains", "day", "includes", "getFullDayName", "toggleAmPm", "timeField", "validateForm", "storedUserId", "userIdNumber", "filter", "schedule", "map", "saveSchedule", "handleError", "fullDayName", "x", "updatedSchedule", "s", "push", "resetForm", "split", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "then", "result", "isConfirmed", "doctor", "cancelButtonText", "next", "err", "getClinicServicesByClinicId", "saveService", "isServiceAlreadyAdded", "some", "service", "clinicServiceCategoryId", "clinicService", "saveClinicService", "response", "deleteClinicService", "status", "message", "_", "ɵɵdirectiveInject", "i1", "ScheduleService", "i2", "Router", "i3", "DoctorService", "_2", "selectors", "decls", "vars", "consts", "template", "ClinicSetupClinicComponent_Template", "rf", "ctx", "ClinicSetupClinicComponent_button_12_Template", "ClinicSetupClinicComponent_Template_input_ngModelChange_15_listener", "$event", "ClinicSetupClinicComponent_Template_input_ngModelChange_21_listener", "ClinicSetupClinicComponent_Template_input_ngModelChange_32_listener", "ClinicSetupClinicComponent_Template_input_ngModelChange_37_listener", "ClinicSetupClinicComponent_Template_button_click_39_listener", "ClinicSetupClinicComponent_tr_60_Template", "ClinicSetupClinicComponent_Template_select_ngModelChange_69_listener", "ClinicSetupClinicComponent_Template_button_click_83_listener", "ClinicSetupClinicComponent_tr_94_Template", "ClinicSetupClinicComponent_tr_130_Template"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-setup-clinic\\clinic-setup-clinic.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-setup-clinic\\clinic-setup-clinic.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { DoctorService } from 'src/app/doctor/doctor.service';\r\nimport { ScheduleService } from './service/schedule.service';\r\nimport { ClinicServices, Schedule } from './service/schedule';\r\nimport Swal from 'sweetalert2';\r\n// Day mapping constant to map day abbreviations to full names\r\nconst DAY_MAPPING: { [key: string]: string } = {\r\n  Su: 'Sunday',\r\n  M: 'Monday',\r\n  TU: 'Tuesday',\r\n  W: 'Wednesday',\r\n  TH: 'Thursday',\r\n  F: 'Friday',\r\n  Sa: 'Saturday',\r\n};\r\n\r\n// Reverse Day mapping to map full names to abbreviations\r\nconst REVERSE_DAY_MAPPING: { [key: string]: string } = {\r\n  Sunday: 'Su',\r\n  Monday: 'M',\r\n  Tuesday: 'TU',\r\n  Wednesday: 'W',\r\n  Thursday: 'TH',\r\n  Friday: 'F',\r\n  Saturday: 'Sa',\r\n};\r\n\r\ninterface Doctor {\r\n  doctorId: number;\r\n  firstName: string;\r\n  regNo: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-clinic-setup-clinic',\r\n  templateUrl: './clinic-setup-clinic.component.html',\r\n  styleUrls: ['./clinic-setup-clinic.component.css'],\r\n})\r\nexport class ClinicSetupClinicComponent implements OnInit {\r\n  dates = ['Su', 'M', 'TU', 'W', 'TH', 'F', 'Sa']; // Days of the week\r\n  disabledDays: string[] = [];\r\n  selectedDay: string = ''; // Track the selected day\r\n  fromTime: string = ''; // Time input for \"from\"\r\n  toTime: string = ''; // Time input for \"to\"\r\n  selectedValue1: string = 'AM'; // Track AM/PM for 'from' time\r\n  selectedValue2: string = 'AM'; // Track AM/PM for 'to' time\r\n  showHolidayDateInput: boolean = false; // Toggle holiday date input visibility\r\n  isClosed: boolean = false; // Status for \"Mark as Closed\"\r\n  holidayDate: boolean = false; // Store the selected holiday date\r\n  isUpdateMode: boolean = false;\r\n  schedules: Schedule[] = []; // Array to store fetched schedules\r\n  clinicId: number = 0; // Initialize to a default value\r\n  editingScheduleId: number | null = null;\r\n  doctors: Doctor[] = []; // Array to store fetched doctors\r\n\r\n  constructor(\r\n    private scheduleService: ScheduleService,\r\n    private router: Router,\r\n    private doctorService: DoctorService\r\n  ) {}\r\n  ngOnInit(): void {\r\n    const userIdString = localStorage.getItem('userid'); // Retrieve userId from localStorage\r\n    const clinicId = localStorage.getItem('clinicId'); // Retrieve clinicId from localStorage\r\n    if (userIdString) {\r\n      console.log('Component initialized. Fetching schedules...');\r\n      this.loadClinicId(); // Load clinicId from localStorage\r\n      this.getSchedules(); // Fetch schedules when component initializes\r\n      this.getDoctors(); // Fetch doctors when the component initializes\r\n      this.loadExistingServices(); // Load existing services on component initialization\r\n    } else {\r\n      alert('User ID not found. Please log in or select a clinic.');\r\n      this.router.navigate(['/login']); // Redirect to login if userId is not found\r\n    }\r\n  }\r\n\r\n  validateTimeRange(fromTime: string, toTime: string): boolean {\r\n    // Convert strings (e.g. \"09:00\", \"17:00\") to Date objects\r\n    const from = new Date(`1970-01-01T${fromTime}:00`);\r\n    const to = new Date(`1970-01-01T${toTime}:00`);\r\n\r\n    if (this.isClosed && this.holidayDate) {\r\n      // Check if times are valid\r\n      if (isNaN(from.getTime()) || isNaN(to.getTime())) {\r\n        Swal.fire('Error', 'Invalid time format!', 'error');\r\n        return false;\r\n      }\r\n    }\r\n\r\n    // fromTime == toTime\r\n    if (from.getTime() === to.getTime()) {\r\n      Swal.fire(\r\n        'Invalid Time',\r\n        'From Time and To Time cannot be the same.',\r\n        'warning'\r\n      );\r\n      return false;\r\n    }\r\n\r\n    // toTime < fromTime\r\n    if (to.getTime() < from.getTime()) {\r\n      Swal.fire(\r\n        'Invalid Time',\r\n        'To Time must be later than From Time.',\r\n        'warning'\r\n      );\r\n      return false;\r\n    }\r\n\r\n    return true; // valid\r\n  }\r\n\r\n  // Load the clinicId from localStorage\r\n  loadClinicId(): void {\r\n    const clinicIdString = localStorage.getItem('clinicId'); // Retrieve the clinicId from localStorage\r\n    if (clinicIdString) {\r\n      this.clinicId = parseInt(clinicIdString, 10); // Convert it to a number\r\n      console.log('Loaded Clinic ID from localStorage:', this.clinicId); // Log the loaded clinicId\r\n    } else {\r\n      console.log('No Clinic ID found in localStorage.'); // Log if no clinicId was found\r\n    }\r\n  }\r\n\r\n  // Fetch all doctors from the backend\r\n  getDoctors(): void {\r\n    const userId = localStorage.getItem('userid'); // Retrieve userId from localStorage\r\n    if (userId) {\r\n      console.log('Fetching doctors for userId:', userId);\r\n      this.doctorService.getAllDoctorsFilter('Assign', Number(localStorage.getItem('clinicId'))).subscribe(\r\n        (data: Doctor[]) => {\r\n          this.doctors = data; // Populate the doctors array\r\n          console.log('Doctors fetched successfully:', data);\r\n        },\r\n        (error) => {\r\n          console.error('Error fetching doctors:', error);\r\n        }\r\n      );\r\n    } else {\r\n      console.warn('No userId found in localStorage.');\r\n    }\r\n  }\r\n\r\n  redirectToDoctorList(): void {\r\n    this.router.navigate(['./list-doctor']);\r\n  }\r\n\r\n  // Toggle the sidebar visibility\r\n  toggleSidebar() {\r\n    const sidebarElement = document.getElementById('sidebar');\r\n    if (sidebarElement) {\r\n      sidebarElement.classList.toggle('open');\r\n      console.log(\r\n        'Sidebar toggled:',\r\n        sidebarElement.classList.contains('open') ? 'opened' : 'closed'\r\n      );\r\n    } else {\r\n      console.error('Sidebar element not found.');\r\n    }\r\n  }\r\n  // Function to select a day and keep the button \"pressed\"\r\n  selectDay(day: string): void {\r\n    if (\r\n      this.disabledDays.includes(day) &&\r\n      (!this.isUpdateMode || this.selectedDay !== day)\r\n    ) {\r\n      // Use SweetAlert dialog\r\n      Swal.fire(\r\n        'Info!',\r\n        `${this.getFullDayName(day)} is already scheduled.`,\r\n        'info'\r\n      );\r\n      return;\r\n    }\r\n    this.selectedDay = this.selectedDay === day ? '' : day;\r\n  }\r\n\r\n  // Toggle between AM/PM for both From and To time\r\n  toggleAmPm(timeField: string) {\r\n    if (timeField === 'from') {\r\n      this.selectedValue1 = this.selectedValue1 === 'AM' ? 'PM' : 'AM';\r\n      console.log('From time AM/PM toggled to:', this.selectedValue1);\r\n    } else {\r\n      this.selectedValue2 = this.selectedValue2 === 'AM' ? 'PM' : 'AM';\r\n      console.log('To time AM/PM toggled to:', this.selectedValue2);\r\n    }\r\n  }\r\n\r\n  // Validate the form before saving\r\n  validateForm(): boolean {\r\n    console.log('Selected Day:', this.selectedDay);\r\n    console.log('From Time:', this.fromTime);\r\n    console.log('To Time:', this.toTime);\r\n    console.log('Clinic ID:', this.clinicId);\r\n    console.log('Closed:', this.isClosed);\r\n\r\n    if (!this.fromTime || !this.toTime || !this.selectedDay) {\r\n      if (this.holidayDate == true && this.selectedDay) {\r\n        return true; // Assuming no time validation is needed for holidays/closed days\r\n      }\r\n      if (this.isClosed == true) {\r\n        return true;\r\n      }\r\n      // Use SweetAlertdialog\r\n      Swal.fire(\r\n        'Missing Information',\r\n        'Time fields and Date cannot be empty.',\r\n        'info'\r\n      );\r\n\r\n      // Swal.fire({\r\n      //   title: 'Error!',\r\n      //   text: 'Time fields and Date cannot be empty.',\r\n      //   icon: 'error',\r\n      //   confirmButtonText: 'Try Again',\r\n      // });\r\n      return false; // times or date not choose then get error message\r\n    } else if (!this.clinicId) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // Get all schedules from the backend\r\n  getSchedules(): void {\r\n    this.scheduleService.getSchedules().subscribe(\r\n      (data: Schedule[]) => {\r\n        const storedUserId = localStorage.getItem('clinicId'); // Use userId\r\n        if (storedUserId) {\r\n          const userIdNumber = Number(storedUserId);\r\n          this.schedules = data.filter(\r\n            (schedule) => schedule.clinics?.clinicId === userIdNumber\r\n          );\r\n\r\n          // Convert full day names in schedules to abbreviations for comparison\r\n          this.disabledDays = this.schedules.map(\r\n            (schedule) => REVERSE_DAY_MAPPING[schedule.date]\r\n          );\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error fetching schedules:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  // Save the selected schedule\r\n  saveSchedule(): void {\r\n    // Validate the form inputs before proceeding\r\n    if (!this.validateForm()) return;\r\n\r\n    // Retrieve clinicId from localStorage\r\n    const clinicIdString = localStorage.getItem('clinicId');\r\n    const clinicId = clinicIdString ? parseInt(clinicIdString, 10) : null;\r\n\r\n    // Handle the case where clinicId is not found or invalid\r\n    if (clinicId === null) {\r\n      this.handleError('Clinic ID not found or invalid');\r\n      return;\r\n    }\r\n\r\n    const fullDayName = this.getFullDayName(this.selectedDay);\r\n\r\n    const schedule: Schedule = {\r\n      clinics: { clinicId: clinicId }, // Use clinicId here\r\n      date: fullDayName,\r\n      fromTime: this.fromTime, // Save in 24-hour format\r\n      toTime: this.toTime, // Save in 24-hour format\r\n      // isClosed: this.isClosed,\r\n      holidayDate: this.holidayDate || this.isClosed,\r\n      scheduleId: this.editingScheduleId,\r\n    };\r\n\r\n    let x = this.validateTimeRange(this.fromTime, this.toTime);\r\n    console.log(x);\r\n    if (!x) {\r\n      return;\r\n    }\r\n\r\n    // Check if we're in update mode\r\n    if (this.isUpdateMode && this.editingScheduleId !== null) {\r\n      this.scheduleService\r\n        .updateSchedule(this.editingScheduleId, schedule)\r\n        .subscribe(\r\n          (updatedSchedule: Schedule) => {\r\n            // Update the schedules list by removing the old schedule and adding the updated one\r\n            this.schedules = this.schedules.filter(\r\n              (s) => s.scheduleId !== this.editingScheduleId\r\n            );\r\n            this.schedules.push(updatedSchedule);\r\n            this.disabledDays.push(this.selectedDay);\r\n            this.getSchedules(); // Refresh the schedules list\r\n            this.resetForm(); // Reset the form inputs\r\n          },\r\n          (error) => this.handleError(error) // Handle any errors\r\n        );\r\n    } else {\r\n      // In create mode, save a new schedule\r\n      this.scheduleService.saveSchedule(schedule).subscribe(\r\n        (data: Schedule) => {\r\n          this.disabledDays.push(this.selectedDay);\r\n          this.getSchedules(); // Refresh the schedules list\r\n          this.resetForm(); // Reset the form inputs\r\n        },\r\n        (error) => this.handleError(error) // Handle any errors\r\n      );\r\n    }\r\n  }\r\n\r\n  // Update a specific schedule\r\n  updateSchedule(schedule: Schedule): void {\r\n    this.isUpdateMode = true;\r\n    this.editingScheduleId = schedule.scheduleId || null;\r\n\r\n    this.selectedDay = REVERSE_DAY_MAPPING[schedule.date];\r\n    this.disabledDays = this.dates.filter((date) => date !== this.selectedDay);\r\n    this.fromTime = schedule.fromTime.split(' ')[0];\r\n    this.selectedValue1 = schedule.fromTime.split(' ')[1];\r\n    this.toTime = schedule.toTime.split(' ')[0];\r\n    this.selectedValue2 = schedule.toTime.split(' ')[1];\r\n  }\r\n\r\n  deleteClinicDoctor(doctorId: number): void {\r\n    // Use SweetAlert2 for confirmation dialog\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: \"You won't be able to revert this!\",\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#3085d6',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: 'Yes, delete it!',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        // Call the deleteDoctor method with doctorId\r\n        this.doctorService.deleteClinicDoctor(doctorId).subscribe(\r\n          () => {\r\n            // Remove the doctor from the list after successful deletion\r\n            this.doctors = this.doctors.filter(\r\n              (doctor) => doctor.doctorId !== doctorId\r\n            );\r\n            console.log(`Doctor with ID ${doctorId} deleted successfully.`);\r\n            Swal.fire('Deleted!', 'Doctor has been deleted.', 'success');\r\n          },\r\n          (error) => {\r\n            console.log(error);\r\n            console.error(`Error deleting doctor with ID ${doctorId}.`, error);\r\n            Swal.fire(\r\n              'Error!',\r\n              'There was an error deleting the doctor. Please try again.',\r\n              'error'\r\n            );\r\n          }\r\n        );\r\n      }\r\n    });\r\n  }\r\n\r\n  // Delete a schedule\r\n  deleteSchedule(scheduleId: number | undefined): void {\r\n    if (!scheduleId) {\r\n      console.error('Invalid scheduleId:', scheduleId);\r\n      return;\r\n    }\r\n    // Use SweetAlert2 for confirmation dialog\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: \"You won't be able to revert this!\",\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#f39c12',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: 'Yes, delete it!',\r\n      cancelButtonText: 'Cancel',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        // Call your delete API\r\n        this.scheduleService.deleteSchedule(scheduleId).subscribe({\r\n          next: () => {\r\n            Swal.fire('Deleted!', 'The schedule has been deleted.', 'success');\r\n            // refresh list\r\n            this.getSchedules();\r\n          },\r\n          error: (err) => {\r\n            Swal.fire('Error!', 'Failed to delete the schedule.', 'error');\r\n          },\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  // Helper method to map the selected day abbreviation to the full day name\r\n  getFullDayName(day: string): string {\r\n    return DAY_MAPPING[day] || day;\r\n  }\r\n\r\n  // Reset the form after saving or updating a schedule\r\n  resetForm(): void {\r\n    console.log('Resetting form...');\r\n    this.selectedDay = '';\r\n    this.fromTime = '';\r\n    this.toTime = '';\r\n    this.selectedValue1 = 'AM';\r\n    this.selectedValue2 = 'AM';\r\n    this.isClosed = false;\r\n    this.holidayDate = false;\r\n  }\r\n\r\n  // General error handler\r\n  handleError(error: any): void {\r\n    console.error('Error occurred:', error);\r\n    alert('An error occurred. Please try again.');\r\n  }\r\n  selectedServiceId: number | null = null; // Bind to ngModel\r\n  existingServices: any[] = []; // To store existing services\r\n\r\n  loadExistingServices(): void {\r\n    this.clinicId = Number(localStorage.getItem('clinicId')); // Retrieve clinicId from local storage\r\n    console.log(this.clinicId + 'hello');\r\n    this.scheduleService.getClinicServicesByClinicId(this.clinicId).subscribe(\r\n      (services) => {\r\n        this.existingServices = services; // Store the existing services\r\n      },\r\n      (error) => {\r\n        console.error('Error loading existing services', error);\r\n        // Handle error response, e.g., show an error message\r\n      }\r\n    );\r\n  }\r\n\r\n  saveService(): void {\r\n    if (this.selectedServiceId !== null && this.clinicId !== null) {\r\n      // Debugging logs\r\n      console.log('Selected Service ID:', this.selectedServiceId);\r\n      console.log('Existing Services:', this.existingServices);\r\n\r\n      // Check if the service is already added\r\n      const isServiceAlreadyAdded = this.existingServices.some(\r\n        (service) =>\r\n          service.services.clinicServiceCategoryId ===\r\n          Number(this.selectedServiceId) // Access the service ID correctly\r\n      );\r\n\r\n      console.log('Is Service Already Added:', isServiceAlreadyAdded); // Debugging log\r\n\r\n      if (isServiceAlreadyAdded) {\r\n        Swal.fire({\r\n          icon: 'warning',\r\n          title: 'Service Already Added',\r\n          text: 'This service has already been added for the selected clinic.',\r\n        });\r\n        return; // Exit the function early\r\n      }\r\n\r\n      // Create the clinic service object\r\n      const clinicService: ClinicServices = {\r\n        clinics: { clinicId: this.clinicId },\r\n        services: { clinicServiceCategoryId: this.selectedServiceId }, // Ensure this matches your interface\r\n      };\r\n\r\n      // Save the new clinic service\r\n      this.scheduleService.saveClinicService(clinicService).subscribe(\r\n        (response) => {\r\n          // if(response.status == false){\r\n\r\n          // }\r\n          console.log('Service saved successfully', response);\r\n          this.loadExistingServices(); // Reload existing services to update the list\r\n          Swal.fire({\r\n            icon: 'success',\r\n            title: 'Service Added',\r\n            text: 'The service has been successfully added.',\r\n          });\r\n        },\r\n        (error) => {\r\n          console.error('Error saving service', error);\r\n          Swal.fire({\r\n            icon: 'error',\r\n            title: 'Error Saving Service',\r\n            text: 'There was an error saving the service. Please try again.',\r\n          });\r\n        }\r\n      );\r\n    } else {\r\n      console.warn('Selected service or clinic ID is missing');\r\n      Swal.fire({\r\n        icon: 'info',\r\n        title: 'Missing Information',\r\n        text: 'Please select a service before saving.',\r\n      });\r\n    }\r\n  }\r\n\r\n  deleteService(clinicServiceId: number | null): void {\r\n    if (clinicServiceId === null) {\r\n      console.warn('Service ID is missing');\r\n      Swal.fire({\r\n        icon: 'info',\r\n        title: 'Missing Information',\r\n        text: 'Unable to delete the service. Please try again.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Show confirmation dialog\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: \"You won't be able to revert this!\",\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#f39c12', // Warning\r\n      cancelButtonColor: '#d33', // Red\r\n      confirmButtonText: 'Yes, delete it!',\r\n      cancelButtonText: 'Cancel',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        // Call the delete API\r\n        this.scheduleService\r\n          .deleteClinicService(clinicServiceId, Number(this.clinicId))\r\n          .subscribe({\r\n            next: (response) => {\r\n              if (response.status === 'true') {\r\n                Swal.fire({\r\n                  icon: 'success',\r\n                  title: 'Service Deleted',\r\n                  text: response.message,\r\n                });\r\n              }else{\r\n                 Swal.fire({\r\n                icon: 'error',\r\n                title: 'Error Deleting Service',\r\n                text: response.message,\r\n              });\r\n              }\r\n              // Refresh the list\r\n              this.loadExistingServices();\r\n            },\r\n            error: (err) => {\r\n              console.error('Error deleting service', err);\r\n              Swal.fire({\r\n                icon: 'error',\r\n                title: 'Error Deleting Service',\r\n                text: 'There was an error deleting the service. Please try again.',\r\n              });\r\n            },\r\n          });\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\" mt-3 container\" style=\"width: 100%;\" >\r\n    <!-- 1st Row -->\r\n    <div class=\"mb-3  first-row\" >\r\n\r\n        <div class=\"d-flex flex-column  first-row-right\">\r\n            <div>\r\n                <p class=\"set-date-time\" id=\"text\"><b>Set Date and Time</b></p>\r\n            </div>\r\n            \r\n            <div id=\"date-time-container\" class=\"d-flex \">\r\n                <div class=\"d-flex\">\r\n                    <p>Days</p>\r\n                </div>\r\n\r\n                <div class=\"day-col  d-flex flex-row flex-wrap overflow-auto\" style=\"gap: 15px; justify-content: center; align-items: center;\">\r\n                    <button *ngFor=\"let date of dates\" \r\n                        [class.active]=\"selectedDay === date\" \r\n                        (click)=\"selectDay(date)\" \r\n                        id=\"day-btn\">\r\n                        {{ date }}\r\n                    </button>\r\n                </div>\r\n                    \r\n              \r\n                <div class=\"d-flex mb-2 mark-holidays\" style=\"margin-top: 50px; justify-content: space-between; \">\r\n                    <label class=\"\" style=\"gap: 4px; display: flex; cursor: pointer;\" >\r\n                        <input type=\"checkbox\" id=\"holidayDate\" style=\"color: #FB751E; background-color: #FB751E; cursor: pointer;\" [(ngModel)]=\"holidayDate\">\r\n                        <a href=\"#\" id=\"markAsHolidayLink\" class=\"disabled-link\">Mark Holidays <i class=\"bi bi-calendar-event\"></i></a>\r\n                    </label>\r\n                    <div class=\" checkbox\" style=\"display: flex;\">\r\n                        <label><input type=\"checkbox\" name=\"closedStatus\" value=\"closed\" [(ngModel)]=\"isClosed\"> Mark as Closed</label>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n\r\n        <div class=\" d-flex flex-column first-row-left\">\r\n            <p class=\"open-hours\" id=\"text\"><b>Open Hours</b></p>\r\n            <div id=\"open-hours\" class=\"flex-grow-1 d-flex flex-column\">\r\n                <!-- From Time -->\r\n                <div class=\" mb-5\">\r\n                    <p>From</p>\r\n                    <div class=\" d-flex flex-row align-items-center\">\r\n                        <input class=\"time\" type=\"time\" [(ngModel)]=\"fromTime\" />\r\n                    </div>\r\n                </div>\r\n                <!-- To Time -->\r\n                <div class=\"mb-5\">\r\n                    <p>To</p>\r\n                    <div class=\"col-12 d-flex flex-row align-items-center\">\r\n                        <input class=\"time\" type=\"time\" [(ngModel)]=\"toTime\" />\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12\">\r\n                <button class=\"btn btn-primary\" id=\"save-btn\" (click)=\"saveSchedule()\">Save</button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n    \r\n    <div class=\"row mb-10\"   >\r\n        <div class=\"col-md-12 \">\r\n            <h4>Schedule List</h4>\r\n            <div class=\"table-responsive\">\r\n                <table class=\"table table-striped\" >\r\n                    <thead>\r\n                        <tr>\r\n                            <!-- <th>Clinic Id</th> -->\r\n                            <th>Day</th>\r\n                            <th>From</th>\r\n                            <th>To</th>\r\n                            <th>Status</th>\r\n                            <!-- <th>Holiday Date</th> -->\r\n                            <th>Actions</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        <tr *ngFor=\"let schedule of schedules\">\r\n                            <td hidden data-label=\"Clinic Id\">{{ schedule.clinics.clinicId }}</td>\r\n                            <td data-label=\"Day\">{{ schedule.date }}</td>\r\n                            <td data-label=\"From\"><ng-container *ngIf=\"!schedule.holidayDate; else holidayPlaceholder\">{{ schedule.fromTime }}</ng-container>\r\n                                <ng-template #holidayPlaceholder></ng-template>\r\n                            </td>\r\n                              \r\n                            <td data-label=\"From\"><ng-container *ngIf=\"!schedule.holidayDate; else holidayPlaceholder\">{{ schedule.toTime }}</ng-container>\r\n                                <ng-template #holidayPlaceholder></ng-template>\r\n                            </td>\r\n                            \r\n                            <td data-label=\"Holiday Date\">{{ schedule.holidayDate || schedule.isClosed ? 'Closed' : 'Open' }}</td>\r\n                           \r\n                            <td data-label=\"Actions\">\r\n                                <button class=\"btn btn-warning btn-sm\" (click)=\"updateSchedule(schedule)\" style=\"margin-right: 10px;\">Edit</button>\r\n                                <button class=\"btn btn-danger btn-sm\"\r\n                                    (click)=\"schedule.scheduleId ? deleteSchedule(schedule.scheduleId) : null\">\r\n                                    Delete\r\n                                </button>\r\n                            </td>\r\n                            \r\n                        </tr>\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n\r\n    <div class=\"container rounded-container p-4 mt-3 col-12\">\r\n        <h3 class=\"text-dark\">Add services</h3>\r\n        <div class=\"col-12 align-items\">\r\n            <div class=\"form-group row d-flex col-12\">\r\n                \r\n                    <div class=\" mb-4 col-12\" style=\"width: 600px;\">\r\n                        <label for=\"serviceIds\" class=\"form-label\">Select Services:</label>\r\n                        <select id=\"serviceIds\" name=\"serviceIds\" class=\"form-select \" [(ngModel)]=\"selectedServiceId\" >\r\n                            <option value=\"1\">Dental Bonding</option>\r\n                            <option value=\"2\">Cosmetic Fillings</option>\r\n                            <option value=\"3\">Invisalign</option>\r\n                            <option value=\"4\">Teeth Cleanings</option>\r\n                            <option value=\"5\">Root Canal Therapy</option>\r\n                            <option value=\"6\">Dental Sealants</option>\r\n                        </select>\r\n                    </div>\r\n             \r\n               \r\n                    <div class=\"form-group mb-4 col-12\">\r\n                        <button class=\"btn btn-custom\" id=\"save-btn-service\" (click)=\"saveService()\">Save</button>\r\n                    </div>\r\n               \r\n            </div>\r\n        </div>\r\n        <div class=\"table-responsive\">\r\n            <table class=\"table table-bordered\">\r\n                <thead class=\"table-light\">\r\n                    <tr>\r\n                        <th>Added Services</th>\r\n                        <th>Action</th>\r\n                    </tr>\r\n                </thead>\r\n                <tbody>\r\n                    <tr *ngFor=\"let service of existingServices\">\r\n                        <td data-label=\"Added Services\">{{ service.services.clinicServiceCategoryName }}</td>\r\n                        <td data-label=\"Action\">\r\n                            <button class=\"btn btn-danger btn-rounded\" (click)=\"deleteService(service.clinicServiceId)\">Delete</button>\r\n                        </td>\r\n                    </tr>\r\n                </tbody>\r\n            </table>\r\n        </div>\r\n    </div>\r\n    \r\n\r\n    <div>\r\n        <br>\r\n        <br>\r\n    </div>\r\n\r\n\r\n\r\n\r\n\r\n    <!-- 3rd Row -->\r\n    <!-- 3rd Row -->\r\n    <div class=\"mb-3\" style=\"width: 100%;\">\r\n        <div class=\" d-flex flex-column\">\r\n            <div id=\"set-date-time\" class=\"flex-grow-1 d-flex flex-column\">\r\n                <div class=\"mb-2\">\r\n                    <div class=\" mb-11 d-flex\" style=\"flex-wrap: wrap; align-items: center; justify-content: space-between; \">\r\n                        <div class=\"\" style=\"padding-right: 40px;\">\r\n                            <p class=\"set-date-time\" id=\"text\">Add Doctors</p>\r\n                        </div>\r\n                        <div class=\"\" style=\"margin-top: -10px;\">\r\n                            <a routerLink=\"/clinic/list-doctor\" id=\"markAsHolidayLink\" >View All Doctors</a>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"\" style=\"margin-top: 10px; margin-bottom: 10px; width: 100%;\">\r\n                        <a class=\"btn btn-primary\" id=\"add-Doctors-btn\" routerLink=\"/clinic/list-doctor\">\r\n                            <i class=\"fa fa-arrow-circle-right icon\"></i> Add Doctors\r\n                        </a>\r\n                    </div>\r\n                    <!-- Doctor List Section -->\r\n                    <div class=\"row mt-3\">\r\n                        <div class=\"col-12\">\r\n                            <h5>Doctor List</h5>\r\n                            <div class=\"table-responsive\">\r\n                                <table class=\"table table-striped\">\r\n                                    <thead>\r\n                                        <tr>\r\n                                            <th>Doctor ID</th>\r\n                                            <th>Name</th>\r\n                                            <th>Reg No</th>\r\n                                            <th>Actions</th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody>\r\n                                        <!-- Loop through doctors and display them in the table -->\r\n                                        <tr *ngFor=\"let doctor of doctors\">\r\n                                            <td data-label=\"Doctor ID\">{{ doctor.doctorId }}</td>\r\n                                            <td data-label=\"Name\">{{ doctor.firstName }}</td>\r\n                                            <td data-label=\"Reg No\">{{ doctor.regNo }}</td>\r\n                                            <td data-label=\"Actions\">\r\n                                                <button class=\"btn btn-danger btn-sm\" (click)=\"deleteClinicDoctor(doctor.doctorId)\">\r\n                                                    <i class=\"fa fa-trash\"></i> Delete\r\n                                                </button>\r\n                                            </td>\r\n                                        </tr>\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <!-- End Doctor List Section -->\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    \r\n\r\n</div>"], "mappings": "AAKA,OAAOA,IAAI,MAAM,aAAa;;;;;;;;;;ICUVC,EAAA,CAAAC,cAAA,iBAGiB;IADbD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAL,OAAA,CAAe;IAAA,EAAC;IAEzBP,EAAA,CAAAa,MAAA,GACJ;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAJLd,EAAA,CAAAe,WAAA,WAAAC,MAAA,CAAAC,WAAA,KAAAV,OAAA,CAAqC;IAGrCP,EAAA,CAAAkB,SAAA,GACJ;IADIlB,EAAA,CAAAmB,kBAAA,MAAAZ,OAAA,MACJ;;;;;IA8D8BP,EAAA,CAAAoB,uBAAA,GAAqE;IAAApB,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAqB,qBAAA,EAAe;;;;IAAtCrB,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAsB,iBAAA,CAAAC,WAAA,CAAAC,QAAA,CAAuB;;;;;;IAI5FxB,EAAA,CAAAoB,uBAAA,GAAqE;IAAApB,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAqB,qBAAA,EAAe;;;;IAApCrB,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAAsB,iBAAA,CAAAC,WAAA,CAAAE,MAAA,CAAqB;;;;;;;IAPpHzB,EAAA,CAAAC,cAAA,SAAuC;IACDD,EAAA,CAAAa,MAAA,GAA+B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACtEd,EAAA,CAAAC,cAAA,aAAqB;IAAAD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC7Cd,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAA0B,UAAA,IAAAC,wDAAA,2BAA2G;IAC7H3B,EAAA,CAAA0B,UAAA,IAAAE,uDAAA,iCAAA5B,EAAA,CAAA6B,sBAAA,CAA+C;IACnD7B,EAAA,CAAAc,YAAA,EAAK;IAELd,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAA0B,UAAA,KAAAI,yDAAA,2BAAyG;IAC3H9B,EAAA,CAAA0B,UAAA,KAAAK,wDAAA,iCAAA/B,EAAA,CAAA6B,sBAAA,CAA+C;IACnD7B,EAAA,CAAAc,YAAA,EAAK;IAELd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,IAAmE;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEtGd,EAAA,CAAAC,cAAA,cAAyB;IACkBD,EAAA,CAAAE,UAAA,mBAAA8B,mEAAA;MAAA,MAAA5B,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA4B,IAAA;MAAA,MAAAV,WAAA,GAAAnB,WAAA,CAAAI,SAAA;MAAA,MAAA0B,OAAA,GAAAlC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuB,OAAA,CAAAC,cAAA,CAAAZ,WAAA,CAAwB;IAAA,EAAC;IAA6BvB,EAAA,CAAAa,MAAA,YAAI;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACnHd,EAAA,CAAAC,cAAA,kBAC+E;IAA3ED,EAAA,CAAAE,UAAA,mBAAAkC,mEAAA;MAAA,MAAAhC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA4B,IAAA;MAAA,MAAAV,WAAA,GAAAnB,WAAA,CAAAI,SAAA;MAAA,MAAA6B,OAAA,GAAArC,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAY,WAAA,CAAAe,UAAA,GAAsBD,OAAA,CAAAE,cAAA,CAAAhB,WAAA,CAAAe,UAAA,CAAmC,GAAG,IAAI;IAAA,EAAC;IAC1EtC,EAAA,CAAAa,MAAA,gBACJ;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;IAjBqBd,EAAA,CAAAkB,SAAA,GAA+B;IAA/BlB,EAAA,CAAAsB,iBAAA,CAAAC,WAAA,CAAAiB,OAAA,CAAAC,QAAA,CAA+B;IAC5CzC,EAAA,CAAAkB,SAAA,GAAmB;IAAnBlB,EAAA,CAAAsB,iBAAA,CAAAC,WAAA,CAAAmB,IAAA,CAAmB;IACH1C,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAA2C,UAAA,UAAApB,WAAA,CAAAqB,WAAA,CAA6B,aAAAC,GAAA;IAI7B7C,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAA2C,UAAA,UAAApB,WAAA,CAAAqB,WAAA,CAA6B,aAAAC,GAAA;IAIpC7C,EAAA,CAAAkB,SAAA,GAAmE;IAAnElB,EAAA,CAAAsB,iBAAA,CAAAC,WAAA,CAAAqB,WAAA,IAAArB,WAAA,CAAAuB,QAAA,qBAAmE;;;;;;IAmDzG9C,EAAA,CAAAC,cAAA,SAA6C;IACTD,EAAA,CAAAa,MAAA,GAAgD;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACrFd,EAAA,CAAAC,cAAA,aAAwB;IACuBD,EAAA,CAAAE,UAAA,mBAAA6C,kEAAA;MAAA,MAAA3C,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAA2C,IAAA;MAAA,MAAAC,WAAA,GAAA7C,WAAA,CAAAI,SAAA;MAAA,MAAA0C,OAAA,GAAAlD,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAuC,OAAA,CAAAC,aAAA,CAAAF,WAAA,CAAAG,eAAA,CAAsC;IAAA,EAAC;IAACpD,EAAA,CAAAa,MAAA,aAAM;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IAF/Ed,EAAA,CAAAkB,SAAA,GAAgD;IAAhDlB,EAAA,CAAAsB,iBAAA,CAAA2B,WAAA,CAAAI,QAAA,CAAAC,yBAAA,CAAgD;;;;;;IAuDhEtD,EAAA,CAAAC,cAAA,SAAmC;IACJD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACrDd,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAa,MAAA,GAAsB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjDd,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAa,MAAA,GAAkB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC/Cd,EAAA,CAAAC,cAAA,aAAyB;IACiBD,EAAA,CAAAE,UAAA,mBAAAqD,mEAAA;MAAA,MAAAnD,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAmD,IAAA;MAAA,MAAAC,UAAA,GAAArD,WAAA,CAAAI,SAAA;MAAA,MAAAkD,OAAA,GAAA1D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+C,OAAA,CAAAC,kBAAA,CAAAF,UAAA,CAAAG,QAAA,CAAmC;IAAA,EAAC;IAC/E5D,EAAA,CAAA6D,SAAA,YAA2B;IAAC7D,EAAA,CAAAa,MAAA,gBAChC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;IANcd,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAAsB,iBAAA,CAAAmC,UAAA,CAAAG,QAAA,CAAqB;IAC1B5D,EAAA,CAAAkB,SAAA,GAAsB;IAAtBlB,EAAA,CAAAsB,iBAAA,CAAAmC,UAAA,CAAAK,SAAA,CAAsB;IACpB9D,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAsB,iBAAA,CAAAmC,UAAA,CAAAM,KAAA,CAAkB;;;ADlMtF;AACA,MAAMC,WAAW,GAA8B;EAC7CC,EAAE,EAAE,QAAQ;EACZC,CAAC,EAAE,QAAQ;EACXC,EAAE,EAAE,SAAS;EACbC,CAAC,EAAE,WAAW;EACdC,EAAE,EAAE,UAAU;EACdC,CAAC,EAAE,QAAQ;EACXC,EAAE,EAAE;CACL;AAED;AACA,MAAMC,mBAAmB,GAA8B;EACrDC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,IAAI;EACbC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,GAAG;EACXC,QAAQ,EAAE;CACX;AAQD,MAKaC,0BAA0B;EAiBrCC,YACUC,eAAgC,EAChCC,MAAc,EACdC,aAA4B;IAF5B,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAnBvB,KAAAC,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IACjD,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAArE,WAAW,GAAW,EAAE,CAAC,CAAC;IAC1B,KAAAO,QAAQ,GAAW,EAAE,CAAC,CAAC;IACvB,KAAAC,MAAM,GAAW,EAAE,CAAC,CAAC;IACrB,KAAA8D,cAAc,GAAW,IAAI,CAAC,CAAC;IAC/B,KAAAC,cAAc,GAAW,IAAI,CAAC,CAAC;IAC/B,KAAAC,oBAAoB,GAAY,KAAK,CAAC,CAAC;IACvC,KAAA3C,QAAQ,GAAY,KAAK,CAAC,CAAC;IAC3B,KAAAF,WAAW,GAAY,KAAK,CAAC,CAAC;IAC9B,KAAA8C,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAe,EAAE,CAAC,CAAC;IAC5B,KAAAlD,QAAQ,GAAW,CAAC,CAAC,CAAC;IACtB,KAAAmD,iBAAiB,GAAkB,IAAI;IACvC,KAAAC,OAAO,GAAa,EAAE,CAAC,CAAC;IAuWxB,KAAAC,iBAAiB,GAAkB,IAAI,CAAC,CAAC;IACzC,KAAAC,gBAAgB,GAAU,EAAE,CAAC,CAAC;EAlW3B;;EACHC,QAAQA,CAAA;IACN,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrD,MAAM1D,QAAQ,GAAGyD,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IACnD,IAAIF,YAAY,EAAE;MAChBG,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,IAAI,CAACC,YAAY,EAAE,CAAC,CAAC;MACrB,IAAI,CAACC,YAAY,EAAE,CAAC,CAAC;MACrB,IAAI,CAACC,UAAU,EAAE,CAAC,CAAC;MACnB,IAAI,CAACC,oBAAoB,EAAE,CAAC,CAAC;KAC9B,MAAM;MACLC,KAAK,CAAC,sDAAsD,CAAC;MAC7D,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtC;;EAEAC,iBAAiBA,CAACpF,QAAgB,EAAEC,MAAc;IAChD;IACA,MAAMoF,IAAI,GAAG,IAAIC,IAAI,CAAC,cAActF,QAAQ,KAAK,CAAC;IAClD,MAAMuF,EAAE,GAAG,IAAID,IAAI,CAAC,cAAcrF,MAAM,KAAK,CAAC;IAE9C,IAAI,IAAI,CAACqB,QAAQ,IAAI,IAAI,CAACF,WAAW,EAAE;MACrC;MACA,IAAIoE,KAAK,CAACH,IAAI,CAACI,OAAO,EAAE,CAAC,IAAID,KAAK,CAACD,EAAE,CAACE,OAAO,EAAE,CAAC,EAAE;QAChDlH,IAAI,CAACmH,IAAI,CAAC,OAAO,EAAE,sBAAsB,EAAE,OAAO,CAAC;QACnD,OAAO,KAAK;;;IAIhB;IACA,IAAIL,IAAI,CAACI,OAAO,EAAE,KAAKF,EAAE,CAACE,OAAO,EAAE,EAAE;MACnClH,IAAI,CAACmH,IAAI,CACP,cAAc,EACd,2CAA2C,EAC3C,SAAS,CACV;MACD,OAAO,KAAK;;IAGd;IACA,IAAIH,EAAE,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,EAAE;MACjClH,IAAI,CAACmH,IAAI,CACP,cAAc,EACd,uCAAuC,EACvC,SAAS,CACV;MACD,OAAO,KAAK;;IAGd,OAAO,IAAI,CAAC,CAAC;EACf;EAEA;EACAZ,YAAYA,CAAA;IACV,MAAMa,cAAc,GAAGjB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IACzD,IAAIgB,cAAc,EAAE;MAClB,IAAI,CAAC1E,QAAQ,GAAG2E,QAAQ,CAACD,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC;MAC9Cf,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC5D,QAAQ,CAAC,CAAC,CAAC;KACpE,MAAM;MACL2D,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAAC,CAAC;;EAExD;EAEA;EACAG,UAAUA,CAAA;IACR,MAAMa,MAAM,GAAGnB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,IAAIkB,MAAM,EAAE;MACVjB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,MAAM,CAAC;MACnD,IAAI,CAACjC,aAAa,CAACkC,mBAAmB,CAAC,QAAQ,EAAEC,MAAM,CAACrB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAACqB,SAAS,CACjGC,IAAc,IAAI;QACjB,IAAI,CAAC5B,OAAO,GAAG4B,IAAI,CAAC,CAAC;QACrBrB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoB,IAAI,CAAC;MACpD,CAAC,EACAC,KAAK,IAAI;QACRtB,OAAO,CAACsB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,CACF;KACF,MAAM;MACLtB,OAAO,CAACuB,IAAI,CAAC,kCAAkC,CAAC;;EAEpD;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAACzC,MAAM,CAACwB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEA;EACAkB,aAAaA,CAAA;IACX,MAAMC,cAAc,GAAGC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC;IACzD,IAAIF,cAAc,EAAE;MAClBA,cAAc,CAACG,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;MACvC9B,OAAO,CAACC,GAAG,CACT,kBAAkB,EAClByB,cAAc,CAACG,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAChE;KACF,MAAM;MACL/B,OAAO,CAACsB,KAAK,CAAC,4BAA4B,CAAC;;EAE/C;EACA;EACA9G,SAASA,CAACwH,GAAW;IACnB,IACE,IAAI,CAAC9C,YAAY,CAAC+C,QAAQ,CAACD,GAAG,CAAC,KAC9B,CAAC,IAAI,CAAC1C,YAAY,IAAI,IAAI,CAACzE,WAAW,KAAKmH,GAAG,CAAC,EAChD;MACA;MACArI,IAAI,CAACmH,IAAI,CACP,OAAO,EACP,GAAG,IAAI,CAACoB,cAAc,CAACF,GAAG,CAAC,wBAAwB,EACnD,MAAM,CACP;MACD;;IAEF,IAAI,CAACnH,WAAW,GAAG,IAAI,CAACA,WAAW,KAAKmH,GAAG,GAAG,EAAE,GAAGA,GAAG;EACxD;EAEA;EACAG,UAAUA,CAACC,SAAiB;IAC1B,IAAIA,SAAS,KAAK,MAAM,EAAE;MACxB,IAAI,CAACjD,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;MAChEa,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACd,cAAc,CAAC;KAChE,MAAM;MACL,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;MAChEY,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACb,cAAc,CAAC;;EAEjE;EAEA;EACAiD,YAAYA,CAAA;IACVrC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACpF,WAAW,CAAC;IAC9CmF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC7E,QAAQ,CAAC;IACxC4E,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC5E,MAAM,CAAC;IACpC2E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC5D,QAAQ,CAAC;IACxC2D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACvD,QAAQ,CAAC;IAErC,IAAI,CAAC,IAAI,CAACtB,QAAQ,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACR,WAAW,EAAE;MACvD,IAAI,IAAI,CAAC2B,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC3B,WAAW,EAAE;QAChD,OAAO,IAAI,CAAC,CAAC;;;MAEf,IAAI,IAAI,CAAC6B,QAAQ,IAAI,IAAI,EAAE;QACzB,OAAO,IAAI;;MAEb;MACA/C,IAAI,CAACmH,IAAI,CACP,qBAAqB,EACrB,uCAAuC,EACvC,MAAM,CACP;MAED;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,KAAK,CAAC,CAAC;KACf,MAAM,IAAI,CAAC,IAAI,CAACzE,QAAQ,EAAE;MACzB,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;EACA8D,YAAYA,CAAA;IACV,IAAI,CAACrB,eAAe,CAACqB,YAAY,EAAE,CAACiB,SAAS,CAC1CC,IAAgB,IAAI;MACnB,MAAMiB,YAAY,GAAGxC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MACvD,IAAIuC,YAAY,EAAE;QAChB,MAAMC,YAAY,GAAGpB,MAAM,CAACmB,YAAY,CAAC;QACzC,IAAI,CAAC/C,SAAS,GAAG8B,IAAI,CAACmB,MAAM,CACzBC,QAAQ,IAAKA,QAAQ,CAACrG,OAAO,EAAEC,QAAQ,KAAKkG,YAAY,CAC1D;QAED;QACA,IAAI,CAACrD,YAAY,GAAG,IAAI,CAACK,SAAS,CAACmD,GAAG,CACnCD,QAAQ,IAAKrE,mBAAmB,CAACqE,QAAQ,CAACnG,IAAI,CAAC,CACjD;;IAEL,CAAC,EACAgF,KAAK,IAAI;MACRtB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,CACF;EACH;EAEA;EACAqB,YAAYA,CAAA;IACV;IACA,IAAI,CAAC,IAAI,CAACN,YAAY,EAAE,EAAE;IAE1B;IACA,MAAMtB,cAAc,GAAGjB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,MAAM1D,QAAQ,GAAG0E,cAAc,GAAGC,QAAQ,CAACD,cAAc,EAAE,EAAE,CAAC,GAAG,IAAI;IAErE;IACA,IAAI1E,QAAQ,KAAK,IAAI,EAAE;MACrB,IAAI,CAACuG,WAAW,CAAC,gCAAgC,CAAC;MAClD;;IAGF,MAAMC,WAAW,GAAG,IAAI,CAACX,cAAc,CAAC,IAAI,CAACrH,WAAW,CAAC;IAEzD,MAAM4H,QAAQ,GAAa;MACzBrG,OAAO,EAAE;QAAEC,QAAQ,EAAEA;MAAQ,CAAE;MAC/BC,IAAI,EAAEuG,WAAW;MACjBzH,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB;MACAmB,WAAW,EAAE,IAAI,CAACA,WAAW,IAAI,IAAI,CAACE,QAAQ;MAC9CR,UAAU,EAAE,IAAI,CAACsD;KAClB;IAED,IAAIsD,CAAC,GAAG,IAAI,CAACtC,iBAAiB,CAAC,IAAI,CAACpF,QAAQ,EAAE,IAAI,CAACC,MAAM,CAAC;IAC1D2E,OAAO,CAACC,GAAG,CAAC6C,CAAC,CAAC;IACd,IAAI,CAACA,CAAC,EAAE;MACN;;IAGF;IACA,IAAI,IAAI,CAACxD,YAAY,IAAI,IAAI,CAACE,iBAAiB,KAAK,IAAI,EAAE;MACxD,IAAI,CAACV,eAAe,CACjB/C,cAAc,CAAC,IAAI,CAACyD,iBAAiB,EAAEiD,QAAQ,CAAC,CAChDrB,SAAS,CACP2B,eAAyB,IAAI;QAC5B;QACA,IAAI,CAACxD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiD,MAAM,CACnCQ,CAAC,IAAKA,CAAC,CAAC9G,UAAU,KAAK,IAAI,CAACsD,iBAAiB,CAC/C;QACD,IAAI,CAACD,SAAS,CAAC0D,IAAI,CAACF,eAAe,CAAC;QACpC,IAAI,CAAC7D,YAAY,CAAC+D,IAAI,CAAC,IAAI,CAACpI,WAAW,CAAC;QACxC,IAAI,CAACsF,YAAY,EAAE,CAAC,CAAC;QACrB,IAAI,CAAC+C,SAAS,EAAE,CAAC,CAAC;MACpB,CAAC,EACA5B,KAAK,IAAK,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC,CAAC;OACpC;KACJ,MAAM;MACL;MACA,IAAI,CAACxC,eAAe,CAAC6D,YAAY,CAACF,QAAQ,CAAC,CAACrB,SAAS,CAClDC,IAAc,IAAI;QACjB,IAAI,CAACnC,YAAY,CAAC+D,IAAI,CAAC,IAAI,CAACpI,WAAW,CAAC;QACxC,IAAI,CAACsF,YAAY,EAAE,CAAC,CAAC;QACrB,IAAI,CAAC+C,SAAS,EAAE,CAAC,CAAC;MACpB,CAAC,EACA5B,KAAK,IAAK,IAAI,CAACsB,WAAW,CAACtB,KAAK,CAAC,CAAC;OACpC;;EAEL;EAEA;EACAvF,cAAcA,CAAC0G,QAAkB;IAC/B,IAAI,CAACnD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACE,iBAAiB,GAAGiD,QAAQ,CAACvG,UAAU,IAAI,IAAI;IAEpD,IAAI,CAACrB,WAAW,GAAGuD,mBAAmB,CAACqE,QAAQ,CAACnG,IAAI,CAAC;IACrD,IAAI,CAAC4C,YAAY,GAAG,IAAI,CAACD,KAAK,CAACuD,MAAM,CAAElG,IAAI,IAAKA,IAAI,KAAK,IAAI,CAACzB,WAAW,CAAC;IAC1E,IAAI,CAACO,QAAQ,GAAGqH,QAAQ,CAACrH,QAAQ,CAAC+H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAI,CAAChE,cAAc,GAAGsD,QAAQ,CAACrH,QAAQ,CAAC+H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrD,IAAI,CAAC9H,MAAM,GAAGoH,QAAQ,CAACpH,MAAM,CAAC8H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI,CAAC/D,cAAc,GAAGqD,QAAQ,CAACpH,MAAM,CAAC8H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACrD;EAEA5F,kBAAkBA,CAACC,QAAgB;IACjC;IACA7D,IAAI,CAACmH,IAAI,CAAC;MACRsC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB;QACA,IAAI,CAAC7E,aAAa,CAACzB,kBAAkB,CAACC,QAAQ,CAAC,CAAC4D,SAAS,CACvD,MAAK;UACH;UACA,IAAI,CAAC3B,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC+C,MAAM,CAC/BsB,MAAM,IAAKA,MAAM,CAACtG,QAAQ,KAAKA,QAAQ,CACzC;UACDwC,OAAO,CAACC,GAAG,CAAC,kBAAkBzC,QAAQ,wBAAwB,CAAC;UAC/D7D,IAAI,CAACmH,IAAI,CAAC,UAAU,EAAE,0BAA0B,EAAE,SAAS,CAAC;QAC9D,CAAC,EACAQ,KAAK,IAAI;UACRtB,OAAO,CAACC,GAAG,CAACqB,KAAK,CAAC;UAClBtB,OAAO,CAACsB,KAAK,CAAC,iCAAiC9D,QAAQ,GAAG,EAAE8D,KAAK,CAAC;UAClE3H,IAAI,CAACmH,IAAI,CACP,QAAQ,EACR,2DAA2D,EAC3D,OAAO,CACR;QACH,CAAC,CACF;;IAEL,CAAC,CAAC;EACJ;EAEA;EACA3E,cAAcA,CAACD,UAA8B;IAC3C,IAAI,CAACA,UAAU,EAAE;MACf8D,OAAO,CAACsB,KAAK,CAAC,qBAAqB,EAAEpF,UAAU,CAAC;MAChD;;IAEF;IACAvC,IAAI,CAACmH,IAAI,CAAC;MACRsC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE,iBAAiB;MACpCK,gBAAgB,EAAE;KACnB,CAAC,CAACJ,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB;QACA,IAAI,CAAC/E,eAAe,CAAC3C,cAAc,CAACD,UAAU,CAAC,CAACkF,SAAS,CAAC;UACxD4C,IAAI,EAAEA,CAAA,KAAK;YACTrK,IAAI,CAACmH,IAAI,CAAC,UAAU,EAAE,gCAAgC,EAAE,SAAS,CAAC;YAClE;YACA,IAAI,CAACX,YAAY,EAAE;UACrB,CAAC;UACDmB,KAAK,EAAG2C,GAAG,IAAI;YACbtK,IAAI,CAACmH,IAAI,CAAC,QAAQ,EAAE,gCAAgC,EAAE,OAAO,CAAC;UAChE;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;EACAoB,cAAcA,CAACF,GAAW;IACxB,OAAOpE,WAAW,CAACoE,GAAG,CAAC,IAAIA,GAAG;EAChC;EAEA;EACAkB,SAASA,CAAA;IACPlD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,IAAI,CAACpF,WAAW,GAAG,EAAE;IACrB,IAAI,CAACO,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC8D,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC1C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACF,WAAW,GAAG,KAAK;EAC1B;EAEA;EACAoG,WAAWA,CAACtB,KAAU;IACpBtB,OAAO,CAACsB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACvChB,KAAK,CAAC,sCAAsC,CAAC;EAC/C;EAIAD,oBAAoBA,CAAA;IAClB,IAAI,CAAChE,QAAQ,GAAG8E,MAAM,CAACrB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1DC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5D,QAAQ,GAAG,OAAO,CAAC;IACpC,IAAI,CAACyC,eAAe,CAACoF,2BAA2B,CAAC,IAAI,CAAC7H,QAAQ,CAAC,CAAC+E,SAAS,CACtEnE,QAAQ,IAAI;MACX,IAAI,CAAC0C,gBAAgB,GAAG1C,QAAQ,CAAC,CAAC;IACpC,CAAC,EACAqE,KAAK,IAAI;MACRtB,OAAO,CAACsB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC,CACF;EACH;;EAEA6C,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzE,iBAAiB,KAAK,IAAI,IAAI,IAAI,CAACrD,QAAQ,KAAK,IAAI,EAAE;MAC7D;MACA2D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACP,iBAAiB,CAAC;MAC3DM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACN,gBAAgB,CAAC;MAExD;MACA,MAAMyE,qBAAqB,GAAG,IAAI,CAACzE,gBAAgB,CAAC0E,IAAI,CACrDC,OAAO,IACNA,OAAO,CAACrH,QAAQ,CAACsH,uBAAuB,KACxCpD,MAAM,CAAC,IAAI,CAACzB,iBAAiB,CAAC,CAAC;OAClC;;MAEDM,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEmE,qBAAqB,CAAC,CAAC,CAAC;MAEjE,IAAIA,qBAAqB,EAAE;QACzBzK,IAAI,CAACmH,IAAI,CAAC;UACRwC,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE;SACP,CAAC;QACF,OAAO,CAAC;;MAGV;MACA,MAAMmB,aAAa,GAAmB;QACpCpI,OAAO,EAAE;UAAEC,QAAQ,EAAE,IAAI,CAACA;QAAQ,CAAE;QACpCY,QAAQ,EAAE;UAAEsH,uBAAuB,EAAE,IAAI,CAAC7E;QAAiB,CAAE,CAAE;OAChE;MAED;MACA,IAAI,CAACZ,eAAe,CAAC2F,iBAAiB,CAACD,aAAa,CAAC,CAACpD,SAAS,CAC5DsD,QAAQ,IAAI;QACX;QAEA;QACA1E,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyE,QAAQ,CAAC;QACnD,IAAI,CAACrE,oBAAoB,EAAE,CAAC,CAAC;QAC7B1G,IAAI,CAACmH,IAAI,CAAC;UACRwC,IAAI,EAAE,SAAS;UACfF,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,EACA/B,KAAK,IAAI;QACRtB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C3H,IAAI,CAACmH,IAAI,CAAC;UACRwC,IAAI,EAAE,OAAO;UACbF,KAAK,EAAE,sBAAsB;UAC7BC,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,CACF;KACF,MAAM;MACLrD,OAAO,CAACuB,IAAI,CAAC,0CAA0C,CAAC;MACxD5H,IAAI,CAACmH,IAAI,CAAC;QACRwC,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE;OACP,CAAC;;EAEN;EAEAtG,aAAaA,CAACC,eAA8B;IAC1C,IAAIA,eAAe,KAAK,IAAI,EAAE;MAC5BgD,OAAO,CAACuB,IAAI,CAAC,uBAAuB,CAAC;MACrC5H,IAAI,CAACmH,IAAI,CAAC;QACRwC,IAAI,EAAE,MAAM;QACZF,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE;OACP,CAAC;MACF;;IAGF;IACA1J,IAAI,CAACmH,IAAI,CAAC;MACRsC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE,iBAAiB;MACpCK,gBAAgB,EAAE;KACnB,CAAC,CAACJ,IAAI,CAAEC,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;QACtB;QACA,IAAI,CAAC/E,eAAe,CACjB6F,mBAAmB,CAAC3H,eAAe,EAAEmE,MAAM,CAAC,IAAI,CAAC9E,QAAQ,CAAC,CAAC,CAC3D+E,SAAS,CAAC;UACT4C,IAAI,EAAGU,QAAQ,IAAI;YACjB,IAAIA,QAAQ,CAACE,MAAM,KAAK,MAAM,EAAE;cAC9BjL,IAAI,CAACmH,IAAI,CAAC;gBACRwC,IAAI,EAAE,SAAS;gBACfF,KAAK,EAAE,iBAAiB;gBACxBC,IAAI,EAAEqB,QAAQ,CAACG;eAChB,CAAC;aACH,MAAI;cACFlL,IAAI,CAACmH,IAAI,CAAC;gBACXwC,IAAI,EAAE,OAAO;gBACbF,KAAK,EAAE,wBAAwB;gBAC/BC,IAAI,EAAEqB,QAAQ,CAACG;eAChB,CAAC;;YAEF;YACA,IAAI,CAACxE,oBAAoB,EAAE;UAC7B,CAAC;UACDiB,KAAK,EAAG2C,GAAG,IAAI;YACbjE,OAAO,CAACsB,KAAK,CAAC,wBAAwB,EAAE2C,GAAG,CAAC;YAC5CtK,IAAI,CAACmH,IAAI,CAAC;cACRwC,IAAI,EAAE,OAAO;cACbF,KAAK,EAAE,wBAAwB;cAC/BC,IAAI,EAAE;aACP,CAAC;UACJ;SACD,CAAC;;IAER,CAAC,CAAC;EACJ;EAAC,QAAAyB,CAAA,G;qBA7fUlG,0BAA0B,EAAAhF,EAAA,CAAAmL,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAArL,EAAA,CAAAmL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAvL,EAAA,CAAAmL,iBAAA,CAAAK,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1B1G,0BAA0B;IAAA2G,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvCvCjM,EAAA,CAAAC,cAAA,aAAmD;QAMGD,EAAA,CAAAa,MAAA,wBAAiB;QAAAb,EAAA,CAAAc,YAAA,EAAI;QAG/Dd,EAAA,CAAAC,cAAA,aAA8C;QAEnCD,EAAA,CAAAa,MAAA,YAAI;QAAAb,EAAA,CAAAc,YAAA,EAAI;QAGfd,EAAA,CAAAC,cAAA,cAA+H;QAC3HD,EAAA,CAAA0B,UAAA,KAAAyK,6CAAA,oBAKS;QACbnM,EAAA,CAAAc,YAAA,EAAM;QAGNd,EAAA,CAAAC,cAAA,cAAkG;QAEkBD,EAAA,CAAAE,UAAA,2BAAAkM,oEAAAC,MAAA;UAAA,OAAAH,GAAA,CAAAtJ,WAAA,GAAAyJ,MAAA;QAAA,EAAyB;QAArIrM,EAAA,CAAAc,YAAA,EAAsI;QACtId,EAAA,CAAAC,cAAA,aAAyD;QAAAD,EAAA,CAAAa,MAAA,sBAAc;QAAAb,EAAA,CAAA6D,SAAA,aAAoC;QAAA7D,EAAA,CAAAc,YAAA,EAAI;QAEnHd,EAAA,CAAAC,cAAA,eAA8C;QACuBD,EAAA,CAAAE,UAAA,2BAAAoM,oEAAAD,MAAA;UAAA,OAAAH,GAAA,CAAApJ,QAAA,GAAAuJ,MAAA;QAAA,EAAsB;QAAhFrM,EAAA,CAAAc,YAAA,EAAiF;QAACd,EAAA,CAAAa,MAAA,uBAAc;QAAAb,EAAA,CAAAc,YAAA,EAAQ;QAO/Hd,EAAA,CAAAC,cAAA,eAAgD;QACTD,EAAA,CAAAa,MAAA,kBAAU;QAAAb,EAAA,CAAAc,YAAA,EAAI;QACjDd,EAAA,CAAAC,cAAA,eAA4D;QAGjDD,EAAA,CAAAa,MAAA,YAAI;QAAAb,EAAA,CAAAc,YAAA,EAAI;QACXd,EAAA,CAAAC,cAAA,eAAiD;QACbD,EAAA,CAAAE,UAAA,2BAAAqM,oEAAAF,MAAA;UAAA,OAAAH,GAAA,CAAA1K,QAAA,GAAA6K,MAAA;QAAA,EAAsB;QAAtDrM,EAAA,CAAAc,YAAA,EAAyD;QAIjEd,EAAA,CAAAC,cAAA,eAAkB;QACXD,EAAA,CAAAa,MAAA,UAAE;QAAAb,EAAA,CAAAc,YAAA,EAAI;QACTd,EAAA,CAAAC,cAAA,eAAuD;QACnBD,EAAA,CAAAE,UAAA,2BAAAsM,oEAAAH,MAAA;UAAA,OAAAH,GAAA,CAAAzK,MAAA,GAAA4K,MAAA;QAAA,EAAoB;QAApDrM,EAAA,CAAAc,YAAA,EAAuD;QAInEd,EAAA,CAAAC,cAAA,eAAoB;QAC8BD,EAAA,CAAAE,UAAA,mBAAAuM,6DAAA;UAAA,OAASP,GAAA,CAAAnD,YAAA,EAAc;QAAA,EAAC;QAAC/I,EAAA,CAAAa,MAAA,YAAI;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAMhGd,EAAA,CAAAC,cAAA,eAA0B;QAEdD,EAAA,CAAAa,MAAA,qBAAa;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACtBd,EAAA,CAAAC,cAAA,eAA8B;QAKVD,EAAA,CAAAa,MAAA,WAAG;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACZd,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAa,MAAA,YAAI;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACbd,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAa,MAAA,UAAE;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACXd,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAa,MAAA,cAAM;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAEfd,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAa,MAAA,eAAO;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAGxBd,EAAA,CAAAC,cAAA,aAAO;QACHD,EAAA,CAAA0B,UAAA,KAAAgL,yCAAA,kBAqBK;QACT1M,EAAA,CAAAc,YAAA,EAAQ;QAOxBd,EAAA,CAAAC,cAAA,eAAyD;QAC/BD,EAAA,CAAAa,MAAA,oBAAY;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACvCd,EAAA,CAAAC,cAAA,eAAgC;QAI2BD,EAAA,CAAAa,MAAA,wBAAgB;QAAAb,EAAA,CAAAc,YAAA,EAAQ;QACnEd,EAAA,CAAAC,cAAA,kBAAgG;QAAjCD,EAAA,CAAAE,UAAA,2BAAAyM,qEAAAN,MAAA;UAAA,OAAAH,GAAA,CAAApG,iBAAA,GAAAuG,MAAA;QAAA,EAA+B;QAC1FrM,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,sBAAc;QAAAb,EAAA,CAAAc,YAAA,EAAS;QACzCd,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,yBAAiB;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAC5Cd,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,kBAAU;QAAAb,EAAA,CAAAc,YAAA,EAAS;QACrCd,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,uBAAe;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAC1Cd,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,0BAAkB;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAC7Cd,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAa,MAAA,uBAAe;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAKlDd,EAAA,CAAAC,cAAA,eAAoC;QACqBD,EAAA,CAAAE,UAAA,mBAAA0M,6DAAA;UAAA,OAASV,GAAA,CAAA3B,WAAA,EAAa;QAAA,EAAC;QAACvK,EAAA,CAAAa,MAAA,YAAI;QAAAb,EAAA,CAAAc,YAAA,EAAS;QAK1Gd,EAAA,CAAAC,cAAA,eAA8B;QAIVD,EAAA,CAAAa,MAAA,sBAAc;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACvBd,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAa,MAAA,cAAM;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAGvBd,EAAA,CAAAC,cAAA,aAAO;QACHD,EAAA,CAAA0B,UAAA,KAAAmL,yCAAA,iBAKK;QACT7M,EAAA,CAAAc,YAAA,EAAQ;QAMpBd,EAAA,CAAAC,cAAA,WAAK;QACDD,EAAA,CAAA6D,SAAA,UAAI;QAER7D,EAAA,CAAAc,YAAA,EAAM;QAQNd,EAAA,CAAAC,cAAA,eAAuC;QAMoBD,EAAA,CAAAa,MAAA,oBAAW;QAAAb,EAAA,CAAAc,YAAA,EAAI;QAEtDd,EAAA,CAAAC,cAAA,gBAAyC;QACuBD,EAAA,CAAAa,MAAA,yBAAgB;QAAAb,EAAA,CAAAc,YAAA,EAAI;QAGxFd,EAAA,CAAAC,cAAA,gBAA0E;QAElED,EAAA,CAAA6D,SAAA,cAA6C;QAAC7D,EAAA,CAAAa,MAAA,sBAClD;QAAAb,EAAA,CAAAc,YAAA,EAAI;QAGRd,EAAA,CAAAC,cAAA,gBAAsB;QAEVD,EAAA,CAAAa,MAAA,oBAAW;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACpBd,EAAA,CAAAC,cAAA,gBAA8B;QAIVD,EAAA,CAAAa,MAAA,kBAAS;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAClBd,EAAA,CAAAC,cAAA,WAAI;QAAAD,EAAA,CAAAa,MAAA,aAAI;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACbd,EAAA,CAAAC,cAAA,WAAI;QAAAD,EAAA,CAAAa,MAAA,eAAM;QAAAb,EAAA,CAAAc,YAAA,EAAK;QACfd,EAAA,CAAAC,cAAA,WAAI;QAAAD,EAAA,CAAAa,MAAA,gBAAO;QAAAb,EAAA,CAAAc,YAAA,EAAK;QAGxBd,EAAA,CAAAC,cAAA,cAAO;QAEHD,EAAA,CAAA0B,UAAA,MAAAoL,0CAAA,kBASK;QACT9M,EAAA,CAAAc,YAAA,EAAQ;;;QAhMCd,EAAA,CAAAkB,SAAA,IAAQ;QAARlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAA7G,KAAA,CAAQ;QAW+ErF,EAAA,CAAAkB,SAAA,GAAyB;QAAzBlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAAtJ,WAAA,CAAyB;QAIpE5C,EAAA,CAAAkB,SAAA,GAAsB;QAAtBlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAApJ,QAAA,CAAsB;QAcvD9C,EAAA,CAAAkB,SAAA,IAAsB;QAAtBlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAA1K,QAAA,CAAsB;QAOtBxB,EAAA,CAAAkB,SAAA,GAAoB;QAApBlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAAzK,MAAA,CAAoB;QA4B3BzB,EAAA,CAAAkB,SAAA,IAAY;QAAZlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAAvG,SAAA,CAAY;QAoC0B3F,EAAA,CAAAkB,SAAA,GAA+B;QAA/BlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAApG,iBAAA,CAA+B;QA0B1E9F,EAAA,CAAAkB,SAAA,IAAmB;QAAnBlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAAnG,gBAAA,CAAmB;QAwDA/F,EAAA,CAAAkB,SAAA,IAAU;QAAVlB,EAAA,CAAA2C,UAAA,YAAAuJ,GAAA,CAAArG,OAAA,CAAU;;;;;;;SD9J5Db,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}