.appointment-booking-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.header p {
  color: #7f8c8d;
  margin: 0;
}

.appointment-form {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.form-label {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 5px;
}

.form-control {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 15px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-control.is-invalid {
  border-color: #e74c3c;
}

.invalid-feedback {
  display: block;
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: -10px;
  margin-bottom: 10px;
}

.selected-appointment-info {
  background: #e8f5e8;
  padding: 15px;
  border-radius: 5px;
  border-left: 4px solid #27ae60;
  margin-top: 20px;
}

.selected-appointment-info h4 {
  color: #27ae60;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.selected-appointment-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.btn {
  padding: 10px 25px;
  border-radius: 5px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  border-color: #95a5a6;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  border-color: #7f8c8d;
}

/* Modal Styles */
.modal-content {
  border-radius: 10px;
  border: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  background: #3498db;
  color: white;
  border-radius: 10px 10px 0 0;
}

.modal-title {
  font-weight: 600;
}

.btn-close {
  filter: invert(1);
}

.time-slots-container {
  max-height: 400px;
  overflow-y: auto;
}

.time-slot-group {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 5px;
}

.time-slot-group h6 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-weight: 600;
}

.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.doctor-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.doctor-card:hover {
  border-color: #3498db;
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
}

.doctor-info h6 {
  margin: 0;
  color: #2c3e50;
  font-size: 0.9rem;
}

.doctor-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.8rem;
}

.no-slots-message {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .appointment-booking-container {
    padding: 10px;
  }
  
  .appointment-form {
    padding: 20px;
  }
  
  .modal-dialog {
    margin: 10px;
  }
}
