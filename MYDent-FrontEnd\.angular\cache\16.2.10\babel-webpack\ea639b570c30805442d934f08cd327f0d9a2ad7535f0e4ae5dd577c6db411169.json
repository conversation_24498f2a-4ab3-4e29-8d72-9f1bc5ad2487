{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DefaultNavbarComponent } from './default-navbar/default-navbar.component';\nimport { PrimaryActionButtonComponent } from './primary-action-button/primary-action-button.component';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nclass CoreModule {\n  static #_ = this.ɵfac = function CoreModule_Factory(t) {\n    return new (t || CoreModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CoreModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule]\n  });\n}\nexport { CoreModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CoreModule, {\n    declarations: [DefaultNavbarComponent, PrimaryActionButtonComponent],\n    imports: [CommonModule, RouterModule],\n    exports: [DefaultNavbarComponent, PrimaryActionButtonComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DefaultNavbarComponent", "PrimaryActionButtonComponent", "RouterModule", "CoreModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\core\\core.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DefaultNavbarComponent } from './default-navbar/default-navbar.component';\r\nimport { PrimaryActionButtonComponent } from './primary-action-button/primary-action-button.component';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DefaultNavbarComponent,\r\n    PrimaryActionButtonComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n  ],\r\n  exports:[DefaultNavbarComponent,PrimaryActionButtonComponent]\r\n})\r\nexport class CoreModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,YAAY,QAAQ,iBAAiB;;AAI9C,MAWaC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cALnBP,YAAY,EACZG,YAAY;EAAA;;SAIHC,UAAU;;2EAAVA,UAAU;IAAAI,YAAA,GATnBP,sBAAsB,EACtBC,4BAA4B;IAAAO,OAAA,GAG5BT,YAAY,EACZG,YAAY;IAAAO,OAAA,GAELT,sBAAsB,EAACC,4BAA4B;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}