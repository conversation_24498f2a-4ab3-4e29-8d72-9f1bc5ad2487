package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.LaboratoryCategories;
import com.navitsa.mydent.repositories. LaboratoryCategoriesRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LaboratoryCategoriesService {

    private final LaboratoryCategoriesRepository laboratoryCategoriesRepository;

    @Autowired
    public  LaboratoryCategoriesService( LaboratoryCategoriesRepository laboratoryCategoriesRepository) {
        this.laboratoryCategoriesRepository = laboratoryCategoriesRepository;
    }

    //Save laboratory categories
    public LaboratoryCategories saveLaboratoryCategory(LaboratoryCategories laboratoryCategories) {
        try {
            return laboratoryCategoriesRepository.save(laboratoryCategories);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the laboratory service.");
        }
    }

    //Get all laboratory services
    public List<LaboratoryCategories> getAllLaboratoryCategories() {
        try {
            return laboratoryCategoriesRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of laboratory services.");
        }
    }

    //Get laboratory service by ID
    public LaboratoryCategories getLaboratoryCategoryById(int id) {
        try {
            return laboratoryCategoriesRepository.findById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory service.");
        }
    }
}
