package com.navitsa.mydent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name= "supplier_categories")
public class SupplierCategories{

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "supplier_category_id")
	private Integer supplierCategoryId;
	
	@Column(name= "supplier_category")
	private String supplierCategory;

	public SupplierCategories(Integer supplierCategoryId, String supplierCategory) {
		super();
		this.supplierCategoryId = supplierCategoryId;
		this.supplierCategory = supplierCategory;
	}

	public SupplierCategories() {
    }

    public Integer getSupplierCategoryId() {
		return supplierCategoryId;
	}

	public void setSupplierCategoryId(Integer supplierCategoryId) {
		this.supplierCategoryId = supplierCategoryId;
	}

	public String getSupplierCategory() {
		return supplierCategory;
	}

	public void setSupplierCategory(String supplierCategory) {
		this.supplierCategory = supplierCategory;
	}

	
}

