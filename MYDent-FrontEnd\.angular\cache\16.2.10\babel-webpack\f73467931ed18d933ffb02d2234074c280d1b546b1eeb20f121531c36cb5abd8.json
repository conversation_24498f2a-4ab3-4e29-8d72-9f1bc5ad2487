{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/default-navbar/default-navbar.component\";\nclass AppointmentPageComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  navigateUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  static #_ = this.ɵfac = function AppointmentPageComponent_Factory(t) {\n    return new (t || AppointmentPageComponent)(i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppointmentPageComponent,\n    selectors: [[\"app-appointment-page\"]],\n    decls: 18,\n    vars: 0,\n    consts: [[\"loggedUser\", \"Test Appointment Page\"], [1, \"cover-page\"], [1, \"color-shape\", \"bottom-right\"], [1, \"text-section\"], [1, \"gradient-text\"], [1, \"appointment-button\", 3, \"click\"], [\"src\", \"assets/images/arrow.png\", \"alt\", \"arrow\", 1, \"arrow\"], [1, \"btn-text\", \"px-2\"]],\n    template: function AppointmentPageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"h1\", 4);\n        i0.ɵɵtext(5, \"Don't Delay It Any Longer.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"h1\", 4);\n        i0.ɵɵtext(7, \"Book Your Dentist Appointment Here.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"h1\", 4);\n        i0.ɵɵtext(9, \"Find Relief And Smile Again.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"h4\");\n        i0.ɵɵtext(11, \" Find your nearest dental clinic\");\n        i0.ɵɵelement(12, \"br\");\n        i0.ɵɵtext(13, \" Book your appointment... \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function AppointmentPageComponent_Template_button_click_14_listener() {\n          return ctx.navigateUserSelection();\n        });\n        i0.ɵɵelement(15, \"img\", 6);\n        i0.ɵɵelementStart(16, \"div\", 7);\n        i0.ɵɵtext(17, \"MAKE APPOINTMENT\");\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    dependencies: [i2.DefaultNavbarComponent],\n    styles: [\"body[_ngcontent-%COMP%], html[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 0;\\n  height: 100%;\\n}\\n\\napp-header[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n}\\n\\n.cover-page[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/back-appointment.png\\\");\\n  background-size: cover;\\n  background-position: center;\\n  min-height: 100vh;\\n  padding-top: 10%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: flex-start;\\n}\\n\\n.text-section[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  margin-left: 5%;\\n}\\n\\n.gradient-text[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #fb751e, #70190f, #fb751e, #70190f);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-size: 35px;\\n  font-weight: bold;\\n  margin: 10px 0;\\n  background-clip: text;\\n}\\n\\nh4[_ngcontent-%COMP%] {\\n  color: #555;\\n  font-size: 24px;\\n  font-style: italic;\\n  margin: 30px 0;\\n  font-weight: bold;\\n}\\n\\n.appointment-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #333333, #fb751e);\\n  color: white;\\n  border: none;\\n  cursor: pointer;\\n  font-size: 20px;\\n  border-radius: 23px;\\n  font-weight: bold;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.appointment-button[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { AppointmentPageComponent };", "map": {"version": 3, "names": ["AppointmentPageComponent", "constructor", "router", "navigateUserSelection", "navigate", "_", "i0", "ɵɵdirectiveInject", "i1", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "AppointmentPageComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AppointmentPageComponent_Template_button_click_14_listener"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointment-page\\appointment-page.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointment-page\\appointment-page.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-appointment-page',\r\n  templateUrl: './appointment-page.component.html',\r\n  styleUrls: ['./appointment-page.component.css'],\r\n})\r\nexport class AppointmentPageComponent {\r\n  constructor(private router: Router) {}\r\n\r\n  navigateUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n", "<app-default-navbar loggedUser=\"Test Appointment Page\"/>\r\n\r\n<div class=\"cover-page\">\r\n  <div class=\"color-shape bottom-right\"></div>\r\n  <div class=\"text-section\">\r\n    <h1 class=\"gradient-text\">Don't Delay It Any Longer.</h1>\r\n    <h1 class=\"gradient-text\">Book Your Dentist Appointment Here.</h1>\r\n    <h1 class=\"gradient-text\">Find Relief And Smile Again.</h1>\r\n    <h4>\r\n      Find your nearest dental clinic<br />\r\n      Book your appointment...\r\n    </h4>\r\n    <button class=\"appointment-button\" (click)=\"navigateUserSelection()\">\r\n      <img src=\"assets/images/arrow.png\" alt=\"arrow\" class=\"arrow\" />\r\n      <div class=\"btn-text px-2\">MAKE APPOINTMENT</div>\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;AAGA,MAKaA,wBAAwB;EACnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErCC,qBAAqBA,CAAA;IACnB,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAAC,QAAAC,CAAA,G;qBALUL,wBAAwB,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAxBV,wBAAwB;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRrCX,EAAA,CAAAa,SAAA,4BAAwD;QAExDb,EAAA,CAAAc,cAAA,aAAwB;QACtBd,EAAA,CAAAa,SAAA,aAA4C;QAC5Cb,EAAA,CAAAc,cAAA,aAA0B;QACEd,EAAA,CAAAe,MAAA,iCAA0B;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QACzDhB,EAAA,CAAAc,cAAA,YAA0B;QAAAd,EAAA,CAAAe,MAAA,0CAAmC;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAClEhB,EAAA,CAAAc,cAAA,YAA0B;QAAAd,EAAA,CAAAe,MAAA,mCAA4B;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QAC3DhB,EAAA,CAAAc,cAAA,UAAI;QACFd,EAAA,CAAAe,MAAA,wCAA+B;QAAAf,EAAA,CAAAa,SAAA,UAAM;QACrCb,EAAA,CAAAe,MAAA,kCACF;QAAAf,EAAA,CAAAgB,YAAA,EAAK;QACLhB,EAAA,CAAAc,cAAA,iBAAqE;QAAlCd,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;UAAA,OAASN,GAAA,CAAAf,qBAAA,EAAuB;QAAA,EAAC;QAClEG,EAAA,CAAAa,SAAA,cAA+D;QAC/Db,EAAA,CAAAc,cAAA,cAA2B;QAAAd,EAAA,CAAAe,MAAA,wBAAgB;QAAAf,EAAA,CAAAgB,YAAA,EAAM;;;;;;;SDN1CtB,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}