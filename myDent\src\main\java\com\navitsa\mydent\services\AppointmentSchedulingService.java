package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.*;
import com.navitsa.mydent.repositories.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AppointmentSchedulingService {

    @Autowired
    private ClinicScheduleRepository clinicScheduleRepository;

    @Autowired
    private AppointmentsRepository appointmentsRepository;

    @Autowired
    private ClinicDoctorRepository clinicDoctorRepository;

    public static class TimeSlot {
        private String startTime;
        private String endTime;
        private String displayTime;
        private boolean available;
        private List<Doctor> availableDoctors;

        public TimeSlot(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
            this.displayTime = formatTimeRange(startTime, endTime);
            this.available = true;
            this.availableDoctors = new ArrayList<>();
        }

        private String formatTimeRange(String start, String end) {
            try {
                LocalTime startTime = LocalTime.parse(start, DateTimeFormatter.ofPattern("HH:mm"));
                LocalTime endTime = LocalTime.parse(end, DateTimeFormatter.ofPattern("HH:mm"));
                DateTimeFormatter displayFormatter = DateTimeFormatter.ofPattern("h:mm a");
                return startTime.format(displayFormatter) + " - " + endTime.format(displayFormatter);
            } catch (Exception e) {
                return start + " - " + end;
            }
        }

        // Getters and setters
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
        public String getDisplayTime() { return displayTime; }
        public void setDisplayTime(String displayTime) { this.displayTime = displayTime; }
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
        public List<Doctor> getAvailableDoctors() { return availableDoctors; }
        public void setAvailableDoctors(List<Doctor> availableDoctors) { this.availableDoctors = availableDoctors; }
    }

    public Map<String, Object> getAvailableTimeSlotsForDate(Integer clinicId, String selectedDate) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Get day name from date
            String dayName = getDayNameFromDate(selectedDate);
            
            // Check if clinic has schedule for this day
            Optional<ClinicSchedule> scheduleOpt = clinicScheduleRepository.findByDateAndClinicId(dayName, clinicId);
            
            if (!scheduleOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "No schedule found for " + dayName + ". Please create a schedule for this day.");
                response.put("needsScheduleCreation", true);
                response.put("dayName", dayName);
                return response;
            }
            
            ClinicSchedule schedule = scheduleOpt.get();
            
            // Generate time slots
            List<TimeSlot> timeSlots = generateTimeSlots(schedule.getFromTime(), schedule.getToTime());
            
            // Get all doctors assigned to this clinic
            List<Doctor> clinicDoctors = clinicDoctorRepository.findDoctorsAssignByClinicId(clinicId);
            
            // Check availability for each time slot
            for (TimeSlot slot : timeSlots) {
                List<Doctor> availableDoctors = getAvailableDoctorsForTimeSlot(
                    clinicId, selectedDate, slot.getStartTime(), slot.getEndTime(), clinicDoctors);
                slot.setAvailableDoctors(availableDoctors);
                slot.setAvailable(!availableDoctors.isEmpty());
            }
            
            // Filter out time slots with no available doctors
            List<TimeSlot> availableSlots = timeSlots.stream()
                .filter(TimeSlot::isAvailable)
                .collect(Collectors.toList());
            
            response.put("success", true);
            response.put("timeSlots", availableSlots);
            response.put("scheduleExists", true);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Error retrieving time slots: " + e.getMessage());
        }
        
        return response;
    }

    private String getDayNameFromDate(String dateString) {
        // Convert date string to day name (e.g., "2024-01-15" -> "Monday")
        try {
            java.time.LocalDate date = java.time.LocalDate.parse(dateString);
            return date.getDayOfWeek().toString().substring(0, 1).toUpperCase() + 
                   date.getDayOfWeek().toString().substring(1).toLowerCase();
        } catch (Exception e) {
            return "Monday"; // Default fallback
        }
    }

    private List<TimeSlot> generateTimeSlots(String fromTime, String toTime) {
        List<TimeSlot> slots = new ArrayList<>();
        
        try {
            LocalTime start = LocalTime.parse(fromTime, DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime end = LocalTime.parse(toTime, DateTimeFormatter.ofPattern("HH:mm"));
            
            LocalTime current = start;
            while (current.isBefore(end)) {
                LocalTime slotEnd = current.plusHours(1);
                if (slotEnd.isAfter(end)) {
                    break; // Don't create partial slots
                }
                
                TimeSlot slot = new TimeSlot(
                    current.format(DateTimeFormatter.ofPattern("HH:mm")),
                    slotEnd.format(DateTimeFormatter.ofPattern("HH:mm"))
                );
                slots.add(slot);
                
                current = slotEnd;
            }
        } catch (Exception e) {
            System.err.println("Error generating time slots: " + e.getMessage());
        }
        
        return slots;
    }

    private List<Doctor> getAvailableDoctorsForTimeSlot(Integer clinicId, String date, 
                                                       String fromTime, String toTime, 
                                                       List<Doctor> allDoctors) {
        // Get appointments for this time slot
        List<Appointments> existingAppointments = appointmentsRepository
            .findByClinicIdAndDateAndTime(clinicId, date, fromTime, toTime);
        
        // Get list of doctors who already have appointments in this slot
        Set<Integer> busyDoctorIds = existingAppointments.stream()
            .filter(apt -> apt.getDoctor() != null)
            .map(apt -> apt.getDoctor().getDoctorId())
            .collect(Collectors.toSet());
        
        // Return doctors who are not busy in this time slot
        return allDoctors.stream()
            .filter(doctor -> !busyDoctorIds.contains(doctor.getDoctorId()))
            .collect(Collectors.toList());
    }
}
