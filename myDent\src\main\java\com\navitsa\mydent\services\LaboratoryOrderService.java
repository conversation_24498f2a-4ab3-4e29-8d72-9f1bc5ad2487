package com.navitsa.mydent.services;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.LaboratoryOrder;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.LaboratoryOrderRepository;
import com.navitsa.mydent.repositories.ClinicRepository;
import com.navitsa.mydent.repositories.UserRepository;

@Service
public class LaboratoryOrderService {

	@Autowired
    private LaboratoryOrderRepository laboratoryOrderRepository;
	
	@Autowired
    private ClinicRepository clinicRepository;
	
	@Autowired
    private UserRepository userRepository;

    public LaboratoryOrder saveLaboratoryOrder(LaboratoryOrder laboratoryOrder, Integer userId){
    	User user = userRepository.findByUserId(userId);
        Clinic clinic = clinicRepository.findByUserId(user).orElse(null);
        
        SimpleDateFormat dateFormatter = new SimpleDateFormat("MM-dd-yyyy");
		SimpleDateFormat timeFormatter = new SimpleDateFormat("HH:mm:ss");

		Date currentDate = new Date();
		String formattedDate = dateFormatter.format(currentDate);
		String formattedTime = timeFormatter.format(currentDate);

        String expectedDate = laboratoryOrder.getExpectedDate();
        String formattedExpectedDate = expectedDate.split("-")[2]+"-"+expectedDate.split("-")[1]+"-"+expectedDate.split("-")[0];
        laboratoryOrder.setExpectedDate(formattedExpectedDate);
        
        laboratoryOrder.setClinicId(clinic);
		laboratoryOrder.setLaboratoryOrderDate(formattedDate);
		laboratoryOrder.setLaboratoryOrderTime(formattedTime);
       
        return laboratoryOrderRepository.save(laboratoryOrder);
    }

    public List<LaboratoryOrder> findAllLaboratoryOrders() {
        try {
            return laboratoryOrderRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of laboratory orders.");
        }
    }

    public LaboratoryOrder getLaboratoryOrderById(int id){
        try {
            return laboratoryOrderRepository.findById(id).orElse(null);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory order");
        }
    }

    public LaboratoryOrder updateLaboratoryOrder(int id, LaboratoryOrder laboratoryOrder) {
        try {
        	LaboratoryOrder updatedLaboratoryOrder = laboratoryOrderRepository.findById(id).orElse(null);
            if (updatedLaboratoryOrder == null){
                throw new RuntimeException("Laboratory order not found with id :" + id);
            }
            return laboratoryOrderRepository.save(updatedLaboratoryOrder);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the laboratory order");
        }
    }

    public void deleteLaboratoryOrder(int id){
        try{
        	laboratoryOrderRepository.deleteById(id);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the laboratory order");
        }
    }

    public List<LaboratoryOrder> getClinicOrdersByLabUserId(Integer userId) {
        return laboratoryOrderRepository.getClinicOrdersByLabUserId(userId);
    }
    
    public List<LaboratoryOrder> getLaboratoryOrdersByClinicId(Integer userId) {
    	User user = userRepository.findByUserId(userId);
        Clinic clinic = clinicRepository.findByUserId(user).orElse(null);
        
        return laboratoryOrderRepository.findByClinicId(clinic);
    }

    public LaboratoryOrder updateOrderStatus(Integer id, String status) {
        try {
            LaboratoryOrder order = laboratoryOrderRepository.findById(id).orElseThrow(() -> new RuntimeException("Order not found with id: " + id));
            order.setStatus(status);
            return laboratoryOrderRepository.save(order);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the Laboratory clinic order");
        }
    }
}