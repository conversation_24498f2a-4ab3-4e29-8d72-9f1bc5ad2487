package com.navitsa.mydent.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.navitsa.mydent.entity.ClinicDoctor;
import com.navitsa.mydent.entity.Doctor;
import com.navitsa.mydent.services.ClinicDoctorService;

@RestController
@RequestMapping("/api")
public class ClinicDoctorController {

    @Autowired
    private ClinicDoctorService clinicDoctorService;

    @PostMapping("/assignDoctor/{userId}/{doctorId}")
    public ResponseEntity<Map<String, String>> assignDoctorToClinic(
        @PathVariable int userId,
        @PathVariable int doctorId) {
        System.out.println(userId + "," + doctorId);
        
        // Logic to assign the doctor to the user
        clinicDoctorService.assignDoctor(userId, doctorId);
        
        // Create a response map
        Map<String, String> response = new HashMap<>();
        response.put("message", "Doctor assigned successfully.");
        
        return ResponseEntity.ok(response);
    }


    
    @GetMapping("/doctors/{userId}")
    public ResponseEntity<List<Doctor>> getDoctorsByClinicUserId(@PathVariable Integer userId) {
        System.out.println("############################################" + userId);
        List<Doctor> doctors = clinicDoctorService.getDoctorsByUserId(userId);
        return ResponseEntity.ok(doctors);
    }

    @GetMapping("getAllDoctorsAssignAndNot/{filterInput}/{clinicId}")
    public ResponseEntity<List<Doctor>> getAllDoctorsAssignAndNot(@PathVariable String filterInput,@PathVariable Integer clinicId) {
        return clinicDoctorService.getAllDoctorFilter(filterInput,clinicId);
    }
    
    @DeleteMapping("/remove/{userId}/{doctorId}")
    public ResponseEntity<Map<String, String>> removeDoctorFromClinic(@PathVariable Integer userId, @PathVariable Integer doctorId) {
        System.out.println("############################################ User ID: " + userId + " Doctor ID: " + doctorId);
        try {
            clinicDoctorService.deleteDoctorFromClinic(userId, doctorId);
            
            // Create a response map for success
            Map<String, String> response = new HashMap<>();
            response.put("message", "Doctor removed from clinic successfully.");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            // Create a response map for error
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "Error removing doctor from clinic: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    
    // DTO for request body
    class AssignDoctorRequest {
        private Long userId;
        private Long doctorId;

        // Getters and setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public Long getDoctorId() {
            return doctorId;
        }

        public void setDoctorId(Long doctorId) {
            this.doctorId = doctorId;
        }
    }
}
