package com.navitsa.mydent.controller;

import com.navitsa.mydent.dtos.ClinicSupplierInventoryItemDto;
import com.navitsa.mydent.dtos.InventoryOrderRequestDto;
import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.entity.SupplierOrderDetails;
import com.navitsa.mydent.entity.SupplierOrderHeader;
import com.navitsa.mydent.services.ClinicSupplierService;
import com.navitsa.mydent.services.SupplierInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class ClinicSupplierOrderController {

    private final SupplierInventoryService supplierInventoryService;
    private final ClinicSupplierService clinicSupplierService;

    @Autowired
    public ClinicSupplierOrderController(SupplierInventoryService supplierInventoryService,ClinicSupplierService clinicSupplierService) {
        this.supplierInventoryService = supplierInventoryService;
        this.clinicSupplierService = clinicSupplierService;
    }

    @GetMapping("/getSuppliersByItemCategoryName/{categoryName}")
    public ResponseEntity<List<Supplier>> getSuppliersByItemCategoryName(@PathVariable String categoryName) {
        List<Supplier> supplierByCategoryId = supplierInventoryService.getSupplierByCategoryName(categoryName);
        return ResponseEntity.ok(supplierByCategoryId);
    }

    @GetMapping("/getSuppliersByItemCategoryNameAndSupplierId")
    public ResponseEntity<List<ClinicSupplierInventoryItemDto>> getSuppliersByItemCategoryNameAndSupplierId(@RequestParam String categoryName, @RequestParam Integer supplierId) {
        List<ClinicSupplierInventoryItemDto> supplierByCategoryId = supplierInventoryService.getInventoryItemsByCategoryNameAndSupplierId(categoryName, supplierId);
        return ResponseEntity.ok(supplierByCategoryId);
    }

    @PostMapping("/saveInventoryOrderRequest")
    public boolean saveInventoryOrderRequest(@RequestBody InventoryOrderRequestDto inventoryOrderRequestDto) {
        return clinicSupplierService.saveClinicSupplierOrder(inventoryOrderRequestDto);
    }

    @GetMapping("/getClinicOrdersHeadersFromSupplierId/{supplierId}")
    public List<SupplierOrderHeader> getClinicOrdersHeadersThroughSupplierId(@PathVariable Integer supplierId){
        return clinicSupplierService.getClinicOrdersHeadersThroughSupplierId(supplierId);
    }

    @GetMapping("/getClinicOrdersDetailsFromSupplierId/{headerId}")
    public List<SupplierOrderDetails> getClinicOrdersDetailsThroughHeaderId(@PathVariable Integer headerId){
        return clinicSupplierService.getClinicOrdersDetailsThroughHeaderId(headerId);
    }

    @GetMapping("/getClinicOrdersHeadersFromClinicUserId/{clinicId}")
    public List<SupplierOrderHeader>  getClinicOrdersHeadersThroughClinicUserId(@PathVariable Integer clinicId){
        return clinicSupplierService.getClinicOrdersHeadersThroughClinicUserId(clinicId);
    }


}
