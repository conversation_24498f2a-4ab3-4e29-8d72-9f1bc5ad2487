package com.navitsa.mydent.repositories;

import com.navitsa.mydent.dtos.ClinicSupplierInventoryItemDto;
import com.navitsa.mydent.entity.Supplier;
import org.springframework.data.jpa.repository.JpaRepository;
import com.navitsa.mydent.entity.SupplierInventory;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface SupplierInventoryRepository extends JpaRepository<SupplierInventory, Integer>{

    @Query("SELECT cs.supplierId FROM SupplierInventory cs WHERE cs.category = :categoryName")
    Optional<List<Supplier>> getSupplierInventoriesByCategory(@Param("categoryName") String category);

    @Query("SELECT new com.navitsa.mydent.dtos.ClinicSupplierInventoryItemDto(cs.supplierInventoryId, cs.description, cs.price, cs.quantity, cs.itemImage) FROM SupplierInventory cs WHERE cs.category = :categoryName AND cs.supplierId.supplierId = :supplierId")
    Optional<List<ClinicSupplierInventoryItemDto>> getInventoriesByCategoryNameAndSupplierId(
            @Param("categoryName") String category,
            @Param("supplierId") Integer supplierId);
}
