package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.entity.Clinic;
import com.navitsa.mydent.entity.SupplierInventory;
import com.navitsa.mydent.services.ClinicService;
import com.navitsa.mydent.services.SupplierInventoryService;
import jakarta.websocket.server.PathParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class ClinicController {
    private final ClinicService clinicService;
    private final SupplierInventoryService supplierInventoryService;

    @Autowired
    public ClinicController(ClinicService clinicService ,SupplierInventoryService supplierInventoryService) {
        this.clinicService = clinicService;
        this.supplierInventoryService = supplierInventoryService;
    }

    @PostMapping("/saveClinic")
    public Clinic saveClinic(@RequestBody Clinic clinic) {
        return clinicService.saveClinic(clinic);
    }

    @GetMapping(value = "/clinicList")
    public List<Clinic> getAllClinics() {
        return clinicService.findAllClinics();
    }

    @PutMapping("/updateClinic/{id}")
    public ResponseEntity<Clinic> updateClinic(@PathVariable int id, @RequestBody Clinic clinicDetails) {
        return ResponseEntity.ok(clinicService.updateClinic(id, clinicDetails));
    }

    @GetMapping("/getClinicById/{id}")
    public Clinic getClinicById(@PathVariable int id) {
        return clinicService.getClinicById(id);
    }
    
    
    
    @GetMapping("/getClinicForAppointment")
    public ResponseEntity<List<Clinic>> findClinics(
            @RequestParam("date") String date,
            @RequestParam("time") String time,
            @RequestParam("city") String city,
            @RequestParam("serviceId") Integer serviceId) {
        
        List<Clinic> clinics = clinicService.findClinics(date, time, city, serviceId);
        
        return ResponseEntity.ok(clinics);
    }

    @DeleteMapping("/deleteClinic/{id}")
    public ResponseEntity<Void> deleteClinic(@PathVariable int id) {
        clinicService.deleteClinic(id);
        return ResponseEntity.noContent().build();
    }
    @GetMapping("/check-clinicName")
    public ResponseEntity<Boolean> checkClinicNameExists(@RequestParam String clinicName){
        boolean isClinicNameExists = clinicService.clinicNameExists(clinicName);
        return ResponseEntity.ok(isClinicNameExists);
    }

    @GetMapping("/getPendingAppointmentList/{userId}")
    public ResponseEntity<List<Appointments>> getPendingAppointmentList(@PathVariable int userId){
        List<Appointments> pendingAppointments = clinicService.getPendingAppointments(userId);
        return ResponseEntity.ok(pendingAppointments);
    }

}


