package com.navitsa.mydent.controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.navitsa.mydent.entity.ClinicServices;
import com.navitsa.mydent.services.ClinicServicesService;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/clinic-services")
public class ClinicServicesController {

    @Autowired
    private ClinicServicesService clinicServicesService;

    // Save a new ClinicService
    @PostMapping("/save")
    public ResponseEntity<ClinicServices> saveClinicService(@RequestBody ClinicServices clinicServices) {
        ClinicServices savedService = clinicServicesService.saveClinicService(clinicServices);
        return ResponseEntity.ok(savedService);
    }

    // Update an existing ClinicService
    @PutMapping("/update")
    public ResponseEntity<ClinicServices> updateClinicService(@RequestBody ClinicServices clinicServices) {
        ClinicServices updatedService = clinicServicesService.updateClinicService(clinicServices);
        return ResponseEntity.ok(updatedService);
    }

    // Get all ClinicServices
    @GetMapping("/all")
    public ResponseEntity<List<ClinicServices>> getAllClinicServices() {
        List<ClinicServices> clinicServicesList = clinicServicesService.getAllClinicServices();
        return ResponseEntity.ok(clinicServicesList);
    }

    // Find a ClinicService by ID
    @GetMapping("/{id}")
    public ResponseEntity<ClinicServices> findClinicServiceById(@PathVariable Integer id) {
        Optional<ClinicServices> clinicServices = clinicServicesService.findClinicServiceById(id);
        return clinicServices.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    // Delete a ClinicService by ID
    @DeleteMapping("/delete/{id}/{clinicId}")
    public Map<String, Object> deleteClinicService(@PathVariable Integer id,
                                                   @PathVariable Integer clinicId) {
        return clinicServicesService.deleteClinicService(id, clinicId);
    }


    @GetMapping("/by-clinic/{clinicId}")
    public ResponseEntity<List<ClinicServices>> getServicesByClinicId(@PathVariable Integer clinicId) {
        List<ClinicServices> services = clinicServicesService.getServicesByClinicId(clinicId);
        for (ClinicServices service : services) {
            String jsonFormat = String.format(
                "{\"clinicServiceId\": %d, \"clinicId\": %d, \"serviceId\": %d, \"serviceName\": \"%s\"}",
                service.getClinicServiceId(),
                service.getClinics().getClinicId(),
                service.getServices().getClinicServiceCategoryId(),
                service.getServices().getClinicServiceCategoryName()
            );
            System.out.println(jsonFormat);
        }
        return ResponseEntity.ok(services);
    }
    
    
}
