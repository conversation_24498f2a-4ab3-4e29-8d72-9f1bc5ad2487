package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.LaboratorySubCategories;
import com.navitsa.mydent.entity.LaboratoryCategories;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LaboratorySubCategoriesRepository extends JpaRepository<LaboratorySubCategories, Integer> {
	
	List<LaboratorySubCategories> findByLaboratoryCategoryId(LaboratoryCategories laboratoryCategoryId);
	
	@Query("SELECT i FROM LaboratorySubCategories i WHERE i.laboratorySubCategoryName = :laboratorySubCategoryName AND i.laboratoryCategoryId.laboratoryCategoryName = :laboratoryCategoryName")
	public LaboratorySubCategories findByLaboratorySubCategoryNameAndLaboratoryCategory(@Param("laboratorySubCategoryName") String laboratorySubCategoryName, @Param("laboratoryCategoryName") String laboratoryCategoryName);
}


