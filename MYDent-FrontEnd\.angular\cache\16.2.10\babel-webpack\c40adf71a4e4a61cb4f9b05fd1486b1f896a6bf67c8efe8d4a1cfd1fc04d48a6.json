{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { map, of } from 'rxjs'; // Import Observable\nimport { SignUpDto, User, UserCategory } from '../../../user/user';\nimport { Appointments } from '../appointments';\nimport { Customer } from './../customer';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../appointments.service\";\nimport * as i3 from \"../../../user/user.service\";\nimport * as i4 from \"../../shared-services/shared.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../core/default-navbar/default-navbar.component\";\nconst _c0 = function (a0) {\n  return {\n    \"active\": a0\n  };\n};\nfunction AppointmentComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"div\", 13);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.currentStep === i_r6 + 1));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r5);\n  }\n}\nfunction AppointmentComponent_div_13_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_6_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_11_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.appointmentForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Address is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_17_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Address must be at least 10 characters long.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_17_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_13_div_17_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.appointmentForm.get(\"address\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.appointmentForm.get(\"address\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Email is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Invalid email format.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.userEmailExistsMessage);\n  }\n}\nfunction AppointmentComponent_div_13_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_22_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_13_div_22_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(3, AppointmentComponent_div_13_div_22_div_3_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.appointmentForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r10.appointmentForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r10.appointmentForm.get(\"email\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"emailExists\"]);\n  }\n}\nfunction AppointmentComponent_div_13_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const district_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", district_r25);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", district_r25, \" \");\n  }\n}\nfunction AppointmentComponent_div_13_div_32_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" District is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_32_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.appointmentForm.get(\"district\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_13_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r27);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", city_r27, \" \");\n  }\n}\nfunction AppointmentComponent_div_13_div_41_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" City is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_41_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r14.appointmentForm.get(\"city\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_47_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Telephone is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_47_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Invalid telephone number format.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_47_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_13_div_47_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r15.appointmentForm.get(\"telephone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r15.appointmentForm.get(\"telephone\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_56_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Password is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_56_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Password must be at least \", (tmp_0_0 = ctx_r32.appointmentForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"minlength\"].requiredLength, \" characters long. \");\n  }\n}\nfunction AppointmentComponent_div_13_div_56_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Password must include at least one uppercase letter, one lowercase letter, and one number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_56_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_13_div_56_div_2_Template, 2, 1, \"div\", 11);\n    i0.ɵɵtemplate(3, AppointmentComponent_div_13_div_56_div_3_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r16.appointmentForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r16.appointmentForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r16.appointmentForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction AppointmentComponent_div_13_div_61_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Re-enter the password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_61_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Passwords do not match. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_13_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_13_div_61_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_13_div_61_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r17.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r17.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"mismatch\"]) && ((tmp_1_0 = ctx_r17.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_1_0.dirty));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nfunction AppointmentComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 14)(2, \"div\", 15)(3, \"label\", 16);\n    i0.ɵɵtext(4, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 17);\n    i0.ɵɵtemplate(6, AppointmentComponent_div_13_div_6_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 15)(8, \"label\", 19);\n    i0.ɵɵtext(9, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 20);\n    i0.ɵɵtemplate(11, AppointmentComponent_div_13_div_11_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"div\", 15)(14, \"label\", 21);\n    i0.ɵɵtext(15, \"Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 22);\n    i0.ɵɵtemplate(17, AppointmentComponent_div_13_div_17_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 15)(19, \"label\", 23);\n    i0.ɵɵtext(20, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"input\", 24);\n    i0.ɵɵtemplate(22, AppointmentComponent_div_13_div_22_Template, 4, 3, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"div\", 15)(25, \"label\", 25);\n    i0.ɵɵtext(26, \"District\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 26)(28, \"select\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AppointmentComponent_div_13_Template_select_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.appointments.state = $event);\n    })(\"change\", function AppointmentComponent_div_13_Template_select_change_28_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onDistrictChange($event));\n    });\n    i0.ɵɵelementStart(29, \"option\", 28);\n    i0.ɵɵtext(30, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, AppointmentComponent_div_13_option_31_Template, 2, 2, \"option\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, AppointmentComponent_div_13_div_32_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 15)(34, \"label\", 30);\n    i0.ɵɵtext(35, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 26)(37, \"select\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function AppointmentComponent_div_13_Template_select_ngModelChange_37_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.selectedCity = $event);\n    })(\"change\", function AppointmentComponent_div_13_Template_select_change_37_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.onCitySelect());\n    });\n    i0.ɵɵelementStart(38, \"option\", 28);\n    i0.ɵɵtext(39, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, AppointmentComponent_div_13_option_40_Template, 2, 2, \"option\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, AppointmentComponent_div_13_div_41_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 14)(43, \"div\", 15)(44, \"label\", 32);\n    i0.ɵɵtext(45, \"Telephone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"input\", 33);\n    i0.ɵɵtemplate(47, AppointmentComponent_div_13_div_47_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 14)(49, \"div\", 15)(50, \"label\", 34);\n    i0.ɵɵtext(51, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 35);\n    i0.ɵɵelement(53, \"input\", 36);\n    i0.ɵɵelementStart(54, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_13_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.togglePasswordVisibility());\n    });\n    i0.ɵɵelement(55, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(56, AppointmentComponent_div_13_div_56_Template, 4, 3, \"div\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 15)(58, \"label\", 40);\n    i0.ɵɵtext(59, \"Confirm Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(60, \"input\", 41);\n    i0.ɵɵtemplate(61, AppointmentComponent_div_13_div_61_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 42)(63, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_13_Template_button_click_63_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.nextStep());\n    });\n    i0.ɵɵtext(64, \"Next\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_6_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_12_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r1.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.invalid) && (((tmp_0_0 = ctx_r1.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.dirty) || ((tmp_0_0 = ctx_r1.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.touched)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_1_0.invalid) && (((tmp_1_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_1_0.dirty) || ((tmp_1_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r1.appointmentForm.get(\"address\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx_r1.appointmentForm.get(\"address\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx_r1.appointmentForm.get(\"address\")) == null ? null : tmp_2_0.touched)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r1.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.dirty) || ((tmp_3_0 = ctx_r1.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.appointments.state);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.districts);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r1.appointmentForm.get(\"district\")) == null ? null : tmp_6_0.invalid) && (((tmp_6_0 = ctx_r1.appointmentForm.get(\"district\")) == null ? null : tmp_6_0.dirty) || ((tmp_6_0 = ctx_r1.appointmentForm.get(\"district\")) == null ? null : tmp_6_0.touched)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.selectedCity);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.cities);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r1.appointmentForm.get(\"city\")) == null ? null : tmp_9_0.invalid) && (((tmp_9_0 = ctx_r1.appointmentForm.get(\"city\")) == null ? null : tmp_9_0.dirty) || ((tmp_9_0 = ctx_r1.appointmentForm.get(\"city\")) == null ? null : tmp_9_0.touched)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r1.appointmentForm.get(\"telephone\")) == null ? null : tmp_10_0.invalid) && (((tmp_10_0 = ctx_r1.appointmentForm.get(\"telephone\")) == null ? null : tmp_10_0.dirty) || ((tmp_10_0 = ctx_r1.appointmentForm.get(\"telephone\")) == null ? null : tmp_10_0.touched)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵpropertyInterpolate(\"type\", ctx_r1.passwordVisible ? \"text\" : \"password\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c1, ((tmp_12_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_12_0.invalid) && (((tmp_12_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_12_0.dirty) || ((tmp_12_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_12_0.touched))));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.passwordVisible ? \"bi bi-eye-fill\" : \"bi bi-eye-slash-fill\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_14_0.invalid) && (((tmp_14_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_14_0.dirty) || ((tmp_14_0 = ctx_r1.appointmentForm.get(\"password\")) == null ? null : tmp_14_0.touched)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r1.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.invalid) && (((tmp_15_0 = ctx_r1.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.dirty) || ((tmp_15_0 = ctx_r1.appointmentForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.touched)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isStepInvalid());\n  }\n}\nfunction AppointmentComponent_div_14_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r48 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r48);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", city_r48, \" \");\n  }\n}\nfunction AppointmentComponent_div_14_div_29_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" City is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_14_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppointmentComponent_div_14_div_29_small_1_Template, 2, 0, \"small\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r44.appointmentForm.get(\"city\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_14_div_36_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Date From is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_14_div_36_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Date From cannot be in the past.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_14_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_14_div_36_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵtemplate(2, AppointmentComponent_div_14_div_36_div_2_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r45.appointmentForm.get(\"fromDate\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r45.appointmentForm.get(\"fromDate\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pastDate\"]);\n  }\n}\nfunction AppointmentComponent_div_14_div_45_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Preferred Time From is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_14_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_14_div_45_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r46.appointmentForm.get(\"fromTime\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_14_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Preferred Time To is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_14_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, AppointmentComponent_div_14_div_50_div_1_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r47.appointmentForm.get(\"toTime\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AppointmentComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 14)(2, \"div\", 15)(3, \"label\", 47);\n    i0.ɵɵtext(4, \"Preferred Service\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"select\", 48)(7, \"option\", 49);\n    i0.ɵɵtext(8, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"option\", 50);\n    i0.ɵɵtext(10, \"Dental Bonding\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 51);\n    i0.ɵɵtext(12, \"Cosmetic Fillings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 52);\n    i0.ɵɵtext(14, \"Invisalign\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 53);\n    i0.ɵɵtext(16, \"Teeth Cleanings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 54);\n    i0.ɵɵtext(18, \"Root Canal Therapy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 55);\n    i0.ɵɵtext(20, \"Dental Sealants\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"label\", 30);\n    i0.ɵɵtext(23, \"City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 26)(25, \"select\", 56)(26, \"option\", 28);\n    i0.ɵɵtext(27, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, AppointmentComponent_div_14_option_28_Template, 2, 2, \"option\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, AppointmentComponent_div_14_div_29_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 14)(31, \"label\", 57);\n    i0.ɵɵtext(32, \"Preferred Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 58)(34, \"div\", 15);\n    i0.ɵɵelement(35, \"input\", 59);\n    i0.ɵɵtemplate(36, AppointmentComponent_div_14_div_36_Template, 3, 2, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 14)(38, \"label\", 60);\n    i0.ɵɵtext(39, \"Preferred Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 58)(41, \"div\", 15)(42, \"label\", 61);\n    i0.ɵɵtext(43, \"From\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 62);\n    i0.ɵɵtemplate(45, AppointmentComponent_div_14_div_45_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 15)(47, \"label\", 63);\n    i0.ɵɵtext(48, \"To\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(49, \"input\", 64);\n    i0.ɵɵtemplate(50, AppointmentComponent_div_14_div_50_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 14)(52, \"div\", 15)(53, \"div\", 65);\n    i0.ɵɵelement(54, \"input\", 66);\n    i0.ɵɵelementStart(55, \"label\", 67);\n    i0.ɵɵtext(56, \"Immediate Booking\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 42)(58, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_14_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.prevStep());\n    });\n    i0.ɵɵtext(59, \"Previous\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_14_Template_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r56 = i0.ɵɵnextContext();\n      ctx_r56.cliniclistload();\n      return i0.ɵɵresetView(ctx_r56.nextStep());\n    });\n    i0.ɵɵtext(61, \"Find Your Nearest Dental Clinic\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    let tmp_3_0;\n    let tmp_5_0;\n    let tmp_7_0;\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.cities);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r2.appointmentForm.get(\"city\")) == null ? null : tmp_1_0.invalid) && (((tmp_1_0 = ctx_r2.appointmentForm.get(\"city\")) == null ? null : tmp_1_0.dirty) || ((tmp_1_0 = ctx_r2.appointmentForm.get(\"city\")) == null ? null : tmp_1_0.touched)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"min\", ctx_r2.currentDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.appointmentForm.get(\"fromDate\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx_r2.appointmentForm.get(\"fromDate\")) == null ? null : tmp_3_0.dirty) || ((tmp_3_0 = ctx_r2.appointmentForm.get(\"fromDate\")) == null ? null : tmp_3_0.touched)));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isImmediateBooking);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r2.appointmentForm.get(\"fromTime\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = ctx_r2.appointmentForm.get(\"fromTime\")) == null ? null : tmp_5_0.dirty) || ((tmp_5_0 = ctx_r2.appointmentForm.get(\"fromTime\")) == null ? null : tmp_5_0.touched)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isImmediateBooking);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.appointmentForm.get(\"toTime\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = ctx_r2.appointmentForm.get(\"toTime\")) == null ? null : tmp_7_0.dirty) || ((tmp_7_0 = ctx_r2.appointmentForm.get(\"toTime\")) == null ? null : tmp_7_0.touched)));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isStepInvalid());\n  }\n}\nfunction AppointmentComponent_div_15_tr_6_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1, \"Book Now\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentComponent_div_15_tr_6_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1, \"Selected\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    \"selected-clinic\": a0\n  };\n};\nfunction AppointmentComponent_div_15_tr_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 76);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_15_tr_6_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r62);\n      const clinic_r58 = restoredCtx.$implicit;\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.selectClinic(clinic_r58.clinicId));\n    });\n    i0.ɵɵelementStart(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 77);\n    i0.ɵɵtemplate(6, AppointmentComponent_div_15_tr_6_span_6_Template, 2, 0, \"span\", 78);\n    i0.ɵɵtemplate(7, AppointmentComponent_div_15_tr_6_span_7_Template, 2, 0, \"span\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const clinic_r58 = ctx.$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, ctx_r57.selectedClinic === clinic_r58.clinicId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(clinic_r58.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(clinic_r58.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.selectedClinic !== clinic_r58.clinicId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r57.selectedClinic === clinic_r58.clinicId);\n  }\n}\nfunction AppointmentComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"h4\", 70);\n    i0.ɵɵtext(2, \"Please select nearest clinic:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"table\", 72)(5, \"tbody\");\n    i0.ɵɵtemplate(6, AppointmentComponent_div_15_tr_6_Template, 8, 7, \"tr\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 74)(8, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.prevStep());\n    });\n    i0.ɵɵtext(9, \"Previous\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 75);\n    i0.ɵɵtext(11, \"Request For The Appointment\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.clinics);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.selectedClinic);\n  }\n}\nfunction AppointmentComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 82)(2, \"h4\", 70);\n    i0.ɵɵtext(3, \"Appointment Submission\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 83);\n    i0.ɵɵelement(5, \"i\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 70);\n    i0.ɵɵtext(7, \"Your Appointment Submission is Sent to Approval Now. \");\n    i0.ɵɵelement(8, \"br\");\n    i0.ɵɵtext(9, \" Please Check Your Notifications or Bookings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 85)(11, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_16_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.cancelAppointment());\n    });\n    i0.ɵɵtext(12, \"Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function AppointmentComponent_div_16_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r67 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r67.saveAppointment());\n    });\n    i0.ɵɵtext(14, \"Done\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nclass AppointmentComponent {\n  getCurrentDate() {\n    const today = new Date();\n    const dd = String(today.getDate()).padStart(2, '0');\n    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!\n    const yyyy = today.getFullYear();\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  constructor(fb, appointmentsService, userService, sharedService) {\n    this.fb = fb;\n    this.appointmentsService = appointmentsService;\n    this.userService = userService;\n    this.sharedService = sharedService;\n    this.appointments = new Appointments();\n    this.customer = new Customer();\n    this.user = new User();\n    this.signUpDto = new SignUpDto();\n    this.userCategory = new UserCategory();\n    this.currentStep = 1;\n    this.steps = ['Personal Info', 'Service Details', 'Clinic Selection', 'Confirmation'];\n    this.currentDate = this.getCurrentDate();\n    this.userEmailExistsMessage = ''; // Ensure this is defined\n    this.isEmailRegistered = false;\n    this.isImmediateBooking = false;\n    this.selectedCity = '';\n    this.nearestCity = '';\n    this.districts = [];\n    this.cities = [];\n    this.clinics = [];\n    this.defaultDate = new Date().toISOString().substr(0, 10);\n    this.passwordVisible = false; // Declare passwordVisible as false initially\n    // Set default date to today\n    const today = new Date();\n    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD\n    // Initialize form\n    this.appointmentForm = this.fb.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      address: ['', [Validators.required, Validators.minLength(10)]],\n      city: [{\n        value: '',\n        disabled: true\n      }, [Validators.required, Validators.minLength(3)]],\n      district: ['', [Validators.required, Validators.minLength(3)]],\n      telephone: ['', [Validators.required, Validators.pattern(/^\\+?[0-9]{7,15}$/)]],\n      email: ['', [Validators.required, Validators.email], [this.checkUserEmail.bind(this)]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).*')]],\n      confirmPassword: ['', Validators.required],\n      preferredservice: [''],\n      nearestCity: [''],\n      fromDate: [{\n        value: this.defaultDate,\n        disabled: false\n      }, Validators.required],\n      toDate: [{\n        value: this.defaultDate,\n        disabled: false\n      }, Validators.required],\n      fromTime: ['', Validators.required],\n      toTime: ['', Validators.required],\n      immediate: [false]\n    }, {\n      validators: this.passwordMatchValidator\n    }); // Note: 'validators' is plural here\n    // Subscribe to changes in the immediate checkbox\n    this.appointmentForm.get('immediate')?.valueChanges.subscribe(value => {\n      this.onImmediateBookingChange(value);\n    });\n  }\n  // Toggle password visibility\n  togglePasswordVisibility() {\n    this.passwordVisible = !this.passwordVisible;\n  }\n  // Accept the value from the checkbox\n  onImmediateBookingChange(isImmediate) {\n    if (isImmediate) {\n      // Get the current date and time\n      const currentDate = new Date().toISOString().substring(0, 10); // Format as YYYY-MM-DD\n      const currentTime = new Date().toTimeString().substring(0, 5); // Format as HH:MM\n      // Update the form values for immediate booking\n      this.appointmentForm.patchValue({\n        fromDate: currentDate,\n        toDate: currentDate,\n        fromTime: currentTime,\n        toTime: '23:59' // Set the end time to the end of the day\n      });\n    } else {\n      // Optionally clear values or handle logic for unchecking\n      this.appointmentForm.patchValue({\n        fromDate: '',\n        toDate: '',\n        fromTime: '',\n        toTime: ''\n      });\n    }\n  }\n  // Custom validator to ensure date is today or in the future\n  futureDateValidator(control) {\n    const selectedDate = new Date(control.value);\n    const today = new Date(this.defaultDate);\n    return selectedDate < today ? {\n      futureDate: true\n    } : null;\n  }\n  passwordMatchValidator(formGroup) {\n    const password = formGroup.get('password');\n    const confirmPassword = formGroup.get('confirmPassword');\n    if (!password || !confirmPassword) {\n      return null;\n    }\n    if (confirmPassword.errors && !confirmPassword.errors['mismatch']) {\n      return null;\n    }\n    if (password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        mismatch: true\n      });\n    } else {\n      confirmPassword.setErrors(null); // Clear the mismatch error if they match\n    }\n\n    return null; // Return null for no errors\n  }\n\n  checkUserEmail(control) {\n    const userEmail = control.value;\n    if (userEmail) {\n      return this.userService.checkUser(userEmail).pipe(map(data => {\n        if (data) {\n          this.isEmailRegistered = true;\n          this.userEmailExistsMessage = 'Email already registered. Try another.';\n          return {\n            emailExists: true\n          }; // Return an error object\n        } else {\n          this.isEmailRegistered = false;\n          this.userEmailExistsMessage = '';\n          return null; // Return null if no error\n        }\n      }));\n    } else {\n      return of(null); // Return null if the control value is empty\n    }\n  }\n\n  ngOnInit() {\n    localStorage.clear();\n    this.userService.getUserCategoryById(5).subscribe(response => {\n      this.userCategory = response;\n    });\n    this.districts = this.sharedService.getDistricts();\n  }\n  onDistrictChange(event) {\n    const selectedDistrict = event.target.value;\n    if (selectedDistrict) {\n      this.appointmentForm.get('city')?.enable();\n      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\n    } else {\n      this.appointmentForm.get('city')?.disable();\n      this.cities = [];\n    }\n    this.appointmentForm.get('city')?.setValue('');\n  }\n  onCitySelect() {\n    this.nearestCity = this.selectedCity;\n    this.appointmentForm.controls['nearestCity'].setValue(this.selectedCity);\n  }\n  nextStep() {\n    if (this.currentStep === 1) {\n      this.registerUser();\n    }\n    if (this.currentStep < this.steps.length) {\n      this.currentStep++;\n    }\n  }\n  cliniclistload() {\n    const fromDate = this.appointmentForm.get('fromDate')?.value;\n    // Convert string to Date object\n    const date = new Date(fromDate);\n    // Get the day of the week (0 is Sunday, 1 is Monday, ..., 6 is Saturday)\n    const dayOfWeek = date.getDay();\n    // Array to convert day index to name\n    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n    const dayName = days[dayOfWeek]; // Get the day name like 'Monday'\n    console.log('Selected Day:', dayName); // Log the selected day for debugging\n    // Retrieve other form values\n    const fromTime = this.appointmentForm.get('fromTime')?.value;\n    const nearestCity = this.appointmentForm.get('city')?.value;\n    const preferredservice = this.appointmentForm.get('preferredservice')?.value;\n    // API call to find clinics\n    this.appointmentsService.findClinics(dayName, fromTime, nearestCity, preferredservice).subscribe(response => {\n      console.log('Selected Nearest response:', response); // Log nearest city for debugging\n      this.clinics = response; // Assign response to clinics array\n      console.log('Clinics found:', this.clinics); // Log the response for debugging\n      // Check if clinics are found\n      if (this.clinics.length === 0) {\n        // Alert if no clinics are loaded\n        alert('No clinics found in ' + nearestCity + '. Please try another nearest city.');\n        this.selectedClinic = undefined; // Reset selected clinic\n        // Set current step to 2 after alert acknowledgment\n        this.currentStep = 2; // Update current step to 2\n        return; // Exit the method to prevent further processing\n      } else {\n        console.log('Clinics loaded:', this.clinics); // Log clinics data\n        this.selectedClinic = undefined; // Reset selected clinic when new data is fetched\n      }\n    }, error => {\n      // Handle error if the API call fails\n      console.error('Error loading clinics:', error);\n      alert('An error occurred while loading clinics. Please try again later.');\n    });\n  }\n  isStepInvalid() {\n    // Step 1 validation\n    if (this.currentStep === 1) {\n      return !!this.appointmentForm.get('firstName')?.invalid || !!this.appointmentForm.get('lastName')?.invalid || !!this.appointmentForm.get('address')?.invalid || !!this.appointmentForm.get('email')?.invalid || !!this.appointmentForm.get('district')?.invalid || !!this.appointmentForm.get('city')?.invalid || !!this.appointmentForm.get('telephone')?.invalid || !!this.appointmentForm.get('password')?.invalid || !!this.appointmentForm.get('confirmPassword')?.invalid;\n    }\n    // Step 2 validation\n    if (this.currentStep === 2) {\n      return !!this.appointmentForm.get('preferredservice')?.invalid || !!this.appointmentForm.get('nearestCity')?.invalid || !!this.appointmentForm.get('fromDate')?.invalid || !!this.appointmentForm.get('fromTime')?.invalid || !!this.appointmentForm.get('toTime')?.invalid;\n    }\n    // Add checks for other steps if needed\n    return false;\n  }\n  prevStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  saveCustomerDetails() {\n    const formValues = this.appointmentForm.value;\n    this.appointments = {\n      ...this.appointments,\n      email: formValues.email\n    };\n    // Create the customer object\n    this.customer = {\n      customerId: 0,\n      user: this.customer.user,\n      firstName: formValues.firstName,\n      lastName: formValues.lastName,\n      address: formValues.address,\n      city: formValues.city,\n      state: formValues.state,\n      country: '',\n      telephone: formValues.telephone,\n      email: this.appointments.email\n    };\n    // Call the service to save the customer\n    this.appointmentsService.saveCustomer(this.customer).subscribe({\n      next: response => {\n        if (response.customerId) {\n          this.appointments.customer.customerId = response.customerId;\n          console.log(' Customer registered successfully:', this.appointments.customer.customerId);\n        }\n      },\n      error: error => {\n        console.error(' Error registering Customer:', error);\n      }\n    });\n  }\n  updateEmail() {\n    this.user.username = this.appointments.email;\n  }\n  selectClinic(clinicId) {\n    this.selectedClinic = clinicId; // Update selected clinic on click\n    console.log('Clinic selected:', clinicId); // Log the clinic that was selected\n    console.log('Current selectedClinic value:', this.selectedClinic); // Log the updated selectedClinic value\n  }\n\n  resetForm() {\n    this.appointmentForm.reset();\n    this.currentStep = 1;\n    this.selectedClinic = undefined;\n  }\n  cancelAppointment() {\n    console.log('Appointment canceled.');\n    this.resetForm();\n  }\n  completeAppointment() {\n    console.log('Appointment completed.');\n    this.resetForm();\n  }\n  onSubmit() {\n    this.saveAppointment();\n    this.currentStep = 4;\n  }\n  // Save Appointment Method\n  saveAppointment() {\n    // Call saveCustomerDetails() and handle its result\n    // this.saveCustomerDetails().pipe(\n    //     switchMap((customer: Customer) => {\n    //         // Check if the customer is saved and populated\n    //         if (customer && customer.customerId) {\n    //             // Populate the appointments with the saved customer details\n    //             const formValues = this.appointmentForm.value;\n    //             this.appointments = {\n    //                 ...this.appointments,\n    //                 firstName: formValues.firstName,\n    //                 lastName: formValues.lastName,\n    //                 address: formValues.address,\n    //                 city: formValues.city,\n    //                 state: formValues.state,\n    //                 district: formValues.district,\n    //                 telephone: formValues.telephone,\n    //                 email: formValues.email,\n    //                 userName: formValues.username,\n    //                 password: formValues.password,\n    //                 preferredservice: formValues.preferredservice,\n    //                 nearestCity: formValues.nearestCity,\n    //                 fromDate: formValues.fromDate,\n    //                 toDate: formValues.toDate,\n    //                 fromTime: formValues.fromTime,\n    //                 toTime: formValues.toTime,\n    //                 clinics: new Clinic(),\n    //                 customer: customer // Use the saved customer directly\n    //             };\n    //             this.appointments.clinics.clinicId = this.selectedClinic;\n    //             // Call the service to save the appointment\n    //             return this.appointmentsService.saveAppointments(this.appointments);\n    //         } else {\n    //             console.error('Customer is not populated correctly');\n    //             return EMPTY; // Return an empty observable to end the chain\n    //         }\n    //     })\n    // ).subscribe(\n    //     (response: Appointments) => {\n    //         console.log('Appointment saved successfully', response);\n    //     },\n    //     (error: any) => {\n    //         console.error('Error saving appointment', error);\n    //     }\n    // );\n    this.appointments.clinics.clinicId = this.selectedClinic;\n    const formValues = this.appointmentForm.value;\n    this.appointments = {\n      appointmentId: 0,\n      firstName: formValues.firstName,\n      lastName: formValues.lastName,\n      address: formValues.address,\n      city: formValues.city,\n      state: formValues.state,\n      district: formValues.district,\n      telephone: formValues.telephone,\n      email: formValues.email,\n      userName: formValues.username,\n      password: formValues.password,\n      preferredservice: formValues.preferredservice,\n      nearestCity: formValues.nearestCity,\n      fromDate: formValues.fromDate,\n      toDate: formValues.toDate,\n      fromTime: this.appointmentForm.value.fromTime,\n      toTime: this.appointmentForm.value.toTime,\n      customer: this.appointments.customer,\n      clinics: this.appointments.clinics,\n      status: 'Pending'\n    };\n    // Log the full object\n    console.log('Appointments object:', this.appointments);\n    this.appointmentsService.saveCustomerAppointments(this.appointments).subscribe({\n      next: response => {\n        console.log(response);\n      },\n      error: error => {\n        console.error(' customer appoinment error', error);\n      }\n    });\n  }\n  //   onUserRegister(): Observable<number> {\n  //     this.signUpDto.userCategoryId = this.userCategory;\n  //     this.signUpDto.firstName = this.appointmentForm.get('firstName')?.value;\n  // this.signUpDto.lastName = this.appointmentForm.get('lastName')?.value;\n  // this.signUpDto.username = this.user.username;\n  //     this.signUpDto.password = String(this.appointmentForm.get('password')?.value);\n  //     console.log('User Category set to:', this.signUpDto);\n  //     return this.userService.register(this.signUpDto).pipe(\n  //       tap((response: { id: number }) => {\n  //         this.user.userId = response.id;\n  //       }),\n  //       map((response: { id: number }) => response.id)\n  //     );\n  //   }\n  registerUser() {\n    this.updateEmail();\n    this.signUpDto.userCategoryId = this.userCategory;\n    this.signUpDto.firstName = this.appointmentForm.get('firstName')?.value;\n    this.signUpDto.lastName = this.appointmentForm.get('lastName')?.value;\n    this.signUpDto.username = this.appointmentForm.get('email')?.value;\n    this.signUpDto.password = this.appointmentForm.get('password')?.value;\n    console.log('Registering user:', this.appointments.email);\n    this.userService.register(this.signUpDto).subscribe({\n      next: response => {\n        if (response.id) {\n          this.customer.user.userId = response.id;\n          this.saveCustomerDetails();\n        }\n      },\n      error: error => {\n        console.error(' Error registering user:', error);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function AppointmentComponent_Factory(t) {\n    return new (t || AppointmentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentsService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.SharedService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppointmentComponent,\n    selectors: [[\"app-appointment\"]],\n    decls: 17,\n    vars: 6,\n    consts: [[\"loggedUser\", \"Test Appointment\"], [2, \"padding-bottom\", \"50px\"], [1, \"appointment-container\"], [1, \"header\", \"py-1\", 2, \"display\", \"flex\", \"justify-content\", \"center\", \"align-items\", \"center\", \"width\", \"100%\"], [2, \"text-align\", \"center\"], [1, \"content\", \"py-1\"], [1, \"navigation\", \"mb-4\"], [1, \"mb-3\", \"header1\"], [1, \"navigation-bar\"], [\"class\", \"navigation-step\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [2, \"padding-inline\", \"20px\", 3, \"formGroup\", \"ngSubmit\"], [4, \"ngIf\"], [1, \"navigation-step\", 3, \"ngClass\"], [1, \"dot\"], [1, \"row\", \"mb-3\"], [1, \"col-md-6\"], [\"for\", \"firstName\", 1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"id\", \"firstName\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"id\", \"lastName\", 1, \"form-control\"], [\"for\", \"address\", 1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"address\", \"id\", \"address\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"formControlName\", \"email\", \"id\", \"email\", 1, \"form-control\"], [\"for\", \"district\", 1, \"form-label\"], [1, \"custom-arrow\"], [\"name\", \"district\", \"id\", \"district\", \"formControlName\", \"district\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"city\", 1, \"form-label\"], [\"id\", \"city\", \"formControlName\", \"city\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"tel\", \"formControlName\", \"telephone\", \"id\", \"telephone\", 1, \"form-control\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"form-group\", \"position-relative\", 2, \"position\", \"relative\"], [\"formControlName\", \"password\", \"id\", \"password\", 1, \"form-control\", 2, \"width\", \"125%\", 3, \"type\", \"ngClass\"], [\"type\", \"button\", 1, \"show-password\", 2, \"position\", \"absolute\", \"right\", \"-35px\", \"top\", \"50%\", \"transform\", \"translateY(-50%)\", \"padding\", \"0\", \"border\", \"none\", \"background\", \"none\", \"color\", \"#6c757d\", \"cursor\", \"pointer\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"text-danger warnings\", 4, \"ngIf\"], [\"for\", \"confirm-password\", 1, \"form-label\"], [\"type\", \"password\", \"formControlName\", \"confirmPassword\", \"id\", \"confirmPassword\", \"placeholder\", \"Re-enter your password\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\", \"btn-field\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"first-next\", 3, \"disabled\", \"click\"], [1, \"text-danger\"], [3, \"value\"], [1, \"text-danger\", \"warnings\"], [\"for\", \"preferredservice\", 1, \"form-label\"], [\"formControlName\", \"preferredservice\", \"id\", \"preferredservice\", \"required\", \"\", 1, \"form-control\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [\"value\", \"6\"], [\"id\", \"nearestCity\", \"formControlName\", \"city\", 1, \"form-control\"], [\"for\", \"preferredDate\", 1, \"form-label\"], [1, \"row\"], [\"type\", \"date\", \"formControlName\", \"fromDate\", \"id\", \"fromDate\", \"required\", \"\", 1, \"form-control\", 3, \"min\"], [\"for\", \"preferredTime\", 1, \"form-label\"], [\"for\", \"fromTime\", 1, \"form-label\"], [\"type\", \"time\", \"formControlName\", \"fromTime\", \"id\", \"fromTime\", \"required\", \"\", 1, \"form-control\", 3, \"disabled\"], [\"for\", \"toTime\", 1, \"form-label\"], [\"type\", \"time\", \"formControlName\", \"toTime\", \"id\", \"toTime\", \"required\", \"\", 1, \"form-control\", 3, \"disabled\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"formControlName\", \"immediate\", \"id\", \"immediate\", 1, \"form-check-input\"], [\"for\", \"immediate\", 1, \"form-check-label\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"second-next\", 3, \"disabled\", \"click\"], [1, \"mb-4\"], [1, \"clinic-list\", \"mb-4\"], [1, \"table\"], [3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [3, \"ngClass\", \"click\"], [1, \"text-end\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [\"class\", \"badge bg-success\", 4, \"ngIf\"], [1, \"text-primary\"], [1, \"badge\", \"bg-success\"], [1, \"text-center\"], [1, \"appointment-success\", \"mb-4\"], [1, \"bi\", \"bi-check-circle-fill\", \"text-success\", \"display-4\"], [1, \"d-flex\", \"justify-content-center\", \"d-none\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"me-3\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function AppointmentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n        i0.ɵɵtext(5, \"MyDent Dental Consultations With Certified Professionals! Book Your Appointment Now!\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"h2\", 7);\n        i0.ɵɵtext(9, \"Make Appointment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 8);\n        i0.ɵɵtemplate(11, AppointmentComponent_div_11_Template, 4, 4, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function AppointmentComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtemplate(13, AppointmentComponent_div_13_Template, 65, 19, \"div\", 11);\n        i0.ɵɵtemplate(14, AppointmentComponent_div_14_Template, 62, 9, \"div\", 11);\n        i0.ɵɵtemplate(15, AppointmentComponent_div_15_Template, 12, 2, \"div\", 11);\n        i0.ɵɵtemplate(16, AppointmentComponent_div_16_Template, 15, 0, \"div\", 11);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"ngForOf\", ctx.steps);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"formGroup\", ctx.appointmentForm);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.DefaultNavbarComponent],\n    styles: [\"\\n\\n\\nbody[_ngcontent-%COMP%] {\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n  color: #333;\\n  margin: 0;\\n  padding: 0;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n\\n.appointment-container[_ngcontent-%COMP%] {\\n  width: 60%;\\n  max-width: 1000px;\\n  margin: auto;\\n  background-color: #fff;\\n  border: 1px solid #ff6600;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  align-items: center;\\n  height: 100%;\\n  box-sizing: border-box;\\n}\\n\\n.warnings[_ngcontent-%COMP%]{\\n  \\n\\n  position: absolute;\\n  display: flex;\\n  flex-direction: column;\\n  width: 350px;\\n  font-size: 14px;\\n}\\n\\n\\n.custom-arrow[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 33px;\\n  padding: 5px;\\n  font-size: 14px;\\n  color: #495057;\\n  background-color: #fff;\\n  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),\\n    linear-gradient(135deg, #ff7a00 50%, transparent 50%);\\n  background-position: calc(100% - 20px) center, calc(100% - 15px) center;\\n  background-size: 5px 5px, 5px 5px;\\n  background-repeat: no-repeat;\\n  border: 1px solid #ced4da;\\n  border-radius: 4px;\\n  appearance: none;\\n  box-sizing: border-box;\\n}\\n\\n.custom-arrow[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\n.custom-arrow[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:invalid {\\n  color: #6c757d;\\n}\\n\\n\\n\\n\\nselect[_ngcontent-%COMP%]::-ms-expand {\\n  display: none;\\n}\\n\\n\\n\\n.custom-arrow[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n\\n\\n\\n\\n.header[_ngcontent-%COMP%] {\\n  height: 8%;\\n  background-color: #ff6600;\\n  color: #fff;\\n  padding: 1px;\\n  text-align: center;\\n  width: 100%;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 0px;\\n  width: 100%;\\n  max-width: 700px;\\n  box-sizing: border-box;\\n  flex-grow: 1;\\n}\\n\\n\\n\\n.navigation-bar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 40px;\\n  position: relative;\\n  margin-top: 20px;\\n  background-color: #fff3e3;\\n  padding-block: 16px;\\n  border-radius: 10px;\\n}\\n\\n\\n\\n.navigation-step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  flex: 1;\\n  text-align: center;\\n}\\n\\n.navigation-step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  width: 15px;\\n  height: 15px;\\n  background-color: #ffffff;\\n  border: 1px;\\n  border-style: solid;\\n  border-color: #333;\\n  border-radius: 50%;\\n  margin-bottom: 5px;\\n  transition: background-color 0.3s;\\n}\\n\\n.navigation-step.active[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  background-color: #ff6600;\\n  border-color: #ff6600;\\n}\\n\\n.navigation-step.active[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #ff6600;\\n}\\n\\n\\n\\n.table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  border: 1px solid #ddd;\\n  cursor: pointer;\\n}\\n\\n.table[_ngcontent-%COMP%]   .selected-clinic[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n}\\n\\n\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #ff6600;\\n  cursor: pointer;\\n}\\n\\n.text-end[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  width: 100%;\\n  margin-bottom: 2px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  width: 40%;\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 5px;\\n  color: #333;\\n  font-weight: 600;\\n}\\n\\n.btn-field[_ngcontent-%COMP%] {\\n  margin-top: 0%;\\n}\\n\\n.first-next[_ngcontent-%COMP%] {\\n  margin-left: 80%;\\n  width: 20%;\\n}\\n\\n.second-next[_ngcontent-%COMP%] {\\n  width: 50%;\\n}\\n\\n\\n\\n.form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: 16px;\\n  border: none;\\n  border-radius: 5px;\\n}\\n\\n\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #ff6600;\\n  border: 2px solid #ff6600;\\n  border-radius: 25px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #FB751E, #5f1108);\\n  color: #fff;\\n  border: none;\\n  border-radius: 25px;\\n}\\n\\n.header1[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-weight: 700;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .form-group[_ngcontent-%COMP%] {\\n    width: 80%;\\n  }\\n\\n  .appointment-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 1000px;\\n    margin: auto;\\n    background-color: #fff;\\n    border: 1px solid #ff6600;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    align-items: center;\\n    height: 1050px;\\n    box-sizing: border-box;\\n  }\\n}\\n\\n@media (min-width: 768px) {\\n  .form-group[_ngcontent-%COMP%] {\\n    width: 80%;\\n  }\\n\\n  .appointment-container[_ngcontent-%COMP%] {\\n    width: 90%;\\n    max-width: 1000px;\\n    margin: auto;\\n    background-color: #fff;\\n    border: 1px solid #ff6600;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    align-items: center;\\n    height: 900px;\\n    box-sizing: border-box;\\n\\n  }\\n\\n}\\n\\n@media (min-width: 1024px) {\\n  .form-group[_ngcontent-%COMP%] {\\n    width: 80%;\\n  }\\n\\n  .appointment-container[_ngcontent-%COMP%] {\\n    width: 60%;\\n    max-width: 1000px;\\n    margin: auto;\\n    background-color: #fff;\\n    border: 1px solid #ff6600;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    align-items: center;\\n    height: 850px;\\n    box-sizing: border-box;\\n    \\n\\n  }\\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { AppointmentComponent };", "map": {"version": 3, "names": ["Validators", "map", "of", "SignUpDto", "User", "UserCategory", "Appointments", "Customer", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "currentStep", "i_r6", "ɵɵadvance", "ɵɵtextInterpolate", "step_r5", "ɵɵtemplate", "AppointmentComponent_div_13_div_6_div_1_Template", "tmp_0_0", "ctx_r7", "appointmentForm", "get", "errors", "AppointmentComponent_div_13_div_11_div_1_Template", "ctx_r8", "AppointmentComponent_div_13_div_17_div_1_Template", "AppointmentComponent_div_13_div_17_div_2_Template", "ctx_r9", "tmp_1_0", "ctx_r24", "userEmailExistsMessage", "AppointmentComponent_div_13_div_22_div_1_Template", "AppointmentComponent_div_13_div_22_div_2_Template", "AppointmentComponent_div_13_div_22_div_3_Template", "ctx_r10", "tmp_2_0", "district_r25", "ɵɵtextInterpolate1", "AppointmentComponent_div_13_div_32_small_1_Template", "ctx_r12", "city_r27", "AppointmentComponent_div_13_div_41_small_1_Template", "ctx_r14", "AppointmentComponent_div_13_div_47_div_1_Template", "AppointmentComponent_div_13_div_47_div_2_Template", "ctx_r15", "ctx_r32", "<PERSON><PERSON><PERSON><PERSON>", "AppointmentComponent_div_13_div_56_div_1_Template", "AppointmentComponent_div_13_div_56_div_2_Template", "AppointmentComponent_div_13_div_56_div_3_Template", "ctx_r16", "AppointmentComponent_div_13_div_61_div_1_Template", "AppointmentComponent_div_13_div_61_div_2_Template", "ctx_r17", "dirty", "AppointmentComponent_div_13_div_6_Template", "AppointmentComponent_div_13_div_11_Template", "AppointmentComponent_div_13_div_17_Template", "AppointmentComponent_div_13_div_22_Template", "ɵɵlistener", "AppointmentComponent_div_13_Template_select_ngModelChange_28_listener", "$event", "ɵɵrestoreView", "_r37", "ctx_r36", "ɵɵnextContext", "ɵɵresetView", "appointments", "state", "AppointmentComponent_div_13_Template_select_change_28_listener", "ctx_r38", "onDistrictChange", "AppointmentComponent_div_13_option_31_Template", "AppointmentComponent_div_13_div_32_Template", "AppointmentComponent_div_13_Template_select_ngModelChange_37_listener", "ctx_r39", "selectedCity", "AppointmentComponent_div_13_Template_select_change_37_listener", "ctx_r40", "onCitySelect", "AppointmentComponent_div_13_option_40_Template", "AppointmentComponent_div_13_div_41_Template", "AppointmentComponent_div_13_div_47_Template", "AppointmentComponent_div_13_Template_button_click_54_listener", "ctx_r41", "togglePasswordVisibility", "AppointmentComponent_div_13_div_56_Template", "AppointmentComponent_div_13_div_61_Template", "AppointmentComponent_div_13_Template_button_click_63_listener", "ctx_r42", "nextStep", "ctx_r1", "invalid", "touched", "tmp_3_0", "districts", "tmp_6_0", "cities", "tmp_9_0", "tmp_10_0", "ɵɵpropertyInterpolate", "passwordVisible", "_c1", "tmp_12_0", "tmp_14_0", "tmp_15_0", "isStepInvalid", "city_r48", "AppointmentComponent_div_14_div_29_small_1_Template", "ctx_r44", "AppointmentComponent_div_14_div_36_div_1_Template", "AppointmentComponent_div_14_div_36_div_2_Template", "ctx_r45", "AppointmentComponent_div_14_div_45_div_1_Template", "ctx_r46", "AppointmentComponent_div_14_div_50_div_1_Template", "ctx_r47", "AppointmentComponent_div_14_option_28_Template", "AppointmentComponent_div_14_div_29_Template", "AppointmentComponent_div_14_div_36_Template", "AppointmentComponent_div_14_div_45_Template", "AppointmentComponent_div_14_div_50_Template", "AppointmentComponent_div_14_Template_button_click_58_listener", "_r55", "ctx_r54", "prevStep", "AppointmentComponent_div_14_Template_button_click_60_listener", "ctx_r56", "cliniclistload", "ctx_r2", "currentDate", "isImmediateBooking", "tmp_5_0", "tmp_7_0", "AppointmentComponent_div_15_tr_6_Template_tr_click_0_listener", "restoredCtx", "_r62", "clinic_r58", "$implicit", "ctx_r61", "selectClinic", "clinicId", "AppointmentComponent_div_15_tr_6_span_6_Template", "AppointmentComponent_div_15_tr_6_span_7_Template", "_c2", "ctx_r57", "selectedClinic", "name", "city", "AppointmentComponent_div_15_tr_6_Template", "AppointmentComponent_div_15_Template_button_click_8_listener", "_r64", "ctx_r63", "ctx_r3", "clinics", "AppointmentComponent_div_16_Template_button_click_11_listener", "_r66", "ctx_r65", "cancelAppointment", "AppointmentComponent_div_16_Template_button_click_13_listener", "ctx_r67", "saveAppointment", "AppointmentComponent", "getCurrentDate", "today", "Date", "dd", "String", "getDate", "padStart", "mm", "getMonth", "yyyy", "getFullYear", "constructor", "fb", "appointmentsService", "userService", "sharedService", "customer", "user", "signUpDto", "userCategory", "steps", "isEmailRegistered", "nearestCity", "defaultDate", "toISOString", "substr", "substring", "group", "firstName", "required", "lastName", "address", "<PERSON><PERSON><PERSON><PERSON>", "value", "disabled", "district", "telephone", "pattern", "email", "checkUserEmail", "bind", "password", "confirmPassword", "preferredservice", "fromDate", "toDate", "fromTime", "toTime", "immediate", "validators", "passwordMatchValidator", "valueChanges", "subscribe", "onImmediateBookingChange", "isImmediate", "currentTime", "toTimeString", "patchValue", "futureDateValidator", "control", "selectedDate", "futureDate", "formGroup", "setErrors", "mismatch", "userEmail", "checkUser", "pipe", "data", "emailExists", "ngOnInit", "localStorage", "clear", "getUserCategoryById", "response", "getDistricts", "event", "selectedDistrict", "target", "enable", "getCitiesByDistrict", "disable", "setValue", "controls", "registerUser", "length", "date", "dayOfWeek", "getDay", "days", "day<PERSON><PERSON>", "console", "log", "findClinics", "alert", "undefined", "error", "saveCustomerDetails", "formValues", "customerId", "country", "saveCustomer", "next", "updateEmail", "username", "resetForm", "reset", "completeAppointment", "onSubmit", "appointmentId", "userName", "status", "saveCustomerAppointments", "userCategoryId", "register", "id", "userId", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AppointmentsService", "i3", "UserService", "i4", "SharedService", "_2", "selectors", "decls", "vars", "consts", "template", "AppointmentComponent_Template", "rf", "ctx", "AppointmentComponent_div_11_Template", "AppointmentComponent_Template_form_ngSubmit_12_listener", "AppointmentComponent_div_13_Template", "AppointmentComponent_div_14_Template", "AppointmentComponent_div_15_Template", "AppointmentComponent_div_16_Template"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\create-appoinment\\appointment.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\create-appoinment\\appointment.component.html"], "sourcesContent": ["import {\r\n  AbstractControl,\r\n  FormBuilder,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { Observable, map, mapTo, of, switchMap, tap } from 'rxjs'; // Import Observable\r\nimport { SignUpDto, User, UserCategory } from '../../../user/user';\r\n\r\nimport { Appointments } from '../appointments';\r\nimport { AppointmentsService } from '../appointments.service';\r\nimport { Customer } from './../customer';\r\nimport { SharedService } from '../../shared-services/shared.service';\r\nimport { UserService } from '../../../user/user.service';\r\n\r\ninterface Clinici {\r\n  clinicId: number;\r\n  name: string;\r\n  city: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-appointment',\r\n  templateUrl: './appointment.component.html',\r\n  styleUrls: ['./appointment.component.css'],\r\n})\r\nexport class AppointmentComponent implements OnInit {\r\n  appointments: Appointments = new Appointments();\r\n  customer: Customer = new Customer();\r\n  user: User = new User();\r\n  signUpDto: SignUpDto = new SignUpDto();\r\n  userCategory: UserCategory = new UserCategory();\r\n  appointmentForm: FormGroup;\r\n  currentStep: number = 1;\r\n  steps: string[] = [\r\n    'Personal Info',\r\n    'Service Details',\r\n    'Clinic Selection',\r\n    'Confirmation',\r\n  ];\r\n  currentDate: string = this.getCurrentDate();\r\n\r\n  userEmailExistsMessage: string = ''; // Ensure this is defined\r\n  isEmailRegistered: boolean = false;\r\n  isImmediateBooking: boolean = false;\r\n\r\n  selectedCity: string = '';\r\n  nearestCity: string = '';\r\n\r\n  districts: string[] = [];\r\n  cities: String[] = [];\r\n\r\n  clinics: Clinici[] = [];\r\n  selectedClinic: any | undefined;\r\n  defaultDate: string = new Date().toISOString().substr(0, 10);\r\n\r\n  passwordVisible: boolean = false; // Declare passwordVisible as false initially\r\n\r\n  private getCurrentDate(): string {\r\n    const today = new Date();\r\n    const dd = String(today.getDate()).padStart(2, '0');\r\n    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!\r\n    const yyyy = today.getFullYear();\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private appointmentsService: AppointmentsService,\r\n    private userService: UserService,\r\n    private sharedService: SharedService\r\n  ) {\r\n    // Set default date to today\r\n    const today = new Date();\r\n    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD\r\n\r\n    // Initialize form\r\n    this.appointmentForm = this.fb.group(\r\n      {\r\n        firstName: ['', Validators.required],\r\n        lastName: ['', Validators.required],\r\n        address: ['', [Validators.required, Validators.minLength(10)]],\r\n        city: [\r\n          { value: '', disabled: true },\r\n          [Validators.required, Validators.minLength(3)],\r\n        ],\r\n        district: ['', [Validators.required, Validators.minLength(3)]],\r\n        telephone: [\r\n          '',\r\n          [Validators.required, Validators.pattern(/^\\+?[0-9]{7,15}$/)],\r\n        ],\r\n        email: [\r\n          '',\r\n          [Validators.required, Validators.email],\r\n          [this.checkUserEmail.bind(this)],\r\n        ],\r\n        password: [\r\n          '',\r\n          [\r\n            Validators.required,\r\n            Validators.minLength(8),\r\n            Validators.pattern('(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9]).*'),\r\n          ],\r\n        ],\r\n        confirmPassword: ['', Validators.required],\r\n        preferredservice: [''],\r\n        nearestCity: [''],\r\n        fromDate: [\r\n          { value: this.defaultDate, disabled: false },\r\n          Validators.required,\r\n        ],\r\n        toDate: [\r\n          { value: this.defaultDate, disabled: false },\r\n          Validators.required,\r\n        ],\r\n        fromTime: ['', Validators.required],\r\n        toTime: ['', Validators.required],\r\n        immediate: [false],\r\n      },\r\n      { validators: this.passwordMatchValidator }\r\n    ); // Note: 'validators' is plural here\r\n\r\n    // Subscribe to changes in the immediate checkbox\r\n    this.appointmentForm.get('immediate')?.valueChanges.subscribe((value) => {\r\n      this.onImmediateBookingChange(value);\r\n    });\r\n  }\r\n  // Toggle password visibility\r\n  togglePasswordVisibility() {\r\n    this.passwordVisible = !this.passwordVisible;\r\n  }\r\n\r\n  // Accept the value from the checkbox\r\n  onImmediateBookingChange(isImmediate: boolean): void {\r\n    if (isImmediate) {\r\n      // Get the current date and time\r\n      const currentDate = new Date().toISOString().substring(0, 10); // Format as YYYY-MM-DD\r\n      const currentTime = new Date().toTimeString().substring(0, 5); // Format as HH:MM\r\n\r\n      // Update the form values for immediate booking\r\n      this.appointmentForm.patchValue({\r\n        fromDate: currentDate,\r\n        toDate: currentDate, // Setting to the same date for immediate booking\r\n        fromTime: currentTime,\r\n        toTime: '23:59', // Set the end time to the end of the day\r\n      });\r\n    } else {\r\n      // Optionally clear values or handle logic for unchecking\r\n      this.appointmentForm.patchValue({\r\n        fromDate: '',\r\n        toDate: '',\r\n        fromTime: '',\r\n        toTime: '',\r\n      });\r\n    }\r\n  }\r\n\r\n  // Custom validator to ensure date is today or in the future\r\n  private futureDateValidator(\r\n    control: AbstractControl\r\n  ): { [key: string]: any } | null {\r\n    const selectedDate = new Date(control.value);\r\n    const today = new Date(this.defaultDate);\r\n\r\n    return selectedDate < today ? { futureDate: true } : null;\r\n  }\r\n\r\n  passwordMatchValidator(formGroup: FormGroup) {\r\n    const password = formGroup.get('password');\r\n    const confirmPassword = formGroup.get('confirmPassword');\r\n\r\n    if (!password || !confirmPassword) {\r\n      return null;\r\n    }\r\n\r\n    if (confirmPassword.errors && !confirmPassword.errors['mismatch']) {\r\n      return null;\r\n    }\r\n\r\n    if (password.value !== confirmPassword.value) {\r\n      confirmPassword.setErrors({ mismatch: true });\r\n    } else {\r\n      confirmPassword.setErrors(null); // Clear the mismatch error if they match\r\n    }\r\n\r\n    return null; // Return null for no errors\r\n  }\r\n  checkUserEmail(control: any): Observable<{ [key: string]: any } | null> {\r\n    const userEmail = control.value;\r\n\r\n    if (userEmail) {\r\n      return this.userService.checkUser(userEmail).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isEmailRegistered = true;\r\n            this.userEmailExistsMessage =\r\n              'Email already registered. Try another.';\r\n            return { emailExists: true }; // Return an error object\r\n          } else {\r\n            this.isEmailRegistered = false;\r\n            this.userEmailExistsMessage = '';\r\n            return null; // Return null if no error\r\n          }\r\n        })\r\n      );\r\n    } else {\r\n      return of(null); // Return null if the control value is empty\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    localStorage.clear();\r\n    this.userService\r\n      .getUserCategoryById(5)\r\n      .subscribe((response: UserCategory) => {\r\n        this.userCategory = response;\r\n      });\r\n    this.districts = this.sharedService.getDistricts();\r\n  }\r\n\r\n  onDistrictChange(event: Event): void {\r\n    const selectedDistrict = (event.target as HTMLSelectElement).value;\r\n\r\n    if (selectedDistrict) {\r\n      this.appointmentForm.get('city')?.enable();\r\n      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\r\n    } else {\r\n      this.appointmentForm.get('city')?.disable();\r\n      this.cities = [];\r\n    }\r\n    this.appointmentForm.get('city')?.setValue('');\r\n  }\r\n\r\n  onCitySelect(): void {\r\n    this.nearestCity = this.selectedCity;\r\n    this.appointmentForm.controls['nearestCity'].setValue(this.selectedCity);\r\n  }\r\n\r\n  nextStep(): void {\r\n    if (this.currentStep === 1) {\r\n      this.registerUser();\r\n    }\r\n\r\n    if (this.currentStep < this.steps.length) {\r\n      this.currentStep++;\r\n    }\r\n  }\r\n  cliniclistload(): void {\r\n    const fromDate = this.appointmentForm.get('fromDate')?.value;\r\n\r\n    // Convert string to Date object\r\n    const date = new Date(fromDate);\r\n\r\n    // Get the day of the week (0 is Sunday, 1 is Monday, ..., 6 is Saturday)\r\n    const dayOfWeek = date.getDay();\r\n\r\n    // Array to convert day index to name\r\n    const days = [\r\n      'Sunday',\r\n      'Monday',\r\n      'Tuesday',\r\n      'Wednesday',\r\n      'Thursday',\r\n      'Friday',\r\n      'Saturday',\r\n    ];\r\n    const dayName = days[dayOfWeek]; // Get the day name like 'Monday'\r\n\r\n    console.log('Selected Day:', dayName); // Log the selected day for debugging\r\n\r\n    // Retrieve other form values\r\n    const fromTime = this.appointmentForm.get('fromTime')?.value;\r\n    const nearestCity = this.appointmentForm.get('city')?.value;\r\n    const preferredservice =\r\n      this.appointmentForm.get('preferredservice')?.value;\r\n\r\n    // API call to find clinics\r\n    this.appointmentsService\r\n      .findClinics(dayName, fromTime, nearestCity, preferredservice)\r\n      .subscribe(\r\n        (response) => {\r\n          console.log('Selected Nearest response:', response); // Log nearest city for debugging\r\n\r\n          this.clinics = response; // Assign response to clinics array\r\n          console.log('Clinics found:', this.clinics); // Log the response for debugging\r\n          // Check if clinics are found\r\n          if (this.clinics.length === 0) {\r\n            // Alert if no clinics are loaded\r\n            alert(\r\n              'No clinics found in ' +\r\n                nearestCity +\r\n                '. Please try another nearest city.'\r\n            );\r\n            this.selectedClinic = undefined; // Reset selected clinic\r\n\r\n            // Set current step to 2 after alert acknowledgment\r\n            this.currentStep = 2; // Update current step to 2\r\n\r\n            return; // Exit the method to prevent further processing\r\n          } else {\r\n            console.log('Clinics loaded:', this.clinics); // Log clinics data\r\n            this.selectedClinic = undefined; // Reset selected clinic when new data is fetched\r\n          }\r\n        },\r\n        (error) => {\r\n          // Handle error if the API call fails\r\n          console.error('Error loading clinics:', error);\r\n          alert(\r\n            'An error occurred while loading clinics. Please try again later.'\r\n          );\r\n        }\r\n      );\r\n  }\r\n\r\n  isStepInvalid(): boolean {\r\n    // Step 1 validation\r\n    if (this.currentStep === 1) {\r\n      return (\r\n        !!this.appointmentForm.get('firstName')?.invalid ||\r\n        !!this.appointmentForm.get('lastName')?.invalid ||\r\n        !!this.appointmentForm.get('address')?.invalid ||\r\n        !!this.appointmentForm.get('email')?.invalid ||\r\n        !!this.appointmentForm.get('district')?.invalid ||\r\n        !!this.appointmentForm.get('city')?.invalid ||\r\n        !!this.appointmentForm.get('telephone')?.invalid ||\r\n        !!this.appointmentForm.get('password')?.invalid ||\r\n        !!this.appointmentForm.get('confirmPassword')?.invalid\r\n      );\r\n    }\r\n\r\n    // Step 2 validation\r\n    if (this.currentStep === 2) {\r\n      return (\r\n        !!this.appointmentForm.get('preferredservice')?.invalid ||\r\n        !!this.appointmentForm.get('nearestCity')?.invalid ||\r\n        !!this.appointmentForm.get('fromDate')?.invalid ||\r\n        !!this.appointmentForm.get('fromTime')?.invalid ||\r\n        !!this.appointmentForm.get('toTime')?.invalid\r\n      );\r\n    }\r\n\r\n    // Add checks for other steps if needed\r\n    return false;\r\n  }\r\n\r\n  prevStep(): void {\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    }\r\n  }\r\n\r\n  saveCustomerDetails() {\r\n    const formValues = this.appointmentForm.value;\r\n\r\n    this.appointments = {\r\n      ...this.appointments,\r\n      email: formValues.email,\r\n    };\r\n\r\n    // Create the customer object\r\n    this.customer = {\r\n      customerId: 0,\r\n      user: this.customer.user,\r\n      firstName: formValues.firstName,\r\n      lastName: formValues.lastName,\r\n      address: formValues.address,\r\n      city: formValues.city,\r\n      state: formValues.state,\r\n      country: '',\r\n      telephone: formValues.telephone,\r\n      email: this.appointments.email,\r\n    };\r\n\r\n    // Call the service to save the customer\r\n    this.appointmentsService.saveCustomer(this.customer).subscribe({\r\n      next: (response) => {\r\n        if (response.customerId) {\r\n          this.appointments.customer.customerId = response.customerId;\r\n          console.log(\r\n            ' Customer registered successfully:',\r\n            this.appointments.customer.customerId\r\n          );\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(' Error registering Customer:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  updateEmail(): void {\r\n    this.user.username = this.appointments.email;\r\n  }\r\n\r\n  selectClinic(clinicId: number): void {\r\n    this.selectedClinic = clinicId; // Update selected clinic on click\r\n    console.log('Clinic selected:', clinicId); // Log the clinic that was selected\r\n    console.log('Current selectedClinic value:', this.selectedClinic); // Log the updated selectedClinic value\r\n  }\r\n\r\n  resetForm(): void {\r\n    this.appointmentForm.reset();\r\n    this.currentStep = 1;\r\n    this.selectedClinic = undefined;\r\n  }\r\n\r\n  cancelAppointment(): void {\r\n    console.log('Appointment canceled.');\r\n    this.resetForm();\r\n  }\r\n\r\n  completeAppointment(): void {\r\n    console.log('Appointment completed.');\r\n    this.resetForm();\r\n  }\r\n\r\n  onSubmit(): void {\r\n    this.saveAppointment();\r\n    this.currentStep = 4;\r\n  }\r\n\r\n  // Save Appointment Method\r\n  saveAppointment(): void {\r\n    // Call saveCustomerDetails() and handle its result\r\n    // this.saveCustomerDetails().pipe(\r\n    //     switchMap((customer: Customer) => {\r\n    //         // Check if the customer is saved and populated\r\n    //         if (customer && customer.customerId) {\r\n    //             // Populate the appointments with the saved customer details\r\n    //             const formValues = this.appointmentForm.value;\r\n    //             this.appointments = {\r\n    //                 ...this.appointments,\r\n    //                 firstName: formValues.firstName,\r\n    //                 lastName: formValues.lastName,\r\n    //                 address: formValues.address,\r\n    //                 city: formValues.city,\r\n    //                 state: formValues.state,\r\n    //                 district: formValues.district,\r\n    //                 telephone: formValues.telephone,\r\n    //                 email: formValues.email,\r\n    //                 userName: formValues.username,\r\n    //                 password: formValues.password,\r\n    //                 preferredservice: formValues.preferredservice,\r\n    //                 nearestCity: formValues.nearestCity,\r\n    //                 fromDate: formValues.fromDate,\r\n    //                 toDate: formValues.toDate,\r\n    //                 fromTime: formValues.fromTime,\r\n    //                 toTime: formValues.toTime,\r\n    //                 clinics: new Clinic(),\r\n    //                 customer: customer // Use the saved customer directly\r\n    //             };\r\n    //             this.appointments.clinics.clinicId = this.selectedClinic;\r\n    //             // Call the service to save the appointment\r\n    //             return this.appointmentsService.saveAppointments(this.appointments);\r\n    //         } else {\r\n    //             console.error('Customer is not populated correctly');\r\n    //             return EMPTY; // Return an empty observable to end the chain\r\n    //         }\r\n    //     })\r\n    // ).subscribe(\r\n    //     (response: Appointments) => {\r\n    //         console.log('Appointment saved successfully', response);\r\n    //     },\r\n    //     (error: any) => {\r\n    //         console.error('Error saving appointment', error);\r\n    //     }\r\n    // );\r\n    this.appointments.clinics.clinicId = this.selectedClinic;\r\n    const formValues = this.appointmentForm.value;\r\n    this.appointments = {\r\n      appointmentId: 0,\r\n      firstName: formValues.firstName,\r\n      lastName: formValues.lastName,\r\n      address: formValues.address,\r\n      city: formValues.city,\r\n      state: formValues.state,\r\n      district: formValues.district,\r\n      telephone: formValues.telephone,\r\n      email: formValues.email,\r\n      userName: formValues.username,\r\n      password: formValues.password,\r\n      preferredservice: formValues.preferredservice,\r\n      nearestCity: formValues.nearestCity,\r\n      fromDate: formValues.fromDate,\r\n      toDate: formValues.toDate,\r\n      fromTime: this.appointmentForm.value.fromTime,\r\n      toTime: this.appointmentForm.value.toTime,\r\n      customer: this.appointments.customer,\r\n      clinics: this.appointments.clinics,\r\n      status: 'Pending'\r\n    };\r\n\r\n    // Log the full object\r\n    console.log('Appointments object:', this.appointments);\r\n\r\n    this.appointmentsService\r\n      .saveCustomerAppointments(this.appointments)\r\n      .subscribe({\r\n        next: (response) => {\r\n          console.log(response);\r\n        },\r\n        error: (error) => {\r\n          console.error(' customer appoinment error', error);\r\n        },\r\n      });\r\n  }\r\n\r\n  //   onUserRegister(): Observable<number> {\r\n  //     this.signUpDto.userCategoryId = this.userCategory;\r\n  //     this.signUpDto.firstName = this.appointmentForm.get('firstName')?.value;\r\n  // this.signUpDto.lastName = this.appointmentForm.get('lastName')?.value;\r\n  // this.signUpDto.username = this.user.username;\r\n  //     this.signUpDto.password = String(this.appointmentForm.get('password')?.value);\r\n  //     console.log('User Category set to:', this.signUpDto);\r\n  //     return this.userService.register(this.signUpDto).pipe(\r\n  //       tap((response: { id: number }) => {\r\n  //         this.user.userId = response.id;\r\n  //       }),\r\n  //       map((response: { id: number }) => response.id)\r\n  //     );\r\n  //   }\r\n\r\n  registerUser() {\r\n    this.updateEmail();\r\n    this.signUpDto.userCategoryId = this.userCategory;\r\n    this.signUpDto.firstName = this.appointmentForm.get('firstName')?.value;\r\n    this.signUpDto.lastName = this.appointmentForm.get('lastName')?.value;\r\n    this.signUpDto.username = this.appointmentForm.get('email')?.value;\r\n    this.signUpDto.password = this.appointmentForm.get('password')?.value;\r\n\r\n    console.log('Registering user:', this.appointments.email);\r\n\r\n    this.userService.register(this.signUpDto).subscribe({\r\n      next: (response) => {\r\n        if (response.id) {\r\n          this.customer.user.userId = response.id;\r\n          this.saveCustomerDetails();\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error(' Error registering user:', error);\r\n      },\r\n    });\r\n  }\r\n}\r\n", "<app-default-navbar loggedUser=\"Test Appointment\"/>\r\n\r\n<div style=\"padding-bottom: 50px;\">\r\n  <div class=\"appointment-container\">\r\n    <div class=\"header py-1\" style=\"display: flex; justify-content: center; align-items: center; width: 100%;\">\r\n      <span style=\"text-align: center;\">MyDent Dental Consultations With Certified Professionals! Book Your\r\n        Appointment Now!</span>\r\n    </div>\r\n\r\n    <div class=\"content py-1\">\r\n      <div class=\"navigation mb-4\">\r\n        <h2 class=\"mb-3 header1\">Make Appointment</h2>\r\n        <div class=\"navigation-bar\">\r\n          <div class=\"navigation-step\" *ngFor=\"let step of steps; let i = index\"\r\n            [ngClass]=\"{'active': currentStep === i + 1}\">\r\n            <div class=\"dot\"></div>\r\n            <span>{{ step }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <form [formGroup]=\"appointmentForm\" (ngSubmit)=\"onSubmit()\" style=\"padding-inline: 20px;\">\r\n        <div *ngIf=\"currentStep === 1\">\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"firstName\" class=\"form-label\">First Name</label>\r\n              <input type=\"text\" formControlName=\"firstName\" id=\"firstName\" class=\"form-control\">\r\n              <div\r\n                *ngIf=\"appointmentForm.get('firstName')?.invalid && (appointmentForm.get('firstName')?.dirty || appointmentForm.get('firstName')?.touched)\"\r\n                class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('firstName')?.errors?.['required']\">First Name is required.\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <label for=\"lastName\" class=\"form-label\">Last Name</label>\r\n              <input type=\"text\" formControlName=\"lastName\" id=\"lastName\" class=\"form-control\">\r\n              <div\r\n                *ngIf=\"appointmentForm.get('lastName')?.invalid && (appointmentForm.get('lastName')?.dirty || appointmentForm.get('lastName')?.touched)\"\r\n                class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('lastName')?.errors?.['required']\">Last Name is required.\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"address\" class=\"form-label\">Address</label>\r\n              <input type=\"text\" formControlName=\"address\" id=\"address\" class=\"form-control\">\r\n              <div\r\n                *ngIf=\"appointmentForm.get('address')?.invalid && (appointmentForm.get('address')?.dirty || appointmentForm.get('address')?.touched)\"\r\n                class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('address')?.errors?.['required']\">Address is required.</div>\r\n                <div *ngIf=\"appointmentForm.get('address')?.errors?.['minlength']\">Address must be at least\r\n                  10 characters long.</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <label for=\"email\" class=\"form-label\">Email</label>\r\n              <input type=\"email\" formControlName=\"email\" id=\"email\" class=\"form-control\">\r\n              <div\r\n                *ngIf=\"appointmentForm.get('email')?.invalid && (appointmentForm.get('email')?.dirty || appointmentForm.get('email')?.touched)\"\r\n                class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('email')?.errors?.['required']\">Email is required.</div>\r\n                <div *ngIf=\"appointmentForm.get('email')?.errors?.['email']\">Invalid email format.</div>\r\n                <div *ngIf=\"appointmentForm.get('email')?.errors?.['emailExists']\">{{ userEmailExistsMessage\r\n                  }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"district\" class=\"form-label\">District</label>\r\n              <div class=\"custom-arrow\">\r\n                <select name=\"district\" id=\"district\" formControlName=\"district\" [(ngModel)]=\"appointments.state\"\r\n                  (change)=\"onDistrictChange($event)\">\r\n                  <option value=\"\" disabled selected>Select</option>\r\n                  <option *ngFor=\"let district of districts\" [value]=\"district\">\r\n                    {{ district }}\r\n                  </option>\r\n                </select>\r\n              </div>\r\n              <div *ngIf=\"\r\n                            appointmentForm.get('district')?.invalid &&\r\n                            (appointmentForm.get('district')?.dirty || appointmentForm.get('district')?.touched)\r\n                        \">\r\n                <small class=\"text-danger\" *ngIf=\"appointmentForm.get('district')?.errors?.['required']\">\r\n                  District is required.\r\n                </small>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <label for=\"city\" class=\"form-label\">City</label>\r\n              <div class=\"custom-arrow\">\r\n                <select id=\"city\" class=\"form-control\" formControlName=\"city\" [(ngModel)]=\"selectedCity\"\r\n                  (change)=\"onCitySelect()\">\r\n                  <option value=\"\" disabled selected>Select</option>\r\n                  <option *ngFor=\"let city of cities\" [value]=\"city\">\r\n                    {{ city }}\r\n                  </option>\r\n                </select>\r\n              </div>\r\n              <div *ngIf=\"\r\n                            appointmentForm.get('city')?.invalid &&\r\n                            (appointmentForm.get('city')?.dirty || appointmentForm.get('city')?.touched)\r\n                            \">\r\n                <small class=\"text-danger\" *ngIf=\"appointmentForm.get('city')?.errors?.['required']\">\r\n                  City is required.\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"telephone\" class=\"form-label\">Telephone</label>\r\n              <input type=\"tel\" formControlName=\"telephone\" id=\"telephone\" class=\"form-control\">\r\n              <div\r\n                *ngIf=\"appointmentForm.get('telephone')?.invalid && (appointmentForm.get('telephone')?.dirty || appointmentForm.get('telephone')?.touched)\"\r\n                class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('telephone')?.errors?.['required']\">Telephone is required.\r\n                </div>\r\n                <div *ngIf=\"appointmentForm.get('telephone')?.errors?.['pattern']\">Invalid telephone number\r\n                  format.</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"password\" class=\"form-label\">Password</label>\r\n              <div class=\"form-group position-relative\" style=\"position: relative;\">\r\n                <input\r\n                  type=\"{{ passwordVisible ? 'text' : 'password' }}\"\r\n                  style=\"width: 125%;\"\r\n                  formControlName=\"password\"\r\n                  id=\"password\"\r\n                  class=\"form-control\"\r\n                  [ngClass]=\"{'is-invalid': appointmentForm.get('password')?.invalid && (appointmentForm.get('password')?.dirty || appointmentForm.get('password')?.touched)}\">\r\n\r\n                <!-- Password visibility toggle button -->\r\n                <button\r\n                  type=\"button\"\r\n                  class=\"show-password\"\r\n                  (click)=\"togglePasswordVisibility()\"\r\n                  style=\"\r\n                  position: absolute;\r\n                  right: -35px;\r\n                  top: 50%;\r\n                  transform: translateY(-50%);\r\n                  padding: 0;\r\n                  border: none;\r\n                  background: none;\r\n                  color: #6c757d;\r\n                  cursor: pointer;\r\n                  \">\r\n                  <i [ngClass]=\"passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'\"></i>\r\n                </button>\r\n\r\n                <!-- Error messages for validation -->\r\n                <div *ngIf=\"appointmentForm.get('password')?.invalid && (appointmentForm.get('password')?.dirty || appointmentForm.get('password')?.touched)\" class=\"text-danger warnings\">\r\n                  <div *ngIf=\"appointmentForm.get('password')?.errors?.['required']\">\r\n                    Password is required.\r\n                  </div>\r\n                  <div *ngIf=\"appointmentForm.get('password')?.errors?.['minlength']\">\r\n                    Password must be at least {{ appointmentForm.get('password')?.errors?.['minlength'].requiredLength }} characters long.\r\n                  </div>\r\n                  <div *ngIf=\"appointmentForm.get('password')?.errors?.['pattern']\">\r\n                    Password must include at least one uppercase letter, one lowercase letter, and one number.\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n\r\n            <div class=\"col-md-6\">\r\n              <label for=\"confirm-password\" class=\"form-label\">Confirm Password</label>\r\n              <input type=\"password\"\r\n                     formControlName=\"confirmPassword\"\r\n                     id=\"confirmPassword\"\r\n                     class=\"form-control\"\r\n                     placeholder=\"Re-enter your password\"/>\r\n              <div *ngIf=\"appointmentForm.get('confirmPassword')?.invalid &&\r\n                (appointmentForm.get('confirmPassword')?.dirty ||\r\n                 appointmentForm.get('confirmPassword')?.touched)\"\r\n                   class=\"text-danger\">\r\n                <div *ngIf=\"appointmentForm.get('confirmPassword')?.errors?.['required']\">\r\n                  Re-enter the password.\r\n                </div>\r\n                <div *ngIf=\"appointmentForm.get('confirmPassword')?.errors?.['mismatch'] &&\r\n                  appointmentForm.get('confirmPassword')?.dirty\">\r\n                  Passwords do not match.\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"d-flex justify-content-between btn-field\">\r\n            <!-- <button type=\"button\" class=\"btn btn-secondary\" [disabled]=\"currentStep === 1\" (click)=\"prevStep()\">Previous</button> -->\r\n            <!-- Use (click) event to trigger nextStep() -->\r\n            <button type=\"button\" class=\"btn btn-primary first-next\" (click)=\"nextStep()\"\r\n              [disabled]=\"isStepInvalid()\">Next</button>\r\n          </div>\r\n        </div>\r\n        <div *ngIf=\"currentStep === 2\">\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <label for=\"preferredservice\" class=\"form-label\">Preferred Service</label>\r\n              <div class=\"custom-arrow\">\r\n                <select formControlName=\"preferredservice\" id=\"preferredservice\" class=\"form-control\" required>\r\n                  <option value=\"\" selected disabled>Select</option>\r\n                  <option value=\"1\">Dental Bonding</option>\r\n                  <option value=\"2\">Cosmetic Fillings</option>\r\n                  <option value=\"3\">Invisalign</option>\r\n                  <option value=\"4\">Teeth Cleanings</option>\r\n                  <option value=\"5\">Root Canal Therapy</option>\r\n                  <option value=\"6\">Dental Sealants</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <label for=\"city\" class=\"form-label\">City</label>\r\n              <div class=\"custom-arrow\">\r\n                <select id=\"nearestCity\" class=\"form-control\" formControlName=\"city\">\r\n                  <option value=\"\" disabled selected>Select</option>\r\n                  <option *ngFor=\"let city of cities\" [value]=\"city\">\r\n                    {{ city }}\r\n                  </option>\r\n                </select>\r\n              </div>\r\n              <div *ngIf=\"\r\n                            appointmentForm.get('city')?.invalid &&\r\n                            (appointmentForm.get('city')?.dirty || appointmentForm.get('city')?.touched)\r\n                            \">\r\n                <small class=\"text-danger\" *ngIf=\"appointmentForm.get('city')?.errors?.['required']\">\r\n                  City is required.\r\n                </small>\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n\r\n          <div class=\"row mb-3\">\r\n            <label for=\"preferredDate\" class=\"form-label\">Preferred Date</label>\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <input type=\"date\" formControlName=\"fromDate\" id=\"fromDate\" class=\"form-control\" [min]=\"currentDate\"\r\n                  required>\r\n                <div\r\n                  *ngIf=\"appointmentForm.get('fromDate')?.invalid && (appointmentForm.get('fromDate')?.dirty || appointmentForm.get('fromDate')?.touched)\"\r\n                  class=\"text-danger\">\r\n                  <div *ngIf=\"appointmentForm.get('fromDate')?.errors?.['required']\">Date From is\r\n                    required.</div>\r\n                  <div *ngIf=\"appointmentForm.get('fromDate')?.errors?.['pastDate']\">Date From cannot be\r\n                    in the past.</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"row mb-3\">\r\n            <label for=\"preferredTime\" class=\"form-label\">Preferred Time</label>\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6\">\r\n                <label for=\"fromTime\" class=\"form-label\">From</label>\r\n                <input type=\"time\" formControlName=\"fromTime\" id=\"fromTime\" class=\"form-control\"\r\n                  [disabled]=\"isImmediateBooking\" required>\r\n                <div\r\n                  *ngIf=\"appointmentForm.get('fromTime')?.invalid && (appointmentForm.get('fromTime')?.dirty || appointmentForm.get('fromTime')?.touched)\"\r\n                  class=\"text-danger\">\r\n                  <div *ngIf=\"appointmentForm.get('fromTime')?.errors?.['required']\">Preferred Time From\r\n                    is required.</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-md-6\">\r\n                <label for=\"toTime\" class=\"form-label\">To</label>\r\n                <input type=\"time\" formControlName=\"toTime\" id=\"toTime\" class=\"form-control\"\r\n                  [disabled]=\"isImmediateBooking\" required>\r\n                <div\r\n                  *ngIf=\"appointmentForm.get('toTime')?.invalid && (appointmentForm.get('toTime')?.dirty || appointmentForm.get('toTime')?.touched)\"\r\n                  class=\"text-danger\">\r\n                  <div *ngIf=\"appointmentForm.get('toTime')?.errors?.['required']\">Preferred Time To is\r\n                    required.</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Immediate Booking Checkbox -->\r\n          <div class=\"row mb-3\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-check\">\r\n                <input type=\"checkbox\" formControlName=\"immediate\" id=\"immediate\" class=\"form-check-input\">\r\n                <label for=\"immediate\" class=\"form-check-label\">Immediate Booking</label>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"d-flex justify-content-between btn-field\">\r\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"prevStep()\">Previous</button>\r\n            <button type=\"button\" class=\"btn btn-primary second-next\" (click)=\"cliniclistload(); nextStep()\"\r\n              [disabled]=\"isStepInvalid()\">Find Your Nearest Dental Clinic</button>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep === 3\">\r\n          <h4 class=\"mb-4\">Please select nearest clinic:</h4>\r\n          <div class=\"clinic-list mb-4\">\r\n            <table class=\"table\">\r\n              <tbody>\r\n                <tr *ngFor=\"let clinic of clinics\" [ngClass]=\"{'selected-clinic': selectedClinic === clinic.clinicId}\"\r\n                  (click)=\"selectClinic(clinic.clinicId)\">\r\n                  <td>{{ clinic.name }}</td>\r\n                  <td>{{ clinic.city}}</td>\r\n                  <td class=\"text-end\">\r\n                    <span class=\"text-primary\" *ngIf=\"selectedClinic !== clinic.clinicId\">Book Now</span>\r\n                    <span class=\"badge bg-success\" *ngIf=\"selectedClinic === clinic.clinicId\">Selected</span>\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          <div class=\"d-flex justify-content-between\">\r\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"prevStep()\">Previous</button>\r\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!selectedClinic\">Request For\r\n              The Appointment</button>\r\n          </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep === 4\">\r\n          <div class=\"text-center\">\r\n            <h4 class=\"mb-4\">Appointment Submission</h4>\r\n            <div class=\"appointment-success mb-4\">\r\n              <i class=\"bi bi-check-circle-fill text-success display-4\"></i>\r\n            </div>\r\n            <p class=\"mb-4\">Your Appointment Submission is Sent to Approval Now. <br> Please Check Your\r\n              Notifications or Bookings.</p>\r\n            <div class=\"d-flex justify-content-center d-none\">\r\n              <button type=\"button\" class=\"btn btn-secondary me-3\" (click)=\"cancelAppointment()\">Cancel</button>\r\n              <button type=\"button\" class=\"btn btn-primary\" (click)=\"saveAppointment()\">Done</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,UAAU,QACL,gBAAgB;AAEvB,SAAqBC,GAAG,EAASC,EAAE,QAAwB,MAAM,CAAC,CAAC;AACnE,SAASC,SAAS,EAAEC,IAAI,EAAEC,YAAY,QAAQ,oBAAoB;AAElE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,QAAQ,QAAQ,eAAe;;;;;;;;;;;;;;;ICC9BC,EAAA,CAAAC,cAAA,cACgD;IAC9CD,EAAA,CAAAE,SAAA,cAAuB;IACvBF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,GAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAFvBJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,WAAA,KAAAC,IAAA,MAA6C;IAEvCV,EAAA,CAAAW,SAAA,GAAU;IAAVX,EAAA,CAAAY,iBAAA,CAAAC,OAAA,CAAU;;;;;IAaZb,EAAA,CAAAC,cAAA,UAAoE;IAAAD,EAAA,CAAAG,MAAA,+BACpE;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJRJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAC,gDAAA,kBACM;IACRf,EAAA,CAAAI,YAAA,EAAM;;;;;IAFEJ,EAAA,CAAAW,SAAA,GAA4D;IAA5DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAC,MAAA,CAAAC,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;;;;;IAUlEpB,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,8BACnE;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJRJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAO,iDAAA,kBACM;IACRrB,EAAA,CAAAI,YAAA,EAAM;;;;;IAFEJ,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAM,MAAA,CAAAJ,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IAYjEpB,EAAA,CAAAC,cAAA,UAAkE;IAAAD,EAAA,CAAAG,MAAA,2BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAC5FJ,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,mDAC9C;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAL7BJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAS,iDAAA,kBAA4F;IAC5FvB,EAAA,CAAAc,UAAA,IAAAU,iDAAA,kBAC2B;IAC7BxB,EAAA,CAAAI,YAAA,EAAM;;;;;;IAHEJ,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAS,MAAA,CAAAP,eAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA0D;IAC1DpB,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAqB,OAAA,GAAAD,MAAA,CAAAP,eAAA,CAAAC,GAAA,8BAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,cAA2D;;;;;IAUjEpB,EAAA,CAAAC,cAAA,UAAgE;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACxFJ,EAAA,CAAAC,cAAA,UAA6D;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACxFJ,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,GAC/D;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADyDJ,EAAA,CAAAW,SAAA,GAC/D;IAD+DX,EAAA,CAAAY,iBAAA,CAAAe,OAAA,CAAAC,sBAAA,CAC/D;;;;;IANN5B,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAe,iDAAA,kBAAwF;IACxF7B,EAAA,CAAAc,UAAA,IAAAgB,iDAAA,kBAAwF;IACxF9B,EAAA,CAAAc,UAAA,IAAAiB,iDAAA,kBACU;IACZ/B,EAAA,CAAAI,YAAA,EAAM;;;;;;;IAJEJ,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAgB,OAAA,CAAAd,eAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IACxDpB,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAK,UAAA,UAAAqB,OAAA,GAAAM,OAAA,CAAAd,eAAA,CAAAC,GAAA,4BAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,UAAqD;IACrDpB,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAA4B,OAAA,GAAAD,OAAA,CAAAd,eAAA,CAAAC,GAAA,4BAAAc,OAAA,CAAAb,MAAA,kBAAAa,OAAA,CAAAb,MAAA,gBAA2D;;;;;IAY/DpB,EAAA,CAAAC,cAAA,iBAA8D;IAC5DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAFkCJ,EAAA,CAAAK,UAAA,UAAA6B,YAAA,CAAkB;IAC3DlC,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmC,kBAAA,MAAAD,YAAA,MACF;;;;;IAOFlC,EAAA,CAAAC,cAAA,gBAAyF;IACvFD,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IANVJ,EAAA,CAAAC,cAAA,UAGY;IACVD,EAAA,CAAAc,UAAA,IAAAsB,mDAAA,oBAEQ;IACVpC,EAAA,CAAAI,YAAA,EAAM;;;;;IAHwBJ,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAqB,OAAA,CAAAnB,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IAWrFpB,EAAA,CAAAC,cAAA,iBAAmD;IACjDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAF2BJ,EAAA,CAAAK,UAAA,UAAAiC,QAAA,CAAc;IAChDtC,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmC,kBAAA,MAAAG,QAAA,MACF;;;;;IAOFtC,EAAA,CAAAC,cAAA,gBAAqF;IACnFD,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IANVJ,EAAA,CAAAC,cAAA,UAGgB;IACdD,EAAA,CAAAc,UAAA,IAAAyB,mDAAA,oBAEQ;IACVvC,EAAA,CAAAI,YAAA,EAAM;;;;;IAHwBJ,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAwB,OAAA,CAAAtB,eAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAuD;;;;;IAanFpB,EAAA,CAAAC,cAAA,UAAoE;IAAAD,EAAA,CAAAG,MAAA,8BACpE;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,uCAC1D;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IANjBJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAA2B,iDAAA,kBACM;IACNzC,EAAA,CAAAc,UAAA,IAAA4B,iDAAA,kBACe;IACjB1C,EAAA,CAAAI,YAAA,EAAM;;;;;;IAJEJ,EAAA,CAAAW,SAAA,GAA4D;IAA5DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAA2B,OAAA,CAAAzB,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;IAE5DpB,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAqB,OAAA,GAAAiB,OAAA,CAAAzB,eAAA,CAAAC,GAAA,gCAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAA2D;;;;;IAsC/DpB,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IADJJ,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmC,kBAAA,iCAAAnB,OAAA,GAAA4B,OAAA,CAAA1B,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,cAAAyB,cAAA,uBACF;;;;;IACA7C,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAG,MAAA,mGACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IATRJ,EAAA,CAAAC,cAAA,cAA2K;IACzKD,EAAA,CAAAc,UAAA,IAAAgC,iDAAA,kBAEM;IACN9C,EAAA,CAAAc,UAAA,IAAAiC,iDAAA,kBAEM;IACN/C,EAAA,CAAAc,UAAA,IAAAkC,iDAAA,kBAEM;IACRhD,EAAA,CAAAI,YAAA,EAAM;;;;;;;IATEJ,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAiC,OAAA,CAAA/B,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;IAG3DpB,EAAA,CAAAW,SAAA,GAA4D;IAA5DX,EAAA,CAAAK,UAAA,UAAAqB,OAAA,GAAAuB,OAAA,CAAA/B,eAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,cAA4D;IAG5DpB,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAK,UAAA,UAAA4B,OAAA,GAAAgB,OAAA,CAAA/B,eAAA,CAAAC,GAAA,+BAAAc,OAAA,CAAAb,MAAA,kBAAAa,OAAA,CAAAb,MAAA,YAA0D;;;;;IAmBlEpB,EAAA,CAAAC,cAAA,UAA0E;IACxED,EAAA,CAAAG,MAAA,+BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAC,cAAA,UACiD;IAC/CD,EAAA,CAAAG,MAAA,gCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAVRJ,EAAA,CAAAC,cAAA,cAGyB;IACvBD,EAAA,CAAAc,UAAA,IAAAoC,iDAAA,kBAEM;IACNlD,EAAA,CAAAc,UAAA,IAAAqC,iDAAA,kBAGM;IACRnD,EAAA,CAAAI,YAAA,EAAM;;;;;;IAPEJ,EAAA,CAAAW,SAAA,GAAkE;IAAlEX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAoC,OAAA,CAAAlC,eAAA,CAAAC,GAAA,sCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkE;IAGlEpB,EAAA,CAAAW,SAAA,GACwC;IADxCX,EAAA,CAAAK,UAAA,WAAAqB,OAAA,GAAA0B,OAAA,CAAAlC,eAAA,CAAAC,GAAA,sCAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,mBAAAM,OAAA,GAAA0B,OAAA,CAAAlC,eAAA,CAAAC,GAAA,sCAAAO,OAAA,CAAA2B,KAAA,EACwC;;;;;;;;;;;IAtKtDrD,EAAA,CAAAC,cAAA,UAA+B;IAGiBD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC5DJ,EAAA,CAAAE,SAAA,gBAAmF;IACnFF,EAAA,CAAAc,UAAA,IAAAwC,0CAAA,kBAKM;IACRtD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAsB;IACqBD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1DJ,EAAA,CAAAE,SAAA,iBAAiF;IACjFF,EAAA,CAAAc,UAAA,KAAAyC,2CAAA,kBAKM;IACRvD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAAsB;IAEsBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvDJ,EAAA,CAAAE,SAAA,iBAA+E;IAC/EF,EAAA,CAAAc,UAAA,KAAA0C,2CAAA,kBAMM;IACRxD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACkBD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnDJ,EAAA,CAAAE,SAAA,iBAA4E;IAC5EF,EAAA,CAAAc,UAAA,KAAA2C,2CAAA,kBAOM;IACRzD,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAAsB;IAEuBD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzDJ,EAAA,CAAAC,cAAA,eAA0B;IACyCD,EAAA,CAAA0D,UAAA,2BAAAC,sEAAAC,MAAA;MAAA5D,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAgE,aAAA;MAAA,OAAahE,EAAA,CAAAiE,WAAA,CAAAF,OAAA,CAAAG,YAAA,CAAAC,KAAA,GAAAP,MAAA,CACzF;IAAA,EAD4G,oBAAAQ,+DAAAR,MAAA;MAAA5D,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAArE,EAAA,CAAAgE,aAAA;MAAA,OACrFhE,EAAA,CAAAiE,WAAA,CAAAI,OAAA,CAAAC,gBAAA,CAAAV,MAAA,CAAwB;IAAA,EAD6D;IAE/F5D,EAAA,CAAAC,cAAA,kBAAmC;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClDJ,EAAA,CAAAc,UAAA,KAAAyD,8CAAA,qBAES;IACXvE,EAAA,CAAAI,YAAA,EAAS;IAEXJ,EAAA,CAAAc,UAAA,KAAA0D,2CAAA,kBAOM;IACRxE,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACiBD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACjDJ,EAAA,CAAAC,cAAA,eAA0B;IACsCD,EAAA,CAAA0D,UAAA,2BAAAe,sEAAAb,MAAA;MAAA5D,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAAY,OAAA,GAAA1E,EAAA,CAAAgE,aAAA;MAAA,OAAAhE,EAAA,CAAAiE,WAAA,CAAAS,OAAA,CAAAC,YAAA,GAAAf,MAAA;IAAA,EAA0B,oBAAAgB,+DAAA;MAAA5E,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAAe,OAAA,GAAA7E,EAAA,CAAAgE,aAAA;MAAA,OAC5EhE,EAAA,CAAAiE,WAAA,CAAAY,OAAA,CAAAC,YAAA,EAAc;IAAA,EAD8D;IAEtF9E,EAAA,CAAAC,cAAA,kBAAmC;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClDJ,EAAA,CAAAc,UAAA,KAAAiE,8CAAA,qBAES;IACX/E,EAAA,CAAAI,YAAA,EAAS;IAEXJ,EAAA,CAAAc,UAAA,KAAAkE,2CAAA,kBAOM;IACRhF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAAsB;IAEwBD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3DJ,EAAA,CAAAE,SAAA,iBAAkF;IAClFF,EAAA,CAAAc,UAAA,KAAAmE,2CAAA,kBAOM;IACRjF,EAAA,CAAAI,YAAA,EAAM;IAERJ,EAAA,CAAAC,cAAA,eAAsB;IAEuBD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzDJ,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAE,SAAA,iBAM+J;IAG/JF,EAAA,CAAAC,cAAA,kBAcI;IAXFD,EAAA,CAAA0D,UAAA,mBAAAwB,8DAAA;MAAAlF,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAAqB,OAAA,GAAAnF,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAAkB,OAAA,CAAAC,wBAAA,EAA0B;IAAA,EAAC;IAYpCpF,EAAA,CAAAE,SAAA,aAA+E;IACjFF,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAAc,UAAA,KAAAuE,2CAAA,kBAUM;IACRrF,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAsB;IAC6BD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzEJ,EAAA,CAAAE,SAAA,iBAI6C;IAC7CF,EAAA,CAAAc,UAAA,KAAAwE,2CAAA,kBAWM;IACRtF,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAC,cAAA,eAAsD;IAGKD,EAAA,CAAA0D,UAAA,mBAAA6B,8DAAA;MAAAvF,EAAA,CAAA6D,aAAA,CAAAC,IAAA;MAAA,MAAA0B,OAAA,GAAAxF,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAAuB,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC9CzF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;;;;;;;;IA3KvCJ,EAAA,CAAAW,SAAA,GAAyI;IAAzIX,EAAA,CAAAK,UAAA,WAAAW,OAAA,GAAA0E,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAA2E,OAAA,QAAA3E,OAAA,GAAA0E,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAqC,KAAA,OAAArC,OAAA,GAAA0E,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAA4E,OAAA,GAAyI;IAUzI5F,EAAA,CAAAW,SAAA,GAAsI;IAAtIX,EAAA,CAAAK,UAAA,WAAAqB,OAAA,GAAAgE,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAiE,OAAA,QAAAjE,OAAA,GAAAgE,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAA2B,KAAA,OAAA3B,OAAA,GAAAgE,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAkE,OAAA,GAAsI;IAYtI5F,EAAA,CAAAW,SAAA,GAAmI;IAAnIX,EAAA,CAAAK,UAAA,WAAA4B,OAAA,GAAAyD,MAAA,CAAAxE,eAAA,CAAAC,GAAA,8BAAAc,OAAA,CAAA0D,OAAA,QAAA1D,OAAA,GAAAyD,MAAA,CAAAxE,eAAA,CAAAC,GAAA,8BAAAc,OAAA,CAAAoB,KAAA,OAAApB,OAAA,GAAAyD,MAAA,CAAAxE,eAAA,CAAAC,GAAA,8BAAAc,OAAA,CAAA2D,OAAA,GAAmI;IAWnI5F,EAAA,CAAAW,SAAA,GAA6H;IAA7HX,EAAA,CAAAK,UAAA,WAAAwF,OAAA,GAAAH,MAAA,CAAAxE,eAAA,CAAAC,GAAA,4BAAA0E,OAAA,CAAAF,OAAA,QAAAE,OAAA,GAAAH,MAAA,CAAAxE,eAAA,CAAAC,GAAA,4BAAA0E,OAAA,CAAAxC,KAAA,OAAAwC,OAAA,GAAAH,MAAA,CAAAxE,eAAA,CAAAC,GAAA,4BAAA0E,OAAA,CAAAD,OAAA,GAA6H;IAa7D5F,EAAA,CAAAW,SAAA,GAAgC;IAAhCX,EAAA,CAAAK,UAAA,YAAAqF,MAAA,CAAAxB,YAAA,CAAAC,KAAA,CAAgC;IAGlEnE,EAAA,CAAAW,SAAA,GAAY;IAAZX,EAAA,CAAAK,UAAA,YAAAqF,MAAA,CAAAI,SAAA,CAAY;IAKvC9F,EAAA,CAAAW,SAAA,GAGM;IAHNX,EAAA,CAAAK,UAAA,WAAA0F,OAAA,GAAAL,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAA4E,OAAA,CAAAJ,OAAA,QAAAI,OAAA,GAAAL,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAA4E,OAAA,CAAA1C,KAAA,OAAA0C,OAAA,GAAAL,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAA4E,OAAA,CAAAH,OAAA,GAGM;IASoD5F,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAK,UAAA,YAAAqF,MAAA,CAAAf,YAAA,CAA0B;IAG7D3E,EAAA,CAAAW,SAAA,GAAS;IAATX,EAAA,CAAAK,UAAA,YAAAqF,MAAA,CAAAM,MAAA,CAAS;IAKhChG,EAAA,CAAAW,SAAA,GAGM;IAHNX,EAAA,CAAAK,UAAA,WAAA4F,OAAA,GAAAP,MAAA,CAAAxE,eAAA,CAAAC,GAAA,2BAAA8E,OAAA,CAAAN,OAAA,QAAAM,OAAA,GAAAP,MAAA,CAAAxE,eAAA,CAAAC,GAAA,2BAAA8E,OAAA,CAAA5C,KAAA,OAAA4C,OAAA,GAAAP,MAAA,CAAAxE,eAAA,CAAAC,GAAA,2BAAA8E,OAAA,CAAAL,OAAA,GAGM;IAYT5F,EAAA,CAAAW,SAAA,GAAyI;IAAzIX,EAAA,CAAAK,UAAA,WAAA6F,QAAA,GAAAR,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAA+E,QAAA,CAAAP,OAAA,QAAAO,QAAA,GAAAR,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAA+E,QAAA,CAAA7C,KAAA,OAAA6C,QAAA,GAAAR,MAAA,CAAAxE,eAAA,CAAAC,GAAA,gCAAA+E,QAAA,CAAAN,OAAA,GAAyI;IAcxI5F,EAAA,CAAAW,SAAA,GAAkD;IAAlDX,EAAA,CAAAmG,qBAAA,SAAAT,MAAA,CAAAU,eAAA,uBAAkD;IAKlDpG,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,KAAA+F,GAAA,IAAAC,QAAA,GAAAZ,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAmF,QAAA,CAAAX,OAAA,QAAAW,QAAA,GAAAZ,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAmF,QAAA,CAAAjD,KAAA,OAAAiD,QAAA,GAAAZ,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAmF,QAAA,CAAAV,OAAA,IAA4J;IAkBzJ5F,EAAA,CAAAW,SAAA,GAAuE;IAAvEX,EAAA,CAAAK,UAAA,YAAAqF,MAAA,CAAAU,eAAA,6CAAuE;IAItEpG,EAAA,CAAAW,SAAA,GAAsI;IAAtIX,EAAA,CAAAK,UAAA,WAAAkG,QAAA,GAAAb,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAoF,QAAA,CAAAZ,OAAA,QAAAY,QAAA,GAAAb,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAoF,QAAA,CAAAlD,KAAA,OAAAkD,QAAA,GAAAb,MAAA,CAAAxE,eAAA,CAAAC,GAAA,+BAAAoF,QAAA,CAAAX,OAAA,GAAsI;IAsBxI5F,EAAA,CAAAW,SAAA,GAE2C;IAF3CX,EAAA,CAAAK,UAAA,WAAAmG,QAAA,GAAAd,MAAA,CAAAxE,eAAA,CAAAC,GAAA,sCAAAqF,QAAA,CAAAb,OAAA,QAAAa,QAAA,GAAAd,MAAA,CAAAxE,eAAA,CAAAC,GAAA,sCAAAqF,QAAA,CAAAnD,KAAA,OAAAmD,QAAA,GAAAd,MAAA,CAAAxE,eAAA,CAAAC,GAAA,sCAAAqF,QAAA,CAAAZ,OAAA,GAE2C;IAiBjD5F,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAK,UAAA,aAAAqF,MAAA,CAAAe,aAAA,GAA4B;;;;;IAwBxBzG,EAAA,CAAAC,cAAA,iBAAmD;IACjDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAF2BJ,EAAA,CAAAK,UAAA,UAAAqG,QAAA,CAAc;IAChD1G,EAAA,CAAAW,SAAA,GACF;IADEX,EAAA,CAAAmC,kBAAA,MAAAuE,QAAA,MACF;;;;;IAOF1G,EAAA,CAAAC,cAAA,gBAAqF;IACnFD,EAAA,CAAAG,MAAA,0BACF;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;;IANVJ,EAAA,CAAAC,cAAA,UAGgB;IACdD,EAAA,CAAAc,UAAA,IAAA6F,mDAAA,oBAEQ;IACV3G,EAAA,CAAAI,YAAA,EAAM;;;;;IAHwBJ,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAA4F,OAAA,CAAA1F,eAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAuD;;;;;IAiBjFpB,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,6BACxD;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IACjBJ,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,uCACrD;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IANtBJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAA+F,iDAAA,kBACiB;IACjB7G,EAAA,CAAAc,UAAA,IAAAgG,iDAAA,kBACoB;IACtB9G,EAAA,CAAAI,YAAA,EAAM;;;;;;IAJEJ,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAA+F,OAAA,CAAA7F,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;IAE3DpB,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAqB,OAAA,GAAAqF,OAAA,CAAA7F,eAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,aAA2D;;;;;IAiBjEpB,EAAA,CAAAC,cAAA,UAAmE;IAAAD,EAAA,CAAAG,MAAA,uCACrD;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJtBJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAkG,iDAAA,kBACoB;IACtBhH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFEJ,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAiG,OAAA,CAAA/F,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IAWjEpB,EAAA,CAAAC,cAAA,UAAiE;IAAAD,EAAA,CAAAG,MAAA,qCACtD;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAJnBJ,EAAA,CAAAC,cAAA,cAEsB;IACpBD,EAAA,CAAAc,UAAA,IAAAoG,iDAAA,kBACiB;IACnBlH,EAAA,CAAAI,YAAA,EAAM;;;;;IAFEJ,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAAK,UAAA,UAAAW,OAAA,GAAAmG,OAAA,CAAAjG,eAAA,CAAAC,GAAA,6BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;;;;;;IA7EzEpB,EAAA,CAAAC,cAAA,UAA+B;IAGwBD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1EJ,EAAA,CAAAC,cAAA,cAA0B;IAEaD,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClDJ,EAAA,CAAAC,cAAA,iBAAkB;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACzCJ,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5CJ,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACrCJ,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC1CJ,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAG,MAAA,0BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC7CJ,EAAA,CAAAC,cAAA,kBAAkB;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAIhDJ,EAAA,CAAAC,cAAA,eAAsB;IACiBD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACjDJ,EAAA,CAAAC,cAAA,eAA0B;IAEaD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClDJ,EAAA,CAAAc,UAAA,KAAAsG,8CAAA,qBAES;IACXpH,EAAA,CAAAI,YAAA,EAAS;IAEXJ,EAAA,CAAAc,UAAA,KAAAuG,2CAAA,kBAOM;IACRrH,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAC,cAAA,eAAsB;IAC0BD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpEJ,EAAA,CAAAC,cAAA,eAAiB;IAEbD,EAAA,CAAAE,SAAA,iBACW;IACXF,EAAA,CAAAc,UAAA,KAAAwG,2CAAA,kBAOM;IACRtH,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAC,cAAA,eAAsB;IAC0BD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpEJ,EAAA,CAAAC,cAAA,eAAiB;IAE4BD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACrDJ,EAAA,CAAAE,SAAA,iBAC2C;IAC3CF,EAAA,CAAAc,UAAA,KAAAyG,2CAAA,kBAKM;IACRvH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACmBD,EAAA,CAAAG,MAAA,UAAE;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACjDJ,EAAA,CAAAE,SAAA,iBAC2C;IAC3CF,EAAA,CAAAc,UAAA,KAAA0G,2CAAA,kBAKM;IACRxH,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,eAAsB;IAGhBD,EAAA,CAAAE,SAAA,iBAA2F;IAC3FF,EAAA,CAAAC,cAAA,iBAAgD;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAK/EJ,EAAA,CAAAC,cAAA,eAAsD;IACJD,EAAA,CAAA0D,UAAA,mBAAA+D,8DAAA;MAAAzH,EAAA,CAAA6D,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA3H,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAA0D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAC5H,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtFJ,EAAA,CAAAC,cAAA,kBAC+B;IAD2BD,EAAA,CAAA0D,UAAA,mBAAAmE,8DAAA;MAAA7H,EAAA,CAAA6D,aAAA,CAAA6D,IAAA;MAAA,MAAAI,OAAA,GAAA9H,EAAA,CAAAgE,aAAA;MAAS8D,OAAA,CAAAC,cAAA,EAAgB;MAAA,OAAE/H,EAAA,CAAAiE,WAAA,CAAA6D,OAAA,CAAArC,QAAA,EAAU;IAAA,EAAC;IACjEzF,EAAA,CAAAG,MAAA,uCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;;;IA5ExCJ,EAAA,CAAAW,SAAA,IAAS;IAATX,EAAA,CAAAK,UAAA,YAAA2H,MAAA,CAAAhC,MAAA,CAAS;IAKhChG,EAAA,CAAAW,SAAA,GAGM;IAHNX,EAAA,CAAAK,UAAA,WAAAqB,OAAA,GAAAsG,MAAA,CAAA9G,eAAA,CAAAC,GAAA,2BAAAO,OAAA,CAAAiE,OAAA,QAAAjE,OAAA,GAAAsG,MAAA,CAAA9G,eAAA,CAAAC,GAAA,2BAAAO,OAAA,CAAA2B,KAAA,OAAA3B,OAAA,GAAAsG,MAAA,CAAA9G,eAAA,CAAAC,GAAA,2BAAAO,OAAA,CAAAkE,OAAA,GAGM;IAauE5F,EAAA,CAAAW,SAAA,GAAmB;IAAnBX,EAAA,CAAAK,UAAA,QAAA2H,MAAA,CAAAC,WAAA,CAAmB;IAGjGjI,EAAA,CAAAW,SAAA,GAAsI;IAAtIX,EAAA,CAAAK,UAAA,WAAAwF,OAAA,GAAAmC,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAA0E,OAAA,CAAAF,OAAA,QAAAE,OAAA,GAAAmC,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAA0E,OAAA,CAAAxC,KAAA,OAAAwC,OAAA,GAAAmC,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAA0E,OAAA,CAAAD,OAAA,GAAsI;IAiBvI5F,EAAA,CAAAW,SAAA,GAA+B;IAA/BX,EAAA,CAAAK,UAAA,aAAA2H,MAAA,CAAAE,kBAAA,CAA+B;IAE9BlI,EAAA,CAAAW,SAAA,GAAsI;IAAtIX,EAAA,CAAAK,UAAA,WAAA8H,OAAA,GAAAH,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAAgH,OAAA,CAAAxC,OAAA,QAAAwC,OAAA,GAAAH,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAAgH,OAAA,CAAA9E,KAAA,OAAA8E,OAAA,GAAAH,MAAA,CAAA9G,eAAA,CAAAC,GAAA,+BAAAgH,OAAA,CAAAvC,OAAA,GAAsI;IASvI5F,EAAA,CAAAW,SAAA,GAA+B;IAA/BX,EAAA,CAAAK,UAAA,aAAA2H,MAAA,CAAAE,kBAAA,CAA+B;IAE9BlI,EAAA,CAAAW,SAAA,GAAgI;IAAhIX,EAAA,CAAAK,UAAA,WAAA+H,OAAA,GAAAJ,MAAA,CAAA9G,eAAA,CAAAC,GAAA,6BAAAiH,OAAA,CAAAzC,OAAA,QAAAyC,OAAA,GAAAJ,MAAA,CAAA9G,eAAA,CAAAC,GAAA,6BAAAiH,OAAA,CAAA/E,KAAA,OAAA+E,OAAA,GAAAJ,MAAA,CAAA9G,eAAA,CAAAC,GAAA,6BAAAiH,OAAA,CAAAxC,OAAA,GAAgI;IAsBrI5F,EAAA,CAAAW,SAAA,IAA4B;IAA5BX,EAAA,CAAAK,UAAA,aAAA2H,MAAA,CAAAvB,aAAA,GAA4B;;;;;IActBzG,EAAA,CAAAC,cAAA,eAAsE;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACrFJ,EAAA,CAAAC,cAAA,eAA0E;IAAAD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;;;;;;IAN7FJ,EAAA,CAAAC,cAAA,aAC0C;IAAxCD,EAAA,CAAA0D,UAAA,mBAAA2E,8DAAA;MAAA,MAAAC,WAAA,GAAAtI,EAAA,CAAA6D,aAAA,CAAA0E,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1I,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAAyE,OAAA,CAAAC,YAAA,CAAAH,UAAA,CAAAI,QAAA,CAA6B;IAAA,EAAC;IACvC5I,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,aAAqB;IACnBD,EAAA,CAAAc,UAAA,IAAA+H,gDAAA,mBAAqF;IACrF7I,EAAA,CAAAc,UAAA,IAAAgI,gDAAA,mBAAyF;IAC3F9I,EAAA,CAAAI,YAAA,EAAK;;;;;IAP4BJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAyI,GAAA,EAAAC,OAAA,CAAAC,cAAA,KAAAT,UAAA,CAAAI,QAAA,EAAmE;IAEhG5I,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAY,iBAAA,CAAA4H,UAAA,CAAAU,IAAA,CAAiB;IACjBlJ,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAY,iBAAA,CAAA4H,UAAA,CAAAW,IAAA,CAAgB;IAEUnJ,EAAA,CAAAW,SAAA,GAAwC;IAAxCX,EAAA,CAAAK,UAAA,SAAA2I,OAAA,CAAAC,cAAA,KAAAT,UAAA,CAAAI,QAAA,CAAwC;IACpC5I,EAAA,CAAAW,SAAA,GAAwC;IAAxCX,EAAA,CAAAK,UAAA,SAAA2I,OAAA,CAAAC,cAAA,KAAAT,UAAA,CAAAI,QAAA,CAAwC;;;;;;IAXpF5I,EAAA,CAAAC,cAAA,UAA+B;IACZD,EAAA,CAAAG,MAAA,oCAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,cAA8B;IAGxBD,EAAA,CAAAc,UAAA,IAAAsI,yCAAA,iBAQK;IACPpJ,EAAA,CAAAI,YAAA,EAAQ;IAGZJ,EAAA,CAAAC,cAAA,cAA4C;IACMD,EAAA,CAAA0D,UAAA,mBAAA2F,6DAAA;MAAArJ,EAAA,CAAA6D,aAAA,CAAAyF,IAAA;MAAA,MAAAC,OAAA,GAAAvJ,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAAsF,OAAA,CAAA3B,QAAA,EAAU;IAAA,EAAC;IAAC5H,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACtFJ,EAAA,CAAAC,cAAA,kBAA2E;IAAAD,EAAA,CAAAG,MAAA,mCAC1D;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAfCJ,EAAA,CAAAW,SAAA,GAAU;IAAVX,EAAA,CAAAK,UAAA,YAAAmJ,MAAA,CAAAC,OAAA,CAAU;IAcSzJ,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAK,UAAA,cAAAmJ,MAAA,CAAAP,cAAA,CAA4B;;;;;;IAK9EjJ,EAAA,CAAAC,cAAA,UAA+B;IAEVD,EAAA,CAAAG,MAAA,6BAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,cAAsC;IACpCD,EAAA,CAAAE,SAAA,YAA8D;IAChEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAG,MAAA,4DAAqD;IAAAH,EAAA,CAAAE,SAAA,SAAI;IAACF,EAAA,CAAAG,MAAA,oDAC9C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChCJ,EAAA,CAAAC,cAAA,eAAkD;IACKD,EAAA,CAAA0D,UAAA,mBAAAgG,8DAAA;MAAA1J,EAAA,CAAA6D,aAAA,CAAA8F,IAAA;MAAA,MAAAC,OAAA,GAAA5J,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAA2F,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAAC7J,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAClGJ,EAAA,CAAAC,cAAA,kBAA0E;IAA5BD,EAAA,CAAA0D,UAAA,mBAAAoG,8DAAA;MAAA9J,EAAA,CAAA6D,aAAA,CAAA8F,IAAA;MAAA,MAAAI,OAAA,GAAA/J,EAAA,CAAAgE,aAAA;MAAA,OAAShE,EAAA,CAAAiE,WAAA,CAAA8F,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAAChK,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;AD1TrG,MAKa6J,oBAAoB;EAgCvBC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,EAAE,GAAGC,MAAM,CAACH,KAAK,CAACI,OAAO,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMC,EAAE,GAAGH,MAAM,CAACH,KAAK,CAACO,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D,MAAMG,IAAI,GAAGR,KAAK,CAACS,WAAW,EAAE;IAChC,OAAO,GAAGD,IAAI,IAAIF,EAAE,IAAIJ,EAAE,EAAE;EAC9B;EAEAQ,YACUC,EAAe,EACfC,mBAAwC,EACxCC,WAAwB,EACxBC,aAA4B;IAH5B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IA3CvB,KAAA/G,YAAY,GAAiB,IAAIpE,YAAY,EAAE;IAC/C,KAAAoL,QAAQ,GAAa,IAAInL,QAAQ,EAAE;IACnC,KAAAoL,IAAI,GAAS,IAAIvL,IAAI,EAAE;IACvB,KAAAwL,SAAS,GAAc,IAAIzL,SAAS,EAAE;IACtC,KAAA0L,YAAY,GAAiB,IAAIxL,YAAY,EAAE;IAE/C,KAAAY,WAAW,GAAW,CAAC;IACvB,KAAA6K,KAAK,GAAa,CAChB,eAAe,EACf,iBAAiB,EACjB,kBAAkB,EAClB,cAAc,CACf;IACD,KAAArD,WAAW,GAAW,IAAI,CAACiC,cAAc,EAAE;IAE3C,KAAAtI,sBAAsB,GAAW,EAAE,CAAC,CAAC;IACrC,KAAA2J,iBAAiB,GAAY,KAAK;IAClC,KAAArD,kBAAkB,GAAY,KAAK;IAEnC,KAAAvD,YAAY,GAAW,EAAE;IACzB,KAAA6G,WAAW,GAAW,EAAE;IAExB,KAAA1F,SAAS,GAAa,EAAE;IACxB,KAAAE,MAAM,GAAa,EAAE;IAErB,KAAAyD,OAAO,GAAc,EAAE;IAEvB,KAAAgC,WAAW,GAAW,IAAIrB,IAAI,EAAE,CAACsB,WAAW,EAAE,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IAE5D,KAAAvF,eAAe,GAAY,KAAK,CAAC,CAAC;IAgBhC;IACA,MAAM+D,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,IAAI,CAACqB,WAAW,GAAGtB,KAAK,CAACuB,WAAW,EAAE,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAEzD;IACA,IAAI,CAAC1K,eAAe,GAAG,IAAI,CAAC4J,EAAE,CAACe,KAAK,CAClC;MACEC,SAAS,EAAE,CAAC,EAAE,EAAEtM,UAAU,CAACuM,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAExM,UAAU,CAACuM,QAAQ,CAAC;MACnCE,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzM,UAAU,CAACuM,QAAQ,EAAEvM,UAAU,CAAC0M,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9D/C,IAAI,EAAE,CACJ;QAAEgD,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,EAC7B,CAAC5M,UAAU,CAACuM,QAAQ,EAAEvM,UAAU,CAAC0M,SAAS,CAAC,CAAC,CAAC,CAAC,CAC/C;MACDG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC7M,UAAU,CAACuM,QAAQ,EAAEvM,UAAU,CAAC0M,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DI,SAAS,EAAE,CACT,EAAE,EACF,CAAC9M,UAAU,CAACuM,QAAQ,EAAEvM,UAAU,CAAC+M,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAC9D;MACDC,KAAK,EAAE,CACL,EAAE,EACF,CAAChN,UAAU,CAACuM,QAAQ,EAAEvM,UAAU,CAACgN,KAAK,CAAC,EACvC,CAAC,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CACjC;MACDC,QAAQ,EAAE,CACR,EAAE,EACF,CACEnN,UAAU,CAACuM,QAAQ,EACnBvM,UAAU,CAAC0M,SAAS,CAAC,CAAC,CAAC,EACvB1M,UAAU,CAAC+M,OAAO,CAAC,qCAAqC,CAAC,CAC1D,CACF;MACDK,eAAe,EAAE,CAAC,EAAE,EAAEpN,UAAU,CAACuM,QAAQ,CAAC;MAC1Cc,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBrB,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBsB,QAAQ,EAAE,CACR;QAAEX,KAAK,EAAE,IAAI,CAACV,WAAW;QAAEW,QAAQ,EAAE;MAAK,CAAE,EAC5C5M,UAAU,CAACuM,QAAQ,CACpB;MACDgB,MAAM,EAAE,CACN;QAAEZ,KAAK,EAAE,IAAI,CAACV,WAAW;QAAEW,QAAQ,EAAE;MAAK,CAAE,EAC5C5M,UAAU,CAACuM,QAAQ,CACpB;MACDiB,QAAQ,EAAE,CAAC,EAAE,EAAExN,UAAU,CAACuM,QAAQ,CAAC;MACnCkB,MAAM,EAAE,CAAC,EAAE,EAAEzN,UAAU,CAACuM,QAAQ,CAAC;MACjCmB,SAAS,EAAE,CAAC,KAAK;KAClB,EACD;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC5C,CAAC,CAAC;IAEH;IACA,IAAI,CAAClM,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEkM,YAAY,CAACC,SAAS,CAAEnB,KAAK,IAAI;MACtE,IAAI,CAACoB,wBAAwB,CAACpB,KAAK,CAAC;IACtC,CAAC,CAAC;EACJ;EACA;EACA/G,wBAAwBA,CAAA;IACtB,IAAI,CAACgB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;EACAmH,wBAAwBA,CAACC,WAAoB;IAC3C,IAAIA,WAAW,EAAE;MACf;MACA,MAAMvF,WAAW,GAAG,IAAImC,IAAI,EAAE,CAACsB,WAAW,EAAE,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC/D,MAAM6B,WAAW,GAAG,IAAIrD,IAAI,EAAE,CAACsD,YAAY,EAAE,CAAC9B,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAE/D;MACA,IAAI,CAAC1K,eAAe,CAACyM,UAAU,CAAC;QAC9Bb,QAAQ,EAAE7E,WAAW;QACrB8E,MAAM,EAAE9E,WAAW;QACnB+E,QAAQ,EAAES,WAAW;QACrBR,MAAM,EAAE,OAAO,CAAE;OAClB,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAC/L,eAAe,CAACyM,UAAU,CAAC;QAC9Bb,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE;OACT,CAAC;;EAEN;EAEA;EACQW,mBAAmBA,CACzBC,OAAwB;IAExB,MAAMC,YAAY,GAAG,IAAI1D,IAAI,CAACyD,OAAO,CAAC1B,KAAK,CAAC;IAC5C,MAAMhC,KAAK,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACqB,WAAW,CAAC;IAExC,OAAOqC,YAAY,GAAG3D,KAAK,GAAG;MAAE4D,UAAU,EAAE;IAAI,CAAE,GAAG,IAAI;EAC3D;EAEAX,sBAAsBA,CAACY,SAAoB;IACzC,MAAMrB,QAAQ,GAAGqB,SAAS,CAAC7M,GAAG,CAAC,UAAU,CAAC;IAC1C,MAAMyL,eAAe,GAAGoB,SAAS,CAAC7M,GAAG,CAAC,iBAAiB,CAAC;IAExD,IAAI,CAACwL,QAAQ,IAAI,CAACC,eAAe,EAAE;MACjC,OAAO,IAAI;;IAGb,IAAIA,eAAe,CAACxL,MAAM,IAAI,CAACwL,eAAe,CAACxL,MAAM,CAAC,UAAU,CAAC,EAAE;MACjE,OAAO,IAAI;;IAGb,IAAIuL,QAAQ,CAACR,KAAK,KAAKS,eAAe,CAACT,KAAK,EAAE;MAC5CS,eAAe,CAACqB,SAAS,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;KAC9C,MAAM;MACLtB,eAAe,CAACqB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAGnC,OAAO,IAAI,CAAC,CAAC;EACf;;EACAxB,cAAcA,CAACoB,OAAY;IACzB,MAAMM,SAAS,GAAGN,OAAO,CAAC1B,KAAK;IAE/B,IAAIgC,SAAS,EAAE;MACb,OAAO,IAAI,CAACnD,WAAW,CAACoD,SAAS,CAACD,SAAS,CAAC,CAACE,IAAI,CAC/C5O,GAAG,CAAE6O,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC/C,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAAC3J,sBAAsB,GACzB,wCAAwC;UAC1C,OAAO;YAAE2M,WAAW,EAAE;UAAI,CAAE,CAAC,CAAC;SAC/B,MAAM;UACL,IAAI,CAAChD,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAAC3J,sBAAsB,GAAG,EAAE;UAChC,OAAO,IAAI,CAAC,CAAC;;MAEjB,CAAC,CAAC,CACH;KACF,MAAM;MACL,OAAOlC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;EAErB;;EAEA8O,QAAQA,CAAA;IACNC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC1D,WAAW,CACb2D,mBAAmB,CAAC,CAAC,CAAC,CACtBrB,SAAS,CAAEsB,QAAsB,IAAI;MACpC,IAAI,CAACvD,YAAY,GAAGuD,QAAQ;IAC9B,CAAC,CAAC;IACJ,IAAI,CAAC9I,SAAS,GAAG,IAAI,CAACmF,aAAa,CAAC4D,YAAY,EAAE;EACpD;EAEAvK,gBAAgBA,CAACwK,KAAY;IAC3B,MAAMC,gBAAgB,GAAID,KAAK,CAACE,MAA4B,CAAC7C,KAAK;IAElE,IAAI4C,gBAAgB,EAAE;MACpB,IAAI,CAAC7N,eAAe,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE8N,MAAM,EAAE;MAC1C,IAAI,CAACjJ,MAAM,GAAG,IAAI,CAACiF,aAAa,CAACiE,mBAAmB,CAACH,gBAAgB,CAAC;KACvE,MAAM;MACL,IAAI,CAAC7N,eAAe,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEgO,OAAO,EAAE;MAC3C,IAAI,CAACnJ,MAAM,GAAG,EAAE;;IAElB,IAAI,CAAC9E,eAAe,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEiO,QAAQ,CAAC,EAAE,CAAC;EAChD;EAEAtK,YAAYA,CAAA;IACV,IAAI,CAAC0G,WAAW,GAAG,IAAI,CAAC7G,YAAY;IACpC,IAAI,CAACzD,eAAe,CAACmO,QAAQ,CAAC,aAAa,CAAC,CAACD,QAAQ,CAAC,IAAI,CAACzK,YAAY,CAAC;EAC1E;EAEAc,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChF,WAAW,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC6O,YAAY,EAAE;;IAGrB,IAAI,IAAI,CAAC7O,WAAW,GAAG,IAAI,CAAC6K,KAAK,CAACiE,MAAM,EAAE;MACxC,IAAI,CAAC9O,WAAW,EAAE;;EAEtB;EACAsH,cAAcA,CAAA;IACZ,MAAM+E,QAAQ,GAAG,IAAI,CAAC5L,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEgL,KAAK;IAE5D;IACA,MAAMqD,IAAI,GAAG,IAAIpF,IAAI,CAAC0C,QAAQ,CAAC;IAE/B;IACA,MAAM2C,SAAS,GAAGD,IAAI,CAACE,MAAM,EAAE;IAE/B;IACA,MAAMC,IAAI,GAAG,CACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,CACX;IACD,MAAMC,OAAO,GAAGD,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;IAEjCI,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,OAAO,CAAC,CAAC,CAAC;IAEvC;IACA,MAAM5C,QAAQ,GAAG,IAAI,CAAC9L,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEgL,KAAK;IAC5D,MAAMX,WAAW,GAAG,IAAI,CAACtK,eAAe,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEgL,KAAK;IAC3D,MAAMU,gBAAgB,GACpB,IAAI,CAAC3L,eAAe,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEgL,KAAK;IAErD;IACA,IAAI,CAACpB,mBAAmB,CACrBgF,WAAW,CAACH,OAAO,EAAE5C,QAAQ,EAAExB,WAAW,EAAEqB,gBAAgB,CAAC,CAC7DS,SAAS,CACPsB,QAAQ,IAAI;MACXiB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAElB,QAAQ,CAAC,CAAC,CAAC;MAErD,IAAI,CAACnF,OAAO,GAAGmF,QAAQ,CAAC,CAAC;MACzBiB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACrG,OAAO,CAAC,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAACA,OAAO,CAAC8F,MAAM,KAAK,CAAC,EAAE;QAC7B;QACAS,KAAK,CACH,sBAAsB,GACpBxE,WAAW,GACX,oCAAoC,CACvC;QACD,IAAI,CAACvC,cAAc,GAAGgH,SAAS,CAAC,CAAC;QAEjC;QACA,IAAI,CAACxP,WAAW,GAAG,CAAC,CAAC,CAAC;QAEtB,OAAO,CAAC;OACT,MAAM;QACLoP,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACrG,OAAO,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACR,cAAc,GAAGgH,SAAS,CAAC,CAAC;;IAErC,CAAC,EACAC,KAAK,IAAI;MACR;MACAL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CF,KAAK,CACH,kEAAkE,CACnE;IACH,CAAC,CACF;EACL;EAEAvJ,aAAaA,CAAA;IACX;IACA,IAAI,IAAI,CAAChG,WAAW,KAAK,CAAC,EAAE;MAC1B,OACE,CAAC,CAAC,IAAI,CAACS,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEwE,OAAO,IAChD,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwE,OAAO,IAC/C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEwE,OAAO,IAC9C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEwE,OAAO,IAC5C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwE,OAAO,IAC/C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEwE,OAAO,IAC3C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEwE,OAAO,IAChD,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwE,OAAO,IAC/C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEwE,OAAO;;IAI1D;IACA,IAAI,IAAI,CAAClF,WAAW,KAAK,CAAC,EAAE;MAC1B,OACE,CAAC,CAAC,IAAI,CAACS,eAAe,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEwE,OAAO,IACvD,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEwE,OAAO,IAClD,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwE,OAAO,IAC/C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEwE,OAAO,IAC/C,CAAC,CAAC,IAAI,CAACzE,eAAe,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEwE,OAAO;;IAIjD;IACA,OAAO,KAAK;EACd;EAEAiC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEA0P,mBAAmBA,CAAA;IACjB,MAAMC,UAAU,GAAG,IAAI,CAAClP,eAAe,CAACiL,KAAK;IAE7C,IAAI,CAACjI,YAAY,GAAG;MAClB,GAAG,IAAI,CAACA,YAAY;MACpBsI,KAAK,EAAE4D,UAAU,CAAC5D;KACnB;IAED;IACA,IAAI,CAACtB,QAAQ,GAAG;MACdmF,UAAU,EAAE,CAAC;MACblF,IAAI,EAAE,IAAI,CAACD,QAAQ,CAACC,IAAI;MACxBW,SAAS,EAAEsE,UAAU,CAACtE,SAAS;MAC/BE,QAAQ,EAAEoE,UAAU,CAACpE,QAAQ;MAC7BC,OAAO,EAAEmE,UAAU,CAACnE,OAAO;MAC3B9C,IAAI,EAAEiH,UAAU,CAACjH,IAAI;MACrBhF,KAAK,EAAEiM,UAAU,CAACjM,KAAK;MACvBmM,OAAO,EAAE,EAAE;MACXhE,SAAS,EAAE8D,UAAU,CAAC9D,SAAS;MAC/BE,KAAK,EAAE,IAAI,CAACtI,YAAY,CAACsI;KAC1B;IAED;IACA,IAAI,CAACzB,mBAAmB,CAACwF,YAAY,CAAC,IAAI,CAACrF,QAAQ,CAAC,CAACoC,SAAS,CAAC;MAC7DkD,IAAI,EAAG5B,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACyB,UAAU,EAAE;UACvB,IAAI,CAACnM,YAAY,CAACgH,QAAQ,CAACmF,UAAU,GAAGzB,QAAQ,CAACyB,UAAU;UAC3DR,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAAC5L,YAAY,CAACgH,QAAQ,CAACmF,UAAU,CACtC;;MAEL,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACJ;EAEAO,WAAWA,CAAA;IACT,IAAI,CAACtF,IAAI,CAACuF,QAAQ,GAAG,IAAI,CAACxM,YAAY,CAACsI,KAAK;EAC9C;EAEA7D,YAAYA,CAACC,QAAgB;IAC3B,IAAI,CAACK,cAAc,GAAGL,QAAQ,CAAC,CAAC;IAChCiH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAElH,QAAQ,CAAC,CAAC,CAAC;IAC3CiH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC7G,cAAc,CAAC,CAAC,CAAC;EACrE;;EAEA0H,SAASA,CAAA;IACP,IAAI,CAACzP,eAAe,CAAC0P,KAAK,EAAE;IAC5B,IAAI,CAACnQ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACwI,cAAc,GAAGgH,SAAS;EACjC;EAEApG,iBAAiBA,CAAA;IACfgG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,IAAI,CAACa,SAAS,EAAE;EAClB;EAEAE,mBAAmBA,CAAA;IACjBhB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI,CAACa,SAAS,EAAE;EAClB;EAEAG,QAAQA,CAAA;IACN,IAAI,CAAC9G,eAAe,EAAE;IACtB,IAAI,CAACvJ,WAAW,GAAG,CAAC;EACtB;EAEA;EACAuJ,eAAeA,CAAA;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC9F,YAAY,CAACuF,OAAO,CAACb,QAAQ,GAAG,IAAI,CAACK,cAAc;IACxD,MAAMmH,UAAU,GAAG,IAAI,CAAClP,eAAe,CAACiL,KAAK;IAC7C,IAAI,CAACjI,YAAY,GAAG;MAClB6M,aAAa,EAAE,CAAC;MAChBjF,SAAS,EAAEsE,UAAU,CAACtE,SAAS;MAC/BE,QAAQ,EAAEoE,UAAU,CAACpE,QAAQ;MAC7BC,OAAO,EAAEmE,UAAU,CAACnE,OAAO;MAC3B9C,IAAI,EAAEiH,UAAU,CAACjH,IAAI;MACrBhF,KAAK,EAAEiM,UAAU,CAACjM,KAAK;MACvBkI,QAAQ,EAAE+D,UAAU,CAAC/D,QAAQ;MAC7BC,SAAS,EAAE8D,UAAU,CAAC9D,SAAS;MAC/BE,KAAK,EAAE4D,UAAU,CAAC5D,KAAK;MACvBwE,QAAQ,EAAEZ,UAAU,CAACM,QAAQ;MAC7B/D,QAAQ,EAAEyD,UAAU,CAACzD,QAAQ;MAC7BE,gBAAgB,EAAEuD,UAAU,CAACvD,gBAAgB;MAC7CrB,WAAW,EAAE4E,UAAU,CAAC5E,WAAW;MACnCsB,QAAQ,EAAEsD,UAAU,CAACtD,QAAQ;MAC7BC,MAAM,EAAEqD,UAAU,CAACrD,MAAM;MACzBC,QAAQ,EAAE,IAAI,CAAC9L,eAAe,CAACiL,KAAK,CAACa,QAAQ;MAC7CC,MAAM,EAAE,IAAI,CAAC/L,eAAe,CAACiL,KAAK,CAACc,MAAM;MACzC/B,QAAQ,EAAE,IAAI,CAAChH,YAAY,CAACgH,QAAQ;MACpCzB,OAAO,EAAE,IAAI,CAACvF,YAAY,CAACuF,OAAO;MAClCwH,MAAM,EAAE;KACT;IAED;IACApB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5L,YAAY,CAAC;IAEtD,IAAI,CAAC6G,mBAAmB,CACrBmG,wBAAwB,CAAC,IAAI,CAAChN,YAAY,CAAC,CAC3CoJ,SAAS,CAAC;MACTkD,IAAI,EAAG5B,QAAQ,IAAI;QACjBiB,OAAO,CAACC,GAAG,CAAClB,QAAQ,CAAC;MACvB,CAAC;MACDsB,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACN;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAZ,YAAYA,CAAA;IACV,IAAI,CAACmB,WAAW,EAAE;IAClB,IAAI,CAACrF,SAAS,CAAC+F,cAAc,GAAG,IAAI,CAAC9F,YAAY;IACjD,IAAI,CAACD,SAAS,CAACU,SAAS,GAAG,IAAI,CAAC5K,eAAe,CAACC,GAAG,CAAC,WAAW,CAAC,EAAEgL,KAAK;IACvE,IAAI,CAACf,SAAS,CAACY,QAAQ,GAAG,IAAI,CAAC9K,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEgL,KAAK;IACrE,IAAI,CAACf,SAAS,CAACsF,QAAQ,GAAG,IAAI,CAACxP,eAAe,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEgL,KAAK;IAClE,IAAI,CAACf,SAAS,CAACuB,QAAQ,GAAG,IAAI,CAACzL,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEgL,KAAK;IAErE0D,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC5L,YAAY,CAACsI,KAAK,CAAC;IAEzD,IAAI,CAACxB,WAAW,CAACoG,QAAQ,CAAC,IAAI,CAAChG,SAAS,CAAC,CAACkC,SAAS,CAAC;MAClDkD,IAAI,EAAG5B,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACyC,EAAE,EAAE;UACf,IAAI,CAACnG,QAAQ,CAACC,IAAI,CAACmG,MAAM,GAAG1C,QAAQ,CAACyC,EAAE;UACvC,IAAI,CAAClB,mBAAmB,EAAE;;MAE9B,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACfL,OAAO,CAACK,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACJ;EAAC,QAAAqB,CAAA,G;qBArgBUtH,oBAAoB,EAAAjK,EAAA,CAAAwR,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1R,EAAA,CAAAwR,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA5R,EAAA,CAAAwR,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9R,EAAA,CAAAwR,iBAAA,CAAAO,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBhI,oBAAoB;IAAAiI,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3BjCxS,EAAA,CAAAE,SAAA,4BAAmD;QAEnDF,EAAA,CAAAC,cAAA,aAAmC;QAGKD,EAAA,CAAAG,MAAA,2FAChB;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAG3BJ,EAAA,CAAAC,cAAA,aAA0B;QAEGD,EAAA,CAAAG,MAAA,uBAAgB;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC9CJ,EAAA,CAAAC,cAAA,cAA4B;QAC1BD,EAAA,CAAAc,UAAA,KAAA4R,oCAAA,iBAIM;QACR1S,EAAA,CAAAI,YAAA,EAAM;QAERJ,EAAA,CAAAC,cAAA,gBAA0F;QAAtDD,EAAA,CAAA0D,UAAA,sBAAAiP,wDAAA;UAAA,OAAYF,GAAA,CAAA3B,QAAA,EAAU;QAAA,EAAC;QACzD9Q,EAAA,CAAAc,UAAA,KAAA8R,oCAAA,oBAmLM;QACN5S,EAAA,CAAAc,UAAA,KAAA+R,oCAAA,mBAmGM;QAEN7S,EAAA,CAAAc,UAAA,KAAAgS,oCAAA,mBAsBM;QAEN9S,EAAA,CAAAc,UAAA,KAAAiS,oCAAA,mBAaM;QACR/S,EAAA,CAAAI,YAAA,EAAO;;;QAvU2CJ,EAAA,CAAAW,SAAA,IAAU;QAAVX,EAAA,CAAAK,UAAA,YAAAoS,GAAA,CAAAnH,KAAA,CAAU;QAOtDtL,EAAA,CAAAW,SAAA,GAA6B;QAA7BX,EAAA,CAAAK,UAAA,cAAAoS,GAAA,CAAAvR,eAAA,CAA6B;QAC3BlB,EAAA,CAAAW,SAAA,GAAuB;QAAvBX,EAAA,CAAAK,UAAA,SAAAoS,GAAA,CAAAhS,WAAA,OAAuB;QAoLvBT,EAAA,CAAAW,SAAA,GAAuB;QAAvBX,EAAA,CAAAK,UAAA,SAAAoS,GAAA,CAAAhS,WAAA,OAAuB;QAqGvBT,EAAA,CAAAW,SAAA,GAAuB;QAAvBX,EAAA,CAAAK,UAAA,SAAAoS,GAAA,CAAAhS,WAAA,OAAuB;QAwBvBT,EAAA,CAAAW,SAAA,GAAuB;QAAvBX,EAAA,CAAAK,UAAA,SAAAoS,GAAA,CAAAhS,WAAA,OAAuB;;;;;;;SD3SxBwJ,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}