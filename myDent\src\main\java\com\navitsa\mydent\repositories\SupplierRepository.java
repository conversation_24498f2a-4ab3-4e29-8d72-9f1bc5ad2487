package com.navitsa.mydent.repositories;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.navitsa.mydent.entity.Supplier;



public interface SupplierRepository extends JpaRepository<Supplier, Integer>  {
	Optional<Supplier> findByName(String supplierName);
	Optional<Supplier> findBySupplierId(Integer supplierId);

    
    @Query("SELECT s FROM Supplier s WHERE s.userId.userId = :userId")
    Optional<Supplier> findByUserId(@Param("userId") Integer userId);

}
