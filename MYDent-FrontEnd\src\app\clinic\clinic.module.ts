import { NgModule } from '@angular/core';

import { ClinicRoutingModule } from './clinic-routing.module';
import { ClinicLayoutComponent } from './clinic-layout/clinic-layout.component';
import { ClinicSideBarComponent } from './components/clinic-side-bar/clinic-side-bar.component';
import { ClinicNavbarComponent } from './components/clinic-navbar/clinic-navbar.component';
import { ClinicOrdersComponent } from './clinic-orders/clinic-orders.component';
import { OrderDetailsPopupComponent } from './components/order-details-popup/order-details-popup.component';
import { AcceptPopupComponent } from './components/accept-popup/accept-popup.component';
import { ClinicAddLaboratorOrderComponent } from './clinic-add-laborator-order/clinic-add-laborator-order.component';
import { ClinicLaboratoryOrderListComponent } from './clinic-laboratory-order-list/clinic-laboratory-order-list.component';
import { AddClinicServiceComponent } from './add-clinic-service/add-clinic-service.component';
import { ClinicSelectLaboratorySetupComponent } from './clinic-select-laboratory-setup/clinic-select-laboratory-setup.component';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTableModule } from '@angular/material/table';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ClinicDashboardComponent } from './clinic-dashboard/clinic-dashboard.component';
import { ClinicSupplierOrderComponent } from './clinic-supplier-order/clinic-supplier-order.component';
import { ClinicSetupClinicComponent } from './clinic-setup-clinic/clinic-setup-clinic.component';
import { ClinicSupplierOrderListComponent } from './clinic-supplier-order-list/clinic-supplier-order-list.component';
import { CoreModule } from "../core/core.module";
import { AppointmentDetailsDialogComponent } from './clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';
import { ClinicNewAppointmentComponent } from './clinic-new-appointment/clinic-new-appointment.component';
import { DoctorSelectComponent } from './clinic-dashboard/appointment-popup/doctor-select/doctor-select.component';


@NgModule({
  declarations: [
    ClinicLayoutComponent,
    ClinicSideBarComponent,
    ClinicNavbarComponent,
    ClinicOrdersComponent,
    ClinicAddLaboratorOrderComponent,
    OrderDetailsPopupComponent,
    AcceptPopupComponent,
    ClinicLaboratoryOrderListComponent,
    AddClinicServiceComponent,
    ClinicSelectLaboratorySetupComponent,
    ClinicDashboardComponent,
    ClinicSupplierOrderComponent,
    ClinicSetupClinicComponent,
    ClinicSupplierOrderListComponent,
    AppointmentDetailsDialogComponent,
    ClinicNewAppointmentComponent,
    DoctorSelectComponent

  ],
  imports: [
    CommonModule,
    ClinicRoutingModule,
    MatPaginatorModule,
    MatProgressSpinnerModule,
    MatTableModule,
    FormsModule,
    ReactiveFormsModule,
    CoreModule
],
})
export class ClinicModule {}
