package com.navitsa.mydent.services;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.Customer;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.CustomerRepository;
import com.navitsa.mydent.repositories.UserRepository;

@Service
public class CustomerService {
	
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private EmailService emailService;

    public Customer saveCustomer(Customer customer) {
        try {
        	Customer savedCustomer = customerRepository.save(customer);
            
           	User user = savedCustomer.getUser();
            String verificationToken = user.getVerificationToken();
            String verificationTokenWithUserType = verificationToken + "&userType=Customer";
            System.out.println("token: " + verificationTokenWithUserType);

            CompletableFuture.runAsync(() ->
                    emailService.sendRegistrationEmail(
                    	savedCustomer.getEmail(),
                    	savedCustomer.getFirstName(),
                        "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType,
                        "Customer"
                    ));
            
            return savedCustomer;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the customer.");
        }
    }

    public List<Customer> findAllCustomers() {
        try {
            return (List<Customer>) customerRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of customers.");
        }
    }

    public Customer getCustomerById(int id) {
        return customerRepository.findById(id).orElse(null);
    }

    public Customer updateCustomer(int id, Customer customerDetails) {
        try {
            Customer customer = customerRepository.findById(id).orElse(null);

            if (customer == null) {
                throw new RuntimeException("Customer not found with id: " + id);
            }

            // Update the customer details
            customer.setFirstName(customerDetails.getFirstName());
            customer.setLastName(customerDetails.getLastName());
            customer.setAddress(customerDetails.getAddress());
            customer.setCity(customerDetails.getCity());
            customer.setState(customerDetails.getState());
            customer.setCountry(customerDetails.getCountry());
            customer.setTelephone(customerDetails.getTelephone());
            customer.setEmail(customerDetails.getEmail());
            customer.setRegisteredDate(customerDetails.getRegisteredDate());
            customer.setLatitude(customerDetails.getLatitude());
            customer.setLongitude(customerDetails.getLongitude());

            return customerRepository.save(customer);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the customer.");
        }
    }

    public void deleteCustomer(int id) {
        try {
            customerRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the customer.");
        }
    }
    
    public Customer getCustomerByUserId(int id) {
    	User user = userRepository.findByUserId(id);    	
        return customerRepository.findByUser(user).orElse(null);
    }

}
