package com.navitsa.mydent.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.services.AppointmentsServices;
import com.navitsa.mydent.repositories.AppointmentsRepository;
import org.springframework.web.server.ResponseStatusException;

@RestController
public class AppointmentsController {
    
    @Autowired
    private AppointmentsServices appointmentsServices;
    
    @Autowired
    private AppointmentsRepository appointmentsRepository;

    @PostMapping("/saveAppointments")
    public Appointments saveAppointments(@RequestBody Appointments appointments) {
        Integer clinicId = appointments.getClinics().getClinicId();
        String fromDate = appointments.getFromDate();
        String fromTime = appointments.getFromTime();
        String toTime = appointments.getToTime();

        boolean available = appointmentsServices.isTimeSlotAvailable(clinicId, fromDate, fromTime, toTime);

        if (!available) {
//            throw new RuntimeException("Time slot already booked.");
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Time slot already booked.");
        }

        return appointmentsServices.saveAppointments(appointments);
    }

    @GetMapping(value = "/appointmentsList")
    public List<Appointments> getAllAppointments() {
        return appointmentsServices.findAllAppointments();
    }

    @PutMapping("/updateAppointments/{id}")
    public ResponseEntity<Appointments> updateAppointments(@PathVariable int id, @RequestBody Appointments appointmentsDetails) {
        return ResponseEntity.ok(appointmentsServices.updateAppointments(id, appointmentsDetails));
    }

    @GetMapping("/getAppointmentsById/{id}")
    public Appointments getAppointmentsById(@PathVariable int id) {
        return appointmentsServices.getAppointmentsById(id);
    }
    
    @DeleteMapping("/deleteAppointments/{id}")
	public ResponseEntity<Void> deleteAppointments(@PathVariable int id) {
    	appointmentsServices.deleteAppointments(id);
		return ResponseEntity.noContent().build();
	}
    
    @GetMapping("/getAppointmentsByCustomerId/{userId}")
    public List<Appointments> getAppointmentsByCustomerId(@PathVariable int userId) {
        return appointmentsServices.getAppointmentsByCustomerId(userId);
    }

    @GetMapping("/clinic/{clinicId}")
    public Optional<List<Appointments>> getAppointmentsByClinicId(@PathVariable int clinicId) {

        return appointmentsServices.getAppointmentsByClinicId(clinicId);
    }

    @PutMapping("/updateAppointmentStatus/{id}")
    public ResponseEntity<Appointments> updateAppointmentStatus(@PathVariable int id, @RequestBody String status) {
        try {
            Appointments updatedAppointment = appointmentsServices.updateAppointmentStatus(id, status);
            return ResponseEntity.ok(updatedAppointment);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
    
    @PostMapping("/saveCustomerAppointment")
    public Appointments saveCustomerAppointment(@RequestBody Appointments appointments) {
    	return appointmentsServices.saveCustomerAppointment(appointments);
    }
    
}