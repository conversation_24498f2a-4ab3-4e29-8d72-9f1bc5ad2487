package com.navitsa.mydent.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.navitsa.mydent.entity.ClinicAddService;
import com.navitsa.mydent.entity.ClinicServices;
import com.navitsa.mydent.repositories.ClinicScheduleRepository;
import com.navitsa.mydent.repositories.ClinicServicesRepository;
import com.navitsa.mydent.services.ClinicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.services.AppointmentsServices;
import com.navitsa.mydent.repositories.AppointmentsRepository;
import org.springframework.web.server.ResponseStatusException;

@RestController
public class AppointmentsController {
    
    @Autowired
    private AppointmentsServices appointmentsServices;
    
    @Autowired
    private AppointmentsRepository appointmentsRepository;

    @Autowired
    private ClinicScheduleRepository clinicScheduleRepository;

    @Autowired
    private ClinicServicesRepository clinicServicesRepository;

    @PostMapping("/saveAppointments")
    public Map<String, Object> saveAppointments(@RequestBody Appointments appointments) {
        Map<String, Object> response = new HashMap<>();
        Integer clinicId = appointments.getClinics().getClinicId();
        String fromDate = appointments.getFromDate();
        String fromTime = appointments.getFromTime();
        String toTime = appointments.getToTime();

        boolean noSchedules = clinicScheduleRepository.findAll().isEmpty();

        if(noSchedules) {
            response.put("status", false);
            response.put("message", "No schedules found Check your schedules");
            return response;
        }

        boolean hollyDayOrCloseDay = appointmentsServices.isHollyDayOrCloseDay(fromDate,clinicId);
         if(hollyDayOrCloseDay) {
             response.put("status", false);
             response.put("message", "This Date Not Available for booking ,It is HolyDay");
             return response;
         }
        boolean availableDate = appointmentsServices.isAvailableDate(clinicId,fromDate,fromTime,toTime);
        if(!availableDate) {
            response.put("status", false);
            response.put("message", "This is not available Time");
            return response;
        }
        boolean available = appointmentsServices.isTimeSlotAvailable(clinicId, fromDate, fromTime, toTime);
        if (!available) {
            response.put("status", false);
            response.put("message", "Time slot is booked");
            return response;
        }

        Boolean haveService = clinicServicesRepository.existsByClinicIdAndServiceId(clinicId, Integer.parseInt(appointments.getPreferredservice()));

        if(haveService.equals(false)) {
            response.put("status", false);
            response.put("message", "This Service you are not arranged");
            return response;
        }

        appointments.setStatus("Pending");
        appointmentsRepository.save(appointments);
        response.put("status", true);
        response.put("message", "Appointment arranged successfully");


        return response;
    }

    @GetMapping(value = "/appointmentsList")
    public List<Appointments> getAllAppointments() {
        return appointmentsServices.findAllAppointments();
    }

    @PutMapping("/updateAppointments/{id}")
    public ResponseEntity<Appointments> updateAppointments(@PathVariable int id, @RequestBody Appointments appointmentsDetails) {
        return ResponseEntity.ok(appointmentsServices.updateAppointments(id, appointmentsDetails));
    }

    @GetMapping("/getAppointmentsById/{id}")
    public Appointments getAppointmentsById(@PathVariable int id) {
        return appointmentsServices.getAppointmentsById(id);
    }
    
    @DeleteMapping("/deleteAppointments/{id}")
	public ResponseEntity<Void> deleteAppointments(@PathVariable int id) {
    	appointmentsServices.deleteAppointments(id);
		return ResponseEntity.noContent().build();
	}
    
    @GetMapping("/getAppointmentsByCustomerId/{userId}")
    public List<Appointments> getAppointmentsByCustomerId(@PathVariable int userId) {
        return appointmentsServices.getAppointmentsByCustomerId(userId);
    }

    @GetMapping("/clinic/{clinicId}")
    public Optional<List<Appointments>> getAppointmentsByClinicId(@PathVariable int clinicId) {

        return appointmentsServices.getAppointmentsByClinicId(clinicId);
    }

    @PutMapping("/updateAppointmentStatus/{id}")
    public ResponseEntity<Appointments> updateAppointmentStatus(@PathVariable int id, @RequestBody String status) {
        try {
            Appointments updatedAppointment = appointmentsServices.updateAppointmentStatus(id, status);
            return ResponseEntity.ok(updatedAppointment);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
    
    @PostMapping("/saveCustomerAppointment")
    public Appointments saveCustomerAppointment(@RequestBody Appointments appointments) {
    	return appointmentsServices.saveCustomerAppointment(appointments);
    }

    @GetMapping("/getAllAppointmentInDates")
    public Map<String, Object> getAllAppointments(
            @RequestParam int clinicId,
            @RequestParam String fromDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Map<String, Object> response = new HashMap<>();

        Pageable pageable = PageRequest.of(page, size);
        Page<Appointments> appointmentsPage = appointmentsRepository
                .findByClinicsClinicIdAndFromDate(clinicId, fromDate, pageable);

        if (appointmentsPage.isEmpty()) {
            response.put("status", false);
            response.put("message", "No appointments found for this clinic on the given date");
            return response;
        }

        response.put("status", true);
        response.put("appointments", appointmentsPage.getContent());
        response.put("currentPage", appointmentsPage.getNumber());
        response.put("totalItems", appointmentsPage.getTotalElements());
        response.put("totalPages", appointmentsPage.getTotalPages());

        return response;
    }

}

