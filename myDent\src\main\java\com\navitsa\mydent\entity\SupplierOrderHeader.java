package com.navitsa.mydent.entity;

import com.navitsa.mydent.enums.ClinicSupplierOrderStatus;
import jakarta.persistence.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "supplier_order_header")
public class SupplierOrderHeader implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "supplier_order_header_id")
    private int supplierOrderHeaderId;

    @ManyToOne
    @JoinColumn(name = "clinic_id",referencedColumnName = "clinic_id")
    private Clinic clinic;

    @ManyToOne
    @JoinColumn(name = "supplier_id",referencedColumnName = "supplier_id")
    private Supplier supplier;

    @Column(name = "order_status")
    private ClinicSupplierOrderStatus clinicSupplierOrderStatus;

    @Column(name = "created_date_time")
    private LocalDateTime createdDateTime;

    public SupplierOrderHeader() {
    }

    public SupplierOrderHeader(Clinic clinic, Supplier supplier, ClinicSupplierOrderStatus clinicSupplierOrderStatus, LocalDateTime createdDateTime) {
        this.clinic = clinic;
        this.supplier = supplier;
        this.clinicSupplierOrderStatus = clinicSupplierOrderStatus;
        this.createdDateTime = createdDateTime;
    }

    public SupplierOrderHeader(int supplierOrderHeaderId, Clinic clinic, Supplier supplier,
                               ClinicSupplierOrderStatus clinicSupplierOrderStatus, LocalDateTime createdDateTime) {
        this.supplierOrderHeaderId = supplierOrderHeaderId;
        this.clinic = clinic;
        this.supplier = supplier;
        this.clinicSupplierOrderStatus = clinicSupplierOrderStatus;
        this.createdDateTime = createdDateTime;
    }

    public int getSupplierOrderHeaderId() {
        return supplierOrderHeaderId;
    }

    public void setSupplierOrderHeaderId(int supplierOrderHeaderId) {
        this.supplierOrderHeaderId = supplierOrderHeaderId;
    }

    public Clinic getClinic() {
        return clinic;
    }

    public void setClinic(Clinic clinic) {
        this.clinic = clinic;
    }

    public ClinicSupplierOrderStatus getOrderStatus() {
        return clinicSupplierOrderStatus;
    }

    public void setOrderStatus(ClinicSupplierOrderStatus clinicSupplierOrderStatus) {
        this.clinicSupplierOrderStatus = clinicSupplierOrderStatus;
    }

    public Supplier getSupplier() {
        return supplier;
    }

    public void setSupplier(Supplier supplier) {
        this.supplier = supplier;
    }

    public LocalDateTime getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(LocalDateTime createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    @Override
    public String toString() {
        return "SupplierOrderHeader{" +
                "supplierOrderHeaderId=" + supplierOrderHeaderId +
                ", clinic=" + clinic +
                ", clinicSupplierOrderStatus=" + clinicSupplierOrderStatus +
                ", createdDateTime=" + createdDateTime +
                '}';
    }
}
