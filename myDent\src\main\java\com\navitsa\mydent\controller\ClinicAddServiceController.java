package com.navitsa.mydent.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.ClinicAddService;
import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.services.ClinicAddServiceServices;


@RestController
public class ClinicAddServiceController {
	
	 
    private final ClinicAddServiceServices clinicAddServiceServices;
 
    @Autowired
    public ClinicAddServiceController(ClinicAddServiceServices clinicAddServiceServices) {
        this.clinicAddServiceServices = clinicAddServiceServices;
    }
    @PostMapping("/saveclinicAddService")
    public ClinicAddService saveSupplier(@RequestBody ClinicAddService  clinicAddService ) {
        return clinicAddServiceServices.saveClinicAddService(clinicAddService);
    }

    @GetMapping("/ClinicAddServiceList")
    public List<ClinicAddService> getAllClinicAddService() {
        return clinicAddServiceServices.findAllClinicAddService();
    }

    @GetMapping("/getClinicAddServiceById/{id}")
    public ClinicAddService getSupplierById(@PathVariable int id) {
        return clinicAddServiceServices. getClinicAddServiceById(id);
    }

    @PutMapping("/updateClinicAddService/{id}")
    public ResponseEntity<ClinicAddService> updateClinicAddService(@PathVariable int id, @RequestBody ClinicAddService ClinicAddServiceDetails) {
        return ResponseEntity.ok(clinicAddServiceServices.updateClinicAddService(id, ClinicAddServiceDetails));
    }
}
