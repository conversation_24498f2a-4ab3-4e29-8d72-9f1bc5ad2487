package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.ClinicSchedule;
import com.navitsa.mydent.services.ClinicScheduleServices;
import com.navitsa.mydent.services.ClinicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class ClinicScheduleController {

    @Autowired
    private ClinicScheduleServices clinicScheduleServices;
    @Autowired
    private ClinicService clinicService;

    @PostMapping("/saveSchedule")
    public ClinicSchedule saveSchedule(@RequestBody ClinicSchedule clinicSchedule){
        return clinicScheduleServices.saveSchedules(clinicSchedule);
    }

    @GetMapping("/clinicId") // Ensure this matches the request in the frontend
    public ResponseEntity<Integer> getClinicIdByUserId(@RequestParam Integer userId) {
        Integer clinicId = clinicService.findClinicIdByUserId(userId);
        if (clinicId != null) {
            return ResponseEntity.ok(clinicId);
        } else {
            return ResponseEntity.notFound().build(); // Handle not found case
        }
    }

    @GetMapping("/scheduleList")
    public List<ClinicSchedule> getAllSchedules(){ return clinicScheduleServices.findAllSchedules(); }

    @PutMapping("/updateSchedule/{id}")
    public ResponseEntity<ClinicSchedule> updateSchedule(@PathVariable int id, @RequestBody ClinicSchedule clinicScheduleDetails) {
        return ResponseEntity.ok(clinicScheduleServices.updateSchedule(id, clinicScheduleDetails));
    }

    @GetMapping("/getScheduleById/{id}")
    public ClinicSchedule getScheduleById(@PathVariable int id) {
        return clinicScheduleServices.getScheduleById(id);
    }

    @DeleteMapping("/deleteSchedule/{id}")
    public ResponseEntity<Void> deleteSchedule(@PathVariable int id) {
        clinicScheduleServices.deleteShedule(id);
        return ResponseEntity.noContent().build();
    }
}
