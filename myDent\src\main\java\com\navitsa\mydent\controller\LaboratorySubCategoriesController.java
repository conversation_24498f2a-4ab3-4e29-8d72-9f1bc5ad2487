package com.navitsa.mydent.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.LaboratorySubCategories;
import com.navitsa.mydent.services.LaboratorySubCategoriesService;

@RestController
public class LaboratorySubCategoriesController {
	
	private final LaboratorySubCategoriesService laboratorySubCategoriesService;

    @Autowired
    public LaboratorySubCategoriesController(LaboratorySubCategoriesService laboratorySubCategoriesService) {
        this.laboratorySubCategoriesService = laboratorySubCategoriesService;
    }
    
    @GetMapping("/getLaboratorySubCategoriesList/{id}")
    public List<LaboratorySubCategories> getSubCategoriesById(@PathVariable int id) {
        return laboratorySubCategoriesService.getLaboratorySubCategoriesById(id);
    }

}
