package com.navitsa.mydent.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.Customer;
import com.navitsa.mydent.services.CustomerService;

@RestController
public class CustomerController {
	 @Autowired
	    private CustomerService customerService;

	    @PostMapping("/saveCustomer")
	    public Customer saveCustomer(@RequestBody Customer customer) {
	        return customerService.saveCustomer(customer);
	    }

	    @GetMapping(value = "/customerList")
	    public List<Customer> getAllCustomers() {
	        return customerService.findAllCustomers();
	    }

	    @PutMapping("/updateCustomer/{id}")
	    public ResponseEntity<Customer> updateCustomer(@PathVariable int id, @RequestBody Customer customerDetails) {
	        return ResponseEntity.ok(customerService.updateCustomer(id, customerDetails));
	    }

	    @GetMapping("/getCustomerById/{id}")
	    public Customer getCustomerById(@PathVariable int id) {
	        return customerService.getCustomerById(id);
	    }

	    @DeleteMapping("/deleteCustomer/{id}")
	    public ResponseEntity<Void> deleteCustomer(@PathVariable int id) {
	        customerService.deleteCustomer(id);
	        return ResponseEntity.noContent().build();
	    }
	    
	    @GetMapping("/getCustomerByuserId/{id}")
	    public Customer getCustomerByuserId(@PathVariable int id) {
	        return customerService.getCustomerByUserId(id);
	    }
}

