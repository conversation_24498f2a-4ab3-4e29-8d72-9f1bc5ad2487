<div class="appointment-container">
  <div class="header-row">
    <h2>Make Appointment</h2>
  </div>

  <form [formGroup]="appointmentForm" (ngSubmit)="onSubmit()">
    <!-- First Name and Last Name Row -->
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="firstName" class="form-label">First Name</label>
        <input
          type="text"
          formControlName="firstName"
          id="firstName"
          class="form-control"
        />
        <div
          *ngIf="
            appointmentForm.get('firstName')?.invalid &&
            (appointmentForm.get('firstName')?.dirty ||
              appointmentForm.get('firstName')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('firstName')?.errors?.['required']">
            First Name is required.
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <label for="lastName" class="form-label">Last Name</label>
        <input
          type="text"
          formControlName="lastName"
          id="lastName"
          class="form-control"
        />
        <div
          *ngIf="
            appointmentForm.get('lastName')?.invalid &&
            (appointmentForm.get('lastName')?.dirty ||
              appointmentForm.get('lastName')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('lastName')?.errors?.['required']">
            Last Name is required.
          </div>
        </div>
      </div>
    </div>

    <!-- Email and Telephone Row -->
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="email" class="form-label">Email</label>
        <input
          type="email"
          formControlName="email"
          id="email"
          class="form-control"
        />
        <div
          *ngIf="
            appointmentForm.get('email')?.invalid &&
            (appointmentForm.get('email')?.dirty ||
              appointmentForm.get('email')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('email')?.errors?.['required']">
            Email is required.
          </div>
          <div *ngIf="appointmentForm.get('email')?.errors?.['email']">
            Invalid email format.
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <label for="telephone" class="form-label">Telephone</label>
        <input
          type="tel"
          formControlName="telephone"
          id="telephone"
          class="form-control"
        />
        <div
          *ngIf="
            appointmentForm.get('telephone')?.invalid &&
            (appointmentForm.get('telephone')?.dirty ||
              appointmentForm.get('telephone')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('telephone')?.errors?.['required']">
            Telephone is required.
          </div>
          <div *ngIf="appointmentForm.get('telephone')?.errors?.['pattern']">
            Invalid telephone number format.
          </div>
        </div>
      </div>
    </div>

    <!-- Service and Date Row -->
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="preferredservice" class="form-label"
          >Preferred Service</label
        >
        <div class="custom-arrow">
          <select
            formControlName="preferredservice"
            id="preferredservice"
            class="form-control"
            required
          >
            <option value="" selected disabled>Select</option>
            <option value="1">Dental Bonding</option>
            <option value="2">Cosmetic Fillings</option>
            <option value="3">Invisalign</option>
            <option value="4">Teeth Cleanings</option>
            <option value="5">Root Canal Therapy</option>
            <option value="6">Dental Sealants</option>
          </select>
        </div>
      </div>

      <div class="col-md-6">
        <label for="preferredDate" class="form-label">Preferred Date</label>
        <input
          type="date"
          formControlName="fromDate"
          id="fromDate"
          class="form-control"
          [min]="currentDate"
          required
        />
        <div
          *ngIf="
            appointmentForm.get('fromDate')?.invalid &&
            (appointmentForm.get('fromDate')?.dirty ||
              appointmentForm.get('fromDate')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('fromDate')?.errors?.['required']">
            Date From is required.
          </div>
        </div>
      </div>
    </div>

    <!-- Time Row -->
    <div class="row">
      <label for="preferredTime" class="form-label">Preferred Time</label>
    </div>
    <div class="row mb-3">
      <div class="col-md-6">
        <label for="fromTime" class="form-label">From</label>
        <input
          type="time"
          formControlName="fromTime"
          id="fromTime"
          class="form-control"
          required
        />
        <div
          *ngIf="
            appointmentForm.get('fromTime')?.invalid &&
            (appointmentForm.get('fromTime')?.dirty ||
              appointmentForm.get('fromTime')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('fromTime')?.errors?.['required']">
            Preferred Time From is required.
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <label for="toTime" class="form-label">To</label>
        <input
          type="time"
          formControlName="toTime"
          id="toTime"
          class="form-control"
          required
        />
        <div
          *ngIf="
            appointmentForm.get('toTime')?.invalid &&
            (appointmentForm.get('toTime')?.dirty ||
              appointmentForm.get('toTime')?.touched)
          "
          class="text-danger"
        >
          <div *ngIf="appointmentForm.get('toTime')?.errors?.['required']">
            Preferred Time To is required.
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-3">
      <div class="col text-end">
        <button
          type="button"
          class="btn btn-secondary me-3"
          (click)="cancelAppointment()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="appointmentForm.invalid"
        >
          Done
        </button>
      </div>
    </div>
  </form>
</div>

<!-- Show Appoinment Date Table -->
<!-- <div class="modal" [ngClass]="{ show: showModal }" (click)="closeModal()">
    <span class="close-btn"[ngClass]="{ show: showModal }" (click)="closeModal()">&times;</span>

       <div class="modal-container">
            
            <div class="modal-header">
                <h2>Select Preferred Time</h2>
                <button class="modal-close-btn" id="closeModalBtn">&times;</button>
            </div>

            <div class="modal-body">
                <div class="form-group">
                    <label for="doctorSelect">Select Doctor</label>
                    <select id="doctorSelect" class="select-doctor">
                        <option>Dr. Aminda Hearath</option>
                        <option>Dr. John Doe</option>
                        <option>Dr. Jane Smith</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>Available Time</label>
                    <div class="time-slot-grid">
                        
                        <button class="time-slot disabled">7.00-8.00</button>
                        <button class="time-slot disabled">8.00-9.00</button>
                        <button class="time-slot available">9.00-10.00</button>
                        <button class="time-slot selected">10.00-11.00</button>
                        
                        <button class="time-slot disabled">11.00-12.00</button>
                        <button class="time-slot available">4.00-5.00</button>
                        <button class="time-slot available">6.00-7.00</button>
                        <button class="time-slot available">7.00-8.00</button>
                        
                        <button class="time-slot available">8.00-9.00</button>

                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="save-btn" (click)="closeModal()" >Save</button>
            </div>
  </div> -->

<!-- Modal -->
<div
  class="modal fade bd-example-modal-lg"
  id="exampleModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg">
  
    <div class="modal-content">
      
      <div class="">

        <h5 class="modal-title font-bold" id="exampleModalLabel" style="font-weight: bold;">
          Select Preferred Time
        </h5>
      </div>

      <div class="mt-3">
        <div class="form-group">
          <label class="mb-3"style="font-weight: bold;">Select Doctor</label>

          <select id="preferredservice" class="form-control" required>
            <option value="" selected disabled>Select</option>
            <option value="1">Dental Bonding</option>
            <option value="2">Cosmetic Fillings</option>
            <option value="3">Invisalign</option>
            <option value="4">Teeth Cleanings</option>
            <option value="5">Root Canal Therapy</option>
            <option value="6">Dental Sealants</option>
          </select>
        </div>
        <div class="mt-3">
          <label class="mb-3" style="font-weight: bold;">Available Time</label>
          <div class="time-slot-grid">
            <button class="time-slot disabled" disabled>7.00-8.00</button>
            <button class="time-slot disabled">8.00-9.00</button>
            <button class="time-slot available">9.00-10.00</button>
            <button class="time-slot available">10.00-11.00</button>

            <button class="time-slot disabled">11.00-12.00</button>
            <button class="time-slot available">4.00-5.00</button>
            <button class="time-slot available">5.00-6.00</button>
            <button class="time-slot available">6.00-7.00</button>

            <button class="time-slot available">7.00-8.00</button>
            <button class="time-slot selected">8.00-9.00</button>
          </div>
        </div>
        <div class="mt-3 mb-3" style="display: flex; justify-content: space-between;" >
         
   <!-- <button
              style="
                width: 90px;
                border-radius: 50px;
                padding-block: 4px;
                background: linear-gradient(to right, #4a3120, #b74438);
                border: none;
                color: white;
              "
            >
              close
            </button>
            <button
              style="
                width: 90px;
                border-radius: 50px;
                padding-block: 4px;
                background: linear-gradient(to right, #fb751e, #b93426);
                border: none;
                color: white;
              "
            >
              save
            </button> -->
 <button
          type="button"
          class="btn btn-secondary me-3"
          (click)="closeModal()"
        >
          close
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          
        >
          Save
        </button>

        </div>
      </div>
    </div>
  </div>

  <!-- Button to Open Modal -->
  <!-- <button class="btn btn-primary" (click)="openModal()">Open Modal</button> -->
</div>
