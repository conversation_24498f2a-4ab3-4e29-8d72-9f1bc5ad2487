{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Appointments } from 'src/app/modules/appointments/appointments';\nimport { Clinic } from '../clinic';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/modules/appointments/appointments.service\";\nimport * as i3 from \"@angular/common\";\nfunction ClinicNewAppointmentComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" First Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_10_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Last Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_15_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Invalid email format. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_21_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵtemplate(2, ClinicNewAppointmentComponent_div_21_div_2_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.appointmentForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.appointmentForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Telephone is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Invalid telephone number format. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_26_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵtemplate(2, ClinicNewAppointmentComponent_div_26_div_2_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.appointmentForm.get(\"telephone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.appointmentForm.get(\"telephone\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_51_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Date From is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_51_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r4.appointmentForm.get(\"fromDate\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_60_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Preferred Time From is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_60_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.appointmentForm.get(\"fromTime\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicNewAppointmentComponent_div_65_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Preferred Time To is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicNewAppointmentComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, ClinicNewAppointmentComponent_div_65_div_1_Template, 2, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.appointmentForm.get(\"toTime\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nclass ClinicNewAppointmentComponent {\n  constructor(fb, appointmentsService) {\n    this.fb = fb;\n    this.appointmentsService = appointmentsService;\n    this.defaultDate = new Date().toISOString().substr(0, 10);\n    this.currentDate = this.getCurrentDate();\n    this.appointment = new Appointments();\n    this.appointmentsList = [];\n    // Pagination\n    this.currentPage = 0;\n    this.totalPages = 0;\n    this.showModal = false;\n    const today = new Date(); // Get today's date\n    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD\n    // Initialize form\n    this.appointmentForm = this.fb.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      telephone: ['', [Validators.required, Validators.pattern(/^\\+?[0-9]{7,15}$/)]],\n      preferredservice: [''],\n      fromDate: [{\n        value: this.defaultDate,\n        disabled: false\n      }, Validators.required],\n      fromTime: ['', Validators.required],\n      toTime: ['', Validators.required]\n    }, {});\n  }\n  ngOnInit() {\n    const clinicId = localStorage.getItem('clinicId');\n    // console.log('Clinic ID:', clinicId);\n    this.appointmentForm.get('fromDate')?.valueChanges.subscribe(date => {\n      if (date) {\n        this.checkAppointments(date, String(clinicId), this.currentPage);\n      }\n    });\n  }\n  getCurrentDate() {\n    const today = new Date();\n    const dd = String(today.getDate()).padStart(2, '0');\n    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!\n    const yyyy = today.getFullYear();\n    return `${yyyy}-${mm}-${dd}`;\n  }\n  cancelAppointment() {\n    this.appointmentForm.reset();\n    this.appointmentsList = [];\n  }\n  timeCheaker() {\n    const fromTime = this.appointmentForm.get('fromTime')?.value;\n    const toTime = this.appointmentForm.get('toTime')?.value;\n    const selectedDate = this.appointmentForm.get('fromDate')?.value; // assuming a date field\n    console.log('Selected Date:', selectedDate);\n    if (!fromTime || !toTime || !selectedDate) {\n      Swal.fire('warning', 'Please select Date, From Time and To Time.', 'warning');\n      return false;\n    }\n    // Parse selected date\n    const selected = new Date(selectedDate);\n    const today = new Date();\n    // Convert times to total minutes\n    const [fromHours, fromMinutes] = fromTime.split(':').map(Number);\n    const [toHours, toMinutes] = toTime.split(':').map(Number);\n    const fromTotal = fromHours * 60 + fromMinutes;\n    const toTotal = toHours * 60 + toMinutes;\n    // Validate time range\n    if (fromTotal >= toTotal) {\n      Swal.fire('warning', 'From time must be before To time.', 'warning');\n      return false;\n    }\n    // Check if selected date is today\n    const isToday = selected.getFullYear() === today.getFullYear() && selected.getMonth() === today.getMonth() && selected.getDate() === today.getDate();\n    if (isToday) {\n      const currentTime = today.getHours() * 60 + today.getMinutes();\n      if (fromTotal < currentTime) {\n        Swal.fire('warning', 'From time cannot be earlier than current time for today.', 'warning');\n        return false;\n      }\n    }\n    return true;\n  }\n  onSubmit() {\n    if (this.timeCheaker() === false) {\n      return;\n    }\n    if (this.appointmentForm.valid) {\n      const formValues = this.appointmentForm.value;\n      // Get the logged-in clinic ID from localStorage\n      const clinicId = localStorage.getItem('clinicId');\n      console.log('Clinic ID from localStorage:', clinicId);\n      if (!clinicId) {\n        console.error('Clinic ID not found in localStorage.');\n        return;\n      }\n      this.appointment = {\n        ...this.appointment,\n        firstName: formValues.firstName,\n        lastName: formValues.lastName,\n        email: formValues.email,\n        telephone: formValues.telephone,\n        preferredservice: formValues.preferredservice,\n        fromDate: formValues.fromDate,\n        fromTime: formValues.fromTime,\n        toTime: formValues.toTime,\n        clinics: new Clinic()\n        // address: formValues.address,\n        // city: formValues.city,\n        // state: formValues.state,\n        // district: formValues.district,\n        // nearestCity: formValues.nearestCity,\n        // userName: formValues.username,\n        // password: formValues.password,\n        // toDate: formValues.toDate,\n        // clinics: new Clinic(),\n        // customer: new Customer()\n      };\n\n      this.appointment.clinics.clinicId = Number(clinicId); // Set the clinic ID\n      this.appointmentsService.saveAppointments(this.appointment).subscribe(response => {\n        if (response.status === true) {\n          Swal.fire('success', response.message, 'success');\n          this.appointmentForm.reset();\n        } else {\n          Swal.fire('Error', response.message, 'error');\n          this.appointmentsList = [];\n        }\n      }, error => {\n        // 409 is the CONFLICT status\n        // alert(error.error.message);\n        Swal.fire('Error', 'Time slot already booked.', 'error');\n        // const message = error?.error?.message;\n        // if (error.status === 409) {\n        //   alert('Selected time slot is already taken. Please choose another.');\n        // } else if (error.status === 401) {\n        //   alert('You are not authorized to make this request. Please log in again.');\n        // } else {\n        //   console.error('Unexpected error:', error);\n        // }\n      });\n    } else {\n      this.appointmentsList = [];\n      console.warn('Form is invalid.');\n    }\n  }\n  // Show table Modal\n  openModal() {\n    this.showModal = true;\n  }\n  // Method to close the modal\n  closeModal() {\n    this.showModal = false;\n    this.appointmentsList = [];\n  }\n  checkAppointments(fromDate, clinicId, currentPage) {\n    this.appointmentsService.getAllAppointmentsInDates(Number(clinicId), fromDate, currentPage).subscribe(response => {\n      if (response.status) {\n        console.log(clinicId);\n        this.openModal();\n        this.appointmentsList = response.appointments;\n        this.currentPage = response.currentPage;\n        this.totalPages = response.totalPages;\n      }\n    }, error => {\n      console.error('Error checking appointments:', error);\n      this.closeModal();\n    });\n  }\n  nextPage() {\n    const clinicId = localStorage.getItem('clinicId');\n    if (this.currentPage < this.totalPages - 1) {\n      this.checkAppointments(this.appointmentForm.get('fromDate')?.value, String(clinicId), this.currentPage + 1);\n    }\n    console.log(this.currentPage);\n  }\n  prevPage() {\n    const clinicId = localStorage.getItem('clinicId');\n    if (this.currentPage > 0) {\n      this.checkAppointments(this.appointmentForm.get('fromDate')?.value, String(clinicId), this.currentPage - 1);\n    }\n  }\n  checkAppointments2(fromDate, clinicId, currentPage) {\n    return this.appointmentsService.getAllAppointmentsInDates(Number(clinicId), fromDate, currentPage);\n  }\n  static #_ = this.ɵfac = function ClinicNewAppointmentComponent_Factory(t) {\n    return new (t || ClinicNewAppointmentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AppointmentsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClinicNewAppointmentComponent,\n    selectors: [[\"app-clinic-new-appointment\"]],\n    decls: 126,\n    vars: 10,\n    consts: [[1, \"appointment-container\"], [1, \"header-row\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"mb-3\"], [1, \"col-md-6\"], [\"for\", \"firstName\", 1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"firstName\", \"id\", \"firstName\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"lastName\", \"id\", \"lastName\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"formControlName\", \"email\", \"id\", \"email\", 1, \"form-control\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"tel\", \"formControlName\", \"telephone\", \"id\", \"telephone\", 1, \"form-control\"], [\"for\", \"preferredservice\", 1, \"form-label\"], [1, \"custom-arrow\"], [\"formControlName\", \"preferredservice\", \"id\", \"preferredservice\", \"required\", \"\", 1, \"form-control\"], [\"value\", \"\", \"selected\", \"\", \"disabled\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"value\", \"5\"], [\"value\", \"6\"], [\"for\", \"preferredDate\", 1, \"form-label\"], [\"type\", \"date\", \"formControlName\", \"fromDate\", \"id\", \"fromDate\", \"required\", \"\", 1, \"form-control\", 3, \"min\"], [1, \"row\"], [\"for\", \"preferredTime\", 1, \"form-label\"], [\"for\", \"fromTime\", 1, \"form-label\"], [\"type\", \"time\", \"formControlName\", \"fromTime\", \"id\", \"fromTime\", \"required\", \"\", 1, \"form-control\"], [\"for\", \"toTime\", 1, \"form-label\"], [\"type\", \"time\", \"formControlName\", \"toTime\", \"id\", \"toTime\", \"required\", \"\", 1, \"form-control\"], [1, \"col\", \"text-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"me-3\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"id\", \"exampleModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"exampleModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\", \"bd-example-modal-lg\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\", \"font-bold\", 2, \"font-weight\", \"bold\"], [1, \"mt-3\"], [1, \"form-group\"], [1, \"mb-3\", 2, \"font-weight\", \"bold\"], [\"id\", \"preferredservice\", \"required\", \"\", 1, \"form-control\"], [1, \"time-slot-grid\"], [\"disabled\", \"\", 1, \"time-slot\", \"disabled\"], [1, \"time-slot\", \"disabled\"], [1, \"time-slot\", \"available\"], [1, \"time-slot\", \"selected\"], [1, \"mt-3\", \"mb-3\", 2, \"display\", \"flex\", \"justify-content\", \"space-between\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [1, \"text-danger\"], [4, \"ngIf\"]],\n    template: function ClinicNewAppointmentComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n        i0.ɵɵtext(3, \"Make Appointment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"form\", 2);\n        i0.ɵɵlistener(\"ngSubmit\", function ClinicNewAppointmentComponent_Template_form_ngSubmit_4_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"label\", 5);\n        i0.ɵɵtext(8, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(9, \"input\", 6);\n        i0.ɵɵtemplate(10, ClinicNewAppointmentComponent_div_10_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 4)(12, \"label\", 8);\n        i0.ɵɵtext(13, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(14, \"input\", 9);\n        i0.ɵɵtemplate(15, ClinicNewAppointmentComponent_div_15_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 3)(17, \"div\", 4)(18, \"label\", 10);\n        i0.ɵɵtext(19, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(20, \"input\", 11);\n        i0.ɵɵtemplate(21, ClinicNewAppointmentComponent_div_21_Template, 3, 2, \"div\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 4)(23, \"label\", 12);\n        i0.ɵɵtext(24, \"Telephone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(25, \"input\", 13);\n        i0.ɵɵtemplate(26, ClinicNewAppointmentComponent_div_26_Template, 3, 2, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 3)(28, \"div\", 4)(29, \"label\", 14);\n        i0.ɵɵtext(30, \"Preferred Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"div\", 15)(32, \"select\", 16)(33, \"option\", 17);\n        i0.ɵɵtext(34, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"option\", 18);\n        i0.ɵɵtext(36, \"Dental Bonding\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"option\", 19);\n        i0.ɵɵtext(38, \"Cosmetic Fillings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"option\", 20);\n        i0.ɵɵtext(40, \"Invisalign\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"option\", 21);\n        i0.ɵɵtext(42, \"Teeth Cleanings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"option\", 22);\n        i0.ɵɵtext(44, \"Root Canal Therapy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"option\", 23);\n        i0.ɵɵtext(46, \"Dental Sealants\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(47, \"div\", 4)(48, \"label\", 24);\n        i0.ɵɵtext(49, \"Preferred Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(50, \"input\", 25);\n        i0.ɵɵtemplate(51, ClinicNewAppointmentComponent_div_51_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(52, \"div\", 26)(53, \"label\", 27);\n        i0.ɵɵtext(54, \"Preferred Time\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(55, \"div\", 3)(56, \"div\", 4)(57, \"label\", 28);\n        i0.ɵɵtext(58, \"From\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(59, \"input\", 29);\n        i0.ɵɵtemplate(60, ClinicNewAppointmentComponent_div_60_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(61, \"div\", 4)(62, \"label\", 30);\n        i0.ɵɵtext(63, \"To\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(64, \"input\", 31);\n        i0.ɵɵtemplate(65, ClinicNewAppointmentComponent_div_65_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(66, \"div\", 3)(67, \"div\", 32)(68, \"button\", 33);\n        i0.ɵɵlistener(\"click\", function ClinicNewAppointmentComponent_Template_button_click_68_listener() {\n          return ctx.cancelAppointment();\n        });\n        i0.ɵɵtext(69, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"button\", 34);\n        i0.ɵɵtext(71, \" Done \");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 36)(74, \"div\", 37)(75, \"div\", 38)(76, \"h5\", 39);\n        i0.ɵɵtext(77, \" Select Preferred Time \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(78, \"div\", 40)(79, \"div\", 41)(80, \"label\", 42);\n        i0.ɵɵtext(81, \"Select Doctor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(82, \"select\", 43)(83, \"option\", 17);\n        i0.ɵɵtext(84, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"option\", 18);\n        i0.ɵɵtext(86, \"Dental Bonding\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(87, \"option\", 19);\n        i0.ɵɵtext(88, \"Cosmetic Fillings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(89, \"option\", 20);\n        i0.ɵɵtext(90, \"Invisalign\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(91, \"option\", 21);\n        i0.ɵɵtext(92, \"Teeth Cleanings\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(93, \"option\", 22);\n        i0.ɵɵtext(94, \"Root Canal Therapy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(95, \"option\", 23);\n        i0.ɵɵtext(96, \"Dental Sealants\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(97, \"div\", 40)(98, \"label\", 42);\n        i0.ɵɵtext(99, \"Available Time\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(100, \"div\", 44)(101, \"button\", 45);\n        i0.ɵɵtext(102, \"7.00-8.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(103, \"button\", 46);\n        i0.ɵɵtext(104, \"8.00-9.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(105, \"button\", 47);\n        i0.ɵɵtext(106, \"9.00-10.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(107, \"button\", 47);\n        i0.ɵɵtext(108, \"10.00-11.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(109, \"button\", 46);\n        i0.ɵɵtext(110, \"11.00-12.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(111, \"button\", 47);\n        i0.ɵɵtext(112, \"4.00-5.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(113, \"button\", 47);\n        i0.ɵɵtext(114, \"5.00-6.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(115, \"button\", 47);\n        i0.ɵɵtext(116, \"6.00-7.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(117, \"button\", 47);\n        i0.ɵɵtext(118, \"7.00-8.00\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(119, \"button\", 48);\n        i0.ɵɵtext(120, \"8.00-9.00\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(121, \"div\", 49)(122, \"button\", 33);\n        i0.ɵɵlistener(\"click\", function ClinicNewAppointmentComponent_Template_button_click_122_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵtext(123, \" close \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(124, \"button\", 50);\n        i0.ɵɵtext(125, \" Save \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_4_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.appointmentForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && (((tmp_1_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.dirty) || ((tmp_1_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_2_0.touched)));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && (((tmp_3_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.dirty) || ((tmp_3_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_3_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_4_0.dirty) || ((tmp_4_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_4_0.touched)));\n        i0.ɵɵadvance(24);\n        i0.ɵɵproperty(\"min\", ctx.currentDate);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.appointmentForm.get(\"fromDate\")) == null ? null : tmp_6_0.invalid) && (((tmp_6_0 = ctx.appointmentForm.get(\"fromDate\")) == null ? null : tmp_6_0.dirty) || ((tmp_6_0 = ctx.appointmentForm.get(\"fromDate\")) == null ? null : tmp_6_0.touched)));\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.appointmentForm.get(\"fromTime\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = ctx.appointmentForm.get(\"fromTime\")) == null ? null : tmp_7_0.dirty) || ((tmp_7_0 = ctx.appointmentForm.get(\"fromTime\")) == null ? null : tmp_7_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.appointmentForm.get(\"toTime\")) == null ? null : tmp_8_0.invalid) && (((tmp_8_0 = ctx.appointmentForm.get(\"toTime\")) == null ? null : tmp_8_0.dirty) || ((tmp_8_0 = ctx.appointmentForm.get(\"toTime\")) == null ? null : tmp_8_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.appointmentForm.invalid);\n      }\n    },\n    dependencies: [i3.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\"body[_ngcontent-%COMP%] {\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n  color: #333;\\n  margin: 0;\\n  padding: 0;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n}\\n\\n.appointment-container[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n  \\n\\n  background-color: #fff;\\n  border: 1px solid #ff6600;\\n  display: flex;\\n  flex-direction: column;\\n  padding: 20px;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n\\n.header-row[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  margin-bottom: 40px;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n[_ngcontent-%COMP%]:root {\\n    --primary-orange: #F97B22; \\n\\n    --light-orange-bg: #FFF4EC; \\n\\n    --light-orange-border: #FBCDB4; \\n\\n    --light-text: #6c757d;\\n    --white: #ffffff;\\n    --disabled-bg: #f8f9fa; \\n\\n    --disabled-text: #adb5bd; \\n\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n    font-weight: 500;\\n    color: var(--light-text);\\n    margin-bottom: 8px;\\n}\\n\\n\\n\\n.time-slot-grid[_ngcontent-%COMP%] {\\n    display: grid;\\n    \\n\\n    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr)); \\n    gap: 10px;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n.time-slot[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    font-size: 0.9rem;\\n    font-weight: 500;\\n    border: 1px solid;\\n    border-radius: 8px;\\n     border-color: #0d6efd ;\\n     color:#0d6efd ;\\n    cursor: pointer;\\n    text-align: center;\\n    transition: all 0.2s ease;\\n}\\n\\n\\n\\n\\n\\n\\n.time-slot.disabled[_ngcontent-%COMP%] {\\n    background-color:  #d8dcdf;\\n    border-color:  #d8dcdf;\\n    color: white;\\n    cursor: not-allowed;\\n    border-radius:  #d8dcdf;\\n         border-radius: 8px;\\n\\n}\\n\\n\\n\\n.time-slot.available[_ngcontent-%COMP%] {\\n    background-color: var(--light-orange-bg);\\n    border-color: var(--light-orange-border);\\n\\n}\\n\\n\\n\\n\\n\\n\\n.time-slot.available[_ngcontent-%COMP%]:hover {\\n      background: linear-gradient(to left,  #0d6efd);\\n      color: #f8f9fa;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n.time-slot.selected[_ngcontent-%COMP%] {\\n   background-color: #0d6efd;\\n    border-color: #0d6efd;\\n    color: white;\\n    font-weight: 600;\\n}\\n\\n\\n.modal-content[_ngcontent-%COMP%]{\\n  padding: 30px;\\n}\\n.modal-dialog[_ngcontent-%COMP%]{\\n\\n  width: 608px;\\n}\\n.f[_ngcontent-%COMP%]{\\n  justify-items: left;\\n  align-content:space-between ;\\n}\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { ClinicNewAppointmentComponent };", "map": {"version": 3, "names": ["Validators", "Appointments", "Clinic", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ClinicNewAppointmentComponent_div_10_div_1_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "appointmentForm", "get", "errors", "ClinicNewAppointmentComponent_div_15_div_1_Template", "ctx_r1", "ClinicNewAppointmentComponent_div_21_div_1_Template", "ClinicNewAppointmentComponent_div_21_div_2_Template", "ctx_r2", "tmp_1_0", "ClinicNewAppointmentComponent_div_26_div_1_Template", "ClinicNewAppointmentComponent_div_26_div_2_Template", "ctx_r3", "ClinicNewAppointmentComponent_div_51_div_1_Template", "ctx_r4", "ClinicNewAppointmentComponent_div_60_div_1_Template", "ctx_r5", "ClinicNewAppointmentComponent_div_65_div_1_Template", "ctx_r6", "ClinicNewAppointmentComponent", "constructor", "fb", "appointmentsService", "defaultDate", "Date", "toISOString", "substr", "currentDate", "getCurrentDate", "appointment", "appointmentsList", "currentPage", "totalPages", "showModal", "today", "substring", "group", "firstName", "required", "lastName", "email", "telephone", "pattern", "preferredservice", "fromDate", "value", "disabled", "fromTime", "toTime", "ngOnInit", "clinicId", "localStorage", "getItem", "valueChanges", "subscribe", "date", "checkAppointments", "String", "dd", "getDate", "padStart", "mm", "getMonth", "yyyy", "getFullYear", "cancelAppointment", "reset", "timeC<PERSON>ker", "selectedDate", "console", "log", "fire", "selected", "fromHours", "fromMinutes", "split", "map", "Number", "toHours", "toMinutes", "fromTotal", "toTotal", "isToday", "currentTime", "getHours", "getMinutes", "onSubmit", "valid", "formValues", "error", "clinics", "saveAppointments", "response", "status", "message", "warn", "openModal", "closeModal", "getAllAppointmentsInDates", "appointments", "nextPage", "prevPage", "checkAppointments2", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AppointmentsService", "_2", "selectors", "decls", "vars", "consts", "template", "ClinicNewAppointmentComponent_Template", "rf", "ctx", "ɵɵlistener", "ClinicNewAppointmentComponent_Template_form_ngSubmit_4_listener", "ɵɵelement", "ClinicNewAppointmentComponent_div_10_Template", "ClinicNewAppointmentComponent_div_15_Template", "ClinicNewAppointmentComponent_div_21_Template", "ClinicNewAppointmentComponent_div_26_Template", "ClinicNewAppointmentComponent_div_51_Template", "ClinicNewAppointmentComponent_div_60_Template", "ClinicNewAppointmentComponent_div_65_Template", "ClinicNewAppointmentComponent_Template_button_click_68_listener", "ClinicNewAppointmentComponent_Template_button_click_122_listener", "invalid", "dirty", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_6_0", "tmp_7_0", "tmp_8_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-new-appointment\\clinic-new-appointment.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-new-appointment\\clinic-new-appointment.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Appointments } from 'src/app/modules/appointments/appointments';\r\nimport { AppointmentsService } from 'src/app/modules/appointments/appointments.service';\r\nimport { Clinic } from '../clinic';\r\nimport Swal from 'sweetalert2';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-clinic-new-appointment',\r\n  templateUrl: './clinic-new-appointment.component.html',\r\n  styleUrls: ['./clinic-new-appointment.component.css'],\r\n})\r\nexport class ClinicNewAppointmentComponent {\r\n  appointmentForm: FormGroup;\r\n  defaultDate: string = new Date().toISOString().substr(0, 10);\r\n  currentDate: string = this.getCurrentDate();\r\n  appointment: Appointments = new Appointments();\r\n  appointmentsList: Appointments[] = [];\r\n\r\n  // Pagination\r\n  currentPage: number = 0;\r\n  totalPages: number = 0;\r\n\r\n  showModal: boolean = false;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private appointmentsService: AppointmentsService\r\n  ) {\r\n    const today = new Date(); // Get today's date\r\n    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD\r\n\r\n    // Initialize form\r\n    this.appointmentForm = this.fb.group(\r\n      {\r\n        firstName: ['', Validators.required],\r\n        lastName: ['', Validators.required],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        telephone: [\r\n          '',\r\n          [Validators.required, Validators.pattern(/^\\+?[0-9]{7,15}$/)],\r\n        ],\r\n        preferredservice: [''],\r\n        fromDate: [\r\n          { value: this.defaultDate, disabled: false },\r\n          Validators.required,\r\n        ],\r\n        fromTime: ['', Validators.required],\r\n        toTime: ['', Validators.required],\r\n      },\r\n      {}\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    const clinicId = localStorage.getItem('clinicId');\r\n    // console.log('Clinic ID:', clinicId);\r\n\r\n    this.appointmentForm.get('fromDate')?.valueChanges.subscribe((date) => {\r\n      if (date) {\r\n        this.checkAppointments(date, String(clinicId), this.currentPage);\r\n      }\r\n    });\r\n  }\r\n\r\n  private getCurrentDate(): string {\r\n    const today = new Date();\r\n    const dd = String(today.getDate()).padStart(2, '0');\r\n    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!\r\n    const yyyy = today.getFullYear();\r\n    return `${yyyy}-${mm}-${dd}`;\r\n  }\r\n\r\n  cancelAppointment(): void {\r\n    this.appointmentForm.reset();\r\n    this.appointmentsList = [];\r\n  }\r\n\r\n  timeCheaker(): boolean {\r\n    const fromTime = this.appointmentForm.get('fromTime')?.value;\r\n    const toTime = this.appointmentForm.get('toTime')?.value;\r\n    const selectedDate = this.appointmentForm.get('fromDate')?.value; // assuming a date field\r\n    console.log('Selected Date:', selectedDate);\r\n\r\n    if (!fromTime || !toTime || !selectedDate) {\r\n      Swal.fire(\r\n        'warning',\r\n        'Please select Date, From Time and To Time.',\r\n        'warning'\r\n      );\r\n      return false;\r\n    }\r\n\r\n    // Parse selected date\r\n    const selected = new Date(selectedDate);\r\n    const today = new Date();\r\n\r\n    // Convert times to total minutes\r\n    const [fromHours, fromMinutes] = fromTime.split(':').map(Number);\r\n    const [toHours, toMinutes] = toTime.split(':').map(Number);\r\n    const fromTotal = fromHours * 60 + fromMinutes;\r\n    const toTotal = toHours * 60 + toMinutes;\r\n\r\n    // Validate time range\r\n    if (fromTotal >= toTotal) {\r\n      Swal.fire('warning', 'From time must be before To time.', 'warning');\r\n      return false;\r\n    }\r\n\r\n    // Check if selected date is today\r\n    const isToday =\r\n      selected.getFullYear() === today.getFullYear() &&\r\n      selected.getMonth() === today.getMonth() &&\r\n      selected.getDate() === today.getDate();\r\n\r\n    if (isToday) {\r\n      const currentTime = today.getHours() * 60 + today.getMinutes();\r\n      if (fromTotal < currentTime) {\r\n        Swal.fire(\r\n          'warning',\r\n          'From time cannot be earlier than current time for today.',\r\n          'warning'\r\n        );\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  onSubmit(): void {\r\n    if (this.timeCheaker() === false) {\r\n      return;\r\n    }\r\n    if (this.appointmentForm.valid) {\r\n      const formValues = this.appointmentForm.value;\r\n\r\n      // Get the logged-in clinic ID from localStorage\r\n      const clinicId = localStorage.getItem('clinicId');\r\n      console.log('Clinic ID from localStorage:', clinicId);\r\n      if (!clinicId) {\r\n        console.error('Clinic ID not found in localStorage.');\r\n        return;\r\n      }\r\n\r\n      this.appointment = {\r\n        ...this.appointment, // Spread the existing appointment properties\r\n        firstName: formValues.firstName,\r\n        lastName: formValues.lastName,\r\n        email: formValues.email,\r\n        telephone: formValues.telephone,\r\n        preferredservice: formValues.preferredservice,\r\n        fromDate: formValues.fromDate,\r\n        fromTime: formValues.fromTime,\r\n        toTime: formValues.toTime,\r\n        clinics: new Clinic(),\r\n\r\n        // address: formValues.address,\r\n        // city: formValues.city,\r\n        // state: formValues.state,\r\n        // district: formValues.district,\r\n        // nearestCity: formValues.nearestCity,\r\n        // userName: formValues.username,\r\n        // password: formValues.password,\r\n        // toDate: formValues.toDate,\r\n        // clinics: new Clinic(),\r\n        // customer: new Customer()\r\n      };\r\n      this.appointment.clinics.clinicId = Number(clinicId); // Set the clinic ID\r\n\r\n      this.appointmentsService.saveAppointments(this.appointment).subscribe(\r\n        (response) => {\r\n          if (response.status === true) {\r\n            Swal.fire('success', response.message, 'success');\r\n            this.appointmentForm.reset();\r\n\r\n          } else {\r\n            Swal.fire('Error', response.message, 'error');\r\n            this.appointmentsList = [];\r\n          }\r\n        },\r\n        (error) => {\r\n          // 409 is the CONFLICT status\r\n          // alert(error.error.message);\r\n          Swal.fire('Error', 'Time slot already booked.', 'error');\r\n\r\n          // const message = error?.error?.message;\r\n\r\n          // if (error.status === 409) {\r\n          //   alert('Selected time slot is already taken. Please choose another.');\r\n          // } else if (error.status === 401) {\r\n          //   alert('You are not authorized to make this request. Please log in again.');\r\n          // } else {\r\n          //   console.error('Unexpected error:', error);\r\n          // }\r\n        }\r\n      );\r\n    } else {\r\n      this.appointmentsList = [];\r\n      console.warn('Form is invalid.');\r\n    }\r\n  }\r\n\r\n  // Show table Modal\r\n  openModal() {\r\n    this.showModal = true;\r\n  }\r\n\r\n  // Method to close the modal\r\n  closeModal() {\r\n    this.showModal = false;\r\n    this.appointmentsList = [];\r\n  }\r\n\r\n  checkAppointments(\r\n    fromDate: string,\r\n    clinicId: string,\r\n    currentPage: number\r\n  ): void {\r\n    this.appointmentsService\r\n      .getAllAppointmentsInDates(Number(clinicId), fromDate, currentPage)\r\n      .subscribe(\r\n        (response) => {\r\n          if (response.status) {\r\n            console.log(clinicId);\r\n            this.openModal();\r\n            this.appointmentsList = response.appointments;\r\n            this.currentPage = response.currentPage;\r\n            this.totalPages = response.totalPages;\r\n          }\r\n        },\r\n        (error) => {\r\n          console.error('Error checking appointments:', error);\r\n          this.closeModal();\r\n        }\r\n      );\r\n  }\r\n\r\n  nextPage() {\r\n    const clinicId = localStorage.getItem('clinicId');\r\n    if (this.currentPage < this.totalPages - 1) {\r\n      this.checkAppointments(\r\n        this.appointmentForm.get('fromDate')?.value,\r\n        String(clinicId),\r\n        this.currentPage + 1\r\n      );\r\n    }\r\n    console.log(this.currentPage);\r\n  }\r\n\r\n  prevPage() {\r\n    const clinicId = localStorage.getItem('clinicId');\r\n    if (this.currentPage > 0) {\r\n      this.checkAppointments(\r\n        this.appointmentForm.get('fromDate')?.value,\r\n        String(clinicId),\r\n        this.currentPage - 1\r\n      );\r\n    }\r\n  }\r\n\r\n  checkAppointments2(\r\n    fromDate: string,\r\n    clinicId: string,\r\n    currentPage: number\r\n  ): Observable<any> {\r\n    return this.appointmentsService.getAllAppointmentsInDates(\r\n      Number(clinicId),\r\n      fromDate,\r\n      currentPage\r\n    );\r\n  }\r\n\r\n  // checkAndSubmit(){\r\n  //   const clinicId = localStorage.getItem('clinicId');\r\n  //   if (!clinicId) {\r\n  //         console.error('Clinic ID not found in localStorage.');\r\n  //         return;\r\n  //       }\r\n  // this.checkAppointments(this.appointmentForm.value.fromDate, clinicId, this.currentPage);\r\n\r\n  // if (this.showModal) {\r\n  //   console.log('Modal is open, not submitting the form.');\r\n  //   return;\r\n  // }else{\r\n  //   this.onSubmit();\r\n  // }\r\n\r\n  // }\r\n}\r\n", "<div class=\"appointment-container\">\r\n  <div class=\"header-row\">\r\n    <h2>Make Appointment</h2>\r\n  </div>\r\n\r\n  <form [formGroup]=\"appointmentForm\" (ngSubmit)=\"onSubmit()\">\r\n    <!-- First Name and Last Name Row -->\r\n    <div class=\"row mb-3\">\r\n      <div class=\"col-md-6\">\r\n        <label for=\"firstName\" class=\"form-label\">First Name</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"firstName\"\r\n          id=\"firstName\"\r\n          class=\"form-control\"\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('firstName')?.invalid &&\r\n            (appointmentForm.get('firstName')?.dirty ||\r\n              appointmentForm.get('firstName')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('firstName')?.errors?.['required']\">\r\n            First Name is required.\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <label for=\"lastName\" class=\"form-label\">Last Name</label>\r\n        <input\r\n          type=\"text\"\r\n          formControlName=\"lastName\"\r\n          id=\"lastName\"\r\n          class=\"form-control\"\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('lastName')?.invalid &&\r\n            (appointmentForm.get('lastName')?.dirty ||\r\n              appointmentForm.get('lastName')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('lastName')?.errors?.['required']\">\r\n            Last Name is required.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Email and Telephone Row -->\r\n    <div class=\"row mb-3\">\r\n      <div class=\"col-md-6\">\r\n        <label for=\"email\" class=\"form-label\">Email</label>\r\n        <input\r\n          type=\"email\"\r\n          formControlName=\"email\"\r\n          id=\"email\"\r\n          class=\"form-control\"\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('email')?.invalid &&\r\n            (appointmentForm.get('email')?.dirty ||\r\n              appointmentForm.get('email')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('email')?.errors?.['required']\">\r\n            Email is required.\r\n          </div>\r\n          <div *ngIf=\"appointmentForm.get('email')?.errors?.['email']\">\r\n            Invalid email format.\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <label for=\"telephone\" class=\"form-label\">Telephone</label>\r\n        <input\r\n          type=\"tel\"\r\n          formControlName=\"telephone\"\r\n          id=\"telephone\"\r\n          class=\"form-control\"\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('telephone')?.invalid &&\r\n            (appointmentForm.get('telephone')?.dirty ||\r\n              appointmentForm.get('telephone')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('telephone')?.errors?.['required']\">\r\n            Telephone is required.\r\n          </div>\r\n          <div *ngIf=\"appointmentForm.get('telephone')?.errors?.['pattern']\">\r\n            Invalid telephone number format.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Service and Date Row -->\r\n    <div class=\"row mb-3\">\r\n      <div class=\"col-md-6\">\r\n        <label for=\"preferredservice\" class=\"form-label\"\r\n          >Preferred Service</label\r\n        >\r\n        <div class=\"custom-arrow\">\r\n          <select\r\n            formControlName=\"preferredservice\"\r\n            id=\"preferredservice\"\r\n            class=\"form-control\"\r\n            required\r\n          >\r\n            <option value=\"\" selected disabled>Select</option>\r\n            <option value=\"1\">Dental Bonding</option>\r\n            <option value=\"2\">Cosmetic Fillings</option>\r\n            <option value=\"3\">Invisalign</option>\r\n            <option value=\"4\">Teeth Cleanings</option>\r\n            <option value=\"5\">Root Canal Therapy</option>\r\n            <option value=\"6\">Dental Sealants</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <label for=\"preferredDate\" class=\"form-label\">Preferred Date</label>\r\n        <input\r\n          type=\"date\"\r\n          formControlName=\"fromDate\"\r\n          id=\"fromDate\"\r\n          class=\"form-control\"\r\n          [min]=\"currentDate\"\r\n          required\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('fromDate')?.invalid &&\r\n            (appointmentForm.get('fromDate')?.dirty ||\r\n              appointmentForm.get('fromDate')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('fromDate')?.errors?.['required']\">\r\n            Date From is required.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Time Row -->\r\n    <div class=\"row\">\r\n      <label for=\"preferredTime\" class=\"form-label\">Preferred Time</label>\r\n    </div>\r\n    <div class=\"row mb-3\">\r\n      <div class=\"col-md-6\">\r\n        <label for=\"fromTime\" class=\"form-label\">From</label>\r\n        <input\r\n          type=\"time\"\r\n          formControlName=\"fromTime\"\r\n          id=\"fromTime\"\r\n          class=\"form-control\"\r\n          required\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('fromTime')?.invalid &&\r\n            (appointmentForm.get('fromTime')?.dirty ||\r\n              appointmentForm.get('fromTime')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('fromTime')?.errors?.['required']\">\r\n            Preferred Time From is required.\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <label for=\"toTime\" class=\"form-label\">To</label>\r\n        <input\r\n          type=\"time\"\r\n          formControlName=\"toTime\"\r\n          id=\"toTime\"\r\n          class=\"form-control\"\r\n          required\r\n        />\r\n        <div\r\n          *ngIf=\"\r\n            appointmentForm.get('toTime')?.invalid &&\r\n            (appointmentForm.get('toTime')?.dirty ||\r\n              appointmentForm.get('toTime')?.touched)\r\n          \"\r\n          class=\"text-danger\"\r\n        >\r\n          <div *ngIf=\"appointmentForm.get('toTime')?.errors?.['required']\">\r\n            Preferred Time To is required.\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row mb-3\">\r\n      <div class=\"col text-end\">\r\n        <button\r\n          type=\"button\"\r\n          class=\"btn btn-secondary me-3\"\r\n          (click)=\"cancelAppointment()\"\r\n        >\r\n          Cancel\r\n        </button>\r\n        <button\r\n          type=\"submit\"\r\n          class=\"btn btn-primary\"\r\n          [disabled]=\"appointmentForm.invalid\"\r\n        >\r\n          Done\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </form>\r\n</div>\r\n\r\n<!-- Show Appoinment Date Table -->\r\n<!-- <div class=\"modal\" [ngClass]=\"{ show: showModal }\" (click)=\"closeModal()\">\r\n    <span class=\"close-btn\"[ngClass]=\"{ show: showModal }\" (click)=\"closeModal()\">&times;</span>\r\n\r\n       <div class=\"modal-container\">\r\n            \r\n            <div class=\"modal-header\">\r\n                <h2>Select Preferred Time</h2>\r\n                <button class=\"modal-close-btn\" id=\"closeModalBtn\">&times;</button>\r\n            </div>\r\n\r\n            <div class=\"modal-body\">\r\n                <div class=\"form-group\">\r\n                    <label for=\"doctorSelect\">Select Doctor</label>\r\n                    <select id=\"doctorSelect\" class=\"select-doctor\">\r\n                        <option>Dr. Aminda Hearath</option>\r\n                        <option>Dr. John Doe</option>\r\n                        <option>Dr. Jane Smith</option>\r\n                    </select>\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                    <label>Available Time</label>\r\n                    <div class=\"time-slot-grid\">\r\n                        \r\n                        <button class=\"time-slot disabled\">7.00-8.00</button>\r\n                        <button class=\"time-slot disabled\">8.00-9.00</button>\r\n                        <button class=\"time-slot available\">9.00-10.00</button>\r\n                        <button class=\"time-slot selected\">10.00-11.00</button>\r\n                        \r\n                        <button class=\"time-slot disabled\">11.00-12.00</button>\r\n                        <button class=\"time-slot available\">4.00-5.00</button>\r\n                        <button class=\"time-slot available\">6.00-7.00</button>\r\n                        <button class=\"time-slot available\">7.00-8.00</button>\r\n                        \r\n                        <button class=\"time-slot available\">8.00-9.00</button>\r\n\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"modal-footer\">\r\n                <button class=\"save-btn\" (click)=\"closeModal()\" >Save</button>\r\n            </div>\r\n  </div> -->\r\n\r\n<!-- Modal -->\r\n<div\r\n  class=\"modal fade bd-example-modal-lg\"\r\n  id=\"exampleModal\"\r\n  tabindex=\"-1\"\r\n  aria-labelledby=\"exampleModalLabel\"\r\n  aria-hidden=\"true\"\r\n>\r\n  <div class=\"modal-dialog modal-lg\">\r\n  \r\n    <div class=\"modal-content\">\r\n      \r\n      <div class=\"\">\r\n\r\n        <h5 class=\"modal-title font-bold\" id=\"exampleModalLabel\" style=\"font-weight: bold;\">\r\n          Select Preferred Time\r\n        </h5>\r\n      </div>\r\n\r\n      <div class=\"mt-3\">\r\n        <div class=\"form-group\">\r\n          <label class=\"mb-3\"style=\"font-weight: bold;\">Select Doctor</label>\r\n\r\n          <select id=\"preferredservice\" class=\"form-control\" required>\r\n            <option value=\"\" selected disabled>Select</option>\r\n            <option value=\"1\">Dental Bonding</option>\r\n            <option value=\"2\">Cosmetic Fillings</option>\r\n            <option value=\"3\">Invisalign</option>\r\n            <option value=\"4\">Teeth Cleanings</option>\r\n            <option value=\"5\">Root Canal Therapy</option>\r\n            <option value=\"6\">Dental Sealants</option>\r\n          </select>\r\n        </div>\r\n        <div class=\"mt-3\">\r\n          <label class=\"mb-3\" style=\"font-weight: bold;\">Available Time</label>\r\n          <div class=\"time-slot-grid\">\r\n            <button class=\"time-slot disabled\" disabled>7.00-8.00</button>\r\n            <button class=\"time-slot disabled\">8.00-9.00</button>\r\n            <button class=\"time-slot available\">9.00-10.00</button>\r\n            <button class=\"time-slot available\">10.00-11.00</button>\r\n\r\n            <button class=\"time-slot disabled\">11.00-12.00</button>\r\n            <button class=\"time-slot available\">4.00-5.00</button>\r\n            <button class=\"time-slot available\">5.00-6.00</button>\r\n            <button class=\"time-slot available\">6.00-7.00</button>\r\n\r\n            <button class=\"time-slot available\">7.00-8.00</button>\r\n            <button class=\"time-slot selected\">8.00-9.00</button>\r\n          </div>\r\n        </div>\r\n        <div class=\"mt-3 mb-3\" style=\"display: flex; justify-content: space-between;\" >\r\n         \r\n   <!-- <button\r\n              style=\"\r\n                width: 90px;\r\n                border-radius: 50px;\r\n                padding-block: 4px;\r\n                background: linear-gradient(to right, #4a3120, #b74438);\r\n                border: none;\r\n                color: white;\r\n              \"\r\n            >\r\n              close\r\n            </button>\r\n            <button\r\n              style=\"\r\n                width: 90px;\r\n                border-radius: 50px;\r\n                padding-block: 4px;\r\n                background: linear-gradient(to right, #fb751e, #b93426);\r\n                border: none;\r\n                color: white;\r\n              \"\r\n            >\r\n              save\r\n            </button> -->\r\n <button\r\n          type=\"button\"\r\n          class=\"btn btn-secondary me-3\"\r\n          (click)=\"closeModal()\"\r\n        >\r\n          close\r\n        </button>\r\n        <button\r\n          type=\"submit\"\r\n          class=\"btn btn-primary\"\r\n          \r\n        >\r\n          Save\r\n        </button>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Button to Open Modal -->\r\n  <!-- <button class=\"btn btn-primary\" (click)=\"openModal()\">Open Modal</button> -->\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAQ,2CAA2C;AAExE,SAASC,MAAM,QAAQ,WAAW;AAClC,OAAOC,IAAI,MAAM,aAAa;;;;;;;ICmBpBC,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAC,mDAAA,kBAEM;IACRL,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;;;;;IAsBlEZ,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAS,mDAAA,kBAEM;IACRb,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAM,MAAA,CAAAJ,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IAyBjEZ,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA6D;IAC3DD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAbRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAW,mDAAA,kBAEM;IACNf,EAAA,CAAAI,UAAA,IAAAY,mDAAA,kBAEM;IACRhB,EAAA,CAAAG,YAAA,EAAM;;;;;;IANEH,EAAA,CAAAM,SAAA,GAAwD;IAAxDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAS,MAAA,CAAAP,eAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IAGxDZ,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAO,UAAA,UAAAW,OAAA,GAAAD,MAAA,CAAAP,eAAA,CAAAC,GAAA,4BAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,UAAqD;;;;;IAsB3DZ,EAAA,CAAAC,cAAA,UAAoE;IAClED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAbRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAe,mDAAA,kBAEM;IACNnB,EAAA,CAAAI,UAAA,IAAAgB,mDAAA,kBAEM;IACRpB,EAAA,CAAAG,YAAA,EAAM;;;;;;IANEH,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAa,MAAA,CAAAX,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;IAG5DZ,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAO,UAAA,UAAAW,OAAA,GAAAG,MAAA,CAAAX,eAAA,CAAAC,GAAA,gCAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,YAA2D;;;;;IAiDjEZ,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAkB,mDAAA,kBAEM;IACRtB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAe,MAAA,CAAAb,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IA6BjEZ,EAAA,CAAAC,cAAA,UAAmE;IACjED,EAAA,CAAAE,MAAA,yCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAoB,mDAAA,kBAEM;IACRxB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAiB,MAAA,CAAAf,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IAuBjEZ,EAAA,CAAAC,cAAA,UAAiE;IAC/DD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAVRH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAsB,mDAAA,kBAEM;IACR1B,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAM,SAAA,GAAyD;IAAzDN,EAAA,CAAAO,UAAA,UAAAC,OAAA,GAAAmB,MAAA,CAAAjB,eAAA,CAAAC,GAAA,6BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;;;ADhMzE,MAKagB,6BAA6B;EAaxCC,YACUC,EAAe,EACfC,mBAAwC;IADxC,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAb7B,KAAAC,WAAW,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5D,KAAAC,WAAW,GAAW,IAAI,CAACC,cAAc,EAAE;IAC3C,KAAAC,WAAW,GAAiB,IAAIzC,YAAY,EAAE;IAC9C,KAAA0C,gBAAgB,GAAmB,EAAE;IAErC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,UAAU,GAAW,CAAC;IAEtB,KAAAC,SAAS,GAAY,KAAK;IAMxB,MAAMC,KAAK,GAAG,IAAIV,IAAI,EAAE,CAAC,CAAC;IAC1B,IAAI,CAACD,WAAW,GAAGW,KAAK,CAACT,WAAW,EAAE,CAACU,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAEzD;IACA,IAAI,CAAClC,eAAe,GAAG,IAAI,CAACoB,EAAE,CAACe,KAAK,CAClC;MACEC,SAAS,EAAE,CAAC,EAAE,EAAElD,UAAU,CAACmD,QAAQ,CAAC;MACpCC,QAAQ,EAAE,CAAC,EAAE,EAAEpD,UAAU,CAACmD,QAAQ,CAAC;MACnCE,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACqD,KAAK,CAAC,CAAC;MACpDC,SAAS,EAAE,CACT,EAAE,EACF,CAACtD,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACuD,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAC9D;MACDC,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,QAAQ,EAAE,CACR;QAAEC,KAAK,EAAE,IAAI,CAACtB,WAAW;QAAEuB,QAAQ,EAAE;MAAK,CAAE,EAC5C3D,UAAU,CAACmD,QAAQ,CACpB;MACDS,QAAQ,EAAE,CAAC,EAAE,EAAE5D,UAAU,CAACmD,QAAQ,CAAC;MACnCU,MAAM,EAAE,CAAC,EAAE,EAAE7D,UAAU,CAACmD,QAAQ;KACjC,EACD,EAAE,CACH;EACH;EAEAW,QAAQA,CAAA;IACN,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD;IAEA,IAAI,CAACnD,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEmD,YAAY,CAACC,SAAS,CAAEC,IAAI,IAAI;MACpE,IAAIA,IAAI,EAAE;QACR,IAAI,CAACC,iBAAiB,CAACD,IAAI,EAAEE,MAAM,CAACP,QAAQ,CAAC,EAAE,IAAI,CAACnB,WAAW,CAAC;;IAEpE,CAAC,CAAC;EACJ;EAEQH,cAAcA,CAAA;IACpB,MAAMM,KAAK,GAAG,IAAIV,IAAI,EAAE;IACxB,MAAMkC,EAAE,GAAGD,MAAM,CAACvB,KAAK,CAACyB,OAAO,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnD,MAAMC,EAAE,GAAGJ,MAAM,CAACvB,KAAK,CAAC4B,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D,MAAMG,IAAI,GAAG7B,KAAK,CAAC8B,WAAW,EAAE;IAChC,OAAO,GAAGD,IAAI,IAAIF,EAAE,IAAIH,EAAE,EAAE;EAC9B;EAEAO,iBAAiBA,CAAA;IACf,IAAI,CAAChE,eAAe,CAACiE,KAAK,EAAE;IAC5B,IAAI,CAACpC,gBAAgB,GAAG,EAAE;EAC5B;EAEAqC,WAAWA,CAAA;IACT,MAAMpB,QAAQ,GAAG,IAAI,CAAC9C,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE2C,KAAK;IAC5D,MAAMG,MAAM,GAAG,IAAI,CAAC/C,eAAe,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAE2C,KAAK;IACxD,MAAMuB,YAAY,GAAG,IAAI,CAACnE,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE2C,KAAK,CAAC,CAAC;IAClEwB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,YAAY,CAAC;IAE3C,IAAI,CAACrB,QAAQ,IAAI,CAACC,MAAM,IAAI,CAACoB,YAAY,EAAE;MACzC9E,IAAI,CAACiF,IAAI,CACP,SAAS,EACT,4CAA4C,EAC5C,SAAS,CACV;MACD,OAAO,KAAK;;IAGd;IACA,MAAMC,QAAQ,GAAG,IAAIhD,IAAI,CAAC4C,YAAY,CAAC;IACvC,MAAMlC,KAAK,GAAG,IAAIV,IAAI,EAAE;IAExB;IACA,MAAM,CAACiD,SAAS,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAChE,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG/B,MAAM,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAC1D,MAAMG,SAAS,GAAGP,SAAS,GAAG,EAAE,GAAGC,WAAW;IAC9C,MAAMO,OAAO,GAAGH,OAAO,GAAG,EAAE,GAAGC,SAAS;IAExC;IACA,IAAIC,SAAS,IAAIC,OAAO,EAAE;MACxB3F,IAAI,CAACiF,IAAI,CAAC,SAAS,EAAE,mCAAmC,EAAE,SAAS,CAAC;MACpE,OAAO,KAAK;;IAGd;IACA,MAAMW,OAAO,GACXV,QAAQ,CAACR,WAAW,EAAE,KAAK9B,KAAK,CAAC8B,WAAW,EAAE,IAC9CQ,QAAQ,CAACV,QAAQ,EAAE,KAAK5B,KAAK,CAAC4B,QAAQ,EAAE,IACxCU,QAAQ,CAACb,OAAO,EAAE,KAAKzB,KAAK,CAACyB,OAAO,EAAE;IAExC,IAAIuB,OAAO,EAAE;MACX,MAAMC,WAAW,GAAGjD,KAAK,CAACkD,QAAQ,EAAE,GAAG,EAAE,GAAGlD,KAAK,CAACmD,UAAU,EAAE;MAC9D,IAAIL,SAAS,GAAGG,WAAW,EAAE;QAC3B7F,IAAI,CAACiF,IAAI,CACP,SAAS,EACT,0DAA0D,EAC1D,SAAS,CACV;QACD,OAAO,KAAK;;;IAIhB,OAAO,IAAI;EACb;EAEAe,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnB,WAAW,EAAE,KAAK,KAAK,EAAE;MAChC;;IAEF,IAAI,IAAI,CAAClE,eAAe,CAACsF,KAAK,EAAE;MAC9B,MAAMC,UAAU,GAAG,IAAI,CAACvF,eAAe,CAAC4C,KAAK;MAE7C;MACA,MAAMK,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjDiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEpB,QAAQ,CAAC;MACrD,IAAI,CAACA,QAAQ,EAAE;QACbmB,OAAO,CAACoB,KAAK,CAAC,sCAAsC,CAAC;QACrD;;MAGF,IAAI,CAAC5D,WAAW,GAAG;QACjB,GAAG,IAAI,CAACA,WAAW;QACnBQ,SAAS,EAAEmD,UAAU,CAACnD,SAAS;QAC/BE,QAAQ,EAAEiD,UAAU,CAACjD,QAAQ;QAC7BC,KAAK,EAAEgD,UAAU,CAAChD,KAAK;QACvBC,SAAS,EAAE+C,UAAU,CAAC/C,SAAS;QAC/BE,gBAAgB,EAAE6C,UAAU,CAAC7C,gBAAgB;QAC7CC,QAAQ,EAAE4C,UAAU,CAAC5C,QAAQ;QAC7BG,QAAQ,EAAEyC,UAAU,CAACzC,QAAQ;QAC7BC,MAAM,EAAEwC,UAAU,CAACxC,MAAM;QACzB0C,OAAO,EAAE,IAAIrG,MAAM;QAEnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;OACD;;MACD,IAAI,CAACwC,WAAW,CAAC6D,OAAO,CAACxC,QAAQ,GAAG2B,MAAM,CAAC3B,QAAQ,CAAC,CAAC,CAAC;MAEtD,IAAI,CAAC5B,mBAAmB,CAACqE,gBAAgB,CAAC,IAAI,CAAC9D,WAAW,CAAC,CAACyB,SAAS,CAClEsC,QAAQ,IAAI;QACX,IAAIA,QAAQ,CAACC,MAAM,KAAK,IAAI,EAAE;UAC5BvG,IAAI,CAACiF,IAAI,CAAC,SAAS,EAAEqB,QAAQ,CAACE,OAAO,EAAE,SAAS,CAAC;UACjD,IAAI,CAAC7F,eAAe,CAACiE,KAAK,EAAE;SAE7B,MAAM;UACL5E,IAAI,CAACiF,IAAI,CAAC,OAAO,EAAEqB,QAAQ,CAACE,OAAO,EAAE,OAAO,CAAC;UAC7C,IAAI,CAAChE,gBAAgB,GAAG,EAAE;;MAE9B,CAAC,EACA2D,KAAK,IAAI;QACR;QACA;QACAnG,IAAI,CAACiF,IAAI,CAAC,OAAO,EAAE,2BAA2B,EAAE,OAAO,CAAC;QAExD;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,CACF;KACF,MAAM;MACL,IAAI,CAACzC,gBAAgB,GAAG,EAAE;MAC1BuC,OAAO,CAAC0B,IAAI,CAAC,kBAAkB,CAAC;;EAEpC;EAEA;EACAC,SAASA,CAAA;IACP,IAAI,CAAC/D,SAAS,GAAG,IAAI;EACvB;EAEA;EACAgE,UAAUA,CAAA;IACR,IAAI,CAAChE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACH,gBAAgB,GAAG,EAAE;EAC5B;EAEA0B,iBAAiBA,CACfZ,QAAgB,EAChBM,QAAgB,EAChBnB,WAAmB;IAEnB,IAAI,CAACT,mBAAmB,CACrB4E,yBAAyB,CAACrB,MAAM,CAAC3B,QAAQ,CAAC,EAAEN,QAAQ,EAAEb,WAAW,CAAC,CAClEuB,SAAS,CACPsC,QAAQ,IAAI;MACX,IAAIA,QAAQ,CAACC,MAAM,EAAE;QACnBxB,OAAO,CAACC,GAAG,CAACpB,QAAQ,CAAC;QACrB,IAAI,CAAC8C,SAAS,EAAE;QAChB,IAAI,CAAClE,gBAAgB,GAAG8D,QAAQ,CAACO,YAAY;QAC7C,IAAI,CAACpE,WAAW,GAAG6D,QAAQ,CAAC7D,WAAW;QACvC,IAAI,CAACC,UAAU,GAAG4D,QAAQ,CAAC5D,UAAU;;IAEzC,CAAC,EACAyD,KAAK,IAAI;MACRpB,OAAO,CAACoB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,IAAI,CAACQ,UAAU,EAAE;IACnB,CAAC,CACF;EACL;EAEAG,QAAQA,CAAA;IACN,MAAMlD,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,IAAI,IAAI,CAACrB,WAAW,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACwB,iBAAiB,CACpB,IAAI,CAACvD,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE2C,KAAK,EAC3CY,MAAM,CAACP,QAAQ,CAAC,EAChB,IAAI,CAACnB,WAAW,GAAG,CAAC,CACrB;;IAEHsC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvC,WAAW,CAAC;EAC/B;EAEAsE,QAAQA,CAAA;IACN,MAAMnD,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,IAAI,IAAI,CAACrB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACyB,iBAAiB,CACpB,IAAI,CAACvD,eAAe,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE2C,KAAK,EAC3CY,MAAM,CAACP,QAAQ,CAAC,EAChB,IAAI,CAACnB,WAAW,GAAG,CAAC,CACrB;;EAEL;EAEAuE,kBAAkBA,CAChB1D,QAAgB,EAChBM,QAAgB,EAChBnB,WAAmB;IAEnB,OAAO,IAAI,CAACT,mBAAmB,CAAC4E,yBAAyB,CACvDrB,MAAM,CAAC3B,QAAQ,CAAC,EAChBN,QAAQ,EACRb,WAAW,CACZ;EACH;EAAC,QAAAwE,CAAA,G;qBAnQUpF,6BAA6B,EAAA5B,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7B1F,6BAA6B;IAAA2F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCb1C7H,EAAA,CAAAC,cAAA,aAAmC;QAE3BD,EAAA,CAAAE,MAAA,uBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAG3BH,EAAA,CAAAC,cAAA,cAA4D;QAAxBD,EAAA,CAAA+H,UAAA,sBAAAC,gEAAA;UAAA,OAAYF,GAAA,CAAA/B,QAAA,EAAU;QAAA,EAAC;QAEzD/F,EAAA,CAAAC,cAAA,aAAsB;QAEwBD,EAAA,CAAAE,MAAA,iBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5DH,EAAA,CAAAiI,SAAA,eAKE;QACFjI,EAAA,CAAAI,UAAA,KAAA8H,6CAAA,iBAWM;QACRlI,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACqBD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC1DH,EAAA,CAAAiI,SAAA,gBAKE;QACFjI,EAAA,CAAAI,UAAA,KAAA+H,6CAAA,iBAWM;QACRnI,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,cAAsB;QAEoBD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACnDH,EAAA,CAAAiI,SAAA,iBAKE;QACFjI,EAAA,CAAAI,UAAA,KAAAgI,6CAAA,iBAcM;QACRpI,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACsBD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC3DH,EAAA,CAAAiI,SAAA,iBAKE;QACFjI,EAAA,CAAAI,UAAA,KAAAiI,6CAAA,iBAcM;QACRrI,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,cAAsB;QAGfD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EACnB;QACDH,EAAA,CAAAC,cAAA,eAA0B;QAOaD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAClDH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACzCH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC5CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrCH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC7CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAKhDH,EAAA,CAAAC,cAAA,cAAsB;QAC0BD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpEH,EAAA,CAAAiI,SAAA,iBAOE;QACFjI,EAAA,CAAAI,UAAA,KAAAkI,6CAAA,iBAWM;QACRtI,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAC,cAAA,eAAiB;QAC+BD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEtEH,EAAA,CAAAC,cAAA,cAAsB;QAEuBD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrDH,EAAA,CAAAiI,SAAA,iBAME;QACFjI,EAAA,CAAAI,UAAA,KAAAmI,6CAAA,iBAWM;QACRvI,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACmBD,EAAA,CAAAE,MAAA,UAAE;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjDH,EAAA,CAAAiI,SAAA,iBAME;QACFjI,EAAA,CAAAI,UAAA,KAAAoI,6CAAA,iBAWM;QACRxI,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAKhBD,EAAA,CAAA+H,UAAA,mBAAAU,gEAAA;UAAA,OAASX,GAAA,CAAApD,iBAAA,EAAmB;QAAA,EAAC;QAE7B1E,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAIC;QACCD,EAAA,CAAAE,MAAA,cACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAqDjBH,EAAA,CAAAC,cAAA,eAMC;QAQSD,EAAA,CAAAE,MAAA,+BACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAGPH,EAAA,CAAAC,cAAA,eAAkB;QAEgCD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEnEH,EAAA,CAAAC,cAAA,kBAA4D;QACvBD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAClDH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACzCH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC5CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrCH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC7CH,EAAA,CAAAC,cAAA,kBAAkB;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAG9CH,EAAA,CAAAC,cAAA,eAAkB;QAC+BD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrEH,EAAA,CAAAC,cAAA,gBAA4B;QACkBD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC9DH,EAAA,CAAAC,cAAA,mBAAmC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACrDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACvDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAExDH,EAAA,CAAAC,cAAA,mBAAmC;QAAAD,EAAA,CAAAE,MAAA,oBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACvDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAEtDH,EAAA,CAAAC,cAAA,mBAAoC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACtDH,EAAA,CAAAC,cAAA,mBAAmC;QAAAD,EAAA,CAAAE,MAAA,kBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGzDH,EAAA,CAAAC,cAAA,gBAA+E;QA6B7ED,EAAA,CAAA+H,UAAA,mBAAAW,iEAAA;UAAA,OAASZ,GAAA,CAAApB,UAAA,EAAY;QAAA,EAAC;QAEtB1G,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,mBAIC;QACCD,EAAA,CAAAE,MAAA,eACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;QAtWTH,EAAA,CAAAM,SAAA,GAA6B;QAA7BN,EAAA,CAAAO,UAAA,cAAAuH,GAAA,CAAApH,eAAA,CAA6B;QAY1BV,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAW,OAAA,GAAA4G,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAO,OAAA,CAAAyH,OAAA,QAAAzH,OAAA,GAAA4G,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAO,OAAA,CAAA0H,KAAA,OAAA1H,OAAA,GAAA4G,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAO,OAAA,CAAA2H,OAAA,GAIF;QAkBE7I,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAuI,OAAA,GAAAhB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAmI,OAAA,CAAAH,OAAA,QAAAG,OAAA,GAAAhB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAmI,OAAA,CAAAF,KAAA,OAAAE,OAAA,GAAAhB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAmI,OAAA,CAAAD,OAAA,GAIF;QAqBE7I,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAwI,OAAA,GAAAjB,GAAA,CAAApH,eAAA,CAAAC,GAAA,4BAAAoI,OAAA,CAAAJ,OAAA,QAAAI,OAAA,GAAAjB,GAAA,CAAApH,eAAA,CAAAC,GAAA,4BAAAoI,OAAA,CAAAH,KAAA,OAAAG,OAAA,GAAAjB,GAAA,CAAApH,eAAA,CAAAC,GAAA,4BAAAoI,OAAA,CAAAF,OAAA,GAIF;QAqBE7I,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAAyI,OAAA,GAAAlB,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAqI,OAAA,CAAAL,OAAA,QAAAK,OAAA,GAAAlB,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAqI,OAAA,CAAAJ,KAAA,OAAAI,OAAA,GAAAlB,GAAA,CAAApH,eAAA,CAAAC,GAAA,gCAAAqI,OAAA,CAAAH,OAAA,GAIF;QA4CC7I,EAAA,CAAAM,SAAA,IAAmB;QAAnBN,EAAA,CAAAO,UAAA,QAAAuH,GAAA,CAAA1F,WAAA,CAAmB;QAIlBpC,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA0I,OAAA,GAAAnB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAsI,OAAA,CAAAN,OAAA,QAAAM,OAAA,GAAAnB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAsI,OAAA,CAAAL,KAAA,OAAAK,OAAA,GAAAnB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAsI,OAAA,CAAAJ,OAAA,GAIF;QAyBE7I,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA2I,OAAA,GAAApB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAuI,OAAA,CAAAP,OAAA,QAAAO,OAAA,GAAApB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAuI,OAAA,CAAAN,KAAA,OAAAM,OAAA,GAAApB,GAAA,CAAApH,eAAA,CAAAC,GAAA,+BAAAuI,OAAA,CAAAL,OAAA,GAIF;QAmBE7I,EAAA,CAAAM,SAAA,GAIF;QAJEN,EAAA,CAAAO,UAAA,WAAA4I,OAAA,GAAArB,GAAA,CAAApH,eAAA,CAAAC,GAAA,6BAAAwI,OAAA,CAAAR,OAAA,QAAAQ,OAAA,GAAArB,GAAA,CAAApH,eAAA,CAAAC,GAAA,6BAAAwI,OAAA,CAAAP,KAAA,OAAAO,OAAA,GAAArB,GAAA,CAAApH,eAAA,CAAAC,GAAA,6BAAAwI,OAAA,CAAAN,OAAA,GAIF;QAsBC7I,EAAA,CAAAM,SAAA,GAAoC;QAApCN,EAAA,CAAAO,UAAA,aAAAuH,GAAA,CAAApH,eAAA,CAAAiI,OAAA,CAAoC;;;;;;;SD9MjC/G,6BAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}