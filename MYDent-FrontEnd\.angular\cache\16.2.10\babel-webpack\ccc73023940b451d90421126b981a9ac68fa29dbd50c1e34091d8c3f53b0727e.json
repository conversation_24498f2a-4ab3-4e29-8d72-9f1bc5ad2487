{"ast": null, "code": "import { UserLoginComponent } from 'src/app/user/user-login/user-login.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nconst _c1 = function () {\n  return {\n    exact: false\n  };\n};\nclass ClinicSideBarComponent extends UserLoginComponent {\n  static #_ = this.ɵfac = /*@__PURE__*/function () {\n    let ɵClinicSideBarComponent_BaseFactory;\n    return function ClinicSideBarComponent_Factory(t) {\n      return (ɵClinicSideBarComponent_BaseFactory || (ɵClinicSideBarComponent_BaseFactory = i0.ɵɵgetInheritedFactory(ClinicSideBarComponent)))(t || ClinicSideBarComponent);\n    };\n  }();\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClinicSideBarComponent,\n    selectors: [[\"app-clinic-side-bar\"]],\n    features: [i0.ɵɵInheritDefinitionFeature],\n    decls: 49,\n    vars: 12,\n    consts: [[\"id\", \"sidebar\", 1, \"row\", \"py-4\", \"position-relative\", 2, \"height\", \"100%\", \"background-color\", \"white\", \"border-right\", \"1px solid rgb(240,240,240)\"], [1, \"col-12\", \"g-0\"], [1, \"nav\", \"flex-column\"], [\"routerLink\", \"dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-columns-gap\", 2, \"font-size\", \"20px\"], [2, \"margin-left\", \"10px\"], [1, \"p-0\", \"m-0\", 2, \"font-size\", \"15px\", \"font-weight\", \"500\"], [1, \"p-0\", \"m-0\", \"text-black-50\", 2, \"font-size\", \"11px\", \"font-weight\", \"400\"], [\"routerLink\", \"clinic-setup\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-nut\", 2, \"font-size\", \"20px\"], [\"routerLink\", \"appointments\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-tags\", 2, \"font-size\", \"20px\"], [\"routerLink\", \"laboratory-orders\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-list\", 2, \"font-size\", \"20px\"], [\"routerLink\", \"supplier-orders\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-list-check\", 2, \"font-size\", \"20px\"], [\"routerLink\", \"purchasing\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"bi\", \"bi-coin\", 2, \"font-size\", \"20px\"], [1, \"nav-link\", \"logout\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"bi\", \"bi-box-arrow-right\", 2, \"font-size\", \"20px\"], [2, \"margin-left\", \"10px\", \"font-size\", \"16px\", \"font-weight\", \"500\", \"color\", \"red\"]],\n    template: function ClinicSideBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"a\", 3);\n        i0.ɵɵelement(4, \"i\", 4);\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"p\", 6);\n        i0.ɵɵtext(7, \"Dashboard\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"p\", 7);\n        i0.ɵɵtext(9, \"Handle Analytics\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"a\", 8);\n        i0.ɵɵelement(11, \"i\", 9);\n        i0.ɵɵelementStart(12, \"div\", 5)(13, \"p\", 6);\n        i0.ɵɵtext(14, \"Setup\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"p\", 7);\n        i0.ɵɵtext(16, \"Update Clinic\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(17, \"a\", 10);\n        i0.ɵɵelement(18, \"i\", 11);\n        i0.ɵɵelementStart(19, \"div\", 5)(20, \"p\", 6);\n        i0.ɵɵtext(21, \"Appointments\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"p\", 7);\n        i0.ɵɵtext(23, \"Patient Appointments\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(24, \"a\", 12);\n        i0.ɵɵelement(25, \"i\", 13);\n        i0.ɵɵelementStart(26, \"div\", 5)(27, \"p\", 6);\n        i0.ɵɵtext(28, \"Lab Orders\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"p\", 7);\n        i0.ɵɵtext(30, \"All Laboratory Orders\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(31, \"a\", 14);\n        i0.ɵɵelement(32, \"i\", 15);\n        i0.ɵɵelementStart(33, \"div\", 5)(34, \"p\", 6);\n        i0.ɵɵtext(35, \"Supplier Orders\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"p\", 7);\n        i0.ɵɵtext(37, \"All Supplier Orders\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(38, \"a\", 16);\n        i0.ɵɵelement(39, \"i\", 17);\n        i0.ɵɵelementStart(40, \"div\", 5)(41, \"p\", 6);\n        i0.ɵɵtext(42, \"Purchasing\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"p\", 7);\n        i0.ɵɵtext(44, \"All Transactions\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(45, \"a\", 18);\n        i0.ɵɵlistener(\"click\", function ClinicSideBarComponent_Template_a_click_45_listener() {\n          return ctx.logout();\n        });\n        i0.ɵɵelement(46, \"i\", 19);\n        i0.ɵɵelementStart(47, \"span\", 20);\n        i0.ɵɵtext(48, \"Logout\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(6, _c0));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(7, _c0));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(8, _c0));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(9, _c1));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(10, _c1));\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(11, _c0));\n      }\n    },\n    dependencies: [i1.RouterLink, i1.RouterLinkActive],\n    styles: [\"\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 20%;\\n  \\n\\n  position: fixed;\\n  top: 80px;\\n  \\n\\n  left: 0;\\n  background-color: #f8f9fa;\\n  padding-top: 20px;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 3vw;\\n  color: black;\\n  \\n\\n  text-decoration: none;\\n  \\n\\n}\\n\\n.nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\n.logout[_ngcontent-%COMP%] {\\n  margin-top: auto;\\n  padding: 10px 3vw;\\n}\\n\\n.logout[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: red;\\n  \\n\\n}\\n\\n.nav-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  \\n\\n  font-size: 18px;\\n  \\n\\n  font-weight: bold;\\n  \\n\\n}\\n\\n\\n\\n@media (max-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 10%;\\n    \\n\\n  }\\n\\n  .nav-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n    \\n\\n  }\\n\\n\\n\\n}\\n\\n\\n\\n@media (min-width: 1024px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 20%;\\n    \\n\\n  }\\n\\n  .nav-link[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: inline;\\n    \\n\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    \\n\\n    top: 0;\\n    left: -100%;\\n    transition: left 0.3s;\\n  }\\n\\n  .sidebar.open[_ngcontent-%COMP%] {\\n    left: 0;\\n  }\\n}\\n\\n.active[_ngcontent-%COMP%]{\\n  position: relative;\\n  background: linear-gradient(to right, white 0%, white 50%, #fb761e27 100%);\\n}\\n.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .active[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:first-child{\\n  color: transparent;\\n  background: linear-gradient(to bottom, #fb761e, #b93526);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n}\\n.active[_ngcontent-%COMP%]::before{\\n  content: '';\\n  position: absolute;\\n  background: linear-gradient(to bottom, #fb761e, #b93526);\\n  height: 75%;\\n  width: 2px;\\n  transform: translate(50%,0%);\\n  right: 0px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { ClinicSideBarComponent };", "map": {"version": 3, "names": ["UserLoginComponent", "ClinicSideBarComponent", "_", "t", "_2", "selectors", "features", "i0", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ClinicSideBarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ClinicSideBarComponent_Template_a_click_45_listener", "logout", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "_c1"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\components\\clinic-side-bar\\clinic-side-bar.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\components\\clinic-side-bar\\clinic-side-bar.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { UserLoginComponent } from 'src/app/user/user-login/user-login.component';\r\n\r\n@Component({\r\n  selector: 'app-clinic-side-bar',\r\n  templateUrl: './clinic-side-bar.component.html',\r\n  styleUrls: ['./clinic-side-bar.component.css']\r\n})\r\nexport class ClinicSideBarComponent extends UserLoginComponent{\r\n\r\n  \r\n}\r\n", "<div class=\"row py-4 position-relative\" style=\"height: 100%;background-color: white; border-right:1px solid rgb(240,240,240) ;\" id=\"sidebar\">\r\n  <div class=\"col-12 g-0\">\r\n    <nav class=\"nav flex-column\" >\r\n      <a class=\"nav-link\" routerLink=\"dashboard\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:true}\">\r\n        <i class=\"bi bi-columns-gap\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Dashboard</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">Handle Analytics</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link\" routerLink=\"clinic-setup\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:true}\">\r\n        <i class=\"bi bi-nut\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Setup</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">Update Clinic</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link\" routerLink=\"appointments\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:true}\">\r\n        <i class=\"bi bi-tags\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Appointments</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">Patient Appointments</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link\" routerLink=\"laboratory-orders\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:false}\">\r\n        <i class=\"bi bi-list\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Lab Orders</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">All Laboratory Orders</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link\" routerLink=\"supplier-orders\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:false}\">\r\n        <i class=\"bi bi-list-check\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Supplier Orders</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">All Supplier Orders</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link\" routerLink=\"purchasing\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact:true}\">\r\n        <i class=\"bi bi-coin\" style=\"font-size: 20px;\"></i>\r\n        <div style=\"margin-left: 10px;\">\r\n          <p class=\"p-0 m-0\" style=\"font-size: 15px; font-weight: 500;\">Purchasing</p>\r\n          <p class=\"p-0 m-0 text-black-50\" style=\"font-size: 11px; font-weight: 400;\">All Transactions</p>\r\n        </div>\r\n      </a>\r\n      <a class=\"nav-link logout\" style=\"cursor: pointer;\" (click)=\"logout()\">\r\n          <i class=\"bi bi-box-arrow-right\" style=\"font-size: 20px;\"></i>\r\n          <span style=\"margin-left: 10px; font-size: 16px; font-weight: 500; color: red;\">Logout</span>\r\n      </a>\r\n    </nav>\r\n  </div>\r\n</div>\r\n\r\n\r\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,8CAA8C;;;;;;;;;;;;;AAEjF,MAKaC,sBAAuB,SAAQD,kBAAkB;EAAA,QAAAE,CAAA,G;;;qHAAjDD,sBAAsB,IAAAE,CAAA,IAAtBF,sBAAsB;IAAA;EAAA;EAAA,QAAAG,EAAA,G;UAAtBH,sBAAsB;IAAAI,SAAA;IAAAC,QAAA,GAAAC,EAAA,CAAAC,0BAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRnCP,EAAA,CAAAS,cAAA,aAA6I;QAIrIT,EAAA,CAAAU,SAAA,WAA0D;QAC1DV,EAAA,CAAAS,cAAA,aAAgC;QACgCT,EAAA,CAAAW,MAAA,gBAAS;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAC3EZ,EAAA,CAAAS,cAAA,WAA4E;QAAAT,EAAA,CAAAW,MAAA,uBAAgB;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGpGZ,EAAA,CAAAS,cAAA,YAAiH;QAC/GT,EAAA,CAAAU,SAAA,YAAkD;QAClDV,EAAA,CAAAS,cAAA,cAAgC;QACgCT,EAAA,CAAAW,MAAA,aAAK;QAAAX,EAAA,CAAAY,YAAA,EAAI;QACvEZ,EAAA,CAAAS,cAAA,YAA4E;QAAAT,EAAA,CAAAW,MAAA,qBAAa;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGjGZ,EAAA,CAAAS,cAAA,aAAiH;QAC/GT,EAAA,CAAAU,SAAA,aAAmD;QACnDV,EAAA,CAAAS,cAAA,cAAgC;QACgCT,EAAA,CAAAW,MAAA,oBAAY;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAC9EZ,EAAA,CAAAS,cAAA,YAA4E;QAAAT,EAAA,CAAAW,MAAA,4BAAoB;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGxGZ,EAAA,CAAAS,cAAA,aAAuH;QACrHT,EAAA,CAAAU,SAAA,aAAmD;QACnDV,EAAA,CAAAS,cAAA,cAAgC;QACgCT,EAAA,CAAAW,MAAA,kBAAU;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAC5EZ,EAAA,CAAAS,cAAA,YAA4E;QAAAT,EAAA,CAAAW,MAAA,6BAAqB;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGzGZ,EAAA,CAAAS,cAAA,aAAqH;QACnHT,EAAA,CAAAU,SAAA,aAAyD;QACzDV,EAAA,CAAAS,cAAA,cAAgC;QACgCT,EAAA,CAAAW,MAAA,uBAAe;QAAAX,EAAA,CAAAY,YAAA,EAAI;QACjFZ,EAAA,CAAAS,cAAA,YAA4E;QAAAT,EAAA,CAAAW,MAAA,2BAAmB;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGvGZ,EAAA,CAAAS,cAAA,aAA+G;QAC7GT,EAAA,CAAAU,SAAA,aAAmD;QACnDV,EAAA,CAAAS,cAAA,cAAgC;QACgCT,EAAA,CAAAW,MAAA,kBAAU;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAC5EZ,EAAA,CAAAS,cAAA,YAA4E;QAAAT,EAAA,CAAAW,MAAA,wBAAgB;QAAAX,EAAA,CAAAY,YAAA,EAAI;QAGpGZ,EAAA,CAAAS,cAAA,aAAuE;QAAnBT,EAAA,CAAAa,UAAA,mBAAAC,oDAAA;UAAA,OAASN,GAAA,CAAAO,MAAA,EAAQ;QAAA,EAAC;QAClEf,EAAA,CAAAU,SAAA,aAA8D;QAC9DV,EAAA,CAAAS,cAAA,gBAAgF;QAAAT,EAAA,CAAAW,MAAA,cAAM;QAAAX,EAAA,CAAAY,YAAA,EAAO;;;QA5C5BZ,EAAA,CAAAgB,SAAA,GAAwC;QAAxChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAwC;QAOrCnB,EAAA,CAAAgB,SAAA,GAAwC;QAAxChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAwC;QAOxCnB,EAAA,CAAAgB,SAAA,GAAwC;QAAxChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,IAAAC,GAAA,EAAwC;QAOnCnB,EAAA,CAAAgB,SAAA,GAAyC;QAAzChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,IAAAE,GAAA,EAAyC;QAO3CpB,EAAA,CAAAgB,SAAA,GAAyC;QAAzChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,KAAAE,GAAA,EAAyC;QAO9CpB,EAAA,CAAAgB,SAAA,GAAwC;QAAxChB,EAAA,CAAAiB,UAAA,4BAAAjB,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAAwC;;;;;;;SD9BvGzB,sBAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}