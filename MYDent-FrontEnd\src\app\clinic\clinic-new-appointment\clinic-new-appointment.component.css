body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  margin: 0;
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 30px;
}

.appointment-container {
  /* width: 60%; */
  /* max-width: 1000px; */
  /* margin: auto; */
  background-color: #fff;
  border: 1px solid #ff6600;
  display: flex;
  flex-direction: column;
  padding: 20px;
  /* justify-content: space-between; */
  /* align-items: center; */
  /* height: 100%; */
  /* box-sizing: border-box; */
}

.header-row h2 {
  font-weight: 700;
  margin-bottom: 40px;
}






/* --- Root Variables (Colors) --- */
:root {
    --primary-orange: #F97B22; /* Selected slot background/Available text */
    --light-orange-bg: #FFF4EC; /* Available slot background */
    --light-orange-border: #FBCDB4; /* Available slot border */
    --light-text: #6c757d;
    --white: #ffffff;
    --disabled-bg: #f8f9fa; /* Light gray background */
    --disabled-text: #adb5bd; /* Darker gray text */
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--light-text);
    margin-bottom: 8px;
}

/* --- Time Slot Grid Layout --- */
.time-slot-grid {
    display: grid;
    /* Sets up 4 columns for the grid */
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr)); 
    gap: 10px;
}

/* .time-slot {
    padding: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid;
    border-radius: 8px;
     border-color: #ff7a00 ;
     color: #ff7a00 ;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s ease;
} */

.time-slot {
    padding: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid;
    border-radius: 8px;
     border-color: #0d6efd ;
     color:#0d6efd ;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s ease;
}

/* --- Time Slot States --- */

/* State 1: Disabled (Grayed Out) */
.time-slot.disabled {
    background-color:  #d8dcdf;
    border-color:  #d8dcdf;
    color: white;
    cursor: not-allowed;
    border-radius:  #d8dcdf;
         border-radius: 8px;

}

/* State 2: Available (Light Orange Border/Background) */
.time-slot.available {
    background-color: var(--light-orange-bg);
    border-color: var(--light-orange-border);

}
/* .time-slot.available:hover {
      background: linear-gradient(to left, #fb751e);
      color: #f8f9fa;
} */

.time-slot.available:hover {
      background: linear-gradient(to left,  #0d6efd);
      color: #f8f9fa;
}

/* State 3: Selected (Solid Orange) */
/* .time-slot.selected {
   background-color:  #fb751e;
    border-color:  #fb751e;
    color: white;
    font-weight: 600;
} */


.time-slot.selected {
   background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    font-weight: 600;
}


.modal-content{
  padding: 30px;
}
.modal-dialog{

  width: 608px;
}
.f{
  justify-items: left;
  align-content:space-between ;
}
/* .modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.5);
}

.modal.show {
  display: block;
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 20px;
  border-radius: 8px;
  width: 70%;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 24px;
  cursor: pointer;
}










 */
