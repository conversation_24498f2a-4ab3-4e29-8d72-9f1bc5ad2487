import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserLayoutComponent } from './user-layout/user-layout.component';
import { UserAppointmentComponent } from './user-appointment/user-appointment.component';
import { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';
import { UserForgetPasswordComponent } from './user-forget-password/user-forget-password.component';
import { UserPasswardChangeComponent } from './user-passward-change/user-passward-change.component';

const routes: Routes = [
  {
    path: '',
    component: UserLayoutComponent,
    children:[
      {path:'',component:UserAppointmentComponent},
      {path:'verify-user', component: UserMailVerificationComponent},
      {path:'forget-password',component:UserForgetPasswordComponent},
      {path:'password-resect',component:UserPasswardChangeComponent}
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserRoutingModule {

}
