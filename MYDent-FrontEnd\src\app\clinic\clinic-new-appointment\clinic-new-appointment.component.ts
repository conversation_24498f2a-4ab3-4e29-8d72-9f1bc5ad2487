import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Appointments } from 'src/app/modules/appointments/appointments';
import { AppointmentsService } from 'src/app/modules/appointments/appointments.service';
import { Clinic } from '../clinic';
import Swal from 'sweetalert2';
import { Observable } from 'rxjs';
import { Modal } from 'bootstrap';

@Component({
  selector: 'app-clinic-new-appointment',
  templateUrl: './clinic-new-appointment.component.html',
  styleUrls: ['./clinic-new-appointment.component.css'],
})
export class ClinicNewAppointmentComponent {
  appointmentForm: FormGroup;
  defaultDate: string = new Date().toISOString().substr(0, 10);
  currentDate: string = this.getCurrentDate();
  appointment: Appointments = new Appointments();
  appointmentsList: Appointments[] = [];

  // Pagination
  currentPage: number = 0;
  totalPages: number = 0;
  
  showModal: boolean = false;
modal: Modal | undefined;
  constructor(
    private fb: FormBuilder,
    private appointmentsService: AppointmentsService
  ) {
    const today = new Date(); // Get today's date
    this.defaultDate = today.toISOString().substring(0, 10); // Format date as YYYY-MM-DD

    // Initialize form
    this.appointmentForm = this.fb.group(
      {
        firstName: ['', Validators.required],
        lastName: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        telephone: [
          '',
          [Validators.required, Validators.pattern(/^\+?[0-9]{7,15}$/)],
        ],
        preferredservice: [''],
        fromDate: [
          { value: this.defaultDate, disabled: false },
          Validators.required,
        ],
        fromTime: ['', Validators.required],
        toTime: ['', Validators.required],
      },
      {}
    );
  }

  ngOnInit(): void {
    const clinicId = localStorage.getItem('clinicId');
    this.openModal();
    // console.log('Clinic ID:', clinicId);

    // this.appointmentForm.get('fromDate')?.valueChanges.subscribe((date) => {
    //   if (date) {
    //     this.checkAppointments(date, String(clinicId), this.currentPage);
    //   }
    // });
  }
  ngAfterViewInit() {
    // Initialize modal
    const modalElement = document.getElementById('exampleModal');
    if (modalElement) {
      this.modal = new Modal(modalElement);
      // Open automatically on page load
      this.modal.show();
    }
  }

  openModal() {
    this.modal?.show();
  }

  closeModal() {
    this.modal?.hide();
  }
  
  private getCurrentDate(): string {
    const today = new Date();
    const dd = String(today.getDate()).padStart(2, '0');
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
    const yyyy = today.getFullYear();
    return `${yyyy}-${mm}-${dd}`;
  }

  cancelAppointment(): void {
    this.appointmentForm.reset();
    this.appointmentsList = [];
  }

  timeCheaker(): boolean {
    const fromTime = this.appointmentForm.get('fromTime')?.value;
    const toTime = this.appointmentForm.get('toTime')?.value;
    const selectedDate = this.appointmentForm.get('fromDate')?.value; // assuming a date field
    console.log('Selected Date:', selectedDate);

    if (!fromTime || !toTime || !selectedDate) {
      Swal.fire(
        'warning',
        'Please select Date, From Time and To Time.',
        'warning'
      );
      return false;
    }

    // Parse selected date
    const selected = new Date(selectedDate);
    const today = new Date();

    // Convert times to total minutes
    const [fromHours, fromMinutes] = fromTime.split(':').map(Number);
    const [toHours, toMinutes] = toTime.split(':').map(Number);
    const fromTotal = fromHours * 60 + fromMinutes;
    const toTotal = toHours * 60 + toMinutes;

    // Validate time range
    if (fromTotal >= toTotal) {
      Swal.fire('warning', 'From time must be before To time.', 'warning');
      return false;
    }

    // Check if selected date is today
    const isToday =
      selected.getFullYear() === today.getFullYear() &&
      selected.getMonth() === today.getMonth() &&
      selected.getDate() === today.getDate();

    if (isToday) {
      const currentTime = today.getHours() * 60 + today.getMinutes();
      if (fromTotal < currentTime) {
        Swal.fire(
          'warning',
          'From time cannot be earlier than current time for today.',
          'warning'
        );
        return false;
      }
    }

    return true;
  }

  onSubmit(): void {
    if (this.timeCheaker() === false) {
      return;
    }
    if (this.appointmentForm.valid) {
      const formValues = this.appointmentForm.value;

      // Get the logged-in clinic ID from localStorage
      const clinicId = localStorage.getItem('clinicId');
      console.log('Clinic ID from localStorage:', clinicId);
      if (!clinicId) {
        console.error('Clinic ID not found in localStorage.');
        return;
      }

      this.appointment = {
        ...this.appointment, // Spread the existing appointment properties
        firstName: formValues.firstName,
        lastName: formValues.lastName,
        email: formValues.email,
        telephone: formValues.telephone,
        preferredservice: formValues.preferredservice,
        fromDate: formValues.fromDate,
        fromTime: formValues.fromTime,
        toTime: formValues.toTime,
        clinics: new Clinic(),

        // address: formValues.address,
        // city: formValues.city,
        // state: formValues.state,
        // district: formValues.district,
        // nearestCity: formValues.nearestCity,
        // userName: formValues.username,
        // password: formValues.password,
        // toDate: formValues.toDate,
        // clinics: new Clinic(),
        // customer: new Customer()
      };
      this.appointment.clinics.clinicId = Number(clinicId); // Set the clinic ID

      this.appointmentsService.saveAppointments(this.appointment).subscribe(
        (response) => {
          if (response.status === true) {
            Swal.fire('success', response.message, 'success');
            this.appointmentForm.reset();

          } else {
            Swal.fire('Error', response.message, 'error');
            this.appointmentsList = [];
          }
        },
        (error) => {
          // 409 is the CONFLICT status
          // alert(error.error.message);
          Swal.fire('Error', 'Time slot already booked.', 'error');

          // const message = error?.error?.message;

          // if (error.status === 409) {
          //   alert('Selected time slot is already taken. Please choose another.');
          // } else if (error.status === 401) {
          //   alert('You are not authorized to make this request. Please log in again.');
          // } else {
          //   console.error('Unexpected error:', error);
          // }
        }
      );
    } else {
      this.appointmentsList = [];
      console.warn('Form is invalid.');
    }
  }

  // Show table Modal
  // openModal() {
  //   this.showModal = true;
  // }

  // Method to close the modal
  // closeModal() {
  //   this.showModal = false;
  //   this.appointmentsList = [];
  // }

  checkAppointments(
    fromDate: string,
    clinicId: string,
    currentPage: number
  ): void {
    this.appointmentsService
      .getAllAppointmentsInDates(Number(clinicId), fromDate, currentPage)
      .subscribe(
        (response) => {
          if (response.status) {
            console.log(clinicId);
            this.openModal();
            this.appointmentsList = response.appointments;
            this.currentPage = response.currentPage;
            this.totalPages = response.totalPages;
          }
        },
        (error) => {
          console.error('Error checking appointments:', error);
          this.closeModal();
        }
      );
  }

  nextPage() {
    const clinicId = localStorage.getItem('clinicId');
    if (this.currentPage < this.totalPages - 1) {
      this.checkAppointments(
        this.appointmentForm.get('fromDate')?.value,
        String(clinicId),
        this.currentPage + 1
      );
    }
    console.log(this.currentPage);
  }

  prevPage() {
    const clinicId = localStorage.getItem('clinicId');
    if (this.currentPage > 0) {
      this.checkAppointments(
        this.appointmentForm.get('fromDate')?.value,
        String(clinicId),
        this.currentPage - 1
      );
    }
  }

  checkAppointments2(
    fromDate: string,
    clinicId: string,
    currentPage: number
  ): Observable<any> {
    return this.appointmentsService.getAllAppointmentsInDates(
      Number(clinicId),
      fromDate,
      currentPage
    );
  }

  // checkAndSubmit(){
  //   const clinicId = localStorage.getItem('clinicId');
  //   if (!clinicId) {
  //         console.error('Clinic ID not found in localStorage.');
  //         return;
  //       }
  // this.checkAppointments(this.appointmentForm.value.fromDate, clinicId, this.currentPage);

  // if (this.showModal) {
  //   console.log('Modal is open, not submitting the form.');
  //   return;
  // }else{
  //   this.onSubmit();
  // }

  // }
}
