package com.navitsa.mydent.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.ClinicServicesCategory;
import com.navitsa.mydent.services.ClinicServicesCategoryServices;



@RestController
public class ClinicServicesCategoryController {
	
	private final ClinicServicesCategoryServices clinicServicesCategoryServices;
	
    @Autowired
	public ClinicServicesCategoryController(ClinicServicesCategoryServices clinicServicesCategoryServices) {
	     this.clinicServicesCategoryServices = clinicServicesCategoryServices;
	 }

    @GetMapping("/getClinicServicesCategoryById/{id}")
    public ClinicServicesCategory getClinicServicesCategoryById(@PathVariable int id) {
        return clinicServicesCategoryServices.getClinicServicesCategoryById(id);
    }

    @GetMapping("/AllClinicServicesCategoryList")
    public List<ClinicServicesCategory> getAllClinicServicesCategory() {
        return clinicServicesCategoryServices.findAllClinicServicesCategory();
    }
    
    @PostMapping("/saveClinicServicesCategory")
	public ClinicServicesCategory saveClinicServicesCategory(@RequestBody ClinicServicesCategory clinicServicesCategory) {
		return clinicServicesCategoryServices.saveClinicServicesCategory(clinicServicesCategory);
	}

}
