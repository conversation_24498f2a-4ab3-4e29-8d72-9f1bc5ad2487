package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.ClinicSchedule;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ClinicScheduleRepository extends JpaRepository<ClinicSchedule,Integer> {
    @Modifying
    @Transactional
    @Query("UPDATE ClinicSchedule c SET c.fromTime = :fromTime, c.toTime = :toTime WHERE c.scheduleId = :id")
    int updateScheduleTime(@Param("id") int id, @Param("fromTime") String fromTime, @Param("toTime") String toTime);

    List<ClinicSchedule> findByHolidayDateAndClinics_ClinicId(boolean day,Integer clinicId);

    @Query("SELECT cs FROM ClinicSchedule cs WHERE cs.date = :dayName AND cs.clinics.clinicId = :clinicId AND cs.holidayDate = false")
    Optional<ClinicSchedule> findByDateAndClinicId(@Param("dayName") String dayName, @Param("clinicId") Integer clinicId);
}
