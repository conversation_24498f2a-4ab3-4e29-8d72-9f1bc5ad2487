package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.ClinicSchedule;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ClinicScheduleRepository extends JpaRepository<ClinicSchedule,Integer> {
    @Modifying
    @Transactional
    @Query("UPDATE ClinicSchedule c SET c.fromTime = :fromTime, c.toTime = :toTime WHERE c.scheduleId = :id")
    int updateScheduleTime(@Param("id") int id, @Param("fromTime") String fromTime, @Param("toTime") String toTime);
}
