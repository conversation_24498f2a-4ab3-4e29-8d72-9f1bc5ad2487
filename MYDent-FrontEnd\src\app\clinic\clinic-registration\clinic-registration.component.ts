import { Component, ElementRef, OnInit,ViewChild } from '@angular/core';
import { User, UserCategory } from '../../user/user';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from '../../user/user.service';
import { map, mapTo, Observable, of, tap } from 'rxjs';
import { SharedService } from '../../modules/shared-services/shared.service';
import { Clinic } from '../clinic';
import { ClinicService } from '../clinic.service';
import Swal from 'sweetalert2';
import { AuthService } from 'src/app/auth/auth.service';
import { UserTemp, UserTempType } from 'src/app/auth/auth';
import { PrimaryActionButtonComponent } from 'src/app/core/primary-action-button/primary-action-button.component';


@Component({
  selector: 'app-clinic-registration',
  templateUrl: './clinic-registration.component.html',
  styleUrls: ['./clinic-registration.component.css'],
})

export class ClinicRegistrationComponent implements OnInit {
  clinic: Clinic = new Clinic();
  user: User = new User();
  userCategory: UserCategory = new UserCategory();
  clinicForm: FormGroup;
  passwordDoNotMatch = false;
  isEmailRegistered: boolean = false;
  userEmailExistsMessage: string = '';
  isClinicRegistered: boolean = false;
  isCityDisabled: boolean = true;
  clinicNameExistsMessage: string = '';

  userTemp:UserTemp = new UserTemp();
  @ViewChild('RegisterButton') registerButton!: PrimaryActionButtonComponent;

  districts: string[] = [];
  cities: String[] = [];

  passwordVisible: boolean = false;

  togglePasswordVisibility() {
    this.passwordVisible = !this.passwordVisible;
  }

  onDistrictChange(event: Event): void {
    const selectedDistrict = (event.target as HTMLSelectElement).value;
    if (selectedDistrict) {
      this.clinicForm.get('city')?.enable();
      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);
      console.log(this.cities);
    } else {
      this.clinicForm.get('city')?.disable();
      this.cities = [];
    }
    this.clinicForm.get('city')?.setValue('');
  }

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private userService: UserService,
    private clinicService: ClinicService,
    private sharedService: SharedService,
    private authService: AuthService
  ) {
    this.clinicForm = this.fb.group(
      {
        clinicName: [
          '',
          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],
        ],
        address: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        district: ['', Validators.required],
        city: [{ value: '', disabled: true }, Validators.required],
        tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
        contactPerson: ['', Validators.required],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            Validators.pattern('^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\W).*$'),
          ],
        ],
        rePassword: ['', Validators.required],
      },
      { validator: this.passwordMatchValidator }
    );
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const rePassword = form.get('rePassword');

    if (
      password?.value &&
      rePassword?.value &&
      (rePassword.dirty || rePassword.touched)
    ) {
      return password.value === rePassword.value ? null : { mismatch: true };
    }

    return null;
  }

  ngOnInit(): void {
    localStorage.clear();
    this.userService.getUserCategoryById(3).subscribe((response) => {
      this.userCategory = response;
    });
    this.districts = this.sharedService.getDistricts();
    this.userTemp.userTempType = UserTempType.CLINIC;
  }

  updateEmail() {
    this.user.username = this.clinic.email;
  }

  onSubmitRegister() {
    if (this.clinicForm.invalid) {
      this.clinicForm.markAllAsTouched();
      return;
    }
    this.checkClinicName().subscribe((isClinicRegistered) => {
      if (!isClinicRegistered) {
        this.checkUserEmail().subscribe((isEmailRegistered) => {
          if (!isEmailRegistered) {

            this.onUserRegister().subscribe(() => {
              this.onClinicRegister().subscribe(() => {
                Swal.fire({
                  title: "Wait until approval!",
                  text: "Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.",
                  icon: 'success',
                  confirmButtonText: 'OK',
                });
              });
            });
          }
        });
      }
    });
  }

  onUserRegister(): Observable<void> {
    this.user.userCategoryId = this.userCategory;
    this.user.firstName = this.clinic.name;
    return this.userService.register(this.user).pipe(
      tap(
        (response) => {
          this.user.userId = response.id;
          this.clinic.userId = this.user;
        },
        (error) => {
          console.log(error);
        }
      ),
      mapTo(void 0)
    );
  }

onClinicRegister(): Observable<void> {
  return this.clinicService.saveClinic(this.clinic).pipe(
    tap(
      () => {
        Swal.fire({
          title: 'Registration Successful!',
          text: 'Thank you for registering! Please verify your email to complete the login process.',
          icon: 'success',
          confirmButtonText: 'OK',
        }).then((result) => {
          if (result.isConfirmed) {
            this.router.navigate(['/user-login']);
          }
        });
      },
      (error) => {
        console.log(error);
        Swal.fire({
          title: 'Error',
          text: 'An error occurred during registration. Please try again later.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    ),
    mapTo(void 0)
  );
}

// UserTemp Saving
onUserTempRegister() {

  if (this.clinicForm.invalid) {
    this.clinicForm.markAllAsTouched();
    return;
  }

  // Log the userTemp object to check what's being sent
  console.log('UserTemp object before sending:', this.userTemp);

  // Disable the register button and show a loading indicator
  this.registerButton.buttonInstance.nativeElement.disabled = true;
  this.registerButton.buttonInstance.nativeElement.innerHTML = `<img src="/assets/icons/more-30.png" />`;

  // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times
  // this.authService
  //   .checkUserTempAvailability(this.userTemp.userEmail)
  //   .subscribe((resp) => {

  //     if (resp !=null) {
  //       Swal.fire({
  //         title: 'Registration Already Exists!',
  //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',
  //         icon: 'info',
  //         confirmButtonText: 'OK',
  //       });

  //       // Reset the button state
  //       this.registerButton.buttonInstance.nativeElement.disabled = false;
  //       this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';
  //       return;
  //     }

      this.authService.saveUserTemp(this.userTemp).subscribe(
        (userTempSaved: UserTemp) => {
          console.log('Full userTempSaved object:', userTempSaved);

          const receivedUserTemp: UserTemp = userTempSaved;
          let title = 'Registration Completed!';
          let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';
          let iconName:
            | 'success'
            | 'info'
            | 'error'
            | 'warning'
            | 'question' = 'success';

          if (!receivedUserTemp) {
            title = 'Registration Failed!';
            message ='An error occurred while registering. Please try again.';
            iconName = 'error';
          }

          Swal.fire({
            title: title,
            text: message,
            icon: iconName,
            confirmButtonText: 'OK',
          });

          // Reset button state
          this.registerButton.buttonInstance.nativeElement.disabled = false;
          this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';
        },
        (error) => {
          console.error('Registration error:', error);

          // Extract error message from backend
          let errorMessage = 'An error occurred during registration. Please try again later.';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          Swal.fire({
            title: 'Registration Failed!',
            text: errorMessage,
            icon: 'error',
            confirmButtonText: 'OK',
          });

          this.registerButton.buttonInstance.nativeElement.disabled = false;
          this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';
        }
      );
    // }); // COMMENTED OUT FOR TESTING
}

  checkUserEmail(): Observable<boolean> {
    if (this.clinicForm.get('email')?.valid) {
      const userEmail = this.clinicForm.get('email')?.value;
      return this.userService.checkUser(userEmail).pipe(
        map((data) => {
          if (data) {
            this.isEmailRegistered = true;
            this.userEmailExistsMessage =
              'Email already registered. Try another.';
          } else {
            this.isEmailRegistered = false;
            this.userEmailExistsMessage = '';
          }
          return this.isEmailRegistered;
        })
      );
    } else {
      this.isEmailRegistered = false;
      this.userEmailExistsMessage = '';
      return of(this.isEmailRegistered);
    }
  }

  checkClinicName(): Observable<boolean> {
    if (this.clinicForm.get('clinicName')?.valid) {
      const clinicName = this.clinicForm.get('clinicName')?.value;
      return this.clinicService.checkClinicName(clinicName).pipe(
        map((data) => {
          if (data) {
            this.isClinicRegistered = true;
            this.clinicNameExistsMessage = 'That name is taken. Try another.';
          } else {
            this.isClinicRegistered = false;
            this.clinicNameExistsMessage = '';
          }
          return this.isClinicRegistered;
        })
      );
    } else {
      this.isClinicRegistered = false;
      this.clinicNameExistsMessage = '';
      return of(this.isClinicRegistered);
    }
  }

  navigateUserSelection() {
    this.router.navigate(['/user-selection']);
  }
}
