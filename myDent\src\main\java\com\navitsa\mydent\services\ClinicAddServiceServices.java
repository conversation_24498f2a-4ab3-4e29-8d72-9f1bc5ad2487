package com.navitsa.mydent.services;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.ClinicAddService;

import com.navitsa.mydent.repositories.ClinicAddServiceRepositories;

@Service
public class ClinicAddServiceServices {

	 
    private final ClinicAddServiceRepositories clinicAddServiceRepositories;
    
    @Autowired
    public ClinicAddServiceServices(ClinicAddServiceRepositories clinicAddServiceRepositories) {
        this.clinicAddServiceRepositories = clinicAddServiceRepositories;
    }
    
    public ClinicAddService saveClinicAddService(ClinicAddService clinicAddService) {
        try {
        	
            SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
            Date currentDate = new Date();
            String formattedDate = dateFormatter.format(currentDate);

            clinicAddService.setAddServiceDate(formattedDate);
            return clinicAddServiceRepositories.save(clinicAddService);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the clinicAddService.");
        }
    }
    public List<ClinicAddService> findAllClinicAddService() {
        try {
            return (List<ClinicAddService>) clinicAddServiceRepositories.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of suppliers.");
        }
    }

    public ClinicAddService getClinicAddServiceById(int id) {
        return clinicAddServiceRepositories.findById(id).orElseThrow(() -> new RuntimeException("clinic Add ServiceRepositories not found with id: " + id));
    }
    
    public ClinicAddService updateClinicAddService(int id, ClinicAddService clinicAddServiceDetails) {
        try {
        	 ClinicAddService  clinicAddService =clinicAddServiceRepositories.findById(id).orElse(null);

            if (clinicAddService == null) {
                throw new RuntimeException("clinic Add Service not found with id: " + id);
            }

            clinicAddService.setClinicServiceCategoryId(clinicAddServiceDetails.getClinicServiceCategoryId());


            return clinicAddServiceRepositories.save(clinicAddService);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the clinicAddService.");
        }
    }
}
