package com.navitsa.mydent.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "user_category")
public class UserCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_category_id")
    private Integer categoryId;

    @Column(name = "user_category_name")
    private String categoryName;

    @Column(name = "user_router_path")
    private String routerPath;

    public UserCategory() {}

    public UserCategory(Integer userCategoryId, String userCategory,String routerPath) {
        super();
        this.categoryId = userCategoryId;
        this.categoryName = userCategory;
        this.routerPath = routerPath;
    }
    public Integer getUserCategoryId() {
        return categoryId;
    }

    public void setUserCategoryId(Integer userCategoryId) {
        this.categoryId = userCategoryId;
    }

    public String getUserCategory() {
        return categoryName;
    }

    public void setUserCategory(String userCategory) {
        this.categoryName = userCategory;
    }

    public String getRouterPath() {
        return routerPath;
    }

    public void setRouterPath(String routerPath) {
        this.routerPath = routerPath;
    }

    @Override
    public String toString() {
        return "UserCategory{" +
                "categoryId=" + categoryId +
                ", categoryName='" + categoryName + '\'' +
                ", routerPath='" + routerPath + '\'' +
                '}';
    }
}

