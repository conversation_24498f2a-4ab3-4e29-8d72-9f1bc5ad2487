// import { User } from '../admin/components/user/user';
import { Clinic } from 'src/app/clinic/clinic';
import { Customer } from './customer';
import { Doctor } from 'src/app/doctor/doctor';

export class Appointments {
  appointmentId: number = 0;
  // userId: number = 0;
  firstName: string = '';
  lastName: string='';
  address: string = '';
  city: string = '';
  state: string = '';
  district: string = '';
  telephone: string = '';
  email: string = '';
  preferredservice: string = '';
  nearestCity: string = '';
  fromDate: string = '';
  toDate: string = '';
  fromTime: string = '';
  toTime: string = '';
  clinics: Clinic= new Clinic();
  userName:string='';
  password:string='';
  status:string='';
  customer: Customer = new Customer(); // Reference to the Customer class
  doctor: Doctor = new Doctor(); // Reference to the Doctor class
}


