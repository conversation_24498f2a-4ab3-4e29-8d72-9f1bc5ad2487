{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/common\";\nclass AppointmentDetailsDialogComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  close() {\n    this.dialogRef.close();\n  }\n  accept() {\n    this.dialogRef.close('accept'); // Send 'accept' as a signal to trigger acceptance\n  }\n\n  reject() {\n    this.dialogRef.close('reject'); // Send 'accept' as a signal to trigger acceptance\n  }\n  static #_ = this.ɵfac = function AppointmentDetailsDialogComponent_Factory(t) {\n    return new (t || AppointmentDetailsDialogComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppointmentDetailsDialogComponent,\n    selectors: [[\"app-appointment-details-dialog\"]],\n    decls: 32,\n    vars: 9,\n    consts: [[2, \"padding\", \"20px\", \"min-width\", \"400px\"], [\"mat-dialog-title\", \"\", 2, \"font-weight\", \"700\"], [\"mat-dialog-content\", \"\", 2, \"display\", \"flex\", \"gap\", \"30px\"], [2, \"display\", \"flex\", \"gap\", \"20px\", \"align-items\", \"flex-start\"], [2, \"width\", \"150px\", \"font-weight\", \"600\", \"color\", \"#333\"], [2, \"flex\", \"1\", \"color\", \"#555\"], [\"mat-dialog-actions\", \"\", \"align\", \"end\", 2, \"display\", \"flex\", \"gap\", \"10px\", \"justify-content\", \"flex-end\", \"margin-top\", \"20px\"], [\"mat-button\", \"\", 2, \"width\", \"100px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #00c820, #0e6001)\", \"border\", \"none\", \"color\", \"white\", 3, \"click\"], [\"mat-button\", \"\", 2, \"width\", \"100px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #fb751e, #b93426)\", \"border\", \"none\", \"color\", \"white\", 3, \"click\"]],\n    template: function AppointmentDetailsDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h4\", 1);\n        i0.ɵɵtext(2, \"Appointment Request\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"br\");\n        i0.ɵɵelementStart(4, \"div\", 2)(5, \"div\", 3)(6, \"div\", 4)(7, \"p\");\n        i0.ɵɵtext(8, \"Name:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"p\");\n        i0.ɵɵtext(10, \"Telephone:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\");\n        i0.ɵɵtext(12, \"Requested Date:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"p\");\n        i0.ɵɵtext(14, \"Requested Time:\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 5)(16, \"p\");\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"p\");\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"p\");\n        i0.ɵɵtext(21);\n        i0.ɵɵpipe(22, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"p\");\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(25, \"div\", 6)(26, \"button\", 7);\n        i0.ɵɵlistener(\"click\", function AppointmentDetailsDialogComponent_Template_button_click_26_listener() {\n          return ctx.accept();\n        });\n        i0.ɵɵtext(27, \" Accept \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function AppointmentDetailsDialogComponent_Template_button_click_28_listener() {\n          return ctx.reject();\n        });\n        i0.ɵɵtext(29, \" Reject \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function AppointmentDetailsDialogComponent_Template_button_click_30_listener() {\n          return ctx.close();\n        });\n        i0.ɵɵtext(31, \" Close \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(17);\n        i0.ɵɵtextInterpolate2(\"\", ctx.data.firstName, \" \", ctx.data.lastName, \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.data.telephone);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 6, ctx.data.fromDate, \"longDate\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate2(\"\", ctx.data.fromTime, \" - \", ctx.data.toTime, \"\");\n      }\n    },\n    dependencies: [i2.DatePipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { AppointmentDetailsDialogComponent };", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "AppointmentDetailsDialogComponent", "constructor", "dialogRef", "data", "close", "accept", "reject", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "_2", "selectors", "decls", "vars", "consts", "template", "AppointmentDetailsDialogComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "AppointmentDetailsDialogComponent_Template_button_click_26_listener", "AppointmentDetailsDialogComponent_Template_button_click_28_listener", "AppointmentDetailsDialogComponent_Template_button_click_30_listener", "ɵɵadvance", "ɵɵtextInterpolate2", "firstName", "lastName", "ɵɵtextInterpolate", "telephone", "ɵɵpipeBind2", "fromDate", "fromTime", "toTime"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\appointment-popup\\appointment-details-dialog\\appointment-details-dialog.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\appointment-popup\\appointment-details-dialog\\appointment-details-dialog.component.html"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\n\r\n@Component({\r\n  selector: 'app-appointment-details-dialog',\r\n  templateUrl: './appointment-details-dialog.component.html',\r\n  styleUrls: ['./appointment-details-dialog.component.css']\r\n})\r\nexport class AppointmentDetailsDialogComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<AppointmentDetailsDialogComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: any\r\n  ) {}\r\n\r\n  close(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  accept(): void {\r\n    this.dialogRef.close('accept'); // Send 'accept' as a signal to trigger acceptance\r\n  }\r\n\r\n  reject(): void {\r\n    this.dialogRef.close('reject'); // Send 'accept' as a signal to trigger acceptance\r\n  }\r\n}\r\n", "<div style=\"padding: 20px; min-width: 400px\">\r\n  <h4 mat-dialog-title style=\"font-weight: 700\">Appointment Request</h4>\r\n  <br />\r\n  <div mat-dialog-content style=\"display: flex; gap: 30px\">\r\n    <div style=\"display: flex; gap: 20px; align-items: flex-start\">\r\n      <div style=\"width: 150px; font-weight: 600; color: #333\">\r\n        <p>Name:</p>\r\n        <p>Telephone:</p>\r\n        <p>Requested Date:</p>\r\n        <p>Requested Time:</p>\r\n        <!-- <p>Address:</p> -->\r\n      </div>\r\n\r\n      <div style=\"flex: 1; color: #555\">\r\n        <p>{{ data.firstName }} {{ data.lastName }}</p>\r\n        <p>{{ data.telephone }}</p>\r\n        <p>{{ data.fromDate | date : \"longDate\" }}</p>\r\n        <p>{{ data.fromTime }} - {{ data.toTime }}</p>\r\n        <!-- <p>{{ data.address }}</p> -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <div\r\n    mat-dialog-actions\r\n    align=\"end\"\r\n    style=\"\r\n      display: flex;\r\n      gap: 10px;\r\n      justify-content: flex-end;\r\n      margin-top: 20px;\r\n    \"\r\n  >\r\n    <button\r\n      mat-button\r\n      style=\"\r\n        width: 100px;\r\n        border-radius: 50px;\r\n        padding-block: 4px;\r\n        background: linear-gradient(to right, #00c820, #0e6001);\r\n        border: none;\r\n        color: white;\r\n      \"\r\n      (click)=\"accept()\"\r\n    >\r\n      Accept\r\n    </button>\r\n    <button\r\n      mat-button\r\n      style=\"\r\n        width: 100px;\r\n        border-radius: 50px;\r\n        padding-block: 4px;\r\n        background: linear-gradient(to right, #fb751e, #b93426);\r\n        border: none;\r\n        color: white;\r\n      \"\r\n      (click)=\"reject()\"\r\n    >\r\n      Reject\r\n    </button>\r\n    <button\r\n      mat-button\r\n      (click)=\"close()\"\r\n      style=\"\r\n        width: 100px;\r\n        border-radius: 50px;\r\n        padding-block: 4px;\r\n        background: linear-gradient(to right, #fb751e, #b93426);\r\n        border: none;\r\n        color: white;\r\n      \"\r\n    >\r\n      Close\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,eAAe,QAAsB,0BAA0B;;;;AAExE,MAKaC,iCAAiC;EAC5CC,YACSC,SAA0D,EACjCC,IAAS;IADlC,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EACnC;EAEHC,KAAKA,CAAA;IACH,IAAI,CAACF,SAAS,CAACE,KAAK,EAAE;EACxB;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClC;;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAACJ,SAAS,CAACE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;EAClC;EAAC,QAAAG,CAAA,G;qBAhBUP,iCAAiC,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGlCV,eAAe;EAAA;EAAA,QAAAa,EAAA,G;UAHdZ,iCAAiC;IAAAa,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR9CX,EAAA,CAAAa,cAAA,aAA6C;QACGb,EAAA,CAAAc,MAAA,0BAAmB;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACtEf,EAAA,CAAAgB,SAAA,SAAM;QACNhB,EAAA,CAAAa,cAAA,aAAyD;QAGhDb,EAAA,CAAAc,MAAA,YAAK;QAAAd,EAAA,CAAAe,YAAA,EAAI;QACZf,EAAA,CAAAa,cAAA,QAAG;QAAAb,EAAA,CAAAc,MAAA,kBAAU;QAAAd,EAAA,CAAAe,YAAA,EAAI;QACjBf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,uBAAe;QAAAd,EAAA,CAAAe,YAAA,EAAI;QACtBf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,uBAAe;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAIxBf,EAAA,CAAAa,cAAA,cAAkC;QAC7Bb,EAAA,CAAAc,MAAA,IAAwC;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC/Cf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,IAAoB;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC3Bf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,IAAuC;;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC9Cf,EAAA,CAAAa,cAAA,SAAG;QAAAb,EAAA,CAAAc,MAAA,IAAuC;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAKpDf,EAAA,CAAAa,cAAA,cASC;QAWGb,EAAA,CAAAiB,UAAA,mBAAAC,oEAAA;UAAA,OAASN,GAAA,CAAAf,MAAA,EAAQ;QAAA,EAAC;QAElBG,EAAA,CAAAc,MAAA,gBACF;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACTf,EAAA,CAAAa,cAAA,iBAWC;QADCb,EAAA,CAAAiB,UAAA,mBAAAE,oEAAA;UAAA,OAASP,GAAA,CAAAd,MAAA,EAAQ;QAAA,EAAC;QAElBE,EAAA,CAAAc,MAAA,gBACF;QAAAd,EAAA,CAAAe,YAAA,EAAS;QACTf,EAAA,CAAAa,cAAA,iBAWC;QATCb,EAAA,CAAAiB,UAAA,mBAAAG,oEAAA;UAAA,OAASR,GAAA,CAAAhB,KAAA,EAAO;QAAA,EAAC;QAUjBI,EAAA,CAAAc,MAAA,eACF;QAAAd,EAAA,CAAAe,YAAA,EAAS;;;QA3DFf,EAAA,CAAAqB,SAAA,IAAwC;QAAxCrB,EAAA,CAAAsB,kBAAA,KAAAV,GAAA,CAAAjB,IAAA,CAAA4B,SAAA,OAAAX,GAAA,CAAAjB,IAAA,CAAA6B,QAAA,KAAwC;QACxCxB,EAAA,CAAAqB,SAAA,GAAoB;QAApBrB,EAAA,CAAAyB,iBAAA,CAAAb,GAAA,CAAAjB,IAAA,CAAA+B,SAAA,CAAoB;QACpB1B,EAAA,CAAAqB,SAAA,GAAuC;QAAvCrB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA2B,WAAA,QAAAf,GAAA,CAAAjB,IAAA,CAAAiC,QAAA,cAAuC;QACvC5B,EAAA,CAAAqB,SAAA,GAAuC;QAAvCrB,EAAA,CAAAsB,kBAAA,KAAAV,GAAA,CAAAjB,IAAA,CAAAkC,QAAA,SAAAjB,GAAA,CAAAjB,IAAA,CAAAmC,MAAA,KAAuC;;;;;;;SDTrCtC,iCAAiC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}