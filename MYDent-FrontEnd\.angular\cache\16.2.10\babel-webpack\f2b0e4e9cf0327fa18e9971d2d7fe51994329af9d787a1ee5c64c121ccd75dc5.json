{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment'; // Import environment config\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass ScheduleService {\n  constructor(http) {\n    this.http = http;\n    this.baseURL = environment.apiUrl; // Backend URL\n  }\n  // Get the authentication token from localStorage\n  getAuthToken() {\n    return window.localStorage.getItem('auth_token');\n  }\n  // Generic method for making HTTP requests\n  request(method, url, data = {}, params, responseType = 'json' // Default to JSON response type\n  ) {\n    let headers = new HttpHeaders();\n    // Set Authorization header if auth token exists\n    if (this.getAuthToken() !== null) {\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\n    }\n    // Prepare request options\n    const options = {\n      headers: headers,\n      params: new HttpParams({\n        fromObject: params\n      }),\n      observe: 'body',\n      responseType: responseType // Set the response type, usually 'json'\n    };\n    // Execute appropriate HTTP method\n    switch (method.toUpperCase()) {\n      case 'GET':\n        return this.http.get(this.baseURL + url, options);\n      case 'POST':\n        return this.http.post(this.baseURL + url, data, options);\n      case 'PUT':\n        return this.http.put(this.baseURL + url, data, options);\n      case 'DELETE':\n        return this.http.delete(this.baseURL + url, options);\n      // Add more HTTP methods as needed\n      default:\n        throw new Error('Unsupported HTTP method');\n    }\n  }\n  // Method to get all schedules\n  getSchedules() {\n    return this.request('GET', '/scheduleList', {});\n  }\n  // Method to get a specific schedule by ID\n  getScheduleById(id) {\n    return this.request('GET', `/getScheduleById/${id}`, {});\n  }\n  // Method to save a new schedule\n  saveSchedule(schedule) {\n    return this.request('POST', '/saveSchedule', schedule);\n  }\n  // Method to update a schedule by ID\n  updateSchedule(scheduleId, schedule) {\n    return this.request('PUT', `/updateSchedule/${scheduleId}`, {\n      ...schedule\n    });\n  }\n  // Method to delete a schedule by ID\n  deleteSchedule(scheduleId) {\n    return this.request('DELETE', `/deleteSchedule/${scheduleId}`);\n  }\n  getClinicIdByUserId(userid) {\n    return this.request('GET', `/clinicId?userId=${userid}`, {});\n  }\n  // New Methods for Clinic Services\n  // Method to save a new clinic service\n  saveClinicService(clinicService) {\n    return this.request('POST', '/clinic-services/save', clinicService);\n  }\n  // Method to update a clinic service by ID\n  updateClinicService(clinicServiceId, clinicService) {\n    return this.request('PUT', `/clinic-services/update/${clinicServiceId}`, clinicService);\n  }\n  // Method to delete a clinic service by ID\n  deleteClinicService(clinicServiceId) {\n    return this.request('DELETE', `/clinic-services/delete/${clinicServiceId}`);\n  }\n  // Method to get all clinic services\n  getAllClinicServices() {\n    return this.request('GET', 'clinic-services/all', {});\n  }\n  // Method to get a specific clinic service by ID\n  getClinicServiceById(clinicServiceId) {\n    return this.request('GET', `/clinic-services/${clinicServiceId}`, {});\n  }\n  getClinicServicesByClinicId(clinicId) {\n    return this.request('GET', `/clinic-services/by-clinic/${clinicId}`, {});\n  }\n  static #_ = this.ɵfac = function ScheduleService_Factory(t) {\n    return new (t || ScheduleService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ScheduleService,\n    factory: ScheduleService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { ScheduleService };", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "environment", "ScheduleService", "constructor", "http", "baseURL", "apiUrl", "getAuthToken", "window", "localStorage", "getItem", "request", "method", "url", "data", "params", "responseType", "headers", "set", "options", "fromObject", "observe", "toUpperCase", "get", "post", "put", "delete", "Error", "getSchedules", "getScheduleById", "id", "saveSchedule", "schedule", "updateSchedule", "scheduleId", "deleteSchedule", "getClinicIdByUserId", "userid", "saveClinicService", "clinicService", "updateClinicService", "clinicServiceId", "deleteClinicService", "getAllClinicServices", "getClinicServiceById", "getClinicServicesByClinicId", "clinicId", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-setup-clinic\\service\\schedule.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment'; // Import environment config\r\nimport { ClinicServices, Schedule } from './schedule';\r\n\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ScheduleService {\r\n  private readonly baseURL = environment.apiUrl; // Backend URL\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  // Get the authentication token from localStorage\r\n  getAuthToken(): string | null {\r\n    return window.localStorage.getItem('auth_token');\r\n  }\r\n\r\n  // Generic method for making HTTP requests\r\n  request(\r\n    method: string,\r\n    url: string,\r\n    data: any = {},\r\n    params?: any,\r\n    responseType: 'json' = 'json' // Default to JSON response type\r\n  ): Observable<any> {\r\n    let headers = new HttpHeaders();\r\n\r\n    // Set Authorization header if auth token exists\r\n    if (this.getAuthToken() !== null) {\r\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\r\n    }\r\n\r\n    // Prepare request options\r\n    const options: any = {\r\n      headers: headers,\r\n      params: new HttpParams({ fromObject: params }),\r\n      observe: 'body' as const, // Ensure we are only observing the response body\r\n      responseType: responseType, // Set the response type, usually 'json'\r\n    };\r\n\r\n    // Execute appropriate HTTP method\r\n    switch (method.toUpperCase()) {\r\n      case 'GET':\r\n        return this.http.get(this.baseURL + url, options);\r\n      case 'POST':\r\n        return this.http.post(this.baseURL + url, data, options);\r\n      case 'PUT':\r\n        return this.http.put(this.baseURL + url, data, options);\r\n      case 'DELETE':\r\n        return this.http.delete(this.baseURL + url, options);\r\n      // Add more HTTP methods as needed\r\n      default:\r\n        throw new Error('Unsupported HTTP method');\r\n    }\r\n  }\r\n\r\n  // Method to get all schedules\r\n  getSchedules(): Observable<Schedule[]> {\r\n    return this.request('GET', '/scheduleList', {});\r\n  }\r\n\r\n  // Method to get a specific schedule by ID\r\n  getScheduleById(id: number): Observable<Schedule> {\r\n    return this.request('GET', `/getScheduleById/${id}`, {});\r\n  }\r\n\r\n  // Method to save a new schedule\r\n  saveSchedule(schedule: Schedule): Observable<Schedule> {\r\n    return this.request('POST', '/saveSchedule', schedule);\r\n  }\r\n\r\n  // Method to update a schedule by ID\r\n  updateSchedule(scheduleId: number, schedule: Schedule): Observable<Schedule> {\r\n    return this.request('PUT',`/updateSchedule/${scheduleId}`, { ...schedule });\r\n  }\r\n\r\n  // Method to delete a schedule by ID\r\n  deleteSchedule(scheduleId: number): Observable<any> {\r\n    return this.request('DELETE', `/deleteSchedule/${scheduleId}`);\r\n  }\r\n\r\n  getClinicIdByUserId(userid: number): Observable<number> {\r\n    return this.request('GET', `/clinicId?userId=${userid}`, {});\r\n  }\r\n\r\n   // New Methods for Clinic Services\r\n\r\n  // Method to save a new clinic service\r\n  saveClinicService(clinicService: ClinicServices): Observable<ClinicServices> {\r\n    return this.request('POST', '/clinic-services/save', clinicService);\r\n  }\r\n\r\n  // Method to update a clinic service by ID\r\n  updateClinicService(clinicServiceId: number, clinicService: ClinicServices): Observable<ClinicServices> {\r\n    return this.request('PUT', `/clinic-services/update/${clinicServiceId}`, clinicService);\r\n  }\r\n\r\n  // Method to delete a clinic service by ID\r\n  deleteClinicService(clinicServiceId: number): Observable<any> {\r\n    return this.request('DELETE', `/clinic-services/delete/${clinicServiceId}`);\r\n  }\r\n\r\n  // Method to get all clinic services\r\n  getAllClinicServices(): Observable<ClinicServices[]> {\r\n    return this.request('GET', 'clinic-services/all', {});\r\n  }\r\n\r\n  // Method to get a specific clinic service by ID\r\n  getClinicServiceById(clinicServiceId: number): Observable<ClinicServices> {\r\n    return this.request('GET', `/clinic-services/${clinicServiceId}`, {});\r\n  }\r\n\r\n  getClinicServicesByClinicId(clinicId: number): Observable<ClinicServices[]> {\r\n    return this.request('GET', `/clinic-services/by-clinic/${clinicId}`, {});\r\n    \r\n}\r\n\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAG1E,SAASC,WAAW,QAAQ,8BAA8B,CAAC,CAAC;;;AAI5D,MAGaC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM,CAAC,CAAC;EAER;EAEvC;EACAC,YAAYA,CAAA;IACV,OAAOC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAClD;EAEA;EACAC,OAAOA,CACLC,MAAc,EACdC,GAAW,EACXC,IAAA,GAAY,EAAE,EACdC,MAAY,EACZC,YAAA,GAAuB,MAAM,CAAC;EAAA,E;IAE9B,IAAIC,OAAO,GAAG,IAAIlB,WAAW,EAAE;IAE/B;IACA,IAAI,IAAI,CAACQ,YAAY,EAAE,KAAK,IAAI,EAAE;MAChCU,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,SAAS,GAAG,IAAI,CAACX,YAAY,EAAE,CAAC;;IAGzE;IACA,MAAMY,OAAO,GAAQ;MACnBF,OAAO,EAAEA,OAAO;MAChBF,MAAM,EAAE,IAAIf,UAAU,CAAC;QAAEoB,UAAU,EAAEL;MAAM,CAAE,CAAC;MAC9CM,OAAO,EAAE,MAAe;MACxBL,YAAY,EAAEA,YAAY,CAAE;KAC7B;IAED;IACA,QAAQJ,MAAM,CAACU,WAAW,EAAE;MAC1B,KAAK,KAAK;QACR,OAAO,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAAC,IAAI,CAAClB,OAAO,GAAGQ,GAAG,EAAEM,OAAO,CAAC;MACnD,KAAK,MAAM;QACT,OAAO,IAAI,CAACf,IAAI,CAACoB,IAAI,CAAC,IAAI,CAACnB,OAAO,GAAGQ,GAAG,EAAEC,IAAI,EAAEK,OAAO,CAAC;MAC1D,KAAK,KAAK;QACR,OAAO,IAAI,CAACf,IAAI,CAACqB,GAAG,CAAC,IAAI,CAACpB,OAAO,GAAGQ,GAAG,EAAEC,IAAI,EAAEK,OAAO,CAAC;MACzD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACf,IAAI,CAACsB,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAGQ,GAAG,EAAEM,OAAO,CAAC;MACtD;MACA;QACE,MAAM,IAAIQ,KAAK,CAAC,yBAAyB,CAAC;;EAEhD;EAEA;EACAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACjB,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC;EACjD;EAEA;EACAkB,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACnB,OAAO,CAAC,KAAK,EAAE,oBAAoBmB,EAAE,EAAE,EAAE,EAAE,CAAC;EAC1D;EAEA;EACAC,YAAYA,CAACC,QAAkB;IAC7B,OAAO,IAAI,CAACrB,OAAO,CAAC,MAAM,EAAE,eAAe,EAAEqB,QAAQ,CAAC;EACxD;EAEA;EACAC,cAAcA,CAACC,UAAkB,EAAEF,QAAkB;IACnD,OAAO,IAAI,CAACrB,OAAO,CAAC,KAAK,EAAC,mBAAmBuB,UAAU,EAAE,EAAE;MAAE,GAAGF;IAAQ,CAAE,CAAC;EAC7E;EAEA;EACAG,cAAcA,CAACD,UAAkB;IAC/B,OAAO,IAAI,CAACvB,OAAO,CAAC,QAAQ,EAAE,mBAAmBuB,UAAU,EAAE,CAAC;EAChE;EAEAE,mBAAmBA,CAACC,MAAc;IAChC,OAAO,IAAI,CAAC1B,OAAO,CAAC,KAAK,EAAE,oBAAoB0B,MAAM,EAAE,EAAE,EAAE,CAAC;EAC9D;EAEC;EAED;EACAC,iBAAiBA,CAACC,aAA6B;IAC7C,OAAO,IAAI,CAAC5B,OAAO,CAAC,MAAM,EAAE,uBAAuB,EAAE4B,aAAa,CAAC;EACrE;EAEA;EACAC,mBAAmBA,CAACC,eAAuB,EAAEF,aAA6B;IACxE,OAAO,IAAI,CAAC5B,OAAO,CAAC,KAAK,EAAE,2BAA2B8B,eAAe,EAAE,EAAEF,aAAa,CAAC;EACzF;EAEA;EACAG,mBAAmBA,CAACD,eAAuB;IACzC,OAAO,IAAI,CAAC9B,OAAO,CAAC,QAAQ,EAAE,2BAA2B8B,eAAe,EAAE,CAAC;EAC7E;EAEA;EACAE,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChC,OAAO,CAAC,KAAK,EAAE,qBAAqB,EAAE,EAAE,CAAC;EACvD;EAEA;EACAiC,oBAAoBA,CAACH,eAAuB;IAC1C,OAAO,IAAI,CAAC9B,OAAO,CAAC,KAAK,EAAE,oBAAoB8B,eAAe,EAAE,EAAE,EAAE,CAAC;EACvE;EAEAI,2BAA2BA,CAACC,QAAgB;IAC1C,OAAO,IAAI,CAACnC,OAAO,CAAC,KAAK,EAAE,8BAA8BmC,QAAQ,EAAE,EAAE,EAAE,CAAC;EAE5E;EAAC,QAAAC,CAAA,G;qBA5GY7C,eAAe,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAflD,eAAe;IAAAmD,OAAA,EAAfnD,eAAe,CAAAoD,IAAA;IAAAC,UAAA,EAFd;EAAM;;SAEPrD,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}