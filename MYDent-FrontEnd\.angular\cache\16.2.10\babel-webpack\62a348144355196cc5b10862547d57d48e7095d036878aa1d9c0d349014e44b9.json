{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../http.service\";\nclass AuthService {\n  constructor(httpService) {\n    this.httpService = httpService;\n  }\n  checkUserTempAvailability(userTempEmail) {\n    return this.httpService.requestWithoutToken('GET', '/auth/user_temp_avb', null, {\n      userTempEmail\n    });\n  }\n  saveUserTemp(userTemp) {\n    return this.httpService.requestWithoutToken('POST', '/auth/user_temp_register', userTemp);\n  }\n  isLoggedIn() {\n    return !!localStorage.getItem('auth_token');\n  }\n  verifyUserTempEmail(token) {\n    return this.httpService.requestWithoutToken('GET', '/auth/verify_user_temp', null, {\n      token\n    });\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { AuthService };", "map": {"version": 3, "names": ["AuthService", "constructor", "httpService", "checkUserTempAvailability", "userTempEmail", "requestWithoutToken", "saveUserTemp", "userTemp", "isLoggedIn", "localStorage", "getItem", "verifyUserTempEmail", "token", "_", "i0", "ɵɵinject", "i1", "HttpService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\auth\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpService } from '../http.service';\r\nimport { UserTemp, UserTempStatus, UserTempType } from './auth';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n  constructor(private httpService: HttpService) {}\r\n\r\n  checkUserTempAvailability(userTempEmail: string): Observable<UserTemp> {\r\n    return this.httpService.requestWithoutToken(\r\n      'GET',\r\n      '/auth/user_temp_avb',\r\n      null,\r\n      { userTempEmail }\r\n    );\r\n  }\r\n\r\n  saveUserTemp(userTemp: UserTemp): Observable<UserTemp> {\r\n    return this.httpService.requestWithoutToken(\r\n      'POST',\r\n      '/auth/user_temp_register',\r\n      userTemp\r\n    );\r\n  }\r\n\r\n  isLoggedIn(): boolean {\r\n    return !!localStorage.getItem('auth_token');\r\n  }\r\n\r\n  verifyUserTempEmail(token: string): Observable<any> {\r\n    return this.httpService.requestWithoutToken(\r\n      'GET',\r\n      '/auth/verify_user_temp',\r\n      null,\r\n      { token }\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": ";;AAKA,MAGaA,WAAW;EACtBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,yBAAyBA,CAACC,aAAqB;IAC7C,OAAO,IAAI,CAACF,WAAW,CAACG,mBAAmB,CACzC,KAAK,EACL,qBAAqB,EACrB,IAAI,EACJ;MAAED;IAAa,CAAE,CAClB;EACH;EAEAE,YAAYA,CAACC,QAAkB;IAC7B,OAAO,IAAI,CAACL,WAAW,CAACG,mBAAmB,CACzC,MAAM,EACN,0BAA0B,EAC1BE,QAAQ,CACT;EACH;EAEAC,UAAUA,CAAA;IACR,OAAO,CAAC,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAC7C;EAEAC,mBAAmBA,CAACC,KAAa;IAC/B,OAAO,IAAI,CAACV,WAAW,CAACG,mBAAmB,CACzC,KAAK,EACL,wBAAwB,EACxB,IAAI,EACJ;MAAEO;IAAK,CAAE,CACV;EACH;EAAC,QAAAC,CAAA,G;qBA/BUb,WAAW,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXlB,WAAW;IAAAmB,OAAA,EAAXnB,WAAW,CAAAoB,IAAA;IAAAC,UAAA,EAFV;EAAM;;SAEPrB,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}