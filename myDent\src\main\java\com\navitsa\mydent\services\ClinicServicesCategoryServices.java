package com.navitsa.mydent.services;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.ClinicServicesCategory;

import com.navitsa.mydent.repositories.ClinicServicesCategoryRepositories;


@Service
public class ClinicServicesCategoryServices {
	
	 private final  ClinicServicesCategoryRepositories  clinicServicesCategoryRepositories;

	    @Autowired
	    public ClinicServicesCategoryServices (ClinicServicesCategoryRepositories  clinicServicesCategoryRepositories) {
	        this.clinicServicesCategoryRepositories = clinicServicesCategoryRepositories;
	    }
	    
	    public ClinicServicesCategory saveClinicServicesCategory(ClinicServicesCategory clinicServicesCategory) {
	        try {
	            return clinicServicesCategoryRepositories.save(clinicServicesCategory);
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new RuntimeException("An error occurred while saving the Clinic Services Category .");
	        }
	    }
	    
	    public List<ClinicServicesCategory> findAllClinicServicesCategory() {
	        try {
	            return clinicServicesCategoryRepositories.findAll();
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new RuntimeException("An error occurred while retrieving the list of Clinic Services Category.");
	        }
	    }
	    
	    public ClinicServicesCategory getClinicServicesCategoryById(int id) {
	        return clinicServicesCategoryRepositories.findById(id).orElse(null);
	    }
	    public ClinicServicesCategory updateClinicServicesCategory(int id, ClinicServicesCategory ClinicServicesCategoryDetails) {
	        try {
	        	ClinicServicesCategory clinicServicesCategory =clinicServicesCategoryRepositories.findById(id).orElse(null);
	            if (clinicServicesCategory == null) {
	                throw new RuntimeException("Clinic Services Category not found with id: " + id);
	            }

	            clinicServicesCategory.setClinicServiceCategoryName(ClinicServicesCategoryDetails.getClinicServiceCategoryName());
	            return clinicServicesCategoryRepositories.save(clinicServicesCategory);
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new RuntimeException("An error occurred while updating the clinic Services Category.");
	        }
	    }
	    public void deleteclinicServicesCategory(int id) {
	        try {
	        	clinicServicesCategoryRepositories.deleteById(id);
	        } catch (Exception e) {
	            e.printStackTrace();
	            throw new RuntimeException("An error occurred while deleting the clinic Services Category.");
	        }
	    }
}
