{"ast": null, "code": "import Swal from 'sweetalert2'; // Import SweetAlert\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../doctor/doctor.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _c0 = function (a0, a1) {\n  return {\n    \"availability available\": a0,\n    \"availability not-available\": a1\n  };\n};\nfunction DoctorListComponent_tr_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"i\", 11);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 12)(13, \"button\", 13);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\", 14)(16, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function DoctorListComponent_tr_31_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r3);\n      const doctor_r1 = restoredCtx.$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.assignDoctor(doctor_r1));\n    });\n    i0.ɵɵelement(17, \"i\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const doctor_r1 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", doctor_r1.firstName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r1.regNo);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r1.specialty);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r1.telephone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r1.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c0, doctor_r1.availability === \"Available\", doctor_r1.availability !== \"Available\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(doctor_r1.availability);\n  }\n}\nclass DoctorListComponent {\n  constructor(doctorService) {\n    this.doctorService = doctorService;\n    this.doctors = []; // Initialize as an empty array\n    this.filteredDoctors = []; // Filtered list for display\n    this.searchTerm = '';\n    this.showModal = false;\n    this.newDoctor = {\n      doctorId: 0,\n      regNo: '',\n      firstName: '',\n      lastName: '',\n      specialty: '',\n      telephone: '',\n      email: '',\n      availability: 'Available'\n    };\n  }\n  ngOnInit() {\n    this.fetchDoctors();\n  }\n  // Fetch doctors from API\n  fetchDoctors() {\n    this.doctorService.getAllDoctorsFilter('notAssign', Number(localStorage.getItem('clinicId'))).subscribe(data => {\n      console.log('Doctors:', data);\n      this.doctors = data;\n      this.filteredDoctors = [];\n    }, error => {\n      console.error('Error fetching doctors:', error);\n    });\n  }\n  // Filter doctors based on the search term\n  searchDoctors() {\n    const searchTermLower = this.searchTerm.toLowerCase().trim();\n    if (searchTermLower === '') {\n      // Do not show any data if search term is empty\n      this.filteredDoctors = [];\n    } else {\n      // Filter doctors by first name or registration number\n      this.filteredDoctors = this.doctors.filter(doctor => doctor.firstName.toLowerCase().includes(searchTermLower) || doctor.regNo && doctor.regNo.toLowerCase().includes(searchTermLower));\n    }\n  }\n  // Show Add Doctor Modal\n  openAddDoctorModal() {\n    this.showModal = true;\n  }\n  // Close Add Doctor Modal\n  closeModal() {\n    this.showModal = false;\n  }\n  // Add a new doctor (locally)\n  addDoctor() {\n    this.doctors.push({\n      ...this.newDoctor\n    });\n    this.filteredDoctors.push({\n      ...this.newDoctor\n    });\n    this.closeModal();\n  }\n  // Assign doctor to clinic\n  assignDoctor(doctor) {\n    const userId = window.localStorage.getItem('userid');\n    console.log('User ID:', userId);\n    if (!userId) {\n      console.error('User ID not found in local storage');\n      return;\n    }\n    this.doctorService.assignDoctorToClinic(userId, doctor.doctorId).subscribe(response => {\n      console.log('Doctor assigned to clinic successfully:', response);\n      this.fetchDoctors(); // Refresh the doctor list\n      // Show success alert with SweetAlert\n      Swal.fire({\n        title: 'Success!',\n        text: 'Doctor assigned to clinic successfully',\n        icon: 'success',\n        confirmButtonText: 'OK'\n      });\n    }, error => {\n      console.error('Error assigning doctor to clinic:', error);\n      // Show error alert with SweetAlert\n      Swal.fire({\n        title: 'Error!',\n        text: 'There was an error assigning the doctor. Please try again.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    });\n  }\n  // Delete doctor locally\n  deleteDoctor(doctor) {\n    const index = this.doctors.indexOf(doctor);\n    if (index > -1) {\n      this.doctors.splice(index, 1);\n      this.filteredDoctors = this.filteredDoctors.filter(d => d.doctorId !== doctor.doctorId); // Remove from filtered list\n    }\n  }\n  static #_ = this.ɵfac = function DoctorListComponent_Factory(t) {\n    return new (t || DoctorListComponent)(i0.ɵɵdirectiveInject(i1.DoctorService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DoctorListComponent,\n    selectors: [[\"app-doctor-list\"]],\n    decls: 32,\n    vars: 2,\n    consts: [[1, \"doctor-container\"], [1, \"header-row\"], [2, \"font-weight\", \"bold\"], [1, \"btn\", \"add-doctor-btn\", 3, \"click\"], [1, \"header-bottom-line\"], [1, \"doctor-list-row\"], [1, \"search-bar\"], [1, \"bi\", \"bi-search\", \"search-icon\"], [\"type\", \"text\", \"placeholder\", \"Search Doctor\", \"id\", \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"doctor-table\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bi\", \"bi-person-circle\", \"profile-icon\"], [1, \"availability-cell\"], [3, \"ngClass\"], [1, \"action-cell\"], [1, \"btn\", \"action-btn\", \"assign-btn\", 3, \"click\"], [1, \"bi\", \"bi-person-plus\"]],\n    template: function DoctorListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n        i0.ɵɵtext(3, \"Doctor\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function DoctorListComponent_Template_button_click_4_listener() {\n          return ctx.openAddDoctorModal();\n        });\n        i0.ɵɵtext(5, \"Add Doctor\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(6, \"div\", 4);\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"h2\", 2);\n        i0.ɵɵtext(9, \"Doctors List\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 6);\n        i0.ɵɵelement(11, \"i\", 7);\n        i0.ɵɵelementStart(12, \"input\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function DoctorListComponent_Template_input_ngModelChange_12_listener($event) {\n          return ctx.searchTerm = $event;\n        })(\"input\", function DoctorListComponent_Template_input_input_12_listener() {\n          return ctx.searchDoctors();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"table\", 9)(14, \"thead\")(15, \"tr\")(16, \"th\");\n        i0.ɵɵtext(17, \"Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"th\");\n        i0.ɵɵtext(19, \"reg_no\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"th\");\n        i0.ɵɵtext(21, \"Specialty\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"th\");\n        i0.ɵɵtext(23, \"Telephone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"th\");\n        i0.ɵɵtext(25, \"Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"th\");\n        i0.ɵɵtext(27, \"Availability\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"th\");\n        i0.ɵɵtext(29, \"Action\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(30, \"tbody\");\n        i0.ɵɵtemplate(31, DoctorListComponent_tr_31_Template, 18, 10, \"tr\", 10);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredDoctors);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel],\n    styles: [\".doctor-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n}\\n\\n.header-row[_ngcontent-%COMP%], .doctor-list-row[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.header-bottom-line[_ngcontent-%COMP%] {\\n    border-bottom: 2px solid #ccc;\\n    margin-top: 10px;\\n}\\n\\n.add-doctor-btn[_ngcontent-%COMP%], .add-doctor-modal-btn[_ngcontent-%COMP%] {\\n    background: linear-gradient(to right, #FB751E, #B93426);\\n    color: white;\\n    border: none;\\n    padding: 10px 20px;\\n    cursor: pointer;\\n    border-radius: 40px;\\n    border-width: 30%;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n    color: #28a745; \\n\\n}\\n\\n\\n.search-bar[_ngcontent-%COMP%] {\\n    position: relative;\\n    margin: 20px 0;\\n    width: 100%;\\n    max-width: 300px;\\n}\\n\\n#search-input[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    width: 100%;\\n    padding-left: 30px;\\n    border: 1px solid #ccc;\\n    border-radius: 40px;\\n    border-width: 30%;\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n    position: absolute;\\n    left: 10px;\\n    top: 50%;\\n    transform: translateY(-50%);\\n    color: #888;\\n}\\n\\n.doctor-table[_ngcontent-%COMP%] {\\n    width: 100%;\\n    border-radius: 10px;\\n    border-collapse: collapse;\\n    margin-top: 20px;\\n}\\n\\n.doctor-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .doctor-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    border: 1px solid #ddd;\\n    padding: 8px;\\n    text-align: left;\\n    border-width: 30%;\\n}\\n\\n.doctor-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background-color: #FFB07D38;\\n    color: rgb(0, 0, 0);\\n}\\n\\n.doctor-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-bottom: 2px solid #ddd;\\n}\\n\\n.profile-icon[_ngcontent-%COMP%] {\\n    color: #FF5722;\\n    margin-right: 8px;\\n    vertical-align: middle;\\n}\\n\\n.availability[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    padding: 5px 10px;\\n    border-width: 30%;\\n}\\n\\n.available[_ngcontent-%COMP%] {\\n    border-radius: 40px;\\n    border-width: 30%;\\n    border: 1px solid;\\n    color: #00C820;\\n    border-color: #00C820;\\n}\\n\\n.not-available[_ngcontent-%COMP%] {\\n    border-radius: 40px;\\n    border-width: 30%;\\n    border: 1px solid;\\n    color: #D85322;\\n    border-color: #D85322;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n    border: none;\\n    background: none;\\n    cursor: pointer;\\n    border-width: 30%;\\n}\\n\\n.delete-btn[_ngcontent-%COMP%] {\\n    color: #D85322;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n    display: none;\\n    position: fixed;\\n    z-index: 1;\\n    left: 0;\\n    top: 0;\\n    width: 100%;\\n    height: 100%;\\n    overflow: auto;\\n    background-color: rgb(0,0,0);\\n    background-color: rgba(0,0,0,0.4);\\n    justify-content: center;\\n    align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n    display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n    background-color: #fefefe;\\n    margin: auto;\\n    padding: 10px 20px;\\n    border: 1px solid #888;\\n    width: 80%;\\n    max-width: 500px;\\n    border-radius: 5px;\\n}\\n\\n.close-btn[_ngcontent-%COMP%] {\\n    color: #aaa;\\n    float: right;\\n    font-size: 28px;\\n    font-weight: bold;\\n    text-align: right;\\n}\\n\\n.close-btn[_ngcontent-%COMP%]:hover, .close-btn[_ngcontent-%COMP%]:focus {\\n    color: black;\\n    text-decoration: none;\\n    cursor: pointer;\\n}\\n\\nform[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: column;\\n}\\n\\nform[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n    margin-top: 10px;\\n}\\n\\nform[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], form[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], form[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    border: 1px solid #ccc;\\n    border-radius: 5px;\\n    margin-top: 5px;\\n    border-width: 30%;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { DoctorListComponent };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DoctorListComponent_tr_31_Template_button_click_16_listener", "restoredCtx", "ɵɵrestoreView", "_r3", "doctor_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "assignDoctor", "ɵɵadvance", "ɵɵtextInterpolate1", "firstName", "ɵɵtextInterpolate", "regNo", "specialty", "telephone", "email", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "availability", "DoctorListComponent", "constructor", "doctorService", "doctors", "filteredDoctors", "searchTerm", "showModal", "newDoctor", "doctorId", "lastName", "ngOnInit", "fetchDoctors", "getAllDoctorsFilter", "Number", "localStorage", "getItem", "subscribe", "data", "console", "log", "error", "searchDoctors", "searchTermLower", "toLowerCase", "trim", "filter", "doctor", "includes", "openAddDoctorModal", "closeModal", "addDoctor", "push", "userId", "window", "assignDoctorToClinic", "response", "fire", "title", "text", "icon", "confirmButtonText", "deleteDoctor", "index", "indexOf", "splice", "d", "_", "ɵɵdirectiveInject", "i1", "DoctorService", "_2", "selectors", "decls", "vars", "consts", "template", "DoctorListComponent_Template", "rf", "ctx", "DoctorListComponent_Template_button_click_4_listener", "DoctorListComponent_Template_input_ngModelChange_12_listener", "$event", "DoctorListComponent_Template_input_input_12_listener", "ɵɵtemplate", "DoctorListComponent_tr_31_Template"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\doctor-list\\doctor-list.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\doctor-list\\doctor-list.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { DoctorService } from '../../doctor/doctor.service';\r\nimport { DoctorList } from './doctorList';\r\nimport Swal from 'sweetalert2'; // Import SweetAlert\r\n\r\n@Component({\r\n  selector: 'app-doctor-list',\r\n  templateUrl: './doctor-list.component.html',\r\n  styleUrls: ['./doctor-list.component.css'],\r\n})\r\nexport class DoctorListComponent implements OnInit {\r\n  doctors: DoctorList[] = []; // Initialize as an empty array\r\n  filteredDoctors: DoctorList[] = []; // Filtered list for display\r\n  searchTerm: string = '';\r\n  showModal: boolean = false;\r\n  newDoctor: DoctorList = {\r\n    doctorId: 0,\r\n    regNo: '',\r\n    firstName: '',\r\n    lastName: '',\r\n    specialty: '',\r\n    telephone: '',\r\n    email: '',\r\n    availability: 'Available',\r\n  };\r\n\r\n  constructor(private doctorService: DoctorService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.fetchDoctors();\r\n  }\r\n\r\n  // Fetch doctors from API\r\n  fetchDoctors() {\r\n    this.doctorService\r\n      .getAllDoctorsFilter(\r\n        'notAssign',\r\n        Number(localStorage.getItem('clinicId'))\r\n      )\r\n      .subscribe(\r\n        (data: DoctorList[]) => {\r\n          console.log('Doctors:', data);\r\n          this.doctors = data;\r\n          this.filteredDoctors = [];\r\n        },\r\n        (error) => {\r\n          console.error('Error fetching doctors:', error);\r\n        }\r\n      );\r\n  }\r\n\r\n  // Filter doctors based on the search term\r\n  searchDoctors(): void {\r\n    const searchTermLower = this.searchTerm.toLowerCase().trim();\r\n\r\n    if (searchTermLower === '') {\r\n      // Do not show any data if search term is empty\r\n      this.filteredDoctors = [];\r\n    } else {\r\n      // Filter doctors by first name or registration number\r\n      this.filteredDoctors = this.doctors.filter(\r\n        (doctor) =>\r\n          doctor.firstName.toLowerCase().includes(searchTermLower) ||\r\n          (doctor.regNo && doctor.regNo.toLowerCase().includes(searchTermLower))\r\n      );\r\n    }\r\n  }\r\n\r\n  // Show Add Doctor Modal\r\n  openAddDoctorModal() {\r\n    this.showModal = true;\r\n  }\r\n\r\n  // Close Add Doctor Modal\r\n  closeModal() {\r\n    this.showModal = false;\r\n  }\r\n\r\n  // Add a new doctor (locally)\r\n  addDoctor() {\r\n    this.doctors.push({ ...this.newDoctor });\r\n    this.filteredDoctors.push({ ...this.newDoctor });\r\n    this.closeModal();\r\n  }\r\n\r\n  // Assign doctor to clinic\r\n  assignDoctor(doctor: DoctorList) {\r\n    const userId = window.localStorage.getItem('userid');\r\n    console.log('User ID:', userId);\r\n    if (!userId) {\r\n      console.error('User ID not found in local storage');\r\n      return;\r\n    }\r\n\r\n    this.doctorService.assignDoctorToClinic(userId, doctor.doctorId).subscribe(\r\n      (response) => {\r\n        console.log('Doctor assigned to clinic successfully:', response);\r\n\r\n        this.fetchDoctors(); // Refresh the doctor list\r\n        // Show success alert with SweetAlert\r\n        Swal.fire({\r\n          title: 'Success!',\r\n          text: 'Doctor assigned to clinic successfully',\r\n          icon: 'success',\r\n          confirmButtonText: 'OK',\r\n        });\r\n      },\r\n      (error) => {\r\n        console.error('Error assigning doctor to clinic:', error);\r\n        // Show error alert with SweetAlert\r\n        Swal.fire({\r\n          title: 'Error!',\r\n          text: 'There was an error assigning the doctor. Please try again.',\r\n          icon: 'error',\r\n          confirmButtonText: 'OK',\r\n        });\r\n      }\r\n    );\r\n  }\r\n\r\n  // Delete doctor locally\r\n  deleteDoctor(doctor: DoctorList) {\r\n    const index = this.doctors.indexOf(doctor);\r\n    if (index > -1) {\r\n      this.doctors.splice(index, 1);\r\n      this.filteredDoctors = this.filteredDoctors.filter(\r\n        (d) => d.doctorId !== doctor.doctorId\r\n      ); // Remove from filtered list\r\n    }\r\n  }\r\n}\r\n", "<div class=\"doctor-container\">\r\n    <div class=\"header-row\">\r\n        <h1 style=\"font-weight: bold;\">Doctor</h1>\r\n        <button class=\"btn add-doctor-btn\" (click)=\"openAddDoctorModal()\">Add Doctor</button>\r\n    </div>\r\n    <div class=\"header-bottom-line\"></div>\r\n\r\n    <div class=\"doctor-list-row\">\r\n        <h2 style=\"font-weight: bold;\">Doctors List</h2>\r\n        <div class=\"search-bar\" >\r\n            <i class=\"bi bi-search search-icon\"></i>\r\n            <input type=\"text\" placeholder=\"Search Doctor\" [(ngModel)]=\"searchTerm\" \r\n            id=\"search-input\" \r\n            (input)=\"searchDoctors()\" />\r\n        </div>\r\n    </div>\r\n\r\n    <table class=\"doctor-table\">\r\n        <thead>\r\n            <tr>\r\n                <th>Name</th>\r\n                <th>reg_no</th>\r\n                <th>Specialty</th>\r\n                <th>Telephone</th>\r\n                <th>Email</th>\r\n                <th>Availability</th>\r\n                <th>Action</th>\r\n            </tr>\r\n        </thead>\r\n        <tbody>\r\n            <tr *ngFor=\"let doctor of filteredDoctors\">\r\n                <td>\r\n                    <i class=\"bi bi-person-circle profile-icon\"></i>\r\n                    {{ doctor.firstName }}\r\n                </td>\r\n                <td>{{ doctor.regNo }}</td>\r\n                <td>{{ doctor.specialty }}</td>\r\n                <td>{{ doctor.telephone }}</td>\r\n                <td>{{ doctor.email }}</td>\r\n                <td class=\"availability-cell\">\r\n                    <button [ngClass]=\"{'availability available': doctor.availability === 'Available', 'availability not-available': doctor.availability !== 'Available'}\">{{ doctor.availability }}</button>\r\n                </td>\r\n               <td class=\"action-cell\">\r\n    <!-- <button class=\"btn action-btn edit-btn\">\r\n        <i class=\"bi bi-pencil\"></i>\r\n    </button> -->\r\n    <!-- <button class=\"btn action-btn delete-btn\" (click)=\"deleteDoctor(doctor)\">\r\n        <i class=\"bi bi-trash\"></i>\r\n    </button> -->\r\n    <!-- Add Assign Doctor icon -->\r\n    <button class=\"btn action-btn assign-btn\" (click)=\"assignDoctor(doctor)\">\r\n        <i class=\"bi bi-person-plus\"></i> <!-- Add icon here -->\r\n    </button>\r\n</td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n    \r\n    <!-- Add Doctor Modal -->\r\n    <!-- <div class=\"modal\" [ngClass]=\"{'show': showModal}\">\r\n        <div class=\"modal-content\">\r\n            <span class=\"close-btn\" (click)=\"closeModal()\">&times;</span>\r\n            <h2>Add Doctor</h2>\r\n            <form (submit)=\"addDoctor()\">\r\n                <label for=\"registrationNumber\">SLMC Registration Number</label>\r\n                <input type=\"text\" id=\"registrationNumber\" [(ngModel)]=\"newDoctor.registrationNumber\" name=\"registrationNumber\" />\r\n\r\n                <label for=\"title\">Title</label>\r\n                <select id=\"title\" [(ngModel)]=\"newDoctor.title\" name=\"title\">\r\n                    <option value=\"Dr.\">Dr.</option>\r\n                    <option value=\"Mr.\">Mr.</option>\r\n                    <option value=\"Ms.\">Ms.</option>\r\n                </select>\r\n\r\n                <label for=\"name\">Name</label>\r\n                <input type=\"text\" id=\"name\" [(ngModel)]=\"newDoctor.name\" name=\"name\" />\r\n\r\n                <label for=\"specialty\">Specialty</label>\r\n                <input type=\"text\" id=\"specialty\" [(ngModel)]=\"newDoctor.specialty\" name=\"specialty\" />\r\n\r\n                <label for=\"qualifications\">Qualifications</label>\r\n                <textarea id=\"qualifications\" [(ngModel)]=\"newDoctor.qualifications\" name=\"qualifications\"></textarea>\r\n\r\n                <label for=\"others\">Others</label>\r\n                <textarea id=\"others\" [(ngModel)]=\"newDoctor.others\" name=\"others\"></textarea>\r\n\r\n                <button type=\"submit\" class=\"btn add-doctor-modal-btn\" style=\"margin-top: 20px;\">Add Doctor</button>\r\n            </form>\r\n        </div>\r\n    </div> -->\r\n</div>\r\n"], "mappings": "AAGA,OAAOA,IAAI,MAAM,aAAa,CAAC,CAAC;;;;;;;;;;;;;;IC2BpBC,EAAA,CAAAC,cAAA,SAA2C;IAEnCD,EAAA,CAAAE,SAAA,YAAgD;IAChDF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,cAA8B;IAC6HD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAE9LJ,EAAA,CAAAC,cAAA,cAAwB;IAQOD,EAAA,CAAAK,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,WAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAASb,EAAA,CAAAc,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAL,SAAA,CAAoB;IAAA,EAAC;IACpEV,EAAA,CAAAE,SAAA,aAAiC;IACrCF,EAAA,CAAAI,YAAA,EAAS;;;;IAnBOJ,EAAA,CAAAgB,SAAA,GACJ;IADIhB,EAAA,CAAAiB,kBAAA,MAAAP,SAAA,CAAAQ,SAAA,MACJ;IACIlB,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAmB,iBAAA,CAAAT,SAAA,CAAAU,KAAA,CAAkB;IAClBpB,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAmB,iBAAA,CAAAT,SAAA,CAAAW,SAAA,CAAsB;IACtBrB,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAmB,iBAAA,CAAAT,SAAA,CAAAY,SAAA,CAAsB;IACtBtB,EAAA,CAAAgB,SAAA,GAAkB;IAAlBhB,EAAA,CAAAmB,iBAAA,CAAAT,SAAA,CAAAa,KAAA,CAAkB;IAEVvB,EAAA,CAAAgB,SAAA,GAA8I;IAA9IhB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAhB,SAAA,CAAAiB,YAAA,kBAAAjB,SAAA,CAAAiB,YAAA,kBAA8I;IAAC3B,EAAA,CAAAgB,SAAA,GAAyB;IAAzBhB,EAAA,CAAAmB,iBAAA,CAAAT,SAAA,CAAAiB,YAAA,CAAyB;;;ADnCpM,MAKaC,mBAAmB;EAgB9BC,YAAoBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAfjC,KAAAC,OAAO,GAAiB,EAAE,CAAC,CAAC;IAC5B,KAAAC,eAAe,GAAiB,EAAE,CAAC,CAAC;IACpC,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,SAAS,GAAe;MACtBC,QAAQ,EAAE,CAAC;MACXhB,KAAK,EAAE,EAAE;MACTF,SAAS,EAAE,EAAE;MACbmB,QAAQ,EAAE,EAAE;MACZhB,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTI,YAAY,EAAE;KACf;EAEkD;EAEnDW,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEA;EACAA,YAAYA,CAAA;IACV,IAAI,CAACT,aAAa,CACfU,mBAAmB,CAClB,WAAW,EACXC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CACzC,CACAC,SAAS,CACPC,IAAkB,IAAI;MACrBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;MAC7B,IAAI,CAACd,OAAO,GAAGc,IAAI;MACnB,IAAI,CAACb,eAAe,GAAG,EAAE;IAC3B,CAAC,EACAgB,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,CACF;EACL;EAEA;EACAC,aAAaA,CAAA;IACX,MAAMC,eAAe,GAAG,IAAI,CAACjB,UAAU,CAACkB,WAAW,EAAE,CAACC,IAAI,EAAE;IAE5D,IAAIF,eAAe,KAAK,EAAE,EAAE;MAC1B;MACA,IAAI,CAAClB,eAAe,GAAG,EAAE;KAC1B,MAAM;MACL;MACA,IAAI,CAACA,eAAe,GAAG,IAAI,CAACD,OAAO,CAACsB,MAAM,CACvCC,MAAM,IACLA,MAAM,CAACpC,SAAS,CAACiC,WAAW,EAAE,CAACI,QAAQ,CAACL,eAAe,CAAC,IACvDI,MAAM,CAAClC,KAAK,IAAIkC,MAAM,CAAClC,KAAK,CAAC+B,WAAW,EAAE,CAACI,QAAQ,CAACL,eAAe,CAAE,CACzE;;EAEL;EAEA;EACAM,kBAAkBA,CAAA;IAChB,IAAI,CAACtB,SAAS,GAAG,IAAI;EACvB;EAEA;EACAuB,UAAUA,CAAA;IACR,IAAI,CAACvB,SAAS,GAAG,KAAK;EACxB;EAEA;EACAwB,SAASA,CAAA;IACP,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CAAC;MAAE,GAAG,IAAI,CAACxB;IAAS,CAAE,CAAC;IACxC,IAAI,CAACH,eAAe,CAAC2B,IAAI,CAAC;MAAE,GAAG,IAAI,CAACxB;IAAS,CAAE,CAAC;IAChD,IAAI,CAACsB,UAAU,EAAE;EACnB;EAEA;EACA1C,YAAYA,CAACuC,MAAkB;IAC7B,MAAMM,MAAM,GAAGC,MAAM,CAACnB,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACpDG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEa,MAAM,CAAC;IAC/B,IAAI,CAACA,MAAM,EAAE;MACXd,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAGF,IAAI,CAAClB,aAAa,CAACgC,oBAAoB,CAACF,MAAM,EAAEN,MAAM,CAAClB,QAAQ,CAAC,CAACQ,SAAS,CACvEmB,QAAQ,IAAI;MACXjB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgB,QAAQ,CAAC;MAEhE,IAAI,CAACxB,YAAY,EAAE,CAAC,CAAC;MACrB;MACAxC,IAAI,CAACiE,IAAI,CAAC;QACRC,KAAK,EAAE,UAAU;QACjBC,IAAI,EAAE,wCAAwC;QAC9CC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,EACApB,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD;MACAjD,IAAI,CAACiE,IAAI,CAAC;QACRC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,4DAA4D;QAClEC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF;EACH;EAEA;EACAC,YAAYA,CAACf,MAAkB;IAC7B,MAAMgB,KAAK,GAAG,IAAI,CAACvC,OAAO,CAACwC,OAAO,CAACjB,MAAM,CAAC;IAC1C,IAAIgB,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACvC,OAAO,CAACyC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC7B,IAAI,CAACtC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACqB,MAAM,CAC/CoB,CAAC,IAAKA,CAAC,CAACrC,QAAQ,KAAKkB,MAAM,CAAClB,QAAQ,CACtC,CAAC,CAAC;;EAEP;EAAC,QAAAsC,CAAA,G;qBAvHU9C,mBAAmB,EAAA5B,EAAA,CAAA2E,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBlD,mBAAmB;IAAAmD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVhCrF,EAAA,CAAAC,cAAA,aAA8B;QAESD,EAAA,CAAAG,MAAA,aAAM;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAC1CJ,EAAA,CAAAC,cAAA,gBAAkE;QAA/BD,EAAA,CAAAK,UAAA,mBAAAkF,qDAAA;UAAA,OAASD,GAAA,CAAA9B,kBAAA,EAAoB;QAAA,EAAC;QAACxD,EAAA,CAAAG,MAAA,iBAAU;QAAAH,EAAA,CAAAI,YAAA,EAAS;QAEzFJ,EAAA,CAAAE,SAAA,aAAsC;QAEtCF,EAAA,CAAAC,cAAA,aAA6B;QACMD,EAAA,CAAAG,MAAA,mBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAChDJ,EAAA,CAAAC,cAAA,cAAyB;QACrBD,EAAA,CAAAE,SAAA,YAAwC;QACxCF,EAAA,CAAAC,cAAA,gBAE4B;QAFmBD,EAAA,CAAAK,UAAA,2BAAAmF,6DAAAC,MAAA;UAAA,OAAAH,GAAA,CAAArD,UAAA,GAAAwD,MAAA;QAAA,EAAwB,mBAAAC,qDAAA;UAAA,OAE9DJ,GAAA,CAAArC,aAAA,EAAe;QAAA,EAF+C;QAAvEjD,EAAA,CAAAI,YAAA,EAE4B;QAIpCJ,EAAA,CAAAC,cAAA,gBAA4B;QAGZD,EAAA,CAAAG,MAAA,YAAI;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACbJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACfJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,iBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAClBJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,iBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAClBJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,aAAK;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACdJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,oBAAY;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACrBJ,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAI,YAAA,EAAK;QAGvBJ,EAAA,CAAAC,cAAA,aAAO;QACHD,EAAA,CAAA2F,UAAA,KAAAC,kCAAA,mBAwBK;QACT5F,EAAA,CAAAI,YAAA,EAAQ;;;QA5C2CJ,EAAA,CAAAgB,SAAA,IAAwB;QAAxBhB,EAAA,CAAAwB,UAAA,YAAA8D,GAAA,CAAArD,UAAA,CAAwB;QAmBhDjC,EAAA,CAAAgB,SAAA,IAAkB;QAAlBhB,EAAA,CAAAwB,UAAA,YAAA8D,GAAA,CAAAtD,eAAA,CAAkB;;;;;;;SDpBxCJ,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}