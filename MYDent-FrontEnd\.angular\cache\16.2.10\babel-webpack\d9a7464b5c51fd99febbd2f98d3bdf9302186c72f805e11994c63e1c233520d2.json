{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from './auth.service';\nexport const authGuard = (route, state) => {\n  const authService = inject(AuthService);\n  const router = inject(Router);\n  if (authService.isLoggedIn()) {\n    return true;\n  } else {\n    router.navigate(['/user-login']);\n    return false;\n  }\n};", "map": {"version": 3, "names": ["inject", "Router", "AuthService", "<PERSON>th<PERSON><PERSON>", "route", "state", "authService", "router", "isLoggedIn", "navigate"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\auth\\auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { AuthService } from './auth.service';\r\n\r\nexport const authGuard: CanActivateFn = (route, state) => {\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  if (authService.isLoggedIn()) {\r\n    return true;\r\n  } else {\r\n    router.navigate(['/user-login']);\r\n    return false;\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,OAAO,MAAMC,SAAS,GAAkBA,CAACC,KAAK,EAAEC,KAAK,KAAI;EACvD,MAAMC,WAAW,GAAGN,MAAM,CAACE,WAAW,CAAC;EACvC,MAAMK,MAAM,GAAGP,MAAM,CAACC,MAAM,CAAC;EAE7B,IAAIK,WAAW,CAACE,UAAU,EAAE,EAAE;IAC5B,OAAO,IAAI;GACZ,MAAM;IACLD,MAAM,CAACE,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IAChC,OAAO,KAAK;;AAEhB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}