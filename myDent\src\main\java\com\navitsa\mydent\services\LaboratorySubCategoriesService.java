package com.navitsa.mydent.services;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.navitsa.mydent.entity.LaboratorySubCategories;
import com.navitsa.mydent.repositories.LaboratorySubCategoriesRepository;
import com.navitsa.mydent.entity.LaboratoryCategories;
import com.navitsa.mydent.repositories.LaboratoryCategoriesRepository;

@Service
public class LaboratorySubCategoriesService {
	
    private final LaboratorySubCategoriesRepository laboratorySubCategoriesRepository;
    private final LaboratoryCategoriesRepository laboratoryCategoriesRepository;
    
    @Autowired
    public  LaboratorySubCategoriesService( LaboratorySubCategoriesRepository laboratorySubCategoriesRepository, LaboratoryCategoriesRepository laboratoryCategoriesRepository) {
        this.laboratorySubCategoriesRepository = laboratorySubCategoriesRepository;
        this.laboratoryCategoriesRepository = laboratoryCategoriesRepository;
    }

    public List<LaboratorySubCategories> getLaboratorySubCategoriesById(int id) {
        LaboratoryCategories laboratoryCategories = laboratoryCategoriesRepository.findById(id);
        
        if (laboratoryCategories == null) {
            throw new RuntimeException("Laboratory Category not found");
        }

        try {
            return laboratorySubCategoriesRepository.findByLaboratoryCategoryId(laboratoryCategories);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory subcategories.");
        }
    }

}
