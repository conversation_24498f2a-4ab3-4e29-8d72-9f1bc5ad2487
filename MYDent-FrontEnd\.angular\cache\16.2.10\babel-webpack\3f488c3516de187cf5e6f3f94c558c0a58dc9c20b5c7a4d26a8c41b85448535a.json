{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../user.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction UserForgetPasswordComponent_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 12);\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserForgetPasswordComponent_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 12);\n    i0.ɵɵtext(1, \" Invalid email format. \");\n    i0.ɵɵelementEnd();\n  }\n}\nclass UserForgetPasswordComponent {\n  constructor(route, userService, router, fb) {\n    this.route = route;\n    this.userService = userService;\n    this.router = router;\n    this.fb = fb;\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]]\n    });\n  }\n  ngOnInit() {}\n  verifyUrlGenarate() {\n    const emailValue = this.loginForm.get('email')?.value;\n    this.userService.verifyEmailForget(emailValue).subscribe(response => {\n      // <-- Type the response as any\n      console.log(response);\n      if (response.status === 'true') {\n        this.router.navigate(['/']);\n      } else {\n        console.log('Password change failed or user not found');\n        // Show error message\n      }\n    }, error => {\n      console.error('Error calling backend:', error);\n      // Handle network/server error\n    });\n  }\n  static #_ = this.ɵfac = function UserForgetPasswordComponent_Factory(t) {\n    return new (t || UserForgetPasswordComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i3.FormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserForgetPasswordComponent,\n    selectors: [[\"app-user-forget-password\"]],\n    decls: 17,\n    vars: 3,\n    consts: [[1, \"page-background\"], [1, \"rec1\"], [1, \"rec2\"], [1, \"verification-container\"], [1, \"verification-content\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"col-12\"], [\"for\", \"email-address\", 1, \"input-label\", \",\", \"mb-3\"], [\"type\", \"email\", \"id\", \"email-address\", \"name\", \"email-address\", \"formControlName\", \"email\", 1, \"form-control\"], [1, \"px-1\", 2, \"font-weight\", \"500\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"type\", \"submit\"], [1, \"text-danger\"]],\n    template: function UserForgetPasswordComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\");\n        i0.ɵɵtext(6, \" Email Verification\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"form\", 5);\n        i0.ɵɵlistener(\"ngSubmit\", function UserForgetPasswordComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.verifyUrlGenarate();\n        });\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"label\", 7);\n        i0.ɵɵtext(10, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"input\", 8);\n        i0.ɵɵelementStart(12, \"div\", 9);\n        i0.ɵɵtemplate(13, UserForgetPasswordComponent_small_13_Template, 2, 0, \"small\", 10);\n        i0.ɵɵtemplate(14, UserForgetPasswordComponent_small_14_Template, 2, 0, \"small\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"button\", 11);\n        i0.ɵɵtext(16, \"Submit\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.hasError(\"required\")) && ((tmp_1_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.hasError(\"email\")) && ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n      }\n    },\n    dependencies: [i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName],\n    styles: [\"html[_ngcontent-%COMP%], body[_ngcontent-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n  font-family: 'Arial', sans-serif;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  height: 100%;\\n  width: 100%;\\n  background-color: #f4f4f9;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.verification-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  z-index: 10;\\n  position: relative;\\n  padding: 20px; \\n\\n}\\n\\n.verification-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #ff6b00;\\n  font-weight: bold;\\n  margin-bottom: 30px;\\n  font-size: 24px; \\n\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background-color: #ff6b00;\\n  border: #ff6b00 1px solid;\\n  border-radius: 15px;\\n  color: white;\\n  margin-top: 20px;\\n  padding: 10px 20px;\\n  font-size: 16px; \\n\\n  width: 100%; \\n\\n  max-width: 200px;\\n}\\n\\np[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px; \\n\\n}\\n\\n.rec1[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  left: 740px;\\n  width: 900px;\\n  height: 1600px;\\n  background: #FB751E;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(72deg);\\n}\\n\\n.rec2[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 225px;\\n  left: -400px;\\n  width: 900px;\\n  height: 1800px;\\n  border: #FB751E 1px solid;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(40deg);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 20px; \\n\\n    margin-bottom: 20px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px; \\n\\n    max-width: 100%; \\n\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 600px; \\n\\n    height: 1200px;\\n    left: 500px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 600px;\\n    height: 1400px;\\n    left: -300px; \\n\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 15px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    padding: 8px 15px;\\n    max-width: 100%;\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1000px;\\n    left: 400px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1200px;\\n    left: -250px;\\n  }\\n  \\n}\\n\\ninput[type=\\\"email\\\"][_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border: 1px solid #fb751e;\\n  \\n\\n  box-shadow: none;\\n  transition: 0.2s ease-in-out;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { UserForgetPasswordComponent };", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "UserForgetPasswordComponent", "constructor", "route", "userService", "router", "fb", "loginForm", "group", "email", "required", "ngOnInit", "verifyUrlGenarate", "emailValue", "get", "value", "verifyEmailForget", "subscribe", "response", "console", "log", "status", "navigate", "error", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "UserService", "Router", "i3", "FormBuilder", "_2", "selectors", "decls", "vars", "consts", "template", "UserForgetPasswordComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "UserForgetPasswordComponent_Template_form_ngSubmit_7_listener", "ɵɵtemplate", "UserForgetPasswordComponent_small_13_Template", "UserForgetPasswordComponent_small_14_Template", "ɵɵadvance", "ɵɵproperty", "tmp_1_0", "<PERSON><PERSON><PERSON><PERSON>", "touched", "tmp_2_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-forget-password\\user-forget-password.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-forget-password\\user-forget-password.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UserService } from '../user.service';\r\nimport {\r\n  FormBuilder,\r\n  FormControl,\r\n  FormGroup,\r\n  Validators,\r\n} from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-user-forget-password',\r\n  templateUrl: './user-forget-password.component.html',\r\n  styleUrls: ['./user-forget-password.component.css'],\r\n})\r\nexport class UserForgetPasswordComponent implements OnInit {\r\n  loginForm: FormGroup;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private userService: UserService,\r\n    private router: Router,\r\n    private fb: FormBuilder\r\n  ) {\r\n    this.loginForm = this.fb.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {}\r\n\r\n  verifyUrlGenarate(): void {\r\n    const emailValue = this.loginForm.get('email')?.value;\r\n    this.userService.verifyEmailForget(emailValue).subscribe(\r\n      (response: any) => {\r\n        // <-- Type the response as any\r\n        console.log(response);\r\n        if (response.status === 'true') {\r\n          this.router.navigate(['/']);\r\n        } else {\r\n          console.log('Password change failed or user not found');\r\n          // Show error message\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error calling backend:', error);\r\n        // Handle network/server error\r\n      }\r\n    );\r\n  }\r\n}\r\n", "<div class=\"page-background\">\r\n  <div class=\"rec1\"></div>\r\n  <div class=\"rec2\"></div>\r\n  <div class=\"verification-container\">\r\n    <div class=\"verification-content\">\r\n      <h2> Email Verification</h2>\r\n\r\n<form [formGroup]=\"loginForm\" (ngSubmit)=\"verifyUrlGenarate()\">\r\n  <div class=\"col-12\">\r\n    <label class=\"input-label , mb-3\"  for=\"email-address\">Email Address</label>\r\n    <input\r\n      type=\"email\"\r\n      id=\"email-address\"\r\n      name=\"email-address\"\r\n      formControlName=\"email\"\r\n      class=\"form-control\"\r\n    />\r\n\r\n    <div class=\"px-1\" style=\"font-weight: 500;\">\r\n      <!-- Required validation -->\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched\"\r\n      >\r\n        Email is required.\r\n      </small>\r\n\r\n      <!-- Invalid email format validation -->\r\n      <small\r\n        class=\"text-danger\"\r\n        *ngIf=\"loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched\"\r\n      >\r\n        Invalid email format.\r\n      </small>\r\n    </div>\r\n  </div>\r\n\r\n  <button type=\"submit\" >Submit</button>\r\n</form>\r\n\r\n\r\n    \r\n     \r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA,SAIEA,UAAU,QACL,gBAAgB;;;;;;;;ICYjBC,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAGRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;ADvBd,MAKaC,2BAA2B;EAGtCC,YACUC,KAAqB,EACrBC,WAAwB,EACxBC,MAAc,EACdC,EAAe;IAHf,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IAEV,IAAI,CAACC,SAAS,GAAG,IAAI,CAACD,EAAE,CAACE,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACb,UAAU,CAACc,QAAQ,EAAEd,UAAU,CAACa,KAAK,CAAC;KACpD,CAAC;EACJ;EAEAE,QAAQA,CAAA,GAAU;EAElBC,iBAAiBA,CAAA;IACf,MAAMC,UAAU,GAAG,IAAI,CAACN,SAAS,CAACO,GAAG,CAAC,OAAO,CAAC,EAAEC,KAAK;IACrD,IAAI,CAACX,WAAW,CAACY,iBAAiB,CAACH,UAAU,CAAC,CAACI,SAAS,CACrDC,QAAa,IAAI;MAChB;MACAC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;MACrB,IAAIA,QAAQ,CAACG,MAAM,KAAK,MAAM,EAAE;QAC9B,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;OAC5B,MAAM;QACLH,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;;IAEJ,CAAC,EACAG,KAAK,IAAI;MACRJ,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC,CACF;EACH;EAAC,QAAAC,CAAA,G;qBAlCUvB,2BAA2B,EAAAJ,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9B,EAAA,CAAA4B,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhC,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAI,MAAA,GAAAjC,EAAA,CAAA4B,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BhC,2BAA2B;IAAAiC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCfxC3C,EAAA,CAAAC,cAAA,aAA6B;QAC3BD,EAAA,CAAA6C,SAAA,aAAwB;QAExB7C,EAAA,CAAAC,cAAA,aAAoC;QAE3BD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAElCH,EAAA,CAAAC,cAAA,cAA+D;QAAjCD,EAAA,CAAA8C,UAAA,sBAAAC,8DAAA;UAAA,OAAYH,GAAA,CAAA7B,iBAAA,EAAmB;QAAA,EAAC;QAC5Df,EAAA,CAAAC,cAAA,aAAoB;QACqCD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5EH,EAAA,CAAA6C,SAAA,gBAME;QAEF7C,EAAA,CAAAC,cAAA,cAA4C;QAE1CD,EAAA,CAAAgD,UAAA,KAAAC,6CAAA,oBAKQ;QAGRjD,EAAA,CAAAgD,UAAA,KAAAE,6CAAA,oBAKQ;QACVlD,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,kBAAuB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;QA9BlCH,EAAA,CAAAmD,SAAA,GAAuB;QAAvBnD,EAAA,CAAAoD,UAAA,cAAAR,GAAA,CAAAlC,SAAA,CAAuB;QAepBV,EAAA,CAAAmD,SAAA,GAAqF;QAArFnD,EAAA,CAAAoD,UAAA,WAAAC,OAAA,GAAAT,GAAA,CAAAlC,SAAA,CAAAO,GAAA,4BAAAoC,OAAA,CAAAC,QAAA,mBAAAD,OAAA,GAAAT,GAAA,CAAAlC,SAAA,CAAAO,GAAA,4BAAAoC,OAAA,CAAAE,OAAA,EAAqF;QAQrFvD,EAAA,CAAAmD,SAAA,GAAkF;QAAlFnD,EAAA,CAAAoD,UAAA,WAAAI,OAAA,GAAAZ,GAAA,CAAAlC,SAAA,CAAAO,GAAA,4BAAAuC,OAAA,CAAAF,QAAA,gBAAAE,OAAA,GAAAZ,GAAA,CAAAlC,SAAA,CAAAO,GAAA,4BAAAuC,OAAA,CAAAD,OAAA,EAAkF;;;;;;;SDf9EnD,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}