import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AppointmentsService } from '../appointments.service';
import { Appointments } from '../appointments';
import { Doctor } from 'src/app/doctor/doctor';
import Swal from 'sweetalert2';

interface TimeSlot {
  startTime: string;
  endTime: string;
  displayTime: string;
  available: boolean;
  availableDoctors: Doctor[];
}

interface ClinicSchedule {
  scheduleId: number;
  date: string;
  fromTime: string;
  toTime: string;
  clinics: { clinicId: number };
}

@Component({
  selector: 'app-appointment-booking',
  templateUrl: './appointment-booking.component.html',
  styleUrls: ['./appointment-booking.component.css']
})
export class AppointmentBookingComponent implements OnInit {
  appointmentForm: FormGroup;
  showDoctorModal = false;
  selectedDate: string = '';
  availableTimeSlots: TimeSlot[] = [];
  selectedTimeSlot: TimeSlot | null = null;
  selectedDoctor: Doctor | null = null;
  clinics: any[] = [];
  services: any[] = [];
  
  constructor(
    private fb: FormBuilder,
    private router: Router,
    private appointmentsService: AppointmentsService
  ) {
    this.appointmentForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]+$/)]],
      lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\s]+$/)]],
      email: ['', [Validators.required, Validators.email]],
      telephone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      preferredService: ['', Validators.required],
      preferredDate: ['', Validators.required],
      fromTime: [''],
      toTime: [''],
      doctorName: [''],
      clinicId: ['']
    });
  }

  ngOnInit(): void {
    this.loadServices();
    this.loadClinics();
  }

  loadServices(): void {
    this.appointmentsService.getServicesList().subscribe(
      (services) => {
        this.services = services;
      },
      (error) => {
        console.error('Error loading services:', error);
      }
    );
  }

  loadClinics(): void {
    this.appointmentsService.getClinicsList().subscribe(
      (clinics) => {
        this.clinics = clinics;
      },
      (error) => {
        console.error('Error loading clinics:', error);
      }
    );
  }

  onDateChange(): void {
    const selectedDate = this.appointmentForm.get('preferredDate')?.value;
    if (selectedDate) {
      this.selectedDate = selectedDate;
      this.checkScheduleAndGenerateTimeSlots();
    }
  }

  checkScheduleAndGenerateTimeSlots(): void {
    const preferredService = this.appointmentForm.get('preferredService')?.value;

    if (!preferredService) {
      Swal.fire({
        title: 'Service Required!',
        text: 'Please select a preferred service first.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return;
    }

    // Find clinics that offer the selected service
    this.appointmentsService.findClinics(
      this.selectedDate,
      '09:00', // Default time for clinic search
      '', // No specific city filter for now
      parseInt(preferredService)
    ).subscribe(
      (clinicsResponse) => {
        if (clinicsResponse && clinicsResponse.length > 0) {
          // Use the first available clinic for now
          const selectedClinic = clinicsResponse[0];
          this.appointmentForm.patchValue({
            clinicId: selectedClinic.clinicId
          });

          // Get available time slots for this clinic
          this.getAvailableTimeSlots(selectedClinic.clinicId);
        } else {
          Swal.fire({
            title: 'No Clinics Available!',
            text: 'No clinics are available for the selected service and date.',
            icon: 'info',
            confirmButtonText: 'OK'
          });
        }
      },
      (error) => {
        console.error('Error finding clinics:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to find available clinics. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    );
  }

  getAvailableTimeSlots(clinicId: number): void {
    this.appointmentsService.getAvailableTimeSlots(clinicId, this.selectedDate).subscribe(
      (response) => {
        if (response.success) {
          this.availableTimeSlots = response.timeSlots || [];
          this.showDoctorModal = true;
        } else {
          if (response.needsScheduleCreation) {
            Swal.fire({
              title: 'No Schedule Found!',
              text: `No schedule found for ${response.dayName}. Please contact the clinic to set up a schedule for this day.`,
              icon: 'info',
              confirmButtonText: 'OK'
            });
          } else {
            Swal.fire({
              title: 'No Time Slots!',
              text: response.message || 'No available time slots for the selected date.',
              icon: 'info',
              confirmButtonText: 'OK'
            });
          }
        }
      },
      (error) => {
        console.error('Error getting time slots:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to get available time slots. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    );
  }

  selectDoctorAndTimeSlot(doctor: Doctor, timeSlot: TimeSlot): void {
    this.selectedDoctor = doctor;
    this.selectedTimeSlot = timeSlot;
    
    // Auto-fill the form
    this.appointmentForm.patchValue({
      fromTime: timeSlot.startTime,
      toTime: timeSlot.endTime,
      doctorName: `${doctor.title} ${doctor.firstName} ${doctor.lastName}`
    });
    
    this.showDoctorModal = false;
  }

  closeModal(): void {
    this.showDoctorModal = false;
  }

  getTodayDate(): string {
    const today = new Date();
    return today.toISOString().split('T')[0];
  }

  onSubmit(): void {
    if (this.appointmentForm.valid && this.selectedDoctor) {
      const formValues = this.appointmentForm.value;

      const appointment: Appointments = {
        appointmentId: 0,
        firstName: formValues.firstName,
        lastName: formValues.lastName,
        email: formValues.email,
        telephone: formValues.telephone,
        preferredservice: formValues.preferredService,
        fromDate: formValues.preferredDate,
        toDate: formValues.preferredDate,
        fromTime: formValues.fromTime,
        toTime: formValues.toTime,
        clinics: { clinicId: formValues.clinicId },
        doctor: this.selectedDoctor,
        status: 'Pending',
        // Other required fields with default values
        address: '',
        city: '',
        state: '',
        district: '',
        nearestCity: '',
        userName: '',
        password: '',
        customer: { customerId: 0 } as any
      } as any;

      this.appointmentsService.saveAppointments(appointment).subscribe(
        (response) => {
          Swal.fire({
            title: 'Success!',
            text: 'Appointment booked successfully!',
            icon: 'success',
            confirmButtonText: 'OK'
          }).then(() => {
            this.router.navigate(['/appointments']);
          });
        },
        (error) => {
          console.error('Error saving appointment:', error);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to book appointment. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK'
          });
        }
      );
    } else {
      this.appointmentForm.markAllAsTouched();
      if (!this.selectedDoctor) {
        Swal.fire({
          title: 'Missing Selection!',
          text: 'Please select a doctor and time slot.',
          icon: 'warning',
          confirmButtonText: 'OK'
        });
      }
    }
  }
}
