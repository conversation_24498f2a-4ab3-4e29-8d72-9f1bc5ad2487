{"ast": null, "code": "import * as i0 from \"@angular/core\";\nclass RejectionReasonPopupComponent {\n  constructor() {\n    this.showReasonPopup = false; // To toggle the RejectionReasonPopupComponent visibility\n  }\n  // Called when the \"Yes\" button is clicked\n  onOpenReasonPopup() {\n    this.showReasonPopup = true; // Display the RejectionReasonPopupComponent\n  }\n  // Called when the RejectionReasonPopupComponent is closed\n  handlePopupClose(reason) {\n    this.showReasonPopup = false; // Hide the RejectionReasonPopupComponent\n    if (reason) {\n      // Handle the rejection reason (you can send it to the backend or process it)\n      console.log('Rejection reason submitted:', reason);\n    }\n  }\n  // Close the current popup (reject-popup)\n  onClose() {\n    // You can implement the logic to close the reject-popup here\n    console.log('Reject popup closed');\n  }\n  static #_ = this.ɵfac = function RejectionReasonPopupComponent_Factory(t) {\n    return new (t || RejectionReasonPopupComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RejectionReasonPopupComponent,\n    selectors: [[\"app-rejection-reason-popup\"]],\n    decls: 2,\n    vars: 0,\n    template: function RejectionReasonPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"rejection-reason-popup works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { RejectionReasonPopupComponent };", "map": {"version": 3, "names": ["RejectionReasonPopupComponent", "constructor", "showReasonPopup", "onOpenReasonPopup", "handlePopupClose", "reason", "console", "log", "onClose", "_", "_2", "selectors", "decls", "vars", "template", "RejectionReasonPopupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\components\\rejection-reason-popup\\rejection-reason-popup.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\components\\rejection-reason-popup\\rejection-reason-popup.component.html"], "sourcesContent": ["import { Component, Output, EventEmitter } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-rejection-reason-popup',\r\n  templateUrl: './rejection-reason-popup.component.html',\r\n  styleUrls: ['./rejection-reason-popup.component.css']\r\n})\r\nexport class RejectionReasonPopupComponent {\r\n  showReasonPopup: boolean = false; // To toggle the RejectionReasonPopupComponent visibility\r\n\r\n  // Called when the \"Yes\" button is clicked\r\n  onOpenReasonPopup() {\r\n    this.showReasonPopup = true; // Display the RejectionReasonPopupComponent\r\n  }\r\n\r\n  // Called when the RejectionReasonPopupComponent is closed\r\n  handlePopupClose(reason: string) {\r\n    this.showReasonPopup = false; // Hide the RejectionReasonPopupComponent\r\n    if (reason) {\r\n      // Handle the rejection reason (you can send it to the backend or process it)\r\n      console.log('Rejection reason submitted:', reason);\r\n    }\r\n  }\r\n\r\n  // Close the current popup (reject-popup)\r\n  onClose() {\r\n    // You can implement the logic to close the reject-popup here\r\n    console.log('Reject popup closed');\r\n  }\r\n}\r\n", "<p>rejection-reason-popup works!</p>\r\n"], "mappings": ";AAEA,MAKaA,6BAA6B;EAL1CC,YAAA;IAME,KAAAC,eAAe,GAAY,KAAK,CAAC,CAAC;;EAElC;EACAC,iBAAiBA,CAAA;IACf,IAAI,CAACD,eAAe,GAAG,IAAI,CAAC,CAAC;EAC/B;EAEA;EACAE,gBAAgBA,CAACC,MAAc;IAC7B,IAAI,CAACH,eAAe,GAAG,KAAK,CAAC,CAAC;IAC9B,IAAIG,MAAM,EAAE;MACV;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,MAAM,CAAC;;EAEtD;EAEA;EACAG,OAAOA,CAAA;IACL;IACAF,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAAC,QAAAE,CAAA,G;qBArBUT,6BAA6B;EAAA;EAAA,QAAAU,EAAA,G;UAA7BV,6BAA6B;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP1CE,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,oCAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;SDOvBrB,6BAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}