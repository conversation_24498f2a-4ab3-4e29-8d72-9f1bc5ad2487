package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.SupplierOrderHeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SupplierOrderHeaderRepository extends JpaRepository<SupplierOrderHeader,Integer> {
    @Query("SELECT soh FROM SupplierOrderHeader soh WHERE soh.supplier.userId.userId = :userId")
    Optional<List<SupplierOrderHeader>> findAllSuppliersByUserId(@Param("userId") Integer userId);

    @Query("SELECT soh FROM SupplierOrderHeader soh WHERE soh.clinic.userId.userId = :userId")
    Optional<List<SupplierOrderHeader>> findAllClinicsByUserId(@Param("userId") Integer userId);
}
