{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nclass AppointmentsService {\n  constructor(http) {\n    this.http = http;\n    this.baseURL = environment.apiUrl;\n  }\n  getAuthToken() {\n    return window.localStorage.getItem('auth_token');\n  }\n  request(method, url, data, params) {\n    let headers = new HttpHeaders();\n    if (this.getAuthToken() !== null) {\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\n    }\n    const options = {\n      headers: headers,\n      params: new HttpParams({\n        fromObject: params\n      })\n    };\n    switch (method.toUpperCase()) {\n      case 'GET':\n        return this.http.get(this.baseURL + url, options);\n      case 'POST':\n        return this.http.post(this.baseURL + url, data, options);\n      case 'PUT':\n        return this.http.put(this.baseURL + url, data, options);\n      case 'DELETE':\n        return this.http.delete(this.baseURL + url, options);\n      // Add more HTTP methods as needed\n      default:\n        throw new Error('Unsupported HTTP method');\n    }\n  }\n  //appointments\n  saveAppointments(appointments) {\n    return this.request('POST', '/saveAppointments', appointments);\n  }\n  saveCustomerAppointments(appointments) {\n    return this.request('POST', '/saveCustomerAppointment', appointments);\n  }\n  appointmentsList() {\n    return this.request('GET', '/appointmentsList', {});\n  }\n  getAppointmentsById(id) {\n    return this.request('GET', '/getAppointmentsById/{id}', {});\n  }\n  updateAppointments(id, appointments) {\n    return this.request('PUT', '/updateAppointments/{id}', appointments);\n  }\n  deleteAppointments(id) {\n    return this.request('DELETE', '/deleteAppointments/{id}', {});\n  }\n  //customer\n  saveCustomer(customer) {\n    return this.request('POST', '/saveCustomer', customer);\n  }\n  findClinics(fromDate, fromTime, nearestCity, preferredService) {\n    const params = new HttpParams().set('date', fromDate).set('time', fromTime).set('city', nearestCity).set('serviceId', preferredService.toString());\n    return this.http.get(`${this.baseURL}/getClinicForAppointment`, {\n      params\n    });\n  }\n  getAvailableTimeSlots(clinicId, date) {\n    const params = new HttpParams().set('clinicId', clinicId.toString()).set('date', date);\n    return this.http.get(`${this.baseURL}/getAvailableTimeSlots`, {\n      params\n    });\n  }\n  getAllAppointmentsInDates(clinicId, fromDate, page) {\n    return this.request('GET', `/getAllAppointmentInDates?clinicId=${clinicId}&fromDate=${fromDate}&page=${page}`, {});\n  }\n  static #_ = this.ɵfac = function AppointmentsService_Factory(t) {\n    return new (t || AppointmentsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AppointmentsService,\n    factory: AppointmentsService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { AppointmentsService };", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "environment", "AppointmentsService", "constructor", "http", "baseURL", "apiUrl", "getAuthToken", "window", "localStorage", "getItem", "request", "method", "url", "data", "params", "headers", "set", "options", "fromObject", "toUpperCase", "get", "post", "put", "delete", "Error", "saveAppointments", "appointments", "saveCustomerAppointments", "appointmentsList", "getAppointmentsById", "id", "updateAppointments", "deleteAppointments", "saveCustomer", "customer", "findClinics", "fromDate", "fromTime", "nearestCity", "preferredService", "toString", "getAvailableTimeSlots", "clinicId", "date", "getAllAppointmentsInDates", "page", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointments.service.ts"], "sourcesContent": ["import { Appointments } from './appointments';\r\nimport { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Customer } from './customer';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class AppointmentsService {\r\n  private readonly baseURL = environment.apiUrl;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getAuthToken(): string | null {\r\n    return window.localStorage.getItem('auth_token');\r\n  }\r\n\r\n  request(\r\n    method: string,\r\n    url: string,\r\n    data: any,\r\n    params?: any\r\n  ): Observable<any> {\r\n    let headers = new HttpHeaders();\r\n\r\n    if (this.getAuthToken() !== null) {\r\n      headers = headers.set('Authorization', 'Bearer ' + this.getAuthToken());\r\n    }\r\n\r\n    const options = {\r\n      headers: headers,\r\n      params: new HttpParams({ fromObject: params }),\r\n    };\r\n\r\n    switch (method.toUpperCase()) {\r\n      case 'GET':\r\n        return this.http.get(this.baseURL + url, options);\r\n      case 'POST':\r\n        return this.http.post(this.baseURL + url, data, options);\r\n      case 'PUT':\r\n        return this.http.put(this.baseURL + url, data, options);\r\n      case 'DELETE':\r\n        return this.http.delete(this.baseURL + url, options);\r\n      // Add more HTTP methods as needed\r\n      default:\r\n        throw new Error('Unsupported HTTP method');\r\n    }\r\n  }\r\n\r\n  //appointments\r\n\r\n  saveAppointments(appointments: Appointments): Observable<any> {\r\n    return this.request('POST', '/saveAppointments', appointments);\r\n  }\r\n\r\n  saveCustomerAppointments(appointments: Appointments): Observable<any> {\r\n    return this.request('POST', '/saveCustomerAppointment', appointments);\r\n  }\r\n\r\n  appointmentsList(): Observable<Appointments[]> {\r\n    return this.request('GET', '/appointmentsList', {});\r\n  }\r\n\r\n  getAppointmentsById(id: number): Observable<Appointments> {\r\n    return this.request('GET', '/getAppointmentsById/{id}', {});\r\n  }\r\n\r\n  updateAppointments(\r\n    id: number,\r\n    appointments: Appointments\r\n  ): Observable<object> {\r\n    return this.request('PUT', '/updateAppointments/{id}', appointments);\r\n  }\r\n\r\n  deleteAppointments(id: number): Observable<any> {\r\n    return this.request('DELETE', '/deleteAppointments/{id}', {});\r\n  }\r\n\r\n  //customer\r\n  saveCustomer(customer: Customer): Observable<any> {\r\n    return this.request('POST', '/saveCustomer', customer);\r\n  }\r\n\r\n  findClinics(\r\n    fromDate: string,\r\n    fromTime: string,\r\n    nearestCity: string,\r\n    preferredService: number\r\n  ): Observable<any> {\r\n    const params = new HttpParams()\r\n      .set('date', fromDate)\r\n      .set('time', fromTime)\r\n      .set('city', nearestCity)\r\n      .set('serviceId', preferredService.toString());\r\n\r\n    return this.http.get(`${this.baseURL}/getClinicForAppointment`, { params });\r\n  }\r\n\r\n  getAvailableTimeSlots(clinicId: number, date: string): Observable<any> {\r\n    const params = new HttpParams()\r\n      .set('clinicId', clinicId.toString())\r\n      .set('date', date);\r\n\r\n    return this.http.get(`${this.baseURL}/getAvailableTimeSlots`, { params });\r\n  }\r\n\r\n  getAllAppointmentsInDates(\r\n    clinicId: number,\r\n    fromDate: string,\r\n    page: number,\r\n  ): Observable<any> {\r\n    return this.request(\r\n      'GET',\r\n      `/getAllAppointmentInDates?clinicId=${clinicId}&fromDate=${fromDate}&page=${page}`,\r\n      {}\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AACA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;AAG1E,SAASC,WAAW,QAAQ,8BAA8B;;;AAG1D,MAGaC,mBAAmB;EAG9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;EAEN;EAEvCC,YAAYA,CAAA;IACV,OAAOC,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;EAClD;EAEAC,OAAOA,CACLC,MAAc,EACdC,GAAW,EACXC,IAAS,EACTC,MAAY;IAEZ,IAAIC,OAAO,GAAG,IAAIjB,WAAW,EAAE;IAE/B,IAAI,IAAI,CAACQ,YAAY,EAAE,KAAK,IAAI,EAAE;MAChCS,OAAO,GAAGA,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,SAAS,GAAG,IAAI,CAACV,YAAY,EAAE,CAAC;;IAGzE,MAAMW,OAAO,GAAG;MACdF,OAAO,EAAEA,OAAO;MAChBD,MAAM,EAAE,IAAIf,UAAU,CAAC;QAAEmB,UAAU,EAAEJ;MAAM,CAAE;KAC9C;IAED,QAAQH,MAAM,CAACQ,WAAW,EAAE;MAC1B,KAAK,KAAK;QACR,OAAO,IAAI,CAAChB,IAAI,CAACiB,GAAG,CAAC,IAAI,CAAChB,OAAO,GAAGQ,GAAG,EAAEK,OAAO,CAAC;MACnD,KAAK,MAAM;QACT,OAAO,IAAI,CAACd,IAAI,CAACkB,IAAI,CAAC,IAAI,CAACjB,OAAO,GAAGQ,GAAG,EAAEC,IAAI,EAAEI,OAAO,CAAC;MAC1D,KAAK,KAAK;QACR,OAAO,IAAI,CAACd,IAAI,CAACmB,GAAG,CAAC,IAAI,CAAClB,OAAO,GAAGQ,GAAG,EAAEC,IAAI,EAAEI,OAAO,CAAC;MACzD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACd,IAAI,CAACoB,MAAM,CAAC,IAAI,CAACnB,OAAO,GAAGQ,GAAG,EAAEK,OAAO,CAAC;MACtD;MACA;QACE,MAAM,IAAIO,KAAK,CAAC,yBAAyB,CAAC;;EAEhD;EAEA;EAEAC,gBAAgBA,CAACC,YAA0B;IACzC,OAAO,IAAI,CAAChB,OAAO,CAAC,MAAM,EAAE,mBAAmB,EAAEgB,YAAY,CAAC;EAChE;EAEAC,wBAAwBA,CAACD,YAA0B;IACjD,OAAO,IAAI,CAAChB,OAAO,CAAC,MAAM,EAAE,0BAA0B,EAAEgB,YAAY,CAAC;EACvE;EAEAE,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAClB,OAAO,CAAC,KAAK,EAAE,mBAAmB,EAAE,EAAE,CAAC;EACrD;EAEAmB,mBAAmBA,CAACC,EAAU;IAC5B,OAAO,IAAI,CAACpB,OAAO,CAAC,KAAK,EAAE,2BAA2B,EAAE,EAAE,CAAC;EAC7D;EAEAqB,kBAAkBA,CAChBD,EAAU,EACVJ,YAA0B;IAE1B,OAAO,IAAI,CAAChB,OAAO,CAAC,KAAK,EAAE,0BAA0B,EAAEgB,YAAY,CAAC;EACtE;EAEAM,kBAAkBA,CAACF,EAAU;IAC3B,OAAO,IAAI,CAACpB,OAAO,CAAC,QAAQ,EAAE,0BAA0B,EAAE,EAAE,CAAC;EAC/D;EAEA;EACAuB,YAAYA,CAACC,QAAkB;IAC7B,OAAO,IAAI,CAACxB,OAAO,CAAC,MAAM,EAAE,eAAe,EAAEwB,QAAQ,CAAC;EACxD;EAEAC,WAAWA,CACTC,QAAgB,EAChBC,QAAgB,EAChBC,WAAmB,EACnBC,gBAAwB;IAExB,MAAMzB,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC5BiB,GAAG,CAAC,MAAM,EAAEoB,QAAQ,CAAC,CACrBpB,GAAG,CAAC,MAAM,EAAEqB,QAAQ,CAAC,CACrBrB,GAAG,CAAC,MAAM,EAAEsB,WAAW,CAAC,CACxBtB,GAAG,CAAC,WAAW,EAAEuB,gBAAgB,CAACC,QAAQ,EAAE,CAAC;IAEhD,OAAO,IAAI,CAACrC,IAAI,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,OAAO,0BAA0B,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC7E;EAEA2B,qBAAqBA,CAACC,QAAgB,EAAEC,IAAY;IAClD,MAAM7B,MAAM,GAAG,IAAIf,UAAU,EAAE,CAC5BiB,GAAG,CAAC,UAAU,EAAE0B,QAAQ,CAACF,QAAQ,EAAE,CAAC,CACpCxB,GAAG,CAAC,MAAM,EAAE2B,IAAI,CAAC;IAEpB,OAAO,IAAI,CAACxC,IAAI,CAACiB,GAAG,CAAC,GAAG,IAAI,CAAChB,OAAO,wBAAwB,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC3E;EAEA8B,yBAAyBA,CACvBF,QAAgB,EAChBN,QAAgB,EAChBS,IAAY;IAEZ,OAAO,IAAI,CAACnC,OAAO,CACjB,KAAK,EACL,sCAAsCgC,QAAQ,aAAaN,QAAQ,SAASS,IAAI,EAAE,EAClF,EAAE,CACH;EACH;EAAC,QAAAC,CAAA,G;qBA5GU7C,mBAAmB,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAnBlD,mBAAmB;IAAAmD,OAAA,EAAnBnD,mBAAmB,CAAAoD,IAAA;IAAAC,UAAA,EAFlB;EAAM;;SAEPrD,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}