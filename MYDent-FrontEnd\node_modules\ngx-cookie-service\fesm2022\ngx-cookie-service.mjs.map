{"version": 3, "file": "ngx-cookie-service.mjs", "sources": ["../../../projects/ngx-cookie-service/src/lib/cookie.service.ts", "../../../projects/ngx-cookie-service/src/public-api.ts", "../../../projects/ngx-cookie-service/src/ngx-cookie-service.ts"], "sourcesContent": ["// This service is based on the `ng2-cookies` package which sadly is not a service and does\n// not use `DOCUMENT` injection and therefore doesn't work well with AoT production builds.\n// Package: https://github.com/BCJTI/ng2-cookies\n\nimport { Inject, Injectable, PLATFORM_ID } from '@angular/core';\nimport { DOCUMENT, isPlatformBrowser } from '@angular/common';\n\nexport type SameSite = 'Lax' | 'None' | 'Strict';\n\nexport interface CookieOptions {\n  expires?: number | Date;\n  path?: string;\n  domain?: string;\n  secure?: boolean;\n  sameSite?: SameSite;\n}\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CookieService {\n  private readonly documentIsAccessible: boolean;\n\n  constructor(\n    @Inject(DOCUMENT) private document: Document,\n    // Get the `PLATFORM_ID` so we can check if we're in a browser.\n    @Inject(PLATFORM_ID) private platformId\n  ) {\n    this.documentIsAccessible = isPlatformBrowser(this.platformId);\n  }\n\n  /**\n   * Get cookie Regular Expression\n   *\n   * @param name Cookie name\n   * @returns property RegExp\n   *\n   * @author: <PERSON><PERSON> @since: 1.0.0\n   */\n  private static getCookieRegExp(name: string): RegExp {\n    const escapedName: string = name.replace(/([\\[\\]{}()|=;+?,.*^$])/gi, '\\\\$1');\n\n    return new RegExp('(?:^' + escapedName + '|;\\\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');\n  }\n\n  /**\n   * Gets the unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @param encodedURIComponent A value representing an encoded URI component.\n   *\n   * @returns The unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  private static safeDecodeURIComponent(encodedURIComponent: string): string {\n    try {\n      return decodeURIComponent(encodedURIComponent);\n    } catch {\n      // probably it is not uri encoded. return as is\n      return encodedURIComponent;\n    }\n  }\n\n  /**\n   * Return `true` if {@link Document} is accessible, otherwise return `false`\n   *\n   * @param name Cookie name\n   * @returns boolean - whether cookie with specified name exists\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  check(name: string): boolean {\n    if (!this.documentIsAccessible) {\n      return false;\n    }\n    name = encodeURIComponent(name);\n    const regExp: RegExp = CookieService.getCookieRegExp(name);\n    return regExp.test(this.document.cookie);\n  }\n\n  /**\n   * Get cookies by name\n   *\n   * @param name Cookie name\n   * @returns property value\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  get(name: string): string {\n    if (this.documentIsAccessible && this.check(name)) {\n      name = encodeURIComponent(name);\n\n      const regExp: RegExp = CookieService.getCookieRegExp(name);\n      const result: RegExpExecArray = regExp.exec(this.document.cookie);\n\n      return result[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';\n    } else {\n      return '';\n    }\n  }\n\n  /**\n   * Get all cookies in JSON format\n   *\n   * @returns all the cookies in json\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  getAll(): { [key: string]: string } {\n    if (!this.documentIsAccessible) {\n      return {};\n    }\n\n    const cookies: { [key: string]: string } = {};\n    const document: any = this.document;\n\n    if (document.cookie && document.cookie !== '') {\n      document.cookie.split(';').forEach((currentCookie) => {\n        const [cookieName, cookieValue] = currentCookie.split('=');\n        cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);\n      });\n    }\n\n    return cookies;\n  }\n\n  /**\n   * Set cookie based on provided information\n   *\n   * @param name     Cookie name\n   * @param value    Cookie value\n   * @param expires  Number of days until the cookies expires or an actual `Date`\n   * @param path     Cookie path\n   * @param domain   Cookie domain\n   * @param secure   Secure flag\n   * @param sameSite OWASP samesite token `Lax`, `None`, or `Strict`. Defaults to `Lax`\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  set(\n    name: string,\n    value: string,\n    expires?: CookieOptions['expires'],\n    path?: CookieOptions['path'],\n    domain?: CookieOptions['domain'],\n    secure?: CookieOptions['secure'],\n    sameSite?: SameSite\n  ): void;\n\n  /**\n   * Set cookie based on provided information\n   *\n   * Cookie's parameters:\n   * <pre>\n   * expires  Number of days until the cookies expires or an actual `Date`\n   * path     Cookie path\n   * domain   Cookie domain\n   * secure   Secure flag\n   * sameSite OWASP samesite token `Lax`, `None`, or `Strict`. Defaults to `Lax`\n   * </pre>\n   *\n   * @param name     Cookie name\n   * @param value    Cookie value\n   * @param options  Body with cookie's params\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  set(name: string, value: string, options?: CookieOptions): void;\n\n  set(\n    name: string,\n    value: string,\n    expiresOrOptions?: CookieOptions['expires'] | CookieOptions,\n    path?: CookieOptions['path'],\n    domain?: CookieOptions['domain'],\n    secure?: CookieOptions['secure'],\n    sameSite?: SameSite\n  ): void {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n\n    if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {\n      const optionsBody = {\n        expires: expiresOrOptions as CookieOptions['expires'],\n        path,\n        domain,\n        secure,\n        sameSite: sameSite ? sameSite : 'Lax',\n      };\n\n      this.set(name, value, optionsBody);\n      return;\n    }\n\n    let cookieString: string = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';\n\n    const options = expiresOrOptions ? expiresOrOptions : {};\n\n    if (options.expires) {\n      if (typeof options.expires === 'number') {\n        const dateExpires: Date = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);\n\n        cookieString += 'expires=' + dateExpires.toUTCString() + ';';\n      } else {\n        cookieString += 'expires=' + options.expires.toUTCString() + ';';\n      }\n    }\n\n    if (options.path) {\n      cookieString += 'path=' + options.path + ';';\n    }\n\n    if (options.domain) {\n      cookieString += 'domain=' + options.domain + ';';\n    }\n\n    if (options.secure === false && options.sameSite === 'None') {\n      options.secure = true;\n      console.warn(\n        `[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` +\n          `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`\n      );\n    }\n    if (options.secure) {\n      cookieString += 'secure;';\n    }\n\n    if (!options.sameSite) {\n      options.sameSite = 'Lax';\n    }\n\n    cookieString += 'sameSite=' + options.sameSite + ';';\n\n    this.document.cookie = cookieString;\n  }\n\n  /**\n   * Delete cookie by name\n   *\n   * @param name   Cookie name\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Cookie secure flag\n   * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  delete(name: string, path?: CookieOptions['path'], domain?: CookieOptions['domain'], secure?: CookieOptions['secure'], sameSite: SameSite = 'Lax'): void {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');\n    this.set(name, '', { expires: expiresDate, path, domain, secure, sameSite });\n  }\n\n  /**\n   * Delete all cookies\n   *\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Is the Cookie secure\n   * @param sameSite Is the cookie same site\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  deleteAll(path?: CookieOptions['path'], domain?: CookieOptions['domain'], secure?: CookieOptions['secure'], sameSite: SameSite = 'Lax'): void {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n\n    const cookies: any = this.getAll();\n\n    for (const cookieName in cookies) {\n      if (cookies.hasOwnProperty(cookieName)) {\n        this.delete(cookieName, path, domain, secure, sameSite);\n      }\n    }\n  }\n}\n", "/*\n * Public API Surface of ngx-cookie-service\n */\n\nexport * from './lib/cookie.service';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;MAkBa,aAAa,CAAA;AAGxB,IAAA,WAAA,CAC4B,QAAkB;;IAEf,UAAU,EAAA;QAFb,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;QAEf,IAAU,CAAA,UAAA,GAAV,UAAU,CAAA;QAEvC,IAAI,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KAChE;AAED;;;;;;;;AAQG;IACK,OAAO,eAAe,CAAC,IAAY,EAAA;QACzC,MAAM,WAAW,GAAW,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;AAE7E,QAAA,OAAO,IAAI,MAAM,CAAC,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC;KAC1F;AAED;;;;;;;;;AASG;IACK,OAAO,sBAAsB,CAAC,mBAA2B,EAAA;QAC/D,IAAI;AACF,YAAA,OAAO,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;AAChD,SAAA;QAAC,MAAM;;AAEN,YAAA,OAAO,mBAAmB,CAAC;AAC5B,SAAA;KACF;AAED;;;;;;;;AAQG;AACH,IAAA,KAAK,CAAC,IAAY,EAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;AACD,QAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,MAAM,GAAW,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC1C;AAED;;;;;;;;AAQG;AACH,IAAA,GAAG,CAAC,IAAY,EAAA;QACd,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACjD,YAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEhC,MAAM,MAAM,GAAW,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC3D,YAAA,MAAM,MAAM,GAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAElE,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACzE,SAAA;AAAM,aAAA;AACL,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;KACF;AAED;;;;;;;AAOG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;AAC9B,YAAA,OAAO,EAAE,CAAC;AACX,SAAA;QAED,MAAM,OAAO,GAA8B,EAAE,CAAC;AAC9C,QAAA,MAAM,QAAQ,GAAQ,IAAI,CAAC,QAAQ,CAAC;QAEpC,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,EAAE,EAAE;AAC7C,YAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,aAAa,KAAI;AACnD,gBAAA,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC3D,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;AAClI,aAAC,CAAC,CAAC;AACJ,SAAA;AAED,QAAA,OAAO,OAAO,CAAC;KAChB;AA+CD,IAAA,GAAG,CACD,IAAY,EACZ,KAAa,EACb,gBAA2D,EAC3D,IAA4B,EAC5B,MAAgC,EAChC,MAAgC,EAChC,QAAmB,EAAA;AAEnB,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,OAAO;AACR,SAAA;AAED,QAAA,IAAI,OAAO,gBAAgB,KAAK,QAAQ,IAAI,gBAAgB,YAAY,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,IAAI,QAAQ,EAAE;AACpH,YAAA,MAAM,WAAW,GAAG;AAClB,gBAAA,OAAO,EAAE,gBAA4C;gBACrD,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ,EAAE,QAAQ,GAAG,QAAQ,GAAG,KAAK;aACtC,CAAC;YAEF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,OAAO;AACR,SAAA;AAED,QAAA,IAAI,YAAY,GAAW,kBAAkB,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;QAE5F,MAAM,OAAO,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,EAAE,CAAC;QAEzD,IAAI,OAAO,CAAC,OAAO,EAAE;AACnB,YAAA,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;gBACvC,MAAM,WAAW,GAAS,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAEjG,YAAY,IAAI,UAAU,GAAG,WAAW,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC;AAC9D,aAAA;AAAM,iBAAA;gBACL,YAAY,IAAI,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC;AAClE,aAAA;AACF,SAAA;QAED,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,YAAY,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC;AAC9C,SAAA;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,YAAY,IAAI,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC;AAClD,SAAA;QAED,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;AAC3D,YAAA,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;AACtB,YAAA,OAAO,CAAC,IAAI,CACV,CAAA,4BAAA,EAA+B,IAAI,CAAqD,mDAAA,CAAA;AACtF,gBAAA,CAAA,mGAAA,CAAqG,CACxG,CAAC;AACH,SAAA;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,YAAY,IAAI,SAAS,CAAC;AAC3B,SAAA;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACrB,YAAA,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC1B,SAAA;QAED,YAAY,IAAI,WAAW,GAAG,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC;AAErD,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC;KACrC;AAED;;;;;;;;;;;AAWG;IACH,MAAM,CAAC,IAAY,EAAE,IAA4B,EAAE,MAAgC,EAAE,MAAgC,EAAE,QAAA,GAAqB,KAAK,EAAA;AAC/I,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,OAAO;AACR,SAAA;AACD,QAAA,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;KAC9E;AAED;;;;;;;;;;AAUG;IACH,SAAS,CAAC,IAA4B,EAAE,MAAgC,EAAE,MAAgC,EAAE,WAAqB,KAAK,EAAA;AACpI,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC9B,OAAO;AACR,SAAA;AAED,QAAA,MAAM,OAAO,GAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;AAEnC,QAAA,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE;AAChC,YAAA,IAAI,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AACtC,gBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;AACzD,aAAA;AACF,SAAA;KACF;8GA3QU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAId,QAAQ,EAAA,EAAA,EAAA,KAAA,EAER,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AANV,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cAFZ,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAEP,aAAa,EAAA,UAAA,EAAA,CAAA;kBAHzB,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA;AACV,oBAAA,UAAU,EAAE,MAAM;AACnB,iBAAA,CAAA;;0BAKI,MAAM;2BAAC,QAAQ,CAAA;;0BAEf,MAAM;2BAAC,WAAW,CAAA;;;AC1BvB;;AAEG;;ACFH;;AAEG;;;;"}