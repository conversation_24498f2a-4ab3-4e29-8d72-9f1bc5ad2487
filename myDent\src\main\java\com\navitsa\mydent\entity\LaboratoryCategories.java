package com.navitsa.mydent.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "laboratory_categories")
public class LaboratoryCategories {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "laboratory_category_id")
    private Integer laboratoryCategoryId;

    @Column(name = "laboratory_category_name")
    private String laboratoryCategoryName;

    public LaboratoryCategories() {}

    public LaboratoryCategories(Integer laboratoryCategoryId, String laboratoryCategoryName) {
        super();
        this.laboratoryCategoryId = laboratoryCategoryId;
        this.laboratoryCategoryName = laboratoryCategoryName;
    }

    public Integer getLaboratoryCategoryId() {
        return laboratoryCategoryId;
    }

    public void setLaboratoryCategoryId(Integer laboratoryCategoryId) {
        this.laboratoryCategoryId = laboratoryCategoryId;
    }

    public String getLaboratoryCategoryName() {
        return laboratoryCategoryName;
    }

    public void setLaboratoryCategoryName(String laboratoryCategoryName) {
        this.laboratoryCategoryName = laboratoryCategoryName;
    }
}
