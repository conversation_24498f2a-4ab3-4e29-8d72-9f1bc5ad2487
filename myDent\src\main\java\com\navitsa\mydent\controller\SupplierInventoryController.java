package com.navitsa.mydent.controller;

import java.io.IOException;
import java.util.List;

import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.repositories.SupplierRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.navitsa.mydent.entity.SupplierInventory;
import com.navitsa.mydent.services.SupplierInventoryService;
import org.springframework.web.multipart.MultipartFile;

@RestController
public class SupplierInventoryController {
  
	@Autowired
	private SupplierInventoryService supplierInventoryService;
	@Autowired
	private SupplierRepository supplierRepository;

	@PostMapping("/saveSupplierInventoryItem")
	public SupplierInventory saveSupplierInventoryItem(
			@RequestParam("supplierId") Integer supplierId,  // Integer supplierId
			@RequestParam("category") String category,
			@RequestParam("subCategory") String subCategory,
			@RequestParam("description") String description,
			@RequestParam("price") double price,
			@RequestParam("quantity") Integer quantity,
			@RequestParam("itemStatus") String itemStatus,
			@RequestParam(value = "itemImage", required = false) MultipartFile itemImage
	) {
		// Fetch the Supplier object using the supplierId
		Supplier supplier = supplierRepository.findById(supplierId)
				.orElseThrow(() -> new RuntimeException("Supplier not found with ID: " + supplierId));

		// Convert the MultipartFile to byte array if present
		byte[] itemImageBytes = null;
		if (itemImage != null && !itemImage.isEmpty()) {
			try {
				itemImageBytes = itemImage.getBytes();
			} catch (IOException e) {
				throw new RuntimeException("Failed to read the image file", e);
			}
		}

		// Create the SupplierInventory object with the fetched Supplier
		SupplierInventory supplierInventory = new SupplierInventory(
				supplier, category, subCategory, description, price, quantity, itemStatus, itemImageBytes
		);

		// Call the service to save the inventory
		return supplierInventoryService.saveSupplierInventory(supplierInventory);
	}


//	@PostMapping("/saveSupplierInventoryItem")
//	public SupplierInventory saveSupplierInventoryItem(@RequestBody SupplierInventory supplierInventory) {
//		return supplierInventoryService.saveSupplierInventory(supplierInventory);
//	}
	
	
	@GetMapping(value = "/supplierInventoryList")
	public List<SupplierInventory> getAllSupplierInventories() {
		return supplierInventoryService.findAllSupplierInventories();
	}
	

	@GetMapping("/getSupplierInventoryById/{id}")
	public SupplierInventory getSupplierInventoryById(@PathVariable int id) {
		return supplierInventoryService.getSupplierInventoryById(id);
	}

}
