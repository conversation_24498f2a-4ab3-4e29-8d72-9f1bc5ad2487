package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.LaboratoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class LaboratoryService {

    private final LaboratoryRepository laboratoryRepository;

    @Autowired
    public LaboratoryService(LaboratoryRepository laboratoryRepository) {
        this.laboratoryRepository = laboratoryRepository;
    }

    @Autowired
    EmailService emailService;
    
    //Save laboratory
    public Laboratory saveLaboratory(Laboratory laboratory) {
        try {
            if (laboratoryRepository.findByName(laboratory.getName()).isPresent()) {
                throw new RuntimeException("Laboratory with " + laboratory.getName() + " already exists.");
            }

            SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");
            Date currentDate = new Date();
            String formattedDate = dateFormatter.format(currentDate);
            laboratory.setRegisteredDate(formattedDate);

            Laboratory savedLaboratory = laboratoryRepository.save(laboratory);
            
           	User user = savedLaboratory.getUserId();
            String verificationToken = user.getVerificationToken();
            String verificationTokenWithUserType = verificationToken + "&userType=Laboratory";
            System.out.println("token: " + verificationTokenWithUserType);

            CompletableFuture.runAsync(() ->
                    emailService.sendRegistrationEmail(
                    	savedLaboratory.getEmail(),
                    	savedLaboratory.getName(),
                        "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType,
                        "Laboratory"
                    ));
            
            return savedLaboratory;

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the laboratory.");
        }
    }

    public Laboratory save(Laboratory laboratory){
        if (laboratoryRepository.findByName(laboratory.getName()).isPresent()) {
            throw new RuntimeException("Laboratory with " + laboratory.getName() + " already exists.");
        }
        return laboratoryRepository.save(laboratory);
    }

    //Get all laboratories
    public List<Laboratory> findAllLaboratories() {
        try {
            return laboratoryRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of laboratories.");
        }
    }

    //Get laboratory by ID
    public Laboratory getLaboratoryById(int id) {
        try {
            return laboratoryRepository.findById(id).orElse(null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory.");
        }
    }

    //Update laboratory
    public Laboratory updateLaboratory(int id, Laboratory laboratoryDetails) {
        try {
            Laboratory laboratory = laboratoryRepository.findById(id).orElse(null);
            if (laboratory == null) {
                throw new RuntimeException("Laboratory not found with id: " + id);
            }
            
            laboratory.setName(laboratoryDetails.getName());
            laboratory.setAddress(laboratoryDetails.getAddress());
            laboratory.setCity(laboratoryDetails.getCity());
            laboratory.setState(laboratoryDetails.getState());
            laboratory.setCountry(laboratoryDetails.getCountry());
            laboratory.setTele(laboratoryDetails.getTele());
            laboratory.setContactPerson(laboratoryDetails.getContactPerson());
            laboratory.setEmail(laboratoryDetails.getEmail());
            laboratory.setWeb(laboratoryDetails.getWeb());
            laboratory.setRegisteredDate(laboratoryDetails.getRegisteredDate());
            laboratory.setLatitude(laboratoryDetails.getLatitude());
            laboratory.setLongitude(laboratoryDetails.getLongitude());

            return laboratoryRepository.save(laboratory);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the laboratory.");
        }
    }

    //Delete laboratory
    public void deleteLaboratory(int id) {
        try {
            laboratoryRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the laboratory.");
        }
    }

    public boolean laboratoryNameExists(String laboratoryName) {
        Optional<Laboratory> existingLaboratory = laboratoryRepository.findByName(laboratoryName);
        return existingLaboratory.isPresent();
    }
}
