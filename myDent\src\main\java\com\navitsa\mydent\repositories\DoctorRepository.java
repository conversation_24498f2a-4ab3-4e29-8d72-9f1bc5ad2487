package com.navitsa.mydent.repositories;
import org.springframework.data.jpa.repository.JpaRepository;
import com.navitsa.mydent.entity.Doctor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface DoctorRepository extends JpaRepository<Doctor, Integer> {
  Optional<Doctor> findByRegNo(String regNo);
  
  List<Doctor> findByDoctorIdIn(List<Integer> doctorIds);

  @Query("SELECT d FROM Doctor d WHERE d.userId.userId =:user_id")
  Optional<Doctor> findDoctorByUserId(@Param("user_id") int userId);

}
