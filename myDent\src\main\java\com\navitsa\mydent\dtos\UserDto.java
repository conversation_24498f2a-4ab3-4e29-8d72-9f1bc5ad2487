package com.navitsa.mydent.dtos;

import com.navitsa.mydent.entity.UserCategory;
import com.navitsa.mydent.enums.UserStatus;

public class UserDto {
	private Integer id;
	private String firstName;
	private String lastName;
	private String username;
	private String email;
	private String userType;
	private String token;
	private Integer companyId;
	private UserCategory userCategoryId;
	private UserStatus userVerified;

	// Getters and Setters
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getUserType() {
		return userType;
	}

	public void setUserType(String userType) {
		this.userType = userType;
	}

	public UserCategory getUserCategoryId() {
		return userCategoryId;
	}

	public void setUserCategoryId(UserCategory userCategoryId) {
		this.userCategoryId = userCategoryId;
	}

	public UserStatus getUserVerified() {
		return userVerified;
	}

	public void setUserVerified(UserStatus userVerified) {
		this.userVerified = userVerified;
	}

	public static class Builder {
		private Integer id;
		private String firstName;
		private String lastName;
		private String username;
		private String email;
		private String userType;
		private String token;
		private Integer companyId;

		public Builder id(Integer id) {
			this.id = id;
			return this;
		}

		public Builder firstName(String firstName) {
			this.firstName = firstName;
			return this;
		}

		public Builder lastName(String lastName) {
			this.lastName = lastName;
			return this;
		}

		public Builder username(String username) {
			this.username = username;
			return this;
		}

		public Builder email(String email) {
			this.email = email;
			return this;
		}

		public Builder userType(String userType) {
			this.userType = userType;
			return this;
		}

		public Builder token(String token) {
			this.token = token;
			return this;
		}

		public Builder companyId(Integer companyId) {
			this.companyId = companyId;
			return this;
		}

		public UserDto build() {
			UserDto user = new UserDto();
			user.setId(this.id);
			user.setFirstName(this.firstName);
			user.setLastName(this.lastName);
			user.setUsername(this.username);
			user.setEmail(this.email);
			user.setUserType(this.userType);
			user.setToken(this.token);
			user.setCompanyId(this.companyId);
			return user;
		}
	}
}
