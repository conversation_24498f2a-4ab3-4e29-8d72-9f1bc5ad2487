package com.navitsa.mydent.services;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.navitsa.mydent.entity.ClinicSchedule;
import com.navitsa.mydent.repositories.ClinicScheduleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.navitsa.mydent.entity.Appointments;
import com.navitsa.mydent.entity.Customer;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.AppointmentsRepository;
import com.navitsa.mydent.repositories.CustomerRepository;
import com.navitsa.mydent.repositories.UserRepository;

import static org.springframework.util.StringUtils.capitalize;

@Service
public class AppointmentsServices {

    @Autowired
    private AppointmentsRepository appointmentsRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ClinicScheduleRepository clinicScheduleRepository;

//	 public Appointments saveAppointments(Appointments appointments) {
//	        try {
//	            return appointmentsRepository.save(appointments);
//	        } catch (Exception e) {
//	            e.printStackTrace();
//	            throw new RuntimeException("An error occurred while saving the appointments.");
//	        }
//	    }

//	 public Appointments saveAppointments(Appointments appointments) {
//
//		 appointments.setStatus("Pending");
//	        // Assuming appointmentsRepository is defined to interact with the database
//	        return appointmentsRepository.save(appointments);
//	    }

    // Get all Appointments
    public List<Appointments> findAllAppointments() {
        try {
            return appointmentsRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of appointmentss.");
        }
    }

    // Get appointments by ID
    public Appointments getAppointmentsById(int id) {
        try {
            return appointmentsRepository.findById(id).orElse(null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the appointments.");
        }
    }

    // Update appointments
    public Appointments updateAppointments(int id, Appointments appointmentsDetails) {
        try {
            Appointments appointments = appointmentsRepository.findById(id).orElse(null);
            if (appointments == null) {
                throw new RuntimeException("appointments not found with id: " + id);
            }
            appointments.setFirstName(appointmentsDetails.getFirstName());
            appointments.setLastName(appointmentsDetails.getLastName());
            appointments.setAddress(appointmentsDetails.getAddress());
            appointments.setCity(appointmentsDetails.getCity());
            appointments.setState(appointmentsDetails.getState());
            appointments.setDistrict(appointmentsDetails.getDistrict());
            appointments.setTelephone(appointmentsDetails.getTelephone());
            appointments.setEmail(appointmentsDetails.getEmail());
            appointments.setPreferredservice(appointmentsDetails.getPreferredservice());
            appointments.setNearestCity(appointmentsDetails.getNearestCity());
            appointments.setFromDate(appointmentsDetails.getFromDate());
            appointments.setToDate(appointmentsDetails.getToDate());
            appointments.setFromTime(appointmentsDetails.getFromTime());
            appointments.setToTime(appointmentsDetails.getToTime());
            appointments.setUserName(appointmentsDetails.getUserName());
            appointments.setPassword(appointmentsDetails.getAddress());

            return appointmentsRepository.save(appointments);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the appointments.");
        }
    }

    // Delete appointments
    public void deleteAppointments(int id) {
        try {
            appointmentsRepository.deleteById(id);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the appointments.");
        }
    }

    // Save both customer and appointments
    public Appointments saveCustomerAndAppointments(Customer customer, Appointments appointments) {
        // First save the customer
        Customer savedCustomer = customerRepository.save(customer);

        // Associate the saved customer with the appointment
        appointments.setCustomer(savedCustomer);

        // Save the appointment
        return appointmentsRepository.save(appointments);
    }

    public List<Appointments> getAppointmentsByCustomerId(int userId) {
        User user = userRepository.findByUserId(userId);
        Customer customer = customerRepository.findByUser(user).orElse(null);

        return appointmentsRepository.findByCustomer(customer);
    }

    public Appointments saveCustomerAppointment(Appointments appointmentsDetails) {
        try {
            Appointments appointments = new Appointments();
            appointments.setFirstName(appointmentsDetails.getFirstName());
            appointments.setLastName(appointmentsDetails.getLastName());
            appointments.setAddress(appointmentsDetails.getAddress());
            appointments.setCity(appointmentsDetails.getCity());
            appointments.setState(appointmentsDetails.getState());
            appointments.setDistrict(appointmentsDetails.getDistrict());
            appointments.setTelephone(appointmentsDetails.getTelephone());
            appointments.setEmail(appointmentsDetails.getEmail());
            appointments.setPreferredservice(appointmentsDetails.getPreferredservice());
            appointments.setNearestCity(appointmentsDetails.getNearestCity());
            appointments.setFromDate(appointmentsDetails.getFromDate());
            appointments.setToDate(appointmentsDetails.getToDate());
            appointments.setFromTime(appointmentsDetails.getFromTime());
            appointments.setToTime(appointmentsDetails.getToTime());
            appointments.setUserName(appointmentsDetails.getUserName());
            appointments.setPassword(appointmentsDetails.getAddress());
            appointments.setClinics(appointmentsDetails.getClinics());
            appointments.setCustomer(appointmentsDetails.getCustomer());

            return appointmentsRepository.save(appointments);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the appointments.");
        }
    }

    // Fetch appointments by clinic ID
    public Optional<List<Appointments>> getAppointmentsByClinicId(int clinicId) {
        return appointmentsRepository.getAllPendingAppointmentsByUserId(clinicId);
    }

    public Appointments updateAppointmentStatus(Integer id, String status) {
        // Find the appointment by ID
        Appointments appointment = appointmentsRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Appointment not found with id: " + id));

        // Update the status
        appointment.setStatus(status);  // Assuming your `Appointments` class has a `setStatus()` method

        // Save and return the updated appointment
        return appointmentsRepository.save(appointment);
    }

    public boolean isTimeSlotAvailable(Integer clinicId, String fromDate, String fromTime, String toTime) {
        List<Appointments> existingAppointments = appointmentsRepository.findByClinicsClinicIdAndFromDate(clinicId, fromDate);

        LocalTime newFrom = LocalTime.parse(fromTime);
        LocalTime newTo = LocalTime.parse(toTime);

        for (Appointments a : existingAppointments) {
            LocalTime existingFrom = LocalTime.parse(a.getFromTime());
            LocalTime existingTo = LocalTime.parse(a.getToTime());

            boolean overlaps = !newTo.isBefore(existingFrom) && !newFrom.isAfter(existingTo);

            if (overlaps) {
                return false;
            }
        }
        return true;
    }

    public List<Appointments> getAllAppointments(int clinicId, String fromDate) {

        return appointmentsRepository.findByClinicsClinicIdAndFromDate(clinicId, fromDate);
    }

    public boolean isHollyDayOrCloseDay(String fromDate, Integer clinicId) {
        ArrayList<String> holyDays = new ArrayList<>();
try {
    List<ClinicSchedule> clinicSchedules = clinicScheduleRepository.findByHolidayDateAndClinics_ClinicId(true, clinicId);

    for (int x = 0; x < clinicSchedules.size(); x++) {
        holyDays.add(clinicSchedules.get(x).getDate());
    }
    LocalDate date = LocalDate.parse(fromDate, DateTimeFormatter.ISO_DATE);

    // Get day of week with first letter capital
    String dayOfWeek = capitalize(date.getDayOfWeek().name());

// Check if fromDate is a holiday
    if (holyDays.contains(dayOfWeek)) {
        return true;

    } else {
        return false;
    }
} catch (Exception e) {
    throw new RuntimeException(e + "An error occurred while checking holy day. ************ isHollyDayOrCloseDay*************");
}
    }

//    public boolean isTimeSlotAvailable(String fromDate, String fromTime, String toTime) {
//        List<Appointments> existingAppointments = appointmentsRepository.findByFromDate(fromDate);
//
//        LocalTime newFrom = LocalTime.parse(fromTime);
//        LocalTime newTo = LocalTime.parse(toTime);
//
//        for (Appointments a : existingAppointments) {
//            LocalTime existingFrom = LocalTime.parse(a.getFromTime());
//            LocalTime existingTo = LocalTime.parse(a.getToTime());
//
//            boolean overlaps = !newTo.isBefore(existingFrom) && !newFrom.isAfter(existingTo);
//
//            if (overlaps) {
//                return false;
//            }
//        }
//        return true;
//    }


    // Converts "Friday"
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }


    public boolean isAvailableDate(Integer clinicId, String fromDate, String fromTime, String toTime) {

            List<ClinicSchedule> availableDates = clinicScheduleRepository.findByHolidayDateAndClinics_ClinicId(false, clinicId);
            LocalDate date2 = LocalDate.parse(fromDate, DateTimeFormatter.ISO_DATE);

            // Get day of week with first letter capital
            String dayOfWeek2 = capitalize(date2.getDayOfWeek().name());

        for (int y = 0; y < availableDates.size(); y++) {

                if (availableDates.get(y).getDate().contains(dayOfWeek2)) {
                    // Example frontend times (from the user/input)
                    DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
                    LocalTime fromTime2 = LocalTime.parse(fromTime, timeFormatter);
                    LocalTime toTime2 = LocalTime.parse(toTime, timeFormatter);

                    LocalTime existingFromTime = LocalTime.parse(availableDates.get(y).getFromTime(), timeFormatter);
                    LocalTime existingToTime = LocalTime.parse(availableDates.get(y).getToTime(), timeFormatter);

                    // Check if frontend time is within the available slot
                    if (!fromTime2.isBefore(existingFromTime) && !toTime2.isAfter(existingToTime)) {
                        return true;
                    }
                }

            }
            return false;
        }

}
