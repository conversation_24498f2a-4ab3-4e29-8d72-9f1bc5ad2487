package com.navitsa.mydent.services;

import com.navitsa.mydent.dtos.InventoryOrderDetailDto;
import com.navitsa.mydent.dtos.InventoryOrderRequestDto;
import com.navitsa.mydent.entity.*;
import com.navitsa.mydent.enums.ClinicSupplierOrderStatus;
import com.navitsa.mydent.repositories.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
public class ClinicSupplierService {

    private final ClinicRepository clinicRepository ;
    private final SupplierRepository supplierRepository;
    private final SupplierInventoryRepository supplierInventoryRepository;
    private final SupplierOrderHeaderRepository supplierOrderHeaderRepository;
    private final SupplierOrderDetailsRepository supplierOrderDetailsRepository;

    @Autowired
    public ClinicSupplierService(ClinicRepository clinicRepository, SupplierRepository supplierRepository, SupplierInventoryRepository supplierInventoryRepository, SupplierOrderHeaderRepository supplierOrderHeaderRepository, SupplierOrderDetailsRepository supplierOrderDetailsRepository) {
        this.clinicRepository = clinicRepository;
        this.supplierRepository = supplierRepository;
        this.supplierInventoryRepository = supplierInventoryRepository;
        this.supplierOrderHeaderRepository = supplierOrderHeaderRepository;
        this.supplierOrderDetailsRepository = supplierOrderDetailsRepository;
    }


    public boolean saveClinicSupplierOrder(InventoryOrderRequestDto inventoryOrderRequestDto){

        boolean hasCompleted = false;

        // Make the SupplierOrderHeader and save it
        Optional<Clinic> clinic = clinicRepository.findByUserId_UserId(inventoryOrderRequestDto.userId());
        Optional<Supplier> supplier = supplierRepository.findBySupplierId(inventoryOrderRequestDto.supplierId());

        if (clinic.isPresent() && supplier.isPresent() && inventoryOrderRequestDto.cartItemList().length >0){
            SupplierOrderHeader supplierOrderHeader = new SupplierOrderHeader(clinic.get(), supplier.get(), ClinicSupplierOrderStatus.CLINIC_CREATED, LocalDateTime.now());
            SupplierOrderHeader savedHeader = supplierOrderHeaderRepository.save(supplierOrderHeader);

            // Then save the SupplierOrderDetails accordingly
            for (InventoryOrderDetailDto inventoryOrderDetailDto : inventoryOrderRequestDto.cartItemList()){
                Optional<SupplierInventory> supplierInventory = supplierInventoryRepository.findById(inventoryOrderDetailDto.item().inventoryItemId());
                if (supplierInventory.isPresent() && inventoryOrderDetailDto.quantity()>0){
                    SupplierOrderDetails supplierOrderDetails = new SupplierOrderDetails(supplierInventory.get(), inventoryOrderDetailDto.quantity(), savedHeader);
                    supplierOrderDetailsRepository.save(supplierOrderDetails);
                    hasCompleted = true;
                }
            }
        }

        return hasCompleted;
    }

    public List<SupplierOrderHeader> getClinicOrdersHeadersThroughSupplierId(Integer userId){
        Optional<List<SupplierOrderHeader>> filteredOrderHeaders = supplierOrderHeaderRepository.findAllSuppliersByUserId(userId);
        return filteredOrderHeaders.orElse(Collections.emptyList());
    }

    public List<SupplierOrderHeader> getClinicOrdersHeadersThroughClinicUserId(Integer userId){
        Optional<List<SupplierOrderHeader>> filteredOrderHeaders = supplierOrderHeaderRepository.findAllClinicsByUserId(userId);
        return filteredOrderHeaders.orElse(Collections.emptyList());
    }

    public List<SupplierOrderDetails> getClinicOrdersDetailsThroughHeaderId(Integer headerId){
        Optional<List<SupplierOrderDetails>> filteredOrderDetails = supplierOrderDetailsRepository.getAllSupplierOrderDetailsByHeadId(headerId);
        return filteredOrderDetails.orElse(Collections.emptyList());
    }


}
