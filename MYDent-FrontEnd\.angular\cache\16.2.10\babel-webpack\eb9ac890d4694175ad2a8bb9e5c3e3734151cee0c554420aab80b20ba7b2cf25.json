{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FutureDentistLayoutComponent } from './future-dentist-layout/future-dentist-layout.component';\nimport { FutureDentistDashboardComponent } from './future-dentist-dashboard/future-dentist-dashboard.component';\nimport { authGuard } from '../auth/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: FutureDentistLayoutComponent,\n  canActivateChild: [authGuard],\n  children: [{\n    path: '',\n    component: FutureDentistDashboardComponent\n  }, {\n    path: '**',\n    redirectTo: ''\n  }]\n}];\nclass FutureDentistRoutingModule {\n  static #_ = this.ɵfac = function FutureDentistRoutingModule_Factory(t) {\n    return new (t || FutureDentistRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: FutureDentistRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\nexport { FutureDentistRoutingModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FutureDentistRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FutureDentistLayoutComponent", "FutureDentistDashboardComponent", "<PERSON>th<PERSON><PERSON>", "routes", "path", "component", "canActivateChild", "children", "redirectTo", "FutureDentistRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\future-dentist\\future-dentist-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { FutureDentistLayoutComponent } from './future-dentist-layout/future-dentist-layout.component';\r\nimport { FutureDentistDashboardComponent } from './future-dentist-dashboard/future-dentist-dashboard.component';\r\nimport { authGuard } from '../auth/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path:'',\r\n    component:FutureDentistLayoutComponent,\r\n    canActivateChild:[authGuard],\r\n    children:[\r\n      {path:'',component:FutureDentistDashboardComponent},\r\n      {path:'**',redirectTo:''}\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class FutureDentistRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,+BAA+B,QAAQ,+DAA+D;AAC/G,SAASC,SAAS,QAAQ,oBAAoB;;;AAE9C,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAC,EAAE;EACPC,SAAS,EAACL,4BAA4B;EACtCM,gBAAgB,EAAC,CAACJ,SAAS,CAAC;EAC5BK,QAAQ,EAAC,CACP;IAACH,IAAI,EAAC,EAAE;IAACC,SAAS,EAACJ;EAA+B,CAAC,EACnD;IAACG,IAAI,EAAC,IAAI;IAACI,UAAU,EAAC;EAAE,CAAC;CAE5B,CACF;AAED,MAIaC,0BAA0B;EAAA,QAAAC,CAAA,G;qBAA1BD,0BAA0B;EAAA;EAAA,QAAAE,EAAA,G;UAA1BF;EAA0B;EAAA,QAAAG,EAAA,G;cAH3Bb,YAAY,CAACc,QAAQ,CAACV,MAAM,CAAC,EAC7BJ,YAAY;EAAA;;SAEXU,0BAA0B;;2EAA1BA,0BAA0B;IAAAK,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAF3BjB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}