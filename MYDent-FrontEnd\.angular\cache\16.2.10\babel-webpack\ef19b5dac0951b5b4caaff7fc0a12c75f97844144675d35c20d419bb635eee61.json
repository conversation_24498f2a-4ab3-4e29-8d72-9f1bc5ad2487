{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../laboratory.service\";\nclass LaboratoryInvoiceComponent {\n  constructor(laboratoryService) {\n    this.laboratoryService = laboratoryService;\n    this.name = '';\n    this.invoices = []; // Array to hold fetched invoices\n  }\n\n  submitInvoice() {\n    // const invoiceData ={\n    //   name : this.name,\n    // }\n    // this.laboratoryService.saveLaboratory(invoiceData).subscribe(\n    //   (response) => {\n    //     console.log('Invoice saved successfully', response);\n    //     alert('Invoice saved successfully!');\n    //   },\n    //   (error) => {\n    //     console.error('Error saving invoice', error);\n    //     alert('Error saving invoice!');\n    //   }\n    // );\n  }\n  static #_ = this.ɵfac = function LaboratoryInvoiceComponent_Factory(t) {\n    return new (t || LaboratoryInvoiceComponent)(i0.ɵɵdirectiveInject(i1.LaboratoryService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LaboratoryInvoiceComponent,\n    selectors: [[\"app-laboratory-invoice\"]],\n    decls: 91,\n    vars: 0,\n    consts: [[1, \"row\"], [1, \"col-12\"], [1, \"header-row\"], [1, \"header-row-h1\"], [1, \"header-bottom-line\"], [1, \"rectangle\", 3, \"submit\"], [\"id\", \"header1\"], [1, \"row\", \"mb-3\"], [1, \"col-md-6\", \"d-flex\", \"flex-column\"], [1, \"bill-p\", 2, \"margin-bottom\", \"10px\"], [\"type\", \"text\", \"id\", \"text-i\", \"placeholder\", \"Name\", \"required\", \"\"], [\"type\", \"text\", \"id\", \"text-i\", \"placeholder\", \"Company Name\", \"required\", \"\"], [\"type\", \"text\", \"id\", \"number-i\", \"placeholder\", \"Contacts\", \"required\", \"\"], [1, \"col-md-6\", \"d-flex\", \"flex-column\", \"align-items-end\"], [1, \"lab-p\"], [\"id\", \"address\"], [\"id\", \"email-r\"], [\"id\", \"tel-r\"], [\"id\", \"note-i\"], [\"type\", \"text\", \"id\", \"number-i\", \"required\", \"\"], [\"type\", \"date\", \"id\", \"date-i\", \"required\", \"\"], [1, \"my-appointments-table\"], [\"colspan\", \"4\", 1, \"section-header\"], [1, \"table-header\"], [1, \"table-row\"], [\"placeholder\", \"Service\", \"type\", \"text\", \"required\", \"\", 1, \"service-input\"], [\"placeholder\", \"Description\", 1, \"service-input\", 2, \"margin-top\", \"30px\"], [\"placeholder\", \"Quantity\", \"type\", \"number\", \"required\", \"\", 1, \"service-input\", 2, \"width\", \"50%\"], [\"placeholder\", \"Price (LKR)\", \"type\", \"number\", \"required\", \"\", 1, \"service-input\", 2, \"width\", \"50%\"], [1, \"row\", \"mb-3\", \"totals-row\"], [1, \"col-md-12\", \"d-flex\", \"justify-content-end\"], [1, \"totals-container\"], [\"id\", \"sub-p\"], [\"id\", \"number-i\", \"type\", \"number\", \"required\", \"\", 2, \"width\", \"200px\"], [\"id\", \"dic-p\"], [\"id\", \"number-i\", \"type\", \"number\", 2, \"width\", \"200px\"], [\"id\", \"total-p\"], [\"id\", \"grand-p\"], [\"id\", \"note\", 2, \"display\", \"flex\"], [1, \"mb-3\", 2, \"width\", \"100%\", \"display\", \"flex\", \"flex-direction\", \"column\", \"justify-content\", \"center\", \"align-items\", \"center\", \"text-align\", \"left\"], [\"id\", \"textarea\"], [1, \"d-flex\", 2, \"display\", \"flex\", \"width\", \"100%\", \"justify-content\", \"end\", \"padding-right\", \"40px\"], [\"id\", \"addProduct\", \"type\", \"submit\"]],\n    template: function LaboratoryInvoiceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 0)(3, \"div\", 1)(4, \"div\", 2)(5, \"div\", 3);\n        i0.ɵɵtext(6, \" Invoice \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(7, \"div\", 4)(8, \"br\");\n        i0.ɵɵelementStart(9, \"form\", 5);\n        i0.ɵɵlistener(\"submit\", function LaboratoryInvoiceComponent_Template_form_submit_9_listener() {\n          return ctx.submitInvoice();\n        });\n        i0.ɵɵelementStart(10, \"div\", 6)(11, \"h2\");\n        i0.ɵɵtext(12, \"Invoice\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"p\", 9);\n        i0.ɵɵtext(16, \"Billing Form\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 10)(18, \"input\", 11)(19, \"input\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 13)(21, \"p\", 14);\n        i0.ɵɵtext(22, \"Laboratory\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"label\", 15);\n        i0.ɵɵtext(24, \"ABL Lab, Colombo 12\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"label\", 16);\n        i0.ɵɵtext(26, \"abc.com\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"label\", 17);\n        i0.ɵɵtext(28, \"0770120120 | 0112520520\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"div\", 7)(30, \"div\", 8)(31, \"p\", 18);\n        i0.ɵɵtext(32, \"Invoice Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"input\", 19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 7)(35, \"div\", 8)(36, \"p\", 18);\n        i0.ɵɵtext(37, \"Invoice Date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(38, \"input\", 20);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"div\", 7)(40, \"table\", 21)(41, \"thead\")(42, \"tr\")(43, \"th\", 22);\n        i0.ɵɵtext(44, \"Item Details\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"tr\", 23)(46, \"th\");\n        i0.ɵɵtext(47, \"Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"th\");\n        i0.ɵɵtext(49, \"Description\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"th\");\n        i0.ɵɵtext(51, \"Qty\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(52, \"th\");\n        i0.ɵɵtext(53, \"Amount\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(54, \"tbody\")(55, \"tr\", 24)(56, \"td\");\n        i0.ɵɵelement(57, \"input\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"td\");\n        i0.ɵɵelement(59, \"textarea\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"td\");\n        i0.ɵɵelement(61, \"input\", 27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"td\");\n        i0.ɵɵelement(63, \"input\", 28);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(64, \"div\", 29)(65, \"div\", 30)(66, \"div\", 31)(67, \"p\")(68, \"span\", 32);\n        i0.ɵɵtext(69, \"Sub Total\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(70, \"input\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(71, \"p\")(72, \"span\", 34);\n        i0.ɵɵtext(73, \"Total Disc Amount\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(74, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(75, \"p\")(76, \"span\", 36);\n        i0.ɵɵtext(77, \"Total GST\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(78, \"input\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(79, \"p\")(80, \"span\", 37)(81, \"b\");\n        i0.ɵɵtext(82, \"Grand Amount\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(83, \"input\", 33);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(84, \"p\", 38);\n        i0.ɵɵtext(85, \"Note\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(86, \"div\", 39);\n        i0.ɵɵelement(87, \"textarea\", 40);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"div\", 41)(89, \"button\", 42);\n        i0.ɵɵtext(90, \"Add Product\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n    },\n    styles: [\".sidebar-container[_ngcontent-%COMP%] {\\n            height: calc(100vh - 80px);\\n            width: 20%;\\n            overflow-y: auto;\\n            display: inline-block;\\n        }\\n\\n        .main-content[_ngcontent-%COMP%] {\\n            height: calc(100vh - 80px);\\n            width: 80%;\\n            overflow-y: auto;\\n            padding: 16px;\\n            display: inline-block;\\n        }\\n\\n        .container[_ngcontent-%COMP%] {\\n            padding: 20px;\\n        }\\n\\n        .header-row[_ngcontent-%COMP%] {\\n            font-weight: bold;\\n            display: flex;\\n            justify-content: space-between;\\n            align-items: center;\\n        }\\n\\n        .header-row-h1[_ngcontent-%COMP%] {\\n            color: black;\\n            width: 390px;\\n            top: 150px;\\n            font-size: 24px;\\n            left: 386px;\\n            font-family: Inter;\\n            font-size: 32px;\\n            font-weight: 600;\\n            line-height: 38.73px;\\n            text-align: left;\\n            white-space: nowrap;\\n            overflow: hidden;\\n            text-overflow: ellipsis;\\n        }\\n\\n        .header-bottom-line[_ngcontent-%COMP%] {\\n            border-bottom: 2px solid #ccc;\\n            margin-top: 15px;\\n        }\\n\\n        .rectangle[_ngcontent-%COMP%] {\\n            width: 100%;\\n            max-width: 1292px;\\n            min-height: auto;\\n            border-radius: 15px;\\n            background: #FFFFFF;\\n            box-shadow: 0px 3px 11.9px -1px #00000040;\\n            margin-top: 20px;\\n            padding-bottom: 20px;\\n            \\n        }\\n\\n        #header1[_ngcontent-%COMP%] {\\n            height: 64px;\\n            border-radius: 15px 15px 0 0;\\n            background: linear-gradient(90deg, #FB751E 49.4%, #DBB800 100%);\\n            display: flex;\\n            align-items: center;\\n            justify-content: center;\\n            color: white;\\n            font-family: Inter, sans-serif;\\n            font-size: 24px;\\n            font-weight: 600;\\n            margin-bottom: 0;\\n        }\\n\\n        h2[_ngcontent-%COMP%] {\\n            margin: 0;\\n        }\\n\\n        .bill-p[_ngcontent-%COMP%] {\\n            font-family: Inter;\\n            font-size: 20px;\\n            font-weight: 600;\\n            line-height: 24.2px;\\n            text-align: left;\\n            color: #333333;\\n            padding-left: 40px;\\n            padding-top: 40px;\\n            margin: 0;\\n        }\\n\\n        .lab-p[_ngcontent-%COMP%] {\\n            font-family: Inter;\\n            font-size: 20px;\\n            font-weight: 600;\\n            line-height: 24.2px;\\n            text-align: right;\\n            color: #333333;\\n            padding-right: 40px;\\n            padding-top: 40px;\\n            margin: 0;\\n        }\\n\\n        #name[_ngcontent-%COMP%], #address[_ngcontent-%COMP%], #email-l[_ngcontent-%COMP%], #email-r[_ngcontent-%COMP%], #tel-l[_ngcontent-%COMP%], #tel-r[_ngcontent-%COMP%] {\\n            font-family: Inter;\\n            font-size: 16px;\\n            font-weight: 500;\\n            line-height: 19.36px;\\n            color: #666666;\\n        }\\n\\n        #name[_ngcontent-%COMP%], #email-l[_ngcontent-%COMP%], #tel-l[_ngcontent-%COMP%] {\\n            text-align: left;\\n            padding-left: 40px;\\n        }\\n\\n        #address[_ngcontent-%COMP%], #email-r[_ngcontent-%COMP%], #tel-r[_ngcontent-%COMP%] {\\n            text-align: right;\\n            padding-right: 40px;\\n        }\\n\\n        #name[_ngcontent-%COMP%], #address[_ngcontent-%COMP%] {\\n            padding-top: 20px;\\n        }\\n\\n        #email-l[_ngcontent-%COMP%], #email-r[_ngcontent-%COMP%], #tel-l[_ngcontent-%COMP%], #tel-r[_ngcontent-%COMP%] {\\n            padding-top: 5px;\\n        }\\n\\n        .align-items-end[_ngcontent-%COMP%] {\\n            align-items: flex-end !important;\\n        }\\n\\n        #note-i[_ngcontent-%COMP%] {\\n            font-family: Inter;\\n            font-size: 20px;\\n            font-weight: 600;\\n            line-height: 24.2px;\\n            color: #333333;\\n            padding-left: 40px;\\n            padding-top: 20px;\\n        }\\n\\n        #number-i[_ngcontent-%COMP%], #date-i[_ngcontent-%COMP%], #text-i[_ngcontent-%COMP%] {\\n            font-family: Inter;\\n            font-size: 16px;\\n            color: #333333;\\n            width: 80%;\\n            padding: 8px;\\n            margin-top: 2px;\\n            border: 1px solid #ccc;\\n            border-radius: 4px;\\n            margin-left: 40px;\\n        }\\n\\n        \\n\\n.my-appointments-table[_ngcontent-%COMP%] {\\n    width: 94%;\\n    border-collapse: collapse;\\n    margin: 0 auto;\\n    font-size: 14px;\\n  }\\n  \\n  .my-appointments-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .my-appointments-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    text-align: left;\\n    border: 1px solid #ddd;\\n  }\\n  \\n  .my-appointments-table[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n    text-align: center;\\n    font-size: 18px;\\n    font-weight: bold;\\n  }\\n  \\n  .service-input[_ngcontent-%COMP%] {\\n    width: 100%;\\n    box-sizing: border-box;\\n    padding: 5px;\\n    border: none;\\n    border-bottom: 2px solid #ccc;\\n    outline: none;\\n    font-size: 14px;\\n  }\\n  \\n  textarea.service-input[_ngcontent-%COMP%] {\\n    resize: none;\\n    height: auto;\\n  }\\n  \\n  \\n\\n  @media (max-width: 1000px) {\\n    .my-appointments-table[_ngcontent-%COMP%] {\\n      font-size: 12px;\\n    }\\n  \\n    .my-appointments-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n      display: none;\\n    }\\n  \\n    .my-appointments-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%], .my-appointments-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%], .my-appointments-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n      display: block;\\n      width: 100%;\\n    }\\n  \\n    .my-appointments-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n      position: relative;\\n      \\n      text-align: left;\\n      border: none;\\n      border-bottom: 1px solid #ddd;\\n    }\\n  \\n    .my-appointments-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:before {\\n      content: attr(data-label);\\n      position: absolute;\\n      left: 10px;\\n      top: 10px;\\n      font-weight: bold;\\n      white-space: nowrap;\\n    }\\n  \\n    .service-input[_ngcontent-%COMP%] {\\n      font-size: 12px;\\n    }\\n  }\\n  \\n  \\n\\n  @media (max-width: 480px) {\\n    .my-appointments-table[_ngcontent-%COMP%] {\\n      font-size: 10px;\\n    }\\n  \\n    .service-input[_ngcontent-%COMP%] {\\n      font-size: 10px;\\n    }\\n  }\\n  \\n\\n        .totals-row[_ngcontent-%COMP%] {\\n            margin-top: 20px;\\n        }\\n\\n        .totals-container[_ngcontent-%COMP%] {\\n            width: 100%;\\n            max-width: 550px;\\n            display: flex;\\n            flex-direction: column;\\n            align-items: flex-end;\\n            padding-right: 40px;\\n        }\\n\\n        .totals-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n            display: flex;\\n            align-items: center;\\n            justify-content: space-between;\\n            width: 320px;\\n            margin: 5px 0;\\n            font-family: Inter, sans-serif;\\n            font-size: 16px;\\n            color: #333333;\\n        }\\n\\n        .totals-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n            width: 158px;\\n            height: 44px;\\n            border-radius: 10px;\\n            border: 1px solid #CEC9C980;\\n            background: #D9D9D94D;\\n            opacity: 1;\\n        }\\n\\n        #textarea[_ngcontent-%COMP%] {\\n            \\n            height: 100px;\\n            top: 1331px;\\n           width: 94%;\\n            justify-content: center;\\n            border-radius: 12px;\\n            border: 1px;\\n          \\n          \\n            \\n            border: 1px solid #B3B3B3;\\n        }\\n\\n        .service-input[_ngcontent-%COMP%]:focus{\\n            border: none;\\n            outline: none;\\n        }\\n\\n        \\n\\n        #note[_ngcontent-%COMP%] {\\n            padding-left: 3%;\\n          \\n            \\n            font-family: Inter;\\n            font-size: 20px;\\n            font-weight: 600;\\n            line-height: 24.2px;\\n            text-align: left;\\n            color: #000;\\n        }\\n        \\n\\n\\n        .container[_ngcontent-%COMP%] {\\n            position: relative;\\n        }\\n\\n        #addProduct[_ngcontent-%COMP%] {\\n            width: 167px;\\n            height: 35px;\\n            border-radius: 18px;\\n            font-family: Inter;\\n            font-size: 16px;\\n            font-weight: 600;\\n            line-height: 19.36px;\\n            text-align: center;\\n            margin-top: 60px;\\n            float: right;\\n            color: #FFFFFF;\\n            border: 1px solid #FB751E;\\n            background: linear-gradient(266.16deg, #B93426 0.91%, #FB751E 60.88%);\\n            right: 0;\\n            bottom: 0;\\n        }\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { LaboratoryInvoiceComponent };", "map": {"version": 3, "names": ["LaboratoryInvoiceComponent", "constructor", "laboratoryService", "name", "invoices", "submitInvoice", "_", "i0", "ɵɵdirectiveInject", "i1", "LaboratoryService", "_2", "selectors", "decls", "vars", "consts", "template", "LaboratoryInvoiceComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "LaboratoryInvoiceComponent_Template_form_submit_9_listener"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\laboratory-invoice\\laboratory-invoice.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\laboratory-invoice\\laboratory-invoice.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { LaboratoryService } from '../laboratory.service';\r\n\r\n\r\n@Component({\r\n  selector: 'app-laboratory-invoice',\r\n  templateUrl: './laboratory-invoice.component.html',\r\n  styleUrls: ['./laboratory-invoice.component.css']\r\n})\r\nexport class LaboratoryInvoiceComponent {\r\n\r\n  \r\n    name :string = '';\r\n    invoices: any[] = []; // Array to hold fetched invoices\r\n  \r\n\r\n  constructor(private laboratoryService: LaboratoryService){}\r\nsubmitInvoice() {\r\n\r\n  // const invoiceData ={\r\n  //   name : this.name,\r\n\r\n  // }\r\n  // this.laboratoryService.saveLaboratory(invoiceData).subscribe(\r\n  //   (response) => {\r\n  //     console.log('Invoice saved successfully', response);\r\n  //     alert('Invoice saved successfully!');\r\n  //   },\r\n  //   (error) => {\r\n  //     console.error('Error saving invoice', error);\r\n  //     alert('Error saving invoice!');\r\n  //   }\r\n  // );\r\n}\r\n\r\n// ngOnInit() {\r\n//   this.getLabData(); // Fetch invoices when the component initializes\r\n// }\r\n\r\n\r\n// getLabData(id: number) : void{\r\n//   const LabIdString = localStorage.getItem('LabId');\r\n  \r\n//   if(LabId)// Retrieve the clinicId from localStorage\r\n//   this.laboratoryService.getLaboratoryById(id).subscribe(\r\n//     (data) => {\r\n//       this.invoices = data; // Set the fetched data to invoices array\r\n//       console.log('Invoices loaded:', this.invoices); // You can remove this line later\r\n//     },\r\n//     (error) => {\r\n//       console.error('Error fetching invoices:', error);\r\n//     }\r\n//   );\r\n// }\r\n  \r\n \r\n}\r\n", "<div class=\"row\">\r\n  <div class=\"col-12\">\r\n    <div class=\"row\">\r\n      <div class=\"col-12\">\r\n          <div class=\" header-row\">\r\n              <div class=\"header-row-h1\">\r\n                  Invoice\r\n              </div>\r\n          </div>\r\n          <div class=\"header-bottom-line\"></div>\r\n          <br>\r\n          <form class=\"rectangle\" (submit)=\"submitInvoice()\" >\r\n              <div id=\"header1\">\r\n                  <h2>Invoice</h2>\r\n              </div>\r\n              <div class=\"row mb-3\">\r\n                  <div class=\"col-md-6 d-flex flex-column\">\r\n                   \r\n                      <p class=\"bill-p\" style=\"margin-bottom: 10px;\">Billing Form</p>\r\n                      <input type=\"text\" id=\"text-i\" placeholder=\"Name\" required>\r\n                      <input type=\"text\" id=\"text-i\" placeholder=\"Company Name\" required>\r\n                      <input type=\"text\" id=\"number-i\" placeholder=\"Contacts\" required>\r\n                      <!-- <label id=\"name\"><PERSON></label>\r\n                      <label id=\"email-l\">abc.com</label>\r\n                      <label id=\"tel-l\">0770120120 | 0112520520</label> -->\r\n                  </div>\r\n                  <div class=\"col-md-6 d-flex flex-column align-items-end\">\r\n                      <p class=\"lab-p\">Laboratory</p>\r\n                      <label id=\"address\">ABL Lab, Colombo 12</label>\r\n                      <label id=\"email-r\">abc.com</label>\r\n                      <label id=\"tel-r\">0770120120 | 0112520520</label>\r\n                  </div>\r\n              </div>\r\n              <div class=\"row mb-3\">\r\n                  <div class=\"col-md-6 d-flex flex-column\">\r\n                      <p id=\"note-i\">Invoice Number</p>\r\n                      <input type=\"text\" id=\"number-i\" required>\r\n                  </div>\r\n              </div>\r\n              <div class=\"row mb-3\">\r\n                  <div class=\"col-md-6 d-flex flex-column\">\r\n                      <p id=\"note-i\">Invoice Date</p>\r\n                      <input type=\"date\" id=\"date-i\" required>\r\n                  </div>\r\n              </div>\r\n              <div class=\"row mb-3\">\r\n                <table class=\"my-appointments-table\">\r\n                  <thead>\r\n                    <tr>\r\n                      <th colspan=\"4\" class=\"section-header\">Item Details</th>\r\n                    </tr>\r\n                    <tr class=\"table-header\">\r\n                      <th>Service</th>\r\n                      <th>Description</th>\r\n                      <th>Qty</th>\r\n                      <th>Amount</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr class=\"table-row\">\r\n                      <td>\r\n                        <input\r\n                          placeholder=\"Service\"\r\n                          type=\"text\"\r\n                          class=\"service-input\"\r\n                          required\r\n                        />\r\n                      </td>\r\n                      <td>\r\n                        <textarea\r\n                          placeholder=\"Description\"\r\n                          class=\"service-input\"\r\n                          style=\"margin-top: 30px;\"\r\n                        ></textarea>\r\n                      </td>\r\n                      <td>\r\n                        <input\r\n                          placeholder=\"Quantity\"\r\n                          type=\"number\"\r\n                          class=\"service-input\"\r\n                          required\r\n                          style=\"width: 50%;\"\r\n                        />\r\n                      </td>\r\n                      <td>\r\n                       \r\n                        <input\r\n                          placeholder=\"Price (LKR)\"\r\n                          type=\"number\"\r\n                          class=\"service-input\"\r\n                          required\r\n                          style=\"width: 50%;\"\r\n                        />\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n              \r\n              <div class=\"row mb-3 totals-row\">\r\n                  <div class=\"col-md-12 d-flex justify-content-end\">\r\n                      <div class=\"totals-container\">\r\n                          <p>\r\n                              <span id=\"sub-p\">Sub Total</span>\r\n                              <input id=\"number-i\" type=\"number\" style=\"width: 200px;\" required/>\r\n\r\n                          </p>\r\n                          <p>\r\n                              <span id=\"dic-p\">Total Disc Amount</span>\r\n                              <input id=\"number-i\" type=\"number\" style=\"width: 200px;\"/>\r\n                          </p>\r\n                          <p>\r\n                              <span id=\"total-p\">Total GST</span>\r\n                              <input id=\"number-i\" type=\"number\" style=\"width: 200px;\"/>\r\n                          </p>\r\n                          <p>\r\n                              <span id=\"grand-p\"><b>Grand Amount</b></span>\r\n                              <input id=\"number-i\" type=\"number\" style=\"width: 200px;\" required/>\r\n                          </p>\r\n                      </div>\r\n                  </div>\r\n              </div>\r\n              <p id=\"note\" style=\" display: flex; \">Note</p>\r\n              <div class=\" mb-3\" style=\"width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: left;\">\r\n                  \r\n                  <textarea id=\"textarea\"></textarea>\r\n              </div>\r\n             \r\n                <div class=\" d-flex \" style=\"display: flex; width: 100%; justify-content: end; padding-right: 40px;\">\r\n                    <button id=\"addProduct\" type=\"submit\">Add Product</button>\r\n                </div>\r\n  \r\n           \r\n            </form>\r\n         \r\n      </div>\r\n  </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;AAIA,MAKaA,0BAA0B;EAOrCC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;IAJnC,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,QAAQ,GAAU,EAAE,CAAC,CAAC;EAGkC;;EAC5DC,aAAaA,CAAA;IAEX;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EACD,QAAAC,CAAA,G;qBAxBYN,0BAA0B,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BX,0BAA0B;IAAAY,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCTvCX,EAAA,CAAAa,cAAA,aAAiB;QAMCb,EAAA,CAAAc,MAAA,gBACJ;QAAAd,EAAA,CAAAe,YAAA,EAAM;QAEVf,EAAA,CAAAgB,SAAA,aAAsC;QAEtChB,EAAA,CAAAa,cAAA,cAAoD;QAA5Bb,EAAA,CAAAiB,UAAA,oBAAAC,2DAAA;UAAA,OAAUN,GAAA,CAAAd,aAAA,EAAe;QAAA,EAAC;QAC9CE,EAAA,CAAAa,cAAA,cAAkB;QACVb,EAAA,CAAAc,MAAA,eAAO;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAEpBf,EAAA,CAAAa,cAAA,cAAsB;QAGiCb,EAAA,CAAAc,MAAA,oBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC/Df,EAAA,CAAAgB,SAAA,iBAA2D;QAM/DhB,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAa,cAAA,eAAyD;QACpCb,EAAA,CAAAc,MAAA,kBAAU;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC/Bf,EAAA,CAAAa,cAAA,iBAAoB;QAAAb,EAAA,CAAAc,MAAA,2BAAmB;QAAAd,EAAA,CAAAe,YAAA,EAAQ;QAC/Cf,EAAA,CAAAa,cAAA,iBAAoB;QAAAb,EAAA,CAAAc,MAAA,eAAO;QAAAd,EAAA,CAAAe,YAAA,EAAQ;QACnCf,EAAA,CAAAa,cAAA,iBAAkB;QAAAb,EAAA,CAAAc,MAAA,+BAAuB;QAAAd,EAAA,CAAAe,YAAA,EAAQ;QAGzDf,EAAA,CAAAa,cAAA,cAAsB;QAECb,EAAA,CAAAc,MAAA,sBAAc;QAAAd,EAAA,CAAAe,YAAA,EAAI;QACjCf,EAAA,CAAAgB,SAAA,iBAA0C;QAC9ChB,EAAA,CAAAe,YAAA,EAAM;QAEVf,EAAA,CAAAa,cAAA,cAAsB;QAECb,EAAA,CAAAc,MAAA,oBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC/Bf,EAAA,CAAAgB,SAAA,iBAAwC;QAC5ChB,EAAA,CAAAe,YAAA,EAAM;QAEVf,EAAA,CAAAa,cAAA,cAAsB;QAIyBb,EAAA,CAAAc,MAAA,oBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAE1Df,EAAA,CAAAa,cAAA,cAAyB;QACnBb,EAAA,CAAAc,MAAA,eAAO;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAChBf,EAAA,CAAAa,cAAA,UAAI;QAAAb,EAAA,CAAAc,MAAA,mBAAW;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACpBf,EAAA,CAAAa,cAAA,UAAI;QAAAb,EAAA,CAAAc,MAAA,WAAG;QAAAd,EAAA,CAAAe,YAAA,EAAK;QACZf,EAAA,CAAAa,cAAA,UAAI;QAAAb,EAAA,CAAAc,MAAA,cAAM;QAAAd,EAAA,CAAAe,YAAA,EAAK;QAGnBf,EAAA,CAAAa,cAAA,aAAO;QAGDb,EAAA,CAAAgB,SAAA,iBAKE;QACJhB,EAAA,CAAAe,YAAA,EAAK;QACLf,EAAA,CAAAa,cAAA,UAAI;QACFb,EAAA,CAAAgB,SAAA,oBAIY;QACdhB,EAAA,CAAAe,YAAA,EAAK;QACLf,EAAA,CAAAa,cAAA,UAAI;QACFb,EAAA,CAAAgB,SAAA,iBAME;QACJhB,EAAA,CAAAe,YAAA,EAAK;QACLf,EAAA,CAAAa,cAAA,UAAI;QAEFb,EAAA,CAAAgB,SAAA,iBAME;QACJhB,EAAA,CAAAe,YAAA,EAAK;QAMbf,EAAA,CAAAa,cAAA,eAAiC;QAIAb,EAAA,CAAAc,MAAA,iBAAS;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACjCf,EAAA,CAAAgB,SAAA,iBAAmE;QAEvEhB,EAAA,CAAAe,YAAA,EAAI;QACJf,EAAA,CAAAa,cAAA,SAAG;QACkBb,EAAA,CAAAc,MAAA,yBAAiB;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACzCf,EAAA,CAAAgB,SAAA,iBAA0D;QAC9DhB,EAAA,CAAAe,YAAA,EAAI;QACJf,EAAA,CAAAa,cAAA,SAAG;QACoBb,EAAA,CAAAc,MAAA,iBAAS;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACnCf,EAAA,CAAAgB,SAAA,iBAA0D;QAC9DhB,EAAA,CAAAe,YAAA,EAAI;QACJf,EAAA,CAAAa,cAAA,SAAG;QACuBb,EAAA,CAAAc,MAAA,oBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAI;QACtCf,EAAA,CAAAgB,SAAA,iBAAmE;QACvEhB,EAAA,CAAAe,YAAA,EAAI;QAIhBf,EAAA,CAAAa,cAAA,aAAsC;QAAAb,EAAA,CAAAc,MAAA,YAAI;QAAAd,EAAA,CAAAe,YAAA,EAAI;QAC9Cf,EAAA,CAAAa,cAAA,eAA+I;QAE3Ib,EAAA,CAAAgB,SAAA,oBAAmC;QACvChB,EAAA,CAAAe,YAAA,EAAM;QAEJf,EAAA,CAAAa,cAAA,eAAqG;QAC3Db,EAAA,CAAAc,MAAA,mBAAW;QAAAd,EAAA,CAAAe,YAAA,EAAS;;;;;;SDxHjEtB,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}