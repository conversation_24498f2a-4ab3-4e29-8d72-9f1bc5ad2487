{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../user.service\";\nimport * as i3 from \"../../auth/auth.service\";\nimport * as i4 from \"@angular/common\";\nfunction UserMailVerificationComponent_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserMailVerificationComponent_p_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Thank you for verifying your email!\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" You can now access your account and start using all the features of our platform. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UserMailVerificationComponent_p_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Your email is already verified.\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" You can now access your account and start using all the features of our platform. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction UserMailVerificationComponent_p_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.message, \" \");\n  }\n}\nfunction UserMailVerificationComponent_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtemplate(1, UserMailVerificationComponent_p_8_ng_container_1_Template, 4, 0, \"ng-container\", 5);\n    i0.ɵɵtemplate(2, UserMailVerificationComponent_p_8_ng_container_2_Template, 4, 0, \"ng-container\", 5);\n    i0.ɵɵtemplate(3, UserMailVerificationComponent_p_8_ng_container_3_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message === \"Email verified successfully!\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message === \"Your email is already verified.\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.message !== \"Email verified successfully!\" && ctx_r1.message !== \"Your email is already verified.\");\n  }\n}\nfunction UserMailVerificationComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function UserMailVerificationComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.goToLogin());\n    });\n    i0.ɵɵtext(1, \" Go to Login \");\n    i0.ɵɵelementEnd();\n  }\n}\nclass UserMailVerificationComponent {\n  constructor(route, userService, authService, router) {\n    this.route = route;\n    this.userService = userService;\n    this.authService = authService;\n    this.router = router;\n    this.loading = true;\n    this.message = '';\n    this.userType = '';\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      const verificationToken = params['token'];\n      this.userType = params['userType'];\n      if (!verificationToken) {\n        this.message = 'No verification token provided.';\n        this.loading = false;\n        return;\n      }\n      this.verifyEmail(verificationToken);\n    });\n  }\n  verifyEmail(token) {\n    this.userService.verifyEmail(token).subscribe(response => {\n      if (response && typeof response === 'object') {\n        this.message = response.message || 'No message provided.';\n      } else {\n        this.message = response;\n      }\n      this.loading = false;\n    }, error => {\n      this.message = error.error && error.error.message ? error.error.message : 'An error occurred. Please try again.';\n      this.loading = false;\n    });\n  }\n  goToLogin() {\n    this.router.navigate(['/user-login']);\n  }\n  static #_ = this.ɵfac = function UserMailVerificationComponent_Factory(t) {\n    return new (t || UserMailVerificationComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i1.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserMailVerificationComponent,\n    selectors: [[\"app-user-mail-verification\"]],\n    decls: 10,\n    vars: 4,\n    consts: [[1, \"page-background\"], [1, \"rec1\"], [1, \"rec2\"], [1, \"verification-container\"], [1, \"verification-content\"], [4, \"ngIf\"], [\"mat-raised-button\", \"\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", 3, \"click\"]],\n    template: function UserMailVerificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"h2\");\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, UserMailVerificationComponent_p_7_Template, 2, 0, \"p\", 5);\n        i0.ɵɵtemplate(8, UserMailVerificationComponent_p_8_Template, 4, 3, \"p\", 5);\n        i0.ɵɵtemplate(9, UserMailVerificationComponent_button_9_Template, 2, 0, \"button\", 6);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"\", ctx.userType, \" Email Verification\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.message === \"Email verified successfully!\" || ctx.message === \"Your email is already verified.\");\n      }\n    },\n    dependencies: [i4.NgIf],\n    styles: [\"html[_ngcontent-%COMP%], body[_ngcontent-%COMP%] {\\n  height: 100%;\\n  margin: 0;\\n  font-family: 'Arial', sans-serif;\\n}\\n\\n.page-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  height: 100%;\\n  width: 100%;\\n  background-color: #f4f4f9;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.verification-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  z-index: 10;\\n  position: relative;\\n  padding: 20px; \\n\\n}\\n\\n.verification-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  width: 100%;\\n  text-align: center;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  color: #ff6b00;\\n  font-weight: bold;\\n  margin-bottom: 30px;\\n  font-size: 24px; \\n\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background-color: #ff6b00;\\n  border: #ff6b00 1px solid;\\n  border-radius: 15px;\\n  color: white;\\n  margin-top: 20px;\\n  padding: 10px 20px;\\n  font-size: 16px; \\n\\n  width: 100%; \\n\\n  max-width: 200px;\\n}\\n\\np[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px; \\n\\n}\\n\\n.rec1[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  left: 740px;\\n  width: 900px;\\n  height: 1600px;\\n  background: #FB751E;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(72deg);\\n}\\n\\n.rec2[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 225px;\\n  left: -400px;\\n  width: 900px;\\n  height: 1800px;\\n  border: #FB751E 1px solid;\\n  z-index: 1;\\n  border-radius: 150px;\\n  transform: rotate(40deg);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 20px; \\n\\n    margin-bottom: 20px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px; \\n\\n    max-width: 100%; \\n\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 600px; \\n\\n    height: 1200px;\\n    left: 500px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 600px;\\n    height: 1400px;\\n    left: -300px; \\n\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  h2[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    margin-bottom: 15px;\\n  }\\n\\n  button[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n    padding: 8px 15px;\\n    max-width: 100%;\\n  }\\n\\n  .rec1[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1000px;\\n    left: 400px; \\n\\n  }\\n\\n  .rec2[_ngcontent-%COMP%] {\\n    width: 500px;\\n    height: 1200px;\\n    left: -250px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { UserMailVerificationComponent };", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r5", "message", "ɵɵtemplate", "UserMailVerificationComponent_p_8_ng_container_1_Template", "UserMailVerificationComponent_p_8_ng_container_2_Template", "UserMailVerificationComponent_p_8_ng_container_3_Template", "ɵɵproperty", "ctx_r1", "ɵɵlistener", "UserMailVerificationComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "UserMailVerificationComponent", "constructor", "route", "userService", "authService", "router", "loading", "userType", "ngOnInit", "queryParams", "subscribe", "params", "verificationToken", "verifyEmail", "token", "response", "error", "navigate", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "UserService", "i3", "AuthService", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "UserMailVerificationComponent_Template", "rf", "ctx", "UserMailVerificationComponent_p_7_Template", "UserMailVerificationComponent_p_8_Template", "UserMailVerificationComponent_button_9_Template"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-mail-verification\\user-mail-verification.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-mail-verification\\user-mail-verification.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { UserService } from '../user.service';\r\nimport { AuthService } from '../../auth/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-user-mail-verification',\r\n  templateUrl: './user-mail-verification.component.html',\r\n  styleUrls: ['./user-mail-verification.component.css'],\r\n})\r\nexport class UserMailVerificationComponent implements OnInit {\r\n  loading = true; \r\n  message = '';\r\n  userType = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private userService: UserService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams.subscribe((params) => {\r\n      const verificationToken = params['token'];\r\n      this.userType = params['userType'];\r\n\r\n      if (!verificationToken) {\r\n        this.message = 'No verification token provided.';\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      this.verifyEmail(verificationToken);\r\n    });\r\n  }\r\n\r\n  verifyEmail(token: string): void {\r\n    this.userService.verifyEmail(token).subscribe(\r\n      (response: any) => { \r\n        if (response && typeof response === 'object') {\r\n          this.message = response.message || 'No message provided.'; \r\n        } else {\r\n          this.message = response; \r\n        }\r\n        this.loading = false;\r\n      },\r\n      (error) => {\r\n        this.message = error.error && error.error.message\r\n          ? error.error.message\r\n          : 'An error occurred. Please try again.';\r\n        this.loading = false;\r\n      }\r\n    );\r\n  }\r\n\r\n  goToLogin(): void {\r\n    this.router.navigate(['/user-login']);\r\n  }\r\n}\r\n", "<div class=\"page-background\">\r\n  <div class=\"rec1\"></div>\r\n  <div class=\"rec2\"></div>\r\n  <div class=\"verification-container\">\r\n    <div class=\"verification-content\">\r\n      <h2>{{ userType }} Email Verification</h2>\r\n\r\n      <p *ngIf=\"loading\">Loading...</p>\r\n      \r\n      <p *ngIf=\"!loading\">\r\n        <ng-container *ngIf=\"message === 'Email verified successfully!'\">\r\n          Thank you for verifying your email!<br>\r\n          You can now access your account and start using all the features of our platform.\r\n        </ng-container>\r\n        \r\n        <ng-container *ngIf=\"message === 'Your email is already verified.'\">\r\n          Your email is already verified.<br>\r\n          You can now access your account and start using all the features of our platform.\r\n        </ng-container>\r\n        \r\n        <ng-container *ngIf=\"message !== 'Email verified successfully!' && message !== 'Your email is already verified.'\">\r\n          {{ message }}\r\n        </ng-container>\r\n      </p>\r\n      \r\n      <button *ngIf=\"message === 'Email verified successfully!' || message === 'Your email is already verified.'\" mat-raised-button (click)=\"goToLogin()\">\r\n        Go to Login\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";;;;;;;ICOMA,EAAA,CAAAC,cAAA,QAAmB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG/BH,EAAA,CAAAI,uBAAA,GAAiE;IAC/DJ,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAK,SAAA,SAAI;IACvCL,EAAA,CAAAE,MAAA,0FACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;;IAEfN,EAAA,CAAAI,uBAAA,GAAoE;IAClEJ,EAAA,CAAAE,MAAA,uCAA+B;IAAAF,EAAA,CAAAK,SAAA,SAAI;IACnCL,EAAA,CAAAE,MAAA,0FACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;;IAEfN,EAAA,CAAAI,uBAAA,GAAkH;IAChHJ,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAM,qBAAA,EAAe;;;;IADbN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAC,MAAA,CAAAC,OAAA,MACF;;;;;IAbFV,EAAA,CAAAC,cAAA,QAAoB;IAClBD,EAAA,CAAAW,UAAA,IAAAC,yDAAA,0BAGe;IAEfZ,EAAA,CAAAW,UAAA,IAAAE,yDAAA,0BAGe;IAEfb,EAAA,CAAAW,UAAA,IAAAG,yDAAA,0BAEe;IACjBd,EAAA,CAAAG,YAAA,EAAI;;;;IAbaH,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,oCAAgD;IAKhDV,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,uCAAmD;IAKnDV,EAAA,CAAAO,SAAA,GAAiG;IAAjGP,EAAA,CAAAe,UAAA,SAAAC,MAAA,CAAAN,OAAA,uCAAAM,MAAA,CAAAN,OAAA,uCAAiG;;;;;;IAKlHV,EAAA,CAAAC,cAAA,gBAAoJ;IAAtBD,EAAA,CAAAiB,UAAA,mBAAAC,wEAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACjJxB,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADtBf,MAKasB,6BAA6B;EAKxCC,YACUC,KAAqB,EACrBC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAArB,OAAO,GAAG,EAAE;IACZ,KAAAsB,QAAQ,GAAG,EAAE;EAOV;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,KAAK,CAACO,WAAW,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC1C,MAAMC,iBAAiB,GAAGD,MAAM,CAAC,OAAO,CAAC;MACzC,IAAI,CAACJ,QAAQ,GAAGI,MAAM,CAAC,UAAU,CAAC;MAElC,IAAI,CAACC,iBAAiB,EAAE;QACtB,IAAI,CAAC3B,OAAO,GAAG,iCAAiC;QAChD,IAAI,CAACqB,OAAO,GAAG,KAAK;QACpB;;MAGF,IAAI,CAACO,WAAW,CAACD,iBAAiB,CAAC;IACrC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACC,KAAa;IACvB,IAAI,CAACX,WAAW,CAACU,WAAW,CAACC,KAAK,CAAC,CAACJ,SAAS,CAC1CK,QAAa,IAAI;MAChB,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAC5C,IAAI,CAAC9B,OAAO,GAAG8B,QAAQ,CAAC9B,OAAO,IAAI,sBAAsB;OAC1D,MAAM;QACL,IAAI,CAACA,OAAO,GAAG8B,QAAQ;;MAEzB,IAAI,CAACT,OAAO,GAAG,KAAK;IACtB,CAAC,EACAU,KAAK,IAAI;MACR,IAAI,CAAC/B,OAAO,GAAG+B,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC/B,OAAO,GAC7C+B,KAAK,CAACA,KAAK,CAAC/B,OAAO,GACnB,sCAAsC;MAC1C,IAAI,CAACqB,OAAO,GAAG,KAAK;IACtB,CAAC,CACF;EACH;EAEAP,SAASA,CAAA;IACP,IAAI,CAACM,MAAM,CAACY,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAC,CAAA,G;qBAhDUlB,6BAA6B,EAAAzB,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA4C,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlD,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAM,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7B3B,6BAA6B;IAAA4B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV1C3D,EAAA,CAAAC,cAAA,aAA6B;QAC3BD,EAAA,CAAAK,SAAA,aAAwB;QAExBL,EAAA,CAAAC,cAAA,aAAoC;QAE5BD,EAAA,CAAAE,MAAA,GAAiC;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1CH,EAAA,CAAAW,UAAA,IAAAkD,0CAAA,eAAiC;QAEjC7D,EAAA,CAAAW,UAAA,IAAAmD,0CAAA,eAcI;QAEJ9D,EAAA,CAAAW,UAAA,IAAAoD,+CAAA,oBAES;QACX/D,EAAA,CAAAG,YAAA,EAAM;;;QAvBAH,EAAA,CAAAO,SAAA,GAAiC;QAAjCP,EAAA,CAAAQ,kBAAA,KAAAoD,GAAA,CAAA5B,QAAA,wBAAiC;QAEjChC,EAAA,CAAAO,SAAA,GAAa;QAAbP,EAAA,CAAAe,UAAA,SAAA6C,GAAA,CAAA7B,OAAA,CAAa;QAEb/B,EAAA,CAAAO,SAAA,GAAc;QAAdP,EAAA,CAAAe,UAAA,UAAA6C,GAAA,CAAA7B,OAAA,CAAc;QAgBT/B,EAAA,CAAAO,SAAA,GAAiG;QAAjGP,EAAA,CAAAe,UAAA,SAAA6C,GAAA,CAAAlD,OAAA,uCAAAkD,GAAA,CAAAlD,OAAA,uCAAiG;;;;;;;SDfnGe,6BAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}