package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.services.LaboratoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class LaboratoryController {

    private final LaboratoryService laboratoryService;

    @Autowired
    public LaboratoryController(LaboratoryService laboratoryService) {
        this.laboratoryService = laboratoryService;
    }

    @PostMapping("/saveLaboratory")
    public Laboratory saveLaboratory(@RequestBody Laboratory laboratory) {
        return laboratoryService.saveLaboratory(laboratory);
    }

    @GetMapping("/laboratoryList")
    public List<Laboratory> getAllLaboratories() {
        return laboratoryService.findAllLaboratories();
    }

    @PutMapping("/updateLaboratory/{id}")
    public ResponseEntity<Laboratory> updateLaboratory(@PathVariable int id, @RequestBody Laboratory laboratoryDetails) {
        return ResponseEntity.ok(laboratoryService.updateLaboratory(id, laboratoryDetails));
    }

    @GetMapping("/getLaboratoryById/{id}")
    public Laboratory getLaboratoryById(@PathVariable int id) {
        return laboratoryService.getLaboratoryById(id);
    }

    @DeleteMapping("/deleteLaboratory/{id}")
    public ResponseEntity<Void> deleteLaboratory(@PathVariable int id) {
        laboratoryService.deleteLaboratory(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/check_laboratoryName")
    public ResponseEntity<Boolean> checkLaboratoryNameExists(@RequestParam String laboratoryName) {
        boolean isLaboratoryNameExists = laboratoryService.laboratoryNameExists(laboratoryName);
        return ResponseEntity.ok(isLaboratoryNameExists);
    }
}
