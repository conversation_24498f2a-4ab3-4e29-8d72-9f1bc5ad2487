{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { UserLayoutComponent } from './user-layout/user-layout.component';\nimport { UserAppointmentComponent } from './user-appointment/user-appointment.component';\nimport { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';\nimport { UserForgetPasswordComponent } from './user-forget-password/user-forget-password.component';\nimport { UserPasswardChangeComponent } from './user-passward-change/user-passward-change.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: UserLayoutComponent,\n  children: [{\n    path: '',\n    component: UserAppointmentComponent\n  }, {\n    path: 'verify-user',\n    component: UserMailVerificationComponent\n  }, {\n    path: 'forget-password',\n    component: UserForgetPasswordComponent\n  }, {\n    path: 'password-resect',\n    component: UserPasswardChangeComponent\n  }]\n}];\nclass UserRoutingModule {\n  static #_ = this.ɵfac = function UserRoutingModule_Factory(t) {\n    return new (t || UserRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UserRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\nexport { UserRoutingModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UserRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "UserLayoutComponent", "UserAppointmentComponent", "UserMailVerificationComponent", "UserForgetPasswordComponent", "UserPasswardChangeComponent", "routes", "path", "component", "children", "UserRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { UserLayoutComponent } from './user-layout/user-layout.component';\r\nimport { UserAppointmentComponent } from './user-appointment/user-appointment.component';\r\nimport { UserMailVerificationComponent } from './user-mail-verification/user-mail-verification.component';\r\nimport { UserForgetPasswordComponent } from './user-forget-password/user-forget-password.component';\r\nimport { UserPasswardChangeComponent } from './user-passward-change/user-passward-change.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: UserLayoutComponent,\r\n    children:[\r\n      {path:'',component:UserAppointmentComponent},\r\n      {path:'verify-user', component: UserMailVerificationComponent},\r\n      {path:'forget-password',component:UserForgetPasswordComponent},\r\n      {path:'password-resect',component:UserPasswardChangeComponent}\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class UserRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,2BAA2B,QAAQ,uDAAuD;;;AAEnG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEP,mBAAmB;EAC9BQ,QAAQ,EAAC,CACP;IAACF,IAAI,EAAC,EAAE;IAACC,SAAS,EAACN;EAAwB,CAAC,EAC5C;IAACK,IAAI,EAAC,aAAa;IAAEC,SAAS,EAAEL;EAA6B,CAAC,EAC9D;IAACI,IAAI,EAAC,iBAAiB;IAACC,SAAS,EAACJ;EAA2B,CAAC,EAC9D;IAACG,IAAI,EAAC,iBAAiB;IAACC,SAAS,EAACH;EAA2B,CAAC;CAEjE,CACF;AAED,MAIaK,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAHlBb,YAAY,CAACc,QAAQ,CAACR,MAAM,CAAC,EAC7BN,YAAY;EAAA;;SAEXU,iBAAiB;;2EAAjBA,iBAAiB;IAAAK,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFlBjB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}