import { Component, OnInit } from '@angular/core';

interface TimeSlot {
  id: string;
  label: string;
  status: 'available' | 'disabled' | 'selected';
}

@Component({
  selector: 'app-doctor-select',
  templateUrl: './doctor-select.component.html',
  styleUrls: ['./doctor-select.component.css']
})
export class DoctorSelectComponent implements OnInit {
  selectedDoctor: string = '';
  selectedTime: string = '';
  selectedTimeId: string = '';

  timeSlots: TimeSlot[] = [
    { id: 'slot-1', label: '7:00 AM - 8:00 AM', status: 'disabled' },
    { id: 'slot-2', label: '8:00 AM - 9:00 AM', status: 'disabled' },
    { id: 'slot-3', label: '9:00 AM - 10:00 AM', status: 'available' },
    { id: 'slot-4', label: '10:00 AM - 11:00 AM', status: 'available' },
    { id: 'slot-5', label: '11:00 AM - 12:00 PM', status: 'disabled' },
    { id: 'slot-6', label: '4:00 PM - 5:00 PM', status: 'available' },
    { id: 'slot-7', label: '5:00 PM - 6:00 PM', status: 'available' },
    { id: 'slot-8', label: '6:00 PM - 7:00 PM', status: 'available' },
    { id: 'slot-9', label: '7:00 PM - 8:00 PM', status: 'available' },
    { id: 'slot-10', label: '8:00 PM - 9:00 PM', status: 'selected' }
  ];

  ngOnInit(): void {
    // Set initial selected time if needed
    const selectedSlot = this.timeSlots.find(slot => slot.status === 'selected');
    if (selectedSlot) {
      this.selectedTime = selectedSlot.label;
      this.selectedTimeId = selectedSlot.id;
    }
  }

  onDoctorChange(event: any): void {
    const selectElement = event.target as HTMLSelectElement;
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    this.selectedDoctor = selectedOption.text;
  }

  selectTimeSlot(slotId: string): void {
    const slot = this.timeSlots.find(s => s.id === slotId);
    if (slot && slot.status !== 'disabled') {
      // Update previous selected slot
      this.timeSlots.forEach(s => {
        if (s.id === this.selectedTimeId && s.status === 'selected') {
          s.status = 'available';
        }
      });

      // Set new selected slot
      slot.status = 'selected';
      this.selectedTime = slot.label;
      this.selectedTimeId = slotId;
    }
  }

  getTimeSlotClasses(slot: TimeSlot): any {
    return {
      'disabled': slot.status === 'disabled',
      'available': slot.status === 'available',
      'selected': slot.status === 'selected'
    };
  }

  confirmAppointment(): void {
    if (this.selectedDoctor && this.selectedTime) {
      console.log('Appointment confirmed:', {
        doctor: this.selectedDoctor,
        timeSlot: this.selectedTime
      });
      // TODO: Call service to send appointment confirmation
      alert(`Appointment scheduled with ${this.selectedDoctor} at ${this.selectedTime}`);
    }
  }
}
