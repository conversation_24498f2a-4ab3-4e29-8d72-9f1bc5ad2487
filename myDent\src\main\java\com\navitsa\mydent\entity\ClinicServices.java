package com.navitsa.mydent.entity;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import java.io.Serializable;

@Entity
@Table(name= "clinic_services")
public class ClinicServices implements Serializable {
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "clinic_service_id")
	private Integer clinicServiceId;
	
	@ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "clinic_id", referencedColumnName = "clinic_id")
    private Clinic clinics;
	
	@ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "service_id", referencedColumnName = "clinicServicecategory_id")
    private ClinicServicesCategory services;
	
    
    public ClinicServices() {
    	
    }


	


	public ClinicServices(Integer clinicServiceId, Clinic clinics, ClinicServicesCategory services) {
		super();
		this.clinicServiceId = clinicServiceId;
		this.clinics = clinics;
		this.services = services;
	}





	public Integer getClinicServiceId() {
		return clinicServiceId;
	}


	public void setClinicServiceId(Integer clinicServiceId) {
		this.clinicServiceId = clinicServiceId;
	}


	public Clinic getClinics() {
		return clinics;
	}


	public void setClinics(Clinic clinics) {
		this.clinics = clinics;
	}


	public ClinicServicesCategory getServices() {
		return services;
	}


	public void setServices(ClinicServicesCategory services) {
		this.services = services;
	}


	
	
	
}

