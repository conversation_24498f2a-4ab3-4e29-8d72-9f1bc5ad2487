{"ast": null, "code": "import { User } from '../user';\nimport Swal from 'sweetalert2'; // Import SweetAlert\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../user.service\";\nimport * as i3 from \"ngx-cookie-service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../core/default-navbar/default-navbar.component\";\nimport * as i7 from \"../../core/primary-action-button/primary-action-button.component\";\nfunction UserLoginComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserLoginComponent_div_27_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Email must be valid one. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserLoginComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, UserLoginComponent_div_27_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵtemplate(2, UserLoginComponent_div_27_div_2_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(26);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"minlength\"]);\n  }\n}\nfunction UserLoginComponent_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Password is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserLoginComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, UserLoginComponent_div_35_div_1_Template, 2, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"opacity\": a0,\n    \"pointer-events\": a1\n  };\n};\nconst _c1 = function () {\n  return [\"/user/forget-password\"];\n};\nclass UserLoginComponent {\n  constructor(router, userService, cookieService) {\n    this.router = router;\n    this.userService = userService;\n    this.cookieService = cookieService;\n    this.username = '';\n    this.password = '';\n    this.user = new User();\n    this.remember = false;\n    this.passwordVisible = false;\n  }\n  ngOnInit() {\n    const encodedData = this.cookieService.get('authData');\n    if (encodedData) {\n      try {\n        const decoded = atob(encodedData);\n        const [token, username, routerPath] = decoded.split('::');\n        console.log('Decoded Cookie Data:', {\n          token,\n          username,\n          routerPath\n        });\n        if (username) {\n          this.username = username;\n          this.remember = true; // checked only if cookie exists\n        }\n\n        if (token && routerPath) {\n          this.router.navigate(['/' + routerPath]);\n        }\n      } catch (e) {\n        console.error('Invalid cookie format');\n        this.remember = false; // reset if cookie invalid\n      }\n    } else {\n      this.remember = false; // no cookie → checkbox unchecked\n    }\n  }\n\n  togglePasswordVisibility() {\n    this.passwordVisible = !this.passwordVisible;\n    const passwordField = document.getElementById('password');\n    passwordField.type = this.passwordVisible ? 'text' : 'password';\n  }\n  onSubmitLogin() {\n    localStorage.clear();\n    this.onLogin();\n  }\n  onLogin() {\n    this.userService.login(this.user).subscribe(response => {\n      console.log('Login Successful', response); // Log the successful response\n      // Set the auth token after successful login\n      this.userService.setAuthToken(response.token);\n      // Log the complete response to check for clinicId\n      console.log('API Response:', response.username);\n      // Store the full user object and other relevant data\n      localStorage.setItem('userid', response.id.toString());\n      localStorage.setItem('firstName', response.firstName || '');\n      localStorage.setItem('lastName', response.lastName || '');\n      localStorage.setItem('companyId', response.companyId || '');\n      localStorage.setItem('username', response.username || '');\n      console.table(response);\n      // ✅ Combine token, username, and routerPath into one string\n      const combined = `${response.token}::${response.username}::${response.userCategoryId?.routerPath || ''}`;\n      // ✅ Encode it using Base64 to hide content\n      const encoded = btoa(combined);\n      // ✅ Save cookie (persistent if Remember Me checked)\n      if (this.remember) {\n        this.cookieService.set('authData', encoded, 30, '/'); // 30 days, path = /\n        console.log('Cookie Set:', this.cookieService.get('authData'));\n      }\n      if (response.userCategoryId != null && response.userCategoryId.routerPath != null) {\n        this.router.navigate(['/' + response.userCategoryId.routerPath]);\n      }\n    }, error => {\n      // Handle login failure\n      this.userService.setAuthToken(null);\n      // Display a user-friendly error message based on the error response\n      const errorMessage = error.error?.message || 'Login Failed: Invalid username or password.';\n      // Use SweetAlert instead of alert\n      Swal.fire({\n        icon: 'error',\n        title: 'Login Failed',\n        text: errorMessage,\n        confirmButtonText: 'OK'\n      });\n      console.log('Login Failed', error); // Log the full error details for further debugging\n    });\n  }\n\n  logout() {\n    localStorage.removeItem('authToken');\n    this.cookieService.delete('authData', '/');\n    // Reset Remember Me checkbox\n    this.remember = false;\n    localStorage.clear();\n    this.router.navigate(['/home-page']);\n  }\n  navigateToUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  static #_ = this.ɵfac = function UserLoginComponent_Factory(t) {\n    return new (t || UserLoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.CookieService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserLoginComponent,\n    selectors: [[\"app-user-login\"]],\n    decls: 50,\n    vars: 14,\n    consts: [[1, \"container-fluid\", \"h-100\", \"custom-background\"], [1, \"row\", 2, \"height\", \"inherit\"], [1, \"col-12\", 2, \"height\", \"80px !important\"], [\"loggedUser\", \"Hello\"], [1, \"col-12\", 2, \"height\", \"calc(100% - 80px) !important\"], [1, \"container\", \"h-100\"], [1, \"row\", \"justify-content-center\", \"align-content-center\", 2, \"height\", \"100% !important\"], [1, \"col-12\", \"col-md-10\", \"col-lg-8\", \"col-xl-6\", \"px-2\", \"px-lg-5\"], [\"id\", \"login-panel\", 1, \"card\", \"border-orange\", \"p-4\", \"p-md-5\"], [1, \"card-body\", \"py-3\"], [1, \"text-start\", \"fs-4\", \"login-header\", \"text-center\"], [1, \"text-black-50\", \"login-sub-header\", \"mt-1\", \"text-center\"], [1, \"mt-4\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\", \"pt-2\"], [\"for\", \"username\", 1, \"input-label\"], [\"type\", \"text\", \"id\", \"username\", \"name\", \"username\", \"required\", \"\", \"minlength\", \"3\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"usernameField\", \"ngModel\"], [\"class\", \"text-danger input-label px-1 mt-1\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"input-label\"], [1, \"form-group\", \"position-relative\", 2, \"position\", \"relative\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"4\", 1, \"form-control\", 2, \"padding-right\", \"40px\", 3, \"ngModel\", \"ngModelChange\"], [\"passwordField\", \"ngModel\"], [\"type\", \"button\", \"id\", \"show-password\", 1, \"show-password\", 2, \"position\", \"absolute\", \"right\", \"15px\", \"top\", \"24px\", \"padding\", \"0\", 3, \"click\"], [3, \"ngClass\"], [\"class\", \"text-danger input-label mt-1 px-1\", 4, \"ngIf\"], [1, \"form-group\", \"d-grid\", \"d-sm-flex\", \"justify-content-sm-between\", \"mt-4\", \"px-1\", \"pt-3\", 2, \"display\", \"flex\", \"flex-direction\", \"row\", \"width\", \"100%\"], [1, \"form-check\", 2, \"display\", \"flex\", \"flex-direction\", \"row\", \"align-items\", \"center\", 3, \"ngStyle\"], [\"type\", \"checkbox\", \"id\", \"remember\", \"name\", \"remember\", 1, \"form-check-input\", \"custom-check-box\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"remember\", 1, \"form-check-label\"], [1, \"d-grid\"], [1, \"forgot-password\", \"w-100\", \"text-end\", \"text-sm-start\", 3, \"routerLink\"], [\"buttonUI\", \"primary\", \"buttonText\", \"Login\", \"buttonType\", \"submit\", 1, \"d-grid\", 3, \"buttonHeight\", \"disabled\"], [1, \"text-center\", \"mt-3\"], [2, \"font-size\", \"14px\"], [\"id\", \"register-link\", 3, \"click\"], [1, \"text-danger\", \"input-label\", \"px-1\", \"mt-1\"], [4, \"ngIf\"], [1, \"text-danger\", \"input-label\", \"mt-1\", \"px-1\"]],\n    template: function UserLoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-default-navbar\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"h3\", 10);\n        i0.ɵɵtext(11, \"Login\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\", 11);\n        i0.ɵɵtext(13, \"Enter the \");\n        i0.ɵɵelementStart(14, \"Strong\");\n        i0.ɵɵtext(15, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(16, \" & \");\n        i0.ɵɵelementStart(17, \"strong\");\n        i0.ɵɵtext(18, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(19, \" Here\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"form\", 12, 13);\n        i0.ɵɵlistener(\"ngSubmit\", function UserLoginComponent_Template_form_ngSubmit_20_listener() {\n          return ctx.onSubmitLogin();\n        });\n        i0.ɵɵelementStart(22, \"div\", 14)(23, \"label\", 15);\n        i0.ɵɵtext(24, \"Email address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"input\", 16, 17);\n        i0.ɵɵlistener(\"ngModelChange\", function UserLoginComponent_Template_input_ngModelChange_25_listener($event) {\n          return ctx.user.username = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(27, UserLoginComponent_div_27_Template, 3, 2, \"div\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"label\", 19);\n        i0.ɵɵtext(29, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 20)(31, \"input\", 21, 22);\n        i0.ɵɵlistener(\"ngModelChange\", function UserLoginComponent_Template_input_ngModelChange_31_listener($event) {\n          return ctx.user.password = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function UserLoginComponent_Template_button_click_33_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelement(34, \"i\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(35, UserLoginComponent_div_35_Template, 2, 1, \"div\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 26)(37, \"div\", 27)(38, \"input\", 28);\n        i0.ɵɵlistener(\"ngModelChange\", function UserLoginComponent_Template_input_ngModelChange_38_listener($event) {\n          return ctx.remember = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"label\", 29);\n        i0.ɵɵtext(40, \"\\u00A0Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(41, \"div\", 30)(42, \"a\", 31);\n        i0.ɵɵtext(43, \"Forgot Password?\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(44, \"app-primary-action-button\", 32);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"div\", 33)(46, \"p\", 34);\n        i0.ɵɵtext(47, \" Not Registered? \");\n        i0.ɵɵelementStart(48, \"a\", 35);\n        i0.ɵɵlistener(\"click\", function UserLoginComponent_Template_a_click_48_listener() {\n          return ctx.navigateToUserSelection();\n        });\n        i0.ɵɵtext(49, \"Register\");\n        i0.ɵɵelementEnd()()()()()()()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(21);\n        const _r1 = i0.ɵɵreference(26);\n        const _r3 = i0.ɵɵreference(32);\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"ngModel\", ctx.user.username);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", _r1.invalid && (_r1.dirty || _r1.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", ctx.passwordVisible ? \"bi bi-eye-fill\" : \"bi bi-eye-slash-fill\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", _r3.invalid && (_r3.dirty || _r3.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(10, _c0, _r1.invalid || _r3.invalid ? 0.5 : 1, _r1.invalid || _r3.invalid ? \"none\" : \"auto\"));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngModel\", ctx.remember);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c1));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"buttonHeight\", 45)(\"disabled\", _r0.invalid);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgIf, i4.NgStyle, i1.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.NgModel, i5.NgForm, i6.DefaultNavbarComponent, i7.PrimaryActionButtonComponent],\n    styles: [\"h1[_ngcontent-%COMP%], h2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%], p[_ngcontent-%COMP%]{\\n  padding: 0;\\n  margin: 0;\\n}\\nbody[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  background: linear-gradient(to top, #c9c9ff 50%, #9090fa 90%) no-repeat;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin: 0;\\n}\\n\\n#background[_ngcontent-%COMP%]{\\n  background-image: url(\\\"/assets/images/background.png\\\");\\n  background-size: cover;\\n  background-position: center;\\n    background-color: #f9f9f9;\\n    min-height: 90vh;\\n    padding: 30px;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n    justify-content: flex-start;\\n}\\n\\n.custom-background[_ngcontent-%COMP%]{\\n  background: url(\\\"/assets/images/background.png\\\") center center no-repeat;\\n  background-size: cover;\\n\\n}\\n\\n#login-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 90%;\\n  padding-top: 5%;\\n  margin: 0 auto;\\n}\\n\\n#login-panel[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 2px solid #fb751e;\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #ff5722;\\n}\\n.login-sub-header[_ngcontent-%COMP%]{\\n  font-size:13px;\\n}\\n\\n.login-sub-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{\\n  font-weight: 600;\\n}\\n\\n.input-label[_ngcontent-%COMP%]{\\n  font-size: 14px; font-weight: 500;\\n  color: rgb(100,100,100);\\n  margin-bottom: 5px;\\n}\\n\\ninput[_ngcontent-%COMP%]{\\n  box-shadow: none !important;\\n  outline: none;\\n}\\ninput[_ngcontent-%COMP%]:not(:disabled):focus, input[_ngcontent-%COMP%]:not(:disabled):hover{\\n  border-color: #fb751e !important;\\n}\\n\\n.custom-check-box[_ngcontent-%COMP%]{\\n  appearance: none;\\n  height: 12px;\\n  width: 12px;\\n  border-radius: 2px;\\n  border: 1px solid rgb(200,200,200);\\n  margin-block: auto;\\n}\\n\\n.custom-check-box[_ngcontent-%COMP%]:checked{\\n  background: linear-gradient(to right, #fb751e, #b93426);\\n  border-radius: 3px;\\n  border: none;\\n}\\n\\n#login-panel[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n#login-panel[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 5px;\\n  border: 1px solid #ddd;\\n  padding: 10px;\\n  width: 100%;\\n}\\n\\n#login-panel[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 25px;\\n  width: 100%;\\n}\\n\\n#login-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #fb751e, #b93426);\\n  border: none;\\n  padding: 10px;\\n}\\n\\n.btn-google[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #444;\\n  border: none;\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 10px;\\n}\\n\\n.btn-google[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n#show-password[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 70%;\\n  transform: translateY(-50%);\\n  padding-top: 5px;\\n  padding-right: 10px;\\n  color: #6c757d;\\n}\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  color: #ff5722;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n\\n#forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n#divider[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  position: relative;\\n  font-weight: bold;\\n  color: #aaa;\\n}\\n\\n#divider[_ngcontent-%COMP%]:before, #divider[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 45%;\\n  height: 1px;\\n  background: #ddd;\\n}\\n\\n#divider[_ngcontent-%COMP%]:before {\\n  left: 0;\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  font-size: 14px !important;\\n  font-weight: 400;\\n}\\n\\n\\n.form-check-input[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.show-password[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: none;\\n}\\n\\n#divider[_ngcontent-%COMP%]:after {\\n  right: 0;\\n}\\n\\n#register-link[_ngcontent-%COMP%] {\\n  color: #ff5722;\\n  text-decoration: none;\\n}\\n\\n#register-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.form-check[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-check-label[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n}\\n\\n.text-center[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n@media (max-width: 768px) {\\n  #login-container[_ngcontent-%COMP%] {\\n    padding-top: 10%;\\n  }\\n\\n  #login-panel[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  #login-header[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n\\n@media (min-width: 640px) {\\n  #show-password[_ngcontent-%COMP%] {\\n\\n\\n    top: 70%;\\n    transform: translateY(-50%);\\n    padding-top: 5px;\\n    padding-right: 10px;\\n    color: #6c757d;\\n  }\\n\\n  #forgot-password[_ngcontent-%COMP%] {\\n    color: #ff5722;\\n    text-decoration: none;\\n    font-size: 15px;\\n  }\\n\\n  #login-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 80%;\\n    padding-top: 5%;\\n    margin: 0 auto;\\n  }\\n}\\n\\n@media (min-width: 1024px) {\\n  #login-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 65%;\\n    padding-top: 5%;\\n    margin: 0 auto;\\n  }\\n\\n  #forgot-password[_ngcontent-%COMP%] {\\n    color: #ff5722;\\n    text-decoration: none;\\n    font-size: 15px;\\n  }\\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n\\n\\n  .form-check-input[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n@media (min-width: 768px) {\\n  #show-password[_ngcontent-%COMP%] {\\n\\n    top: 70%;\\n    transform: translateY(-50%);\\n    padding-top: 5px;\\n    padding-right: 10px;\\n    color: #6c757d;\\n  }\\n\\n  #login-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 80%;\\n    padding-top: 5%;\\n    margin: 0 auto;\\n  }\\n\\n  #forgot-password[_ngcontent-%COMP%] {\\n    color: #ff5722;\\n    text-decoration: none;\\n    font-size: 15px;\\n  }\\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n\\n\\n  .form-check-input[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n@media (min-width: 1280px) {\\n  #login-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 65%;\\n    padding-top: 5%;\\n    margin: 0 auto;\\n  }\\n\\n  #forgot-password[_ngcontent-%COMP%] {\\n    color: #ff5722;\\n    text-decoration: none;\\n    font-size: 15px;\\n  }\\n\\n  .form-check-label[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n  }\\n\\n\\n  .form-check-input[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { UserLoginComponent };", "map": {"version": 3, "names": ["User", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "UserLoginComponent_div_27_div_1_Template", "UserLoginComponent_div_27_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "UserLoginComponent_div_35_div_1_Template", "_r3", "UserLoginComponent", "constructor", "router", "userService", "cookieService", "username", "password", "user", "remember", "passwordVisible", "ngOnInit", "encodedData", "get", "decoded", "atob", "token", "routerPath", "split", "console", "log", "navigate", "e", "error", "togglePasswordVisibility", "passwordField", "document", "getElementById", "type", "onSubmitLogin", "localStorage", "clear", "onLogin", "login", "subscribe", "response", "setAuthToken", "setItem", "id", "toString", "firstName", "lastName", "companyId", "table", "combined", "userCategoryId", "encoded", "btoa", "set", "errorMessage", "message", "fire", "icon", "title", "text", "confirmButtonText", "logout", "removeItem", "delete", "navigateToUserSelection", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "UserService", "i3", "CookieService", "_2", "selectors", "decls", "vars", "consts", "template", "UserLoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "UserLoginComponent_Template_form_ngSubmit_20_listener", "UserLoginComponent_Template_input_ngModelChange_25_listener", "$event", "UserLoginComponent_div_27_Template", "UserLoginComponent_Template_input_ngModelChange_31_listener", "UserLoginComponent_Template_button_click_33_listener", "UserLoginComponent_div_35_Template", "UserLoginComponent_Template_input_ngModelChange_38_listener", "UserLoginComponent_Template_a_click_48_listener", "invalid", "dirty", "touched", "ɵɵpureFunction2", "_c0", "ɵɵpureFunction0", "_c1", "_r0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-login\\user-login.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user-login\\user-login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from '../user.service';\r\nimport { User } from '../user';\r\nimport Swal from 'sweetalert2'; // Import SweetAlert\r\nimport { CookieService } from 'ngx-cookie-service';\r\n\r\n@Component({\r\n  selector: 'app-user-login',\r\n  templateUrl: './user-login.component.html',\r\n  styleUrls: ['./user-login.component.css'],\r\n})\r\nexport class UserLoginComponent {\r\n  username: string = '';\r\n  password: string = '';\r\n  user: User = new User();\r\n  remember: boolean = false;\r\n  passwordVisible: boolean = false;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private cookieService: CookieService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const encodedData = this.cookieService.get('authData');\r\n\r\n    if (encodedData) {\r\n      try {\r\n        const decoded = atob(encodedData);\r\n        const [token, username, routerPath] = decoded.split('::');\r\nconsole.log('Decoded Cookie Data:', { token, username, routerPath });\r\n        if (username) {\r\n          this.username = username;\r\n          this.remember = true; // checked only if cookie exists\r\n        }\r\n\r\n        if (token && routerPath) {\r\n          this.router.navigate(['/' + routerPath]);\r\n        }\r\n      } catch (e) {\r\n        console.error('Invalid cookie format');\r\n        this.remember = false; // reset if cookie invalid\r\n      }\r\n    } else {\r\n      this.remember = false; // no cookie → checkbox unchecked\r\n    }\r\n  }\r\n\r\n  togglePasswordVisibility() {\r\n    this.passwordVisible = !this.passwordVisible;\r\n    const passwordField = document.getElementById(\r\n      'password'\r\n    ) as HTMLInputElement;\r\n    passwordField.type = this.passwordVisible ? 'text' : 'password';\r\n  }\r\n\r\n  onSubmitLogin() {\r\n    localStorage.clear();\r\n    this.onLogin();\r\n  }\r\n\r\n  onLogin() {\r\n    this.userService.login(this.user).subscribe(\r\n      (response) => {\r\n        console.log('Login Successful', response); // Log the successful response\r\n        // Set the auth token after successful login\r\n        this.userService.setAuthToken(response.token);\r\n\r\n        // Log the complete response to check for clinicId\r\n        console.log('API Response:', response.username);\r\n\r\n        // Store the full user object and other relevant data\r\n        localStorage.setItem('userid', response.id.toString());\r\n        localStorage.setItem('firstName', response.firstName || '');\r\n        localStorage.setItem('lastName', response.lastName || '');\r\n        localStorage.setItem('companyId', response.companyId || '');\r\n        localStorage.setItem('username', response.username || '');\r\n        console.table(response);\r\n\r\n        // ✅ Combine token, username, and routerPath into one string\r\n        const combined = `${response.token}::${response.username}::${\r\n          response.userCategoryId?.routerPath || ''\r\n        }`;\r\n\r\n        // ✅ Encode it using Base64 to hide content\r\n        const encoded = btoa(combined);\r\n\r\n        // ✅ Save cookie (persistent if Remember Me checked)\r\n        if (this.remember) {\r\n          this.cookieService.set('authData', encoded, 30, '/'); // 30 days, path = /\r\n          console.log('Cookie Set:', this.cookieService.get('authData'));\r\n        }\r\n\r\n        if (\r\n          response.userCategoryId != null &&\r\n          response.userCategoryId.routerPath != null\r\n        ) {\r\n          this.router.navigate(['/' + response.userCategoryId.routerPath]);\r\n        }\r\n      },\r\n      (error) => {\r\n        // Handle login failure\r\n        this.userService.setAuthToken(null);\r\n\r\n        // Display a user-friendly error message based on the error response\r\n        const errorMessage =\r\n          error.error?.message || 'Login Failed: Invalid username or password.';\r\n\r\n        // Use SweetAlert instead of alert\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Login Failed',\r\n          text: errorMessage,\r\n          confirmButtonText: 'OK',\r\n        });\r\n\r\n        console.log('Login Failed', error); // Log the full error details for further debugging\r\n      }\r\n    );\r\n  }\r\n\r\n  public logout(): void {\r\n    localStorage.removeItem('authToken');\r\n    this.cookieService.delete('authData', '/');\r\n    \r\n    // Reset Remember Me checkbox\r\n    this.remember = false;\r\n\r\n    localStorage.clear();\r\n    this.router.navigate(['/home-page']);\r\n  }\r\n\r\n  navigateToUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n", "<div class=\"container-fluid h-100 custom-background\" >\r\n  <div class=\"row\" style=\"height: inherit;\">\r\n    <div class=\"col-12\" style=\"height: 80px !important;\">\r\n      <app-default-navbar loggedUser=\"Hello\" />\r\n    </div>\r\n    <div class=\"col-12\" style=\"height: calc(100% - 80px) !important;\">\r\n      <div class=\"container h-100\">\r\n        <div class=\"row justify-content-center align-content-center\" style=\"height: 100% !important;\">\r\n          <div class=\"col-12 col-md-10 col-lg-8 col-xl-6 px-2 px-lg-5\">\r\n            <div id=\"login-panel\" class=\"card border-orange p-4 p-md-5\">\r\n              <div class=\"card-body py-3\">\r\n                <h3 class=\"text-start fs-4 login-header text-center\" >Login</h3>\r\n                <p class=\"text-black-50 login-sub-header mt-1 text-center\">Enter the <Strong>Username</Strong> & <strong>Password</strong> Here</p>\r\n                <form class=\"mt-4\" (ngSubmit)=\"onSubmitLogin()\" #loginForm=\"ngForm\">\r\n                  <div class=\"form-group pt-2\">\r\n                    <label for=\"username\" class=\"input-label\">Email address</label>\r\n                    <input type=\"text\" id=\"username\" name=\"username\" [(ngModel)]=\"user.username\" class=\"form-control\" required\r\n                      minlength=\"3\" #usernameField=\"ngModel\" />\r\n                    <div *ngIf=\"\r\n                        usernameField.invalid &&\r\n                        (usernameField.dirty || usernameField.touched)\r\n                      \" class=\"text-danger input-label px-1 mt-1\">\r\n                      <div *ngIf=\"usernameField.errors?.['required']\">\r\n                        Email is required.\r\n                      </div>\r\n                      <div *ngIf=\"usernameField.errors?.['minlength']\">\r\n                        Email must be valid one.\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <label class=\"input-label\" for=\"password\">Password</label>\r\n                  <div class=\"form-group position-relative\" style=\"position: relative;\">\r\n                    <input type=\"password\" id=\"password\" name=\"password\" [(ngModel)]=\"user.password\" class=\"form-control\"\r\n                      required minlength=\"4\" #passwordField=\"ngModel\" style=\"padding-right: 40px;\" />\r\n                    <button type=\"button\" id=\"show-password\" class=\"show-password\" (click)=\"togglePasswordVisibility()\" style=\"\r\n                        position: absolute;\r\n                        right: 15px;\r\n                        top: 24px;\r\n                        padding: 0;\r\n                      \">\r\n                      <i [ngClass]=\"passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'\"></i>\r\n                    </button>\r\n                    <div *ngIf=\"\r\n                        passwordField.invalid && (passwordField.dirty || passwordField.touched)\r\n                      \" class=\"text-danger input-label mt-1 px-1\">\r\n                      <div *ngIf=\"passwordField.errors?.['required']\">\r\n                        Password is required.\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"form-group d-grid d-sm-flex justify-content-sm-between mt-4 px-1 pt-3\"\r\n                    style=\"display: flex; flex-direction: row; width: 100%;\">\r\n                    <div\r\n                    class=\"form-check\"\r\n                    style=\"display: flex; flex-direction: row; align-items: center;\"\r\n                    [ngStyle]=\"{ 'opacity': (usernameField.invalid || passwordField.invalid) ? 0.5 : 1, 'pointer-events': (usernameField.invalid || passwordField.invalid) ? 'none' : 'auto' }\"\r\n                  >\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      class=\"form-check-input custom-check-box\"\r\n                      id=\"remember\"\r\n                      [(ngModel)]=\"remember\"\r\n                      name=\"remember\"\r\n                    />\r\n                    <label class=\"form-check-label\" for=\"remember\">&nbsp;Remember me</label>\r\n                    </div>\r\n                    <div class=\"d-grid\">\r\n                      <a class=\"forgot-password w-100 text-end text-sm-start\" [routerLink]=\"['/user/forget-password']\" >Forgot Password?</a>\r\n                    </div>\r\n                  </div>\r\n                  <app-primary-action-button buttonUI=\"primary\" [buttonHeight]=45 class=\"d-grid\" buttonText=\"Login\" buttonType=\"submit\" [disabled]=\"loginForm.invalid\"/>\r\n                </form>\r\n                <div class=\"text-center mt-3\">\r\n                  <p style=\"font-size: 14px;\">\r\n                    Not Registered?\r\n                    <a id=\"register-link\" (click)=\"navigateToUserSelection()\">Register</a>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,SAAS;AAC9B,OAAOC,IAAI,MAAM,aAAa,CAAC,CAAC;;;;;;;;;;;ICkBVC,EAAA,CAAAC,cAAA,UAAgD;IAC9CD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAiD;IAC/CD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IATRH,EAAA,CAAAC,cAAA,cAG8C;IAC5CD,EAAA,CAAAI,UAAA,IAAAC,wCAAA,kBAEM;IACNL,EAAA,CAAAI,UAAA,IAAAE,wCAAA,kBAEM;IACRN,EAAA,CAAAG,YAAA,EAAM;;;;;IANEH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAwC;IAGxCV,EAAA,CAAAO,SAAA,GAAyC;IAAzCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,cAAyC;;;;;IAoB/CV,EAAA,CAAAC,cAAA,UAAgD;IAC9CD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAE8C;IAC5CD,EAAA,CAAAI,UAAA,IAAAO,wCAAA,kBAEM;IACRX,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,SAAAI,GAAA,CAAAF,MAAA,kBAAAE,GAAA,CAAAF,MAAA,aAAwC;;;;;;;;;;;;ADtCpE,MAKaG,kBAAkB;EAO7BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,aAA4B;IAF5B,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IATvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,IAAI,GAAS,IAAItB,IAAI,EAAE;IACvB,KAAAuB,QAAQ,GAAY,KAAK;IACzB,KAAAC,eAAe,GAAY,KAAK;EAM7B;EAEHC,QAAQA,CAAA;IACN,MAAMC,WAAW,GAAG,IAAI,CAACP,aAAa,CAACQ,GAAG,CAAC,UAAU,CAAC;IAEtD,IAAID,WAAW,EAAE;MACf,IAAI;QACF,MAAME,OAAO,GAAGC,IAAI,CAACH,WAAW,CAAC;QACjC,MAAM,CAACI,KAAK,EAAEV,QAAQ,EAAEW,UAAU,CAAC,GAAGH,OAAO,CAACI,KAAK,CAAC,IAAI,CAAC;QACjEC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAAEJ,KAAK;UAAEV,QAAQ;UAAEW;QAAU,CAAE,CAAC;QAC5D,IAAIX,QAAQ,EAAE;UACZ,IAAI,CAACA,QAAQ,GAAGA,QAAQ;UACxB,IAAI,CAACG,QAAQ,GAAG,IAAI,CAAC,CAAC;;;QAGxB,IAAIO,KAAK,IAAIC,UAAU,EAAE;UACvB,IAAI,CAACd,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAGJ,UAAU,CAAC,CAAC;;OAE3C,CAAC,OAAOK,CAAC,EAAE;QACVH,OAAO,CAACI,KAAK,CAAC,uBAAuB,CAAC;QACtC,IAAI,CAACd,QAAQ,GAAG,KAAK,CAAC,CAAC;;KAE1B,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,KAAK,CAAC,CAAC;;EAE3B;;EAEAe,wBAAwBA,CAAA;IACtB,IAAI,CAACd,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,MAAMe,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAC3C,UAAU,CACS;IACrBF,aAAa,CAACG,IAAI,GAAG,IAAI,CAAClB,eAAe,GAAG,MAAM,GAAG,UAAU;EACjE;EAEAmB,aAAaA,CAAA;IACXC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAACC,OAAO,EAAE;EAChB;EAEAA,OAAOA,CAAA;IACL,IAAI,CAAC5B,WAAW,CAAC6B,KAAK,CAAC,IAAI,CAACzB,IAAI,CAAC,CAAC0B,SAAS,CACxCC,QAAQ,IAAI;MACXhB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEe,QAAQ,CAAC,CAAC,CAAC;MAC3C;MACA,IAAI,CAAC/B,WAAW,CAACgC,YAAY,CAACD,QAAQ,CAACnB,KAAK,CAAC;MAE7C;MACAG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEe,QAAQ,CAAC7B,QAAQ,CAAC;MAE/C;MACAwB,YAAY,CAACO,OAAO,CAAC,QAAQ,EAAEF,QAAQ,CAACG,EAAE,CAACC,QAAQ,EAAE,CAAC;MACtDT,YAAY,CAACO,OAAO,CAAC,WAAW,EAAEF,QAAQ,CAACK,SAAS,IAAI,EAAE,CAAC;MAC3DV,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACM,QAAQ,IAAI,EAAE,CAAC;MACzDX,YAAY,CAACO,OAAO,CAAC,WAAW,EAAEF,QAAQ,CAACO,SAAS,IAAI,EAAE,CAAC;MAC3DZ,YAAY,CAACO,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAAC7B,QAAQ,IAAI,EAAE,CAAC;MACzDa,OAAO,CAACwB,KAAK,CAACR,QAAQ,CAAC;MAEvB;MACA,MAAMS,QAAQ,GAAG,GAAGT,QAAQ,CAACnB,KAAK,KAAKmB,QAAQ,CAAC7B,QAAQ,KACtD6B,QAAQ,CAACU,cAAc,EAAE5B,UAAU,IAAI,EACzC,EAAE;MAEF;MACA,MAAM6B,OAAO,GAAGC,IAAI,CAACH,QAAQ,CAAC;MAE9B;MACA,IAAI,IAAI,CAACnC,QAAQ,EAAE;QACjB,IAAI,CAACJ,aAAa,CAAC2C,GAAG,CAAC,UAAU,EAAEF,OAAO,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACtD3B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACf,aAAa,CAACQ,GAAG,CAAC,UAAU,CAAC,CAAC;;MAGhE,IACEsB,QAAQ,CAACU,cAAc,IAAI,IAAI,IAC/BV,QAAQ,CAACU,cAAc,CAAC5B,UAAU,IAAI,IAAI,EAC1C;QACA,IAAI,CAACd,MAAM,CAACkB,QAAQ,CAAC,CAAC,GAAG,GAAGc,QAAQ,CAACU,cAAc,CAAC5B,UAAU,CAAC,CAAC;;IAEpE,CAAC,EACAM,KAAK,IAAI;MACR;MACA,IAAI,CAACnB,WAAW,CAACgC,YAAY,CAAC,IAAI,CAAC;MAEnC;MACA,MAAMa,YAAY,GAChB1B,KAAK,CAACA,KAAK,EAAE2B,OAAO,IAAI,6CAA6C;MAEvE;MACA/D,IAAI,CAACgE,IAAI,CAAC;QACRC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAEL,YAAY;QAClBM,iBAAiB,EAAE;OACpB,CAAC;MAEFpC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC,CACF;EACH;;EAEOiC,MAAMA,CAAA;IACX1B,YAAY,CAAC2B,UAAU,CAAC,WAAW,CAAC;IACpC,IAAI,CAACpD,aAAa,CAACqD,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC;IAE1C;IACA,IAAI,CAACjD,QAAQ,GAAG,KAAK;IAErBqB,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC5B,MAAM,CAACkB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEAsC,uBAAuBA,CAAA;IACrB,IAAI,CAACxD,MAAM,CAACkB,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAAC,QAAAuC,CAAA,G;qBA5HU3D,kBAAkB,EAAAb,EAAA,CAAAyE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3E,EAAA,CAAAyE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7E,EAAA,CAAAyE,iBAAA,CAAAK,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBnE,kBAAkB;IAAAoE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ/BvF,EAAA,CAAAC,cAAA,aAAsD;QAGhDD,EAAA,CAAAyF,SAAA,4BAAyC;QAC3CzF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAkE;QAMAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChEH,EAAA,CAAAC,cAAA,aAA2D;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,WAAE;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,aAAI;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACnIH,EAAA,CAAAC,cAAA,oBAAoE;QAAjDD,EAAA,CAAA0F,UAAA,sBAAAC,sDAAA;UAAA,OAAYH,GAAA,CAAA/C,aAAA,EAAe;QAAA,EAAC;QAC7CzC,EAAA,CAAAC,cAAA,eAA6B;QACeD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC/DH,EAAA,CAAAC,cAAA,qBAC2C;QADMD,EAAA,CAAA0F,UAAA,2BAAAE,4DAAAC,MAAA;UAAA,OAAAL,GAAA,CAAApE,IAAA,CAAAF,QAAA,GAAA2E,MAAA;QAAA,EAA2B;QAA5E7F,EAAA,CAAAG,YAAA,EAC2C;QAC3CH,EAAA,CAAAI,UAAA,KAAA0F,kCAAA,kBAUM;QACR9F,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAA0C;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC1DH,EAAA,CAAAC,cAAA,eAAsE;QACfD,EAAA,CAAA0F,UAAA,2BAAAK,4DAAAF,MAAA;UAAA,OAAAL,GAAA,CAAApE,IAAA,CAAAD,QAAA,GAAA0E,MAAA;QAAA,EAA2B;QAAhF7F,EAAA,CAAAG,YAAA,EACiF;QACjFH,EAAA,CAAAC,cAAA,kBAKI;QAL2DD,EAAA,CAAA0F,UAAA,mBAAAM,qDAAA;UAAA,OAASR,GAAA,CAAApD,wBAAA,EAA0B;QAAA,EAAC;QAMjGpC,EAAA,CAAAyF,SAAA,aAA+E;QACjFzF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAI,UAAA,KAAA6F,kCAAA,kBAMM;QACRjG,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAC2D;QAUvDD,EAAA,CAAA0F,UAAA,2BAAAQ,4DAAAL,MAAA;UAAA,OAAAL,GAAA,CAAAnE,QAAA,GAAAwE,MAAA;QAAA,EAAsB;QAJxB7F,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAExEH,EAAA,CAAAC,cAAA,eAAoB;QACgFD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG1HH,EAAA,CAAAyF,SAAA,qCAAsJ;QACxJzF,EAAA,CAAAG,YAAA,EAAO;QACPH,EAAA,CAAAC,cAAA,eAA8B;QAE1BD,EAAA,CAAAE,MAAA,yBACA;QAAAF,EAAA,CAAAC,cAAA,aAA0D;QAApCD,EAAA,CAAA0F,UAAA,mBAAAS,gDAAA;UAAA,OAASX,GAAA,CAAAjB,uBAAA,EAAyB;QAAA,EAAC;QAACvE,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;QA5DrBH,EAAA,CAAAO,SAAA,IAA2B;QAA3BP,EAAA,CAAAQ,UAAA,YAAAgF,GAAA,CAAApE,IAAA,CAAAF,QAAA,CAA2B;QAEtElB,EAAA,CAAAO,SAAA,GAGJ;QAHIP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAA2F,OAAA,KAAA3F,GAAA,CAAA4F,KAAA,IAAA5F,GAAA,CAAA6F,OAAA,EAGJ;QAWmDtG,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAQ,UAAA,YAAAgF,GAAA,CAAApE,IAAA,CAAAD,QAAA,CAA2B;QAQ3EnB,EAAA,CAAAO,SAAA,GAAuE;QAAvEP,EAAA,CAAAQ,UAAA,YAAAgF,GAAA,CAAAlE,eAAA,6CAAuE;QAEtEtB,EAAA,CAAAO,SAAA,GAEH;QAFGP,EAAA,CAAAQ,UAAA,SAAAI,GAAA,CAAAwF,OAAA,KAAAxF,GAAA,CAAAyF,KAAA,IAAAzF,GAAA,CAAA0F,OAAA,EAEH;QAYHtG,EAAA,CAAAO,SAAA,GAA2K;QAA3KP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAuG,eAAA,KAAAC,GAAA,EAAA/F,GAAA,CAAA2F,OAAA,IAAAxF,GAAA,CAAAwF,OAAA,YAAA3F,GAAA,CAAA2F,OAAA,IAAAxF,GAAA,CAAAwF,OAAA,oBAA2K;QAMzKpG,EAAA,CAAAO,SAAA,GAAsB;QAAtBP,EAAA,CAAAQ,UAAA,YAAAgF,GAAA,CAAAnE,QAAA,CAAsB;QAMkCrB,EAAA,CAAAO,SAAA,GAAwC;QAAxCP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAAyG,eAAA,KAAAC,GAAA,EAAwC;QAGtD1G,EAAA,CAAAO,SAAA,GAAiB;QAAjBP,EAAA,CAAAQ,UAAA,oBAAiB,aAAAmG,GAAA,CAAAP,OAAA;;;;;;;SD3DpEvF,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}