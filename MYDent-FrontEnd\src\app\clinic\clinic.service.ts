import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Clinic, ClinicLaboratoryOrder, ClinicServices, InventoryOrderRequestDto } from './clinic';
import { HttpService } from '../http.service';
import { Appointments } from '../modules/appointments/appointments';

@Injectable({
  providedIn: 'root'
})

export class ClinicService {

  constructor(private httpService:HttpService) {}

  //Clinic APIs

  saveClinic(clinic: Clinic): Observable<any> {
    return this.httpService.request('POST', '/saveClinic', clinic);
  }

  getClinicList(): Observable<Clinic[]> {
    return this.httpService.request('GET', '/clinicList', {});
  }

  getClinicById(id: number): Observable<Clinic> {
    return this.httpService.request('GET', `/getClinicById/${id}`, {});
  }

  updateClinic(id: number, clinic: Clinic): Observable<object> {
    return this.httpService.request('PUT', `/updateClinic/${id}`, clinic);
  }

  deleteClinic(id: number): Observable<any> {
    return this.httpService.request('DELETE', `/deleteClinic/${id}`, {});
  }

  checkClinicName(clinicName: string): Observable<any> {
    const params = { clinicName };
    return this.httpService.request('GET', `/check-clinicName`, null, params);
  }

  // Clinic Services APIs

  saveClinicServicesCategory(clinicService: ClinicServices): Observable<any> {
    return this.httpService.request('POST', '/saveClinicServicesCategory', clinicService);
  }

  getAllClinicServicesCategory(): Observable< ClinicServices[]> {
    return this.httpService.request('GET', '/AllClinicServicesCategoryList', {});
  }

  getLaboratorySetupById(id: number): Observable< ClinicServices> {
    return this.httpService.request('GET', `/getClinicServicesCategoryById/${id}`, {});
  }


  // Clinic - Appoinemnt APIs

  getPendingAppointmentList() : Observable<any> {
    const userId = localStorage.getItem('userid');
    if (userId != null) {
      return this.httpService.request('GET',`/getPendingAppointmentList/${userId}`, null);
    }else{
      return of(null);
    }
  }



  // Clinic Laboratory Order APIs

  saveLaboratoryOrder(orderData: ClinicLaboratoryOrder, userId: number): Observable<ClinicLaboratoryOrder> {
    return this.httpService.request('POST', `/saveLaboratoryOrder/${userId}`, orderData);
  }

  getAllLaboratoryOrders(): Observable<ClinicLaboratoryOrder[]> {
    return this.httpService.request('GET', '/LaboratoryOrderlist', {});
  }

  getLaboratoryOrderById(id: number): Observable<ClinicLaboratoryOrder> {
    return this.httpService.request('GET', `/getLaboratoryOrderById/${id}`, {});
  }

  updateLaboratoryOrder(id: number, clinicLaboratoryOrder: ClinicLaboratoryOrder): Observable<ClinicLaboratoryOrder> {
    return this.httpService.request('PUT', `/updateLaboratoryOrder/${id}`, clinicLaboratoryOrder);
  }

  deleteLaboratoryOrder(id: number): Observable<void> {
    return this.httpService.request('DELETE', `/deleteLaboratoryOrder/${id}`, {});
  }

  getLaboratoryOrderByClinicId(id: number): Observable<ClinicLaboratoryOrder[]> {
    return this.httpService.request('GET', `/getLaboratoryOrdersByClinicId/${id}`, {});
  }


  // Clinic-Supplier Order APIs
  getSupplierItemCategories(){
    return this.httpService.request('GET','/getSupplierItemCategoryList',null)
  }

  getSupplierListByCategoryName(categoryName:String){
    return this.httpService.request('GET',`/getSuppliersByItemCategoryName/${categoryName}`,null)
  }

  getSupplierItemListByCategoryNameAndSupplierId(categoryName:String,supplierId:number){
    const params = {categoryName,supplierId}
    return this.httpService.request('GET',`/getSuppliersByItemCategoryNameAndSupplierId`,null,params)
  }

  getSupplierItemsBySupplierId(supplierId:number){
    return this.httpService.request('GET',`/getSupplierItemsBySupplierId/${supplierId}`,null)
  }

  saveInventoryOrderRequest(inventoryOrderRequestDto:InventoryOrderRequestDto){
    return this.httpService.request('POST',`/saveInventoryOrderRequest`,inventoryOrderRequestDto)
  }

  // Supplier-Clinic APIs
  getOrderRequestByClinicUserId(clinicId:number) {
    return this.httpService.request('GET',`/getClinicOrdersHeadersFromClinicUserId/${clinicId}`,null)
  }

  getOrderDetailsByClinicUserId(headerId:number) {
    return this.httpService.request('GET',`/getClinicOrdersDetailsFromSupplierId/${headerId}`,null)
  }
  // Fetch appointments based on clinicId
  getAppointmentsByClinicId(clinicId: number): Observable<Appointments[]> {
    return this.httpService.request('GET', `/clinic/${clinicId}`, {}); // Adjust the endpoint as needed
  }

   // Update appointment status
   updateAppointmentStatus(id: number, status: String): Observable<any> {
    return this.httpService.request('PUT', `/updateAppointmentStatus/${id}`,  status );
  }

  // getAllAppointmentToday(clinicId:number) {
  //   return this.httpService.request('GET',`/getAppoinmentByClinicTodayAll/${clinicId}`,null)
  // }

}
