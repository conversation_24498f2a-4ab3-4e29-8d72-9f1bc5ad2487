package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.UserTemp;
import com.navitsa.mydent.enums.UserTempStatus;
import com.navitsa.mydent.enums.UserTempType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserTempRepository extends JpaRepository<UserTemp,Long> {

    Optional<UserTemp> findByUserEmail(String email);
}
