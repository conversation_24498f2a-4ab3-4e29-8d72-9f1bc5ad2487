{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomerDashboardComponent } from './customer-dashboard/customer-dashboard.component';\nimport { CustomerNewAppoinmentComponent } from './customer-new-appointment/customer-new-appointment.component';\nimport { CustomerLayoutComponent } from './customer-layout/customer-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomerLayoutComponent,\n  children: [{\n    path: 'dashboard',\n    component: CustomerDashboardComponent\n  }, {\n    path: 'add-appointment',\n    component: CustomerNewAppoinmentComponent\n  }]\n}];\nclass CustomerRoutingModule {\n  static #_ = this.ɵfac = function CustomerRoutingModule_Factory(t) {\n    return new (t || CustomerRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: CustomerRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\nexport { CustomerRoutingModule };\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomerDashboardComponent", "CustomerNewAppoinmentComponent", "CustomerLayoutComponent", "routes", "path", "component", "children", "CustomerRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\customer\\customer-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CustomerDashboardComponent } from './customer-dashboard/customer-dashboard.component';\r\nimport { CustomerNewAppoinmentComponent } from './customer-new-appointment/customer-new-appointment.component';\r\nimport { CustomerLayoutComponent } from './customer-layout/customer-layout.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path:'',\r\n    component: CustomerLayoutComponent,\r\n    children:[\r\n      {path:'dashboard', component: CustomerDashboardComponent },\r\n      {path:'add-appointment', component: CustomerNewAppoinmentComponent }\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class CustomerRoutingModule {\r\n\r\n}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,8BAA8B,QAAQ,+DAA+D;AAC9G,SAASC,uBAAuB,QAAQ,6CAA6C;;;AAErF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAC,EAAE;EACPC,SAAS,EAAEH,uBAAuB;EAClCI,QAAQ,EAAC,CACP;IAACF,IAAI,EAAC,WAAW;IAAEC,SAAS,EAAEL;EAA0B,CAAE,EAC1D;IAACI,IAAI,EAAC,iBAAiB;IAAEC,SAAS,EAAEJ;EAA8B,CAAE;CAEvE,CACF;AAED,MAIaM,qBAAqB;EAAA,QAAAC,CAAA,G;qBAArBD,qBAAqB;EAAA;EAAA,QAAAE,EAAA,G;UAArBF;EAAqB;EAAA,QAAAG,EAAA,G;cAHtBX,YAAY,CAACY,QAAQ,CAACR,MAAM,CAAC,EAC7BJ,YAAY;EAAA;;SAEXQ,qBAAqB;;2EAArBA,qBAAqB;IAAAK,OAAA,GAAAC,EAAA,CAAAd,YAAA;IAAAe,OAAA,GAFtBf,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}