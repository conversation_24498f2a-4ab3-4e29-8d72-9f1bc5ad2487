{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../appointments.service\";\nfunction AppointmentBookingComponent_div_16_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"First name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_16_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Only letters and spaces allowed\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AppointmentBookingComponent_div_16_small_1_Template, 2, 0, \"small\", 29);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_div_16_small_2_Template, 2, 0, \"small\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.appointmentForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AppointmentBookingComponent_div_21_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Last name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_21_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Only letters and spaces allowed\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AppointmentBookingComponent_div_21_small_1_Template, 2, 0, \"small\", 29);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_div_21_small_2_Template, 2, 0, \"small\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.appointmentForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AppointmentBookingComponent_div_27_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_27_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AppointmentBookingComponent_div_27_small_1_Template, 2, 0, \"small\", 29);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_div_27_small_2_Template, 2, 0, \"small\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.appointmentForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.appointmentForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction AppointmentBookingComponent_div_32_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Telephone number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_32_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\");\n    i0.ɵɵtext(1, \"Please enter a valid 10-digit phone number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppointmentBookingComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, AppointmentBookingComponent_div_32_small_1_Template, 2, 0, \"small\", 29);\n    i0.ɵɵtemplate(2, AppointmentBookingComponent_div_32_small_2_Template, 2, 0, \"small\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.appointmentForm.get(\"telephone\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.appointmentForm.get(\"telephone\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AppointmentBookingComponent_option_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const service_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", service_r18.clinicServiceCategoryId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", service_r18.serviceName, \" \");\n  }\n}\nfunction AppointmentBookingComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"small\");\n    i0.ɵɵtext(2, \"Please select a service\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"small\");\n    i0.ɵɵtext(2, \"Please select a date\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 31)(2, \"div\", 32)(3, \"h4\");\n    i0.ɵɵtext(4, \"Selected Appointment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Doctor:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Time:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r7.selectedDoctor.title, \" \", ctx_r7.selectedDoctor.firstName, \" \", ctx_r7.selectedDoctor.lastName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.selectedTimeSlot.displayTime, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(17, 5, ctx_r7.selectedDate, \"fullDate\"), \"\");\n  }\n}\nfunction AppointmentBookingComponent_div_56_div_12_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function AppointmentBookingComponent_div_56_div_12_div_4_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r25);\n      const doctor_r23 = restoredCtx.$implicit;\n      const timeSlot_r21 = i0.ɵɵnextContext().$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.selectDoctorAndTimeSlot(doctor_r23, timeSlot_r21));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47)(2, \"h6\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 48);\n    i0.ɵɵtext(7, \"Select\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const doctor_r23 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", doctor_r23.title, \" \", doctor_r23.firstName, \" \", doctor_r23.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(doctor_r23.qualifications);\n  }\n}\nfunction AppointmentBookingComponent_div_56_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 44);\n    i0.ɵɵtemplate(4, AppointmentBookingComponent_div_56_div_12_div_4_Template, 8, 4, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const timeSlot_r21 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(timeSlot_r21.displayTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", timeSlot_r21.availableDoctors);\n  }\n}\nfunction AppointmentBookingComponent_div_56_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"p\");\n    i0.ɵɵtext(2, \"No available time slots for the selected date. Please choose a different date.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppointmentBookingComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 35)(3, \"div\", 36)(4, \"h5\", 37);\n    i0.ɵɵtext(5, \"Select Doctor & Time Slot\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function AppointmentBookingComponent_div_56_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.closeModal());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 40);\n    i0.ɵɵtemplate(12, AppointmentBookingComponent_div_56_div_12_Template, 5, 2, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, AppointmentBookingComponent_div_56_div_13_Template, 3, 0, \"div\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r8.showDoctorModal ? \"block\" : \"none\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"Available appointments for \", i0.ɵɵpipeBind2(10, 5, ctx_r8.selectedDate, \"fullDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.availableTimeSlots);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.availableTimeSlots.length === 0);\n  }\n}\nfunction AppointmentBookingComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 50);\n  }\n}\nclass AppointmentBookingComponent {\n  constructor(fb, router, appointmentsService) {\n    this.fb = fb;\n    this.router = router;\n    this.appointmentsService = appointmentsService;\n    this.showDoctorModal = false;\n    this.selectedDate = '';\n    this.availableTimeSlots = [];\n    this.selectedTimeSlot = null;\n    this.selectedDoctor = null;\n    this.clinics = [];\n    this.services = [];\n    this.appointmentForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\\s]+$/)]],\n      lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\\s]+$/)]],\n      email: ['', [Validators.required, Validators.email]],\n      telephone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      preferredService: ['', Validators.required],\n      preferredDate: ['', Validators.required],\n      fromTime: [''],\n      toTime: [''],\n      doctorName: [''],\n      clinicId: ['']\n    });\n  }\n  ngOnInit() {\n    this.loadServices();\n    this.loadClinics();\n  }\n  loadServices() {\n    this.appointmentsService.getServicesList().subscribe(services => {\n      this.services = services;\n    }, error => {\n      console.error('Error loading services:', error);\n    });\n  }\n  loadClinics() {\n    this.appointmentsService.getClinicsList().subscribe(clinics => {\n      this.clinics = clinics;\n    }, error => {\n      console.error('Error loading clinics:', error);\n    });\n  }\n  onDateChange() {\n    const selectedDate = this.appointmentForm.get('preferredDate')?.value;\n    if (selectedDate) {\n      this.selectedDate = selectedDate;\n      this.checkScheduleAndGenerateTimeSlots();\n    }\n  }\n  checkScheduleAndGenerateTimeSlots() {\n    const preferredService = this.appointmentForm.get('preferredService')?.value;\n    if (!preferredService) {\n      Swal.fire({\n        title: 'Service Required!',\n        text: 'Please select a preferred service first.',\n        icon: 'warning',\n        confirmButtonText: 'OK'\n      });\n      return;\n    }\n    // Find clinics that offer the selected service\n    this.appointmentsService.findClinics(this.selectedDate, '09:00',\n    // Default time for clinic search\n    '',\n    // No specific city filter for now\n    parseInt(preferredService)).subscribe(clinicsResponse => {\n      if (clinicsResponse && clinicsResponse.length > 0) {\n        // Use the first available clinic for now\n        const selectedClinic = clinicsResponse[0];\n        this.appointmentForm.patchValue({\n          clinicId: selectedClinic.clinicId\n        });\n        // Get available time slots for this clinic\n        this.getAvailableTimeSlots(selectedClinic.clinicId);\n      } else {\n        Swal.fire({\n          title: 'No Clinics Available!',\n          text: 'No clinics are available for the selected service and date.',\n          icon: 'info',\n          confirmButtonText: 'OK'\n        });\n      }\n    }, error => {\n      console.error('Error finding clinics:', error);\n      Swal.fire({\n        title: 'Error!',\n        text: 'Failed to find available clinics. Please try again.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    });\n  }\n  getAvailableTimeSlots(clinicId) {\n    this.appointmentsService.getAvailableTimeSlots(clinicId, this.selectedDate).subscribe(response => {\n      if (response.success) {\n        this.availableTimeSlots = response.timeSlots || [];\n        this.showDoctorModal = true;\n      } else {\n        if (response.needsScheduleCreation) {\n          Swal.fire({\n            title: 'No Schedule Found!',\n            text: `No schedule found for ${response.dayName}. Please contact the clinic to set up a schedule for this day.`,\n            icon: 'info',\n            confirmButtonText: 'OK'\n          });\n        } else {\n          Swal.fire({\n            title: 'No Time Slots!',\n            text: response.message || 'No available time slots for the selected date.',\n            icon: 'info',\n            confirmButtonText: 'OK'\n          });\n        }\n      }\n    }, error => {\n      console.error('Error getting time slots:', error);\n      Swal.fire({\n        title: 'Error!',\n        text: 'Failed to get available time slots. Please try again.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    });\n  }\n  selectDoctorAndTimeSlot(doctor, timeSlot) {\n    this.selectedDoctor = doctor;\n    this.selectedTimeSlot = timeSlot;\n    // Auto-fill the form\n    this.appointmentForm.patchValue({\n      fromTime: timeSlot.startTime,\n      toTime: timeSlot.endTime,\n      doctorName: `${doctor.title} ${doctor.firstName} ${doctor.lastName}`\n    });\n    this.showDoctorModal = false;\n  }\n  closeModal() {\n    this.showDoctorModal = false;\n  }\n  getTodayDate() {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  }\n  onSubmit() {\n    if (this.appointmentForm.valid && this.selectedDoctor) {\n      const formValues = this.appointmentForm.value;\n      const appointment = {\n        appointmentId: 0,\n        firstName: formValues.firstName,\n        lastName: formValues.lastName,\n        email: formValues.email,\n        telephone: formValues.telephone,\n        preferredservice: formValues.preferredService,\n        fromDate: formValues.preferredDate,\n        toDate: formValues.preferredDate,\n        fromTime: formValues.fromTime,\n        toTime: formValues.toTime,\n        clinics: {\n          clinicId: formValues.clinicId\n        },\n        doctor: this.selectedDoctor,\n        status: 'Pending',\n        // Other required fields with default values\n        address: '',\n        city: '',\n        state: '',\n        district: '',\n        nearestCity: '',\n        userName: '',\n        password: '',\n        customer: {\n          customerId: 0\n        }\n      };\n      this.appointmentsService.saveAppointments(appointment).subscribe(response => {\n        Swal.fire({\n          title: 'Success!',\n          text: 'Appointment booked successfully!',\n          icon: 'success',\n          confirmButtonText: 'OK'\n        }).then(() => {\n          this.router.navigate(['/appointments']);\n        });\n      }, error => {\n        console.error('Error saving appointment:', error);\n        Swal.fire({\n          title: 'Error!',\n          text: 'Failed to book appointment. Please try again.',\n          icon: 'error',\n          confirmButtonText: 'OK'\n        });\n      });\n    } else {\n      this.appointmentForm.markAllAsTouched();\n      if (!this.selectedDoctor) {\n        Swal.fire({\n          title: 'Missing Selection!',\n          text: 'Please select a doctor and time slot.',\n          icon: 'warning',\n          confirmButtonText: 'OK'\n        });\n      }\n    }\n  }\n  static #_ = this.ɵfac = function AppointmentBookingComponent_Factory(t) {\n    return new (t || AppointmentBookingComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AppointmentsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppointmentBookingComponent,\n    selectors: [[\"app-appointment-booking\"]],\n    decls: 58,\n    vars: 25,\n    consts: [[\"loggedUser\", \"Book Appointment\"], [1, \"appointment-booking-container\"], [1, \"header\"], [1, \"appointment-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-section\"], [1, \"row\"], [1, \"col-md-6\"], [\"for\", \"firstName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", 1, \"form-control\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"telephone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"telephone\", \"formControlName\", \"telephone\", 1, \"form-control\"], [\"for\", \"preferredService\", 1, \"form-label\"], [\"id\", \"preferredService\", \"formControlName\", \"preferredService\", 1, \"form-control\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"preferredDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"preferredDate\", \"formControlName\", \"preferredDate\", 1, \"form-control\", 3, \"min\", \"change\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"me-3\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [\"class\", \"modal fade show\", \"tabindex\", \"-1\", 3, \"display\", 4, \"ngIf\"], [\"class\", \"modal-backdrop fade show\", 4, \"ngIf\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [3, \"value\"], [1, \"col-12\"], [1, \"selected-appointment-info\"], [\"tabindex\", \"-1\", 1, \"modal\", \"fade\", \"show\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [1, \"time-slots-container\"], [\"class\", \"time-slot-group\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-slots-message\", 4, \"ngIf\"], [1, \"time-slot-group\"], [1, \"doctors-list\"], [\"class\", \"doctor-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"doctor-card\", 3, \"click\"], [1, \"doctor-info\"], [1, \"btn\", \"btn-sm\", \"btn-primary\"], [1, \"no-slots-message\"], [1, \"modal-backdrop\", \"fade\", \"show\"]],\n    template: function AppointmentBookingComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n        i0.ɵɵtext(4, \"Book Your Appointment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"p\");\n        i0.ɵɵtext(6, \"Fill in your details and select your preferred date to book an appointment\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"form\", 3);\n        i0.ɵɵlistener(\"ngSubmit\", function AppointmentBookingComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(8, \"div\", 4)(9, \"h3\");\n        i0.ɵɵtext(10, \"Patient Information\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 5)(12, \"div\", 6)(13, \"label\", 7);\n        i0.ɵɵtext(14, \"First Name *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"input\", 8);\n        i0.ɵɵtemplate(16, AppointmentBookingComponent_div_16_Template, 3, 2, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 6)(18, \"label\", 10);\n        i0.ɵɵtext(19, \"Last Name *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(20, \"input\", 11);\n        i0.ɵɵtemplate(21, AppointmentBookingComponent_div_21_Template, 3, 2, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 5)(23, \"div\", 6)(24, \"label\", 12);\n        i0.ɵɵtext(25, \"Email *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(26, \"input\", 13);\n        i0.ɵɵtemplate(27, AppointmentBookingComponent_div_27_Template, 3, 2, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 6)(29, \"label\", 14);\n        i0.ɵɵtext(30, \"Telephone Number *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(31, \"input\", 15);\n        i0.ɵɵtemplate(32, AppointmentBookingComponent_div_32_Template, 3, 2, \"div\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 4)(34, \"h3\");\n        i0.ɵɵtext(35, \"Appointment Details\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 5)(37, \"div\", 6)(38, \"label\", 16);\n        i0.ɵɵtext(39, \"Preferred Service *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"select\", 17)(41, \"option\", 18);\n        i0.ɵɵtext(42, \"Select a service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(43, AppointmentBookingComponent_option_43_Template, 2, 2, \"option\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(44, AppointmentBookingComponent_div_44_Template, 3, 0, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"div\", 6)(46, \"label\", 20);\n        i0.ɵɵtext(47, \"Preferred Date *\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"input\", 21);\n        i0.ɵɵlistener(\"change\", function AppointmentBookingComponent_Template_input_change_48_listener() {\n          return ctx.onDateChange();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(49, AppointmentBookingComponent_div_49_Template, 3, 0, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(50, AppointmentBookingComponent_div_50_Template, 18, 8, \"div\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(51, \"div\", 23)(52, \"button\", 24);\n        i0.ɵɵlistener(\"click\", function AppointmentBookingComponent_Template_button_click_52_listener() {\n          return ctx.router.navigate([\"/appointments\"]);\n        });\n        i0.ɵɵtext(53, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(54, \"button\", 25);\n        i0.ɵɵtext(55, \" Book Appointment \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(56, AppointmentBookingComponent_div_56_Template, 14, 8, \"div\", 26);\n        i0.ɵɵtemplate(57, AppointmentBookingComponent_div_57_Template, 1, 0, \"div\", 27);\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        let tmp_9_0;\n        let tmp_11_0;\n        let tmp_12_0;\n        let tmp_14_0;\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"formGroup\", ctx.appointmentForm);\n        i0.ɵɵadvance(8);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.appointmentForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.appointmentForm.get(\"lastName\")) == null ? null : tmp_4_0.touched));\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.appointmentForm.get(\"email\")) == null ? null : tmp_6_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_7_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.appointmentForm.get(\"telephone\")) == null ? null : tmp_8_0.touched));\n        i0.ɵɵadvance(8);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx.appointmentForm.get(\"preferredService\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.appointmentForm.get(\"preferredService\")) == null ? null : tmp_9_0.touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.services);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.appointmentForm.get(\"preferredService\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.appointmentForm.get(\"preferredService\")) == null ? null : tmp_11_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"is-invalid\", ((tmp_12_0 = ctx.appointmentForm.get(\"preferredDate\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.appointmentForm.get(\"preferredDate\")) == null ? null : tmp_12_0.touched));\n        i0.ɵɵproperty(\"min\", ctx.getTodayDate());\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.appointmentForm.get(\"preferredDate\")) == null ? null : tmp_14_0.invalid) && ((tmp_14_0 = ctx.appointmentForm.get(\"preferredDate\")) == null ? null : tmp_14_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedDoctor && ctx.selectedTimeSlot);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"disabled\", ctx.appointmentForm.invalid || !ctx.selectedDoctor);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showDoctorModal);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showDoctorModal);\n      }\n    },\n    styles: [\".appointment-booking-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: white;\\n  border-radius: 10px;\\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\\n}\\n\\n.header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #7f8c8d;\\n  margin: 0;\\n}\\n\\n.appointment-form[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 10px;\\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  padding-bottom: 20px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.form-section[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 20px;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #2c3e50;\\n  margin-bottom: 5px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  padding: 10px;\\n  margin-bottom: 15px;\\n  transition: border-color 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #3498db;\\n  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);\\n}\\n\\n.form-control.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n}\\n\\n.invalid-feedback[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #e74c3c;\\n  font-size: 0.875rem;\\n  margin-top: -10px;\\n  margin-bottom: 10px;\\n}\\n\\n.selected-appointment-info[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  padding: 15px;\\n  border-radius: 5px;\\n  border-left: 4px solid #27ae60;\\n  margin-top: 20px;\\n}\\n\\n.selected-appointment-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #27ae60;\\n  margin-bottom: 10px;\\n  font-size: 1.1rem;\\n}\\n\\n.selected-appointment-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0;\\n  color: #2c3e50;\\n}\\n\\n.form-actions[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n  padding-top: 20px;\\n  border-top: 1px solid #eee;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  border-radius: 5px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background-color: #3498db;\\n  border-color: #3498db;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background-color: #2980b9;\\n  border-color: #2980b9;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #95a5a6;\\n  border-color: #95a5a6;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #7f8c8d;\\n  border-color: #7f8c8d;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: none;\\n  box-shadow: 0 10px 30px rgba(0,0,0,0.3);\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  background: #3498db;\\n  color: white;\\n  border-radius: 10px 10px 0 0;\\n}\\n\\n.modal-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  filter: invert(1);\\n}\\n\\n.time-slots-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.time-slot-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  padding: 15px;\\n  background: #f8f9fa;\\n  border-radius: 5px;\\n}\\n\\n.time-slot-group[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n  font-weight: 600;\\n}\\n\\n.doctors-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n}\\n\\n.doctor-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px;\\n  background: white;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.doctor-card[_ngcontent-%COMP%]:hover {\\n  border-color: #3498db;\\n  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);\\n}\\n\\n.doctor-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.doctor-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #7f8c8d;\\n  font-size: 0.8rem;\\n}\\n\\n.no-slots-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  color: #7f8c8d;\\n}\\n\\n@media (max-width: 768px) {\\n  .appointment-booking-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .appointment-form[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  \\n  .modal-dialog[_ngcontent-%COMP%] {\\n    margin: 10px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { AppointmentBookingComponent };", "map": {"version": 3, "names": ["Validators", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AppointmentBookingComponent_div_16_small_1_Template", "AppointmentBookingComponent_div_16_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "appointmentForm", "get", "errors", "tmp_1_0", "AppointmentBookingComponent_div_21_small_1_Template", "AppointmentBookingComponent_div_21_small_2_Template", "ctx_r1", "AppointmentBookingComponent_div_27_small_1_Template", "AppointmentBookingComponent_div_27_small_2_Template", "ctx_r2", "AppointmentBookingComponent_div_32_small_1_Template", "AppointmentBookingComponent_div_32_small_2_Template", "ctx_r3", "service_r18", "clinicServiceCategoryId", "ɵɵtextInterpolate1", "serviceName", "ɵɵtextInterpolate3", "ctx_r7", "<PERSON><PERSON><PERSON><PERSON>", "title", "firstName", "lastName", "selectedTimeSlot", "displayTime", "ɵɵpipeBind2", "selectedDate", "ɵɵlistener", "AppointmentBookingComponent_div_56_div_12_div_4_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r25", "doctor_r23", "$implicit", "timeSlot_r21", "ɵɵnextContext", "ctx_r24", "ɵɵresetView", "selectDoctorAndTimeSlot", "ɵɵtextInterpolate", "qualifications", "AppointmentBookingComponent_div_56_div_12_div_4_Template", "availableDoctors", "AppointmentBookingComponent_div_56_Template_button_click_6_listener", "_r28", "ctx_r27", "closeModal", "AppointmentBookingComponent_div_56_div_12_Template", "AppointmentBookingComponent_div_56_div_13_Template", "ɵɵstyleProp", "ctx_r8", "showDoctorModal", "availableTimeSlots", "length", "ɵɵelement", "AppointmentBookingComponent", "constructor", "fb", "router", "appointmentsService", "clinics", "services", "group", "required", "pattern", "email", "telephone", "preferredService", "preferredDate", "fromTime", "toTime", "<PERSON><PERSON><PERSON>", "clinicId", "ngOnInit", "loadServices", "loadClinics", "getServicesList", "subscribe", "error", "console", "getClinicsList", "onDateChange", "value", "checkScheduleAndGenerateTimeSlots", "fire", "text", "icon", "confirmButtonText", "findClinics", "parseInt", "clinicsResponse", "selectedClinic", "patchValue", "getAvailableTimeSlots", "response", "success", "timeSlots", "needsScheduleCreation", "day<PERSON><PERSON>", "message", "doctor", "timeSlot", "startTime", "endTime", "getTodayDate", "today", "Date", "toISOString", "split", "onSubmit", "valid", "formValues", "appointment", "appointmentId", "preferredservice", "fromDate", "toDate", "status", "address", "city", "state", "district", "nearestCity", "userName", "password", "customer", "customerId", "saveAppointments", "then", "navigate", "mark<PERSON>llAsTouched", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "AppointmentsService", "_2", "selectors", "decls", "vars", "consts", "template", "AppointmentBookingComponent_Template", "rf", "ctx", "AppointmentBookingComponent_Template_form_ngSubmit_7_listener", "AppointmentBookingComponent_div_16_Template", "AppointmentBookingComponent_div_21_Template", "AppointmentBookingComponent_div_27_Template", "AppointmentBookingComponent_div_32_Template", "AppointmentBookingComponent_option_43_Template", "AppointmentBookingComponent_div_44_Template", "AppointmentBookingComponent_Template_input_change_48_listener", "AppointmentBookingComponent_div_49_Template", "AppointmentBookingComponent_div_50_Template", "AppointmentBookingComponent_Template_button_click_52_listener", "AppointmentBookingComponent_div_56_Template", "AppointmentBookingComponent_div_57_Template", "ɵɵclassProp", "invalid", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_11_0", "tmp_12_0", "tmp_14_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointment-booking\\appointment-booking.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\appointment-booking\\appointment-booking.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AppointmentsService } from '../appointments.service';\nimport { Appointments } from '../appointments';\nimport { Doctor } from 'src/app/doctor/doctor';\nimport Swal from 'sweetalert2';\n\ninterface TimeSlot {\n  startTime: string;\n  endTime: string;\n  displayTime: string;\n  available: boolean;\n  availableDoctors: Doctor[];\n}\n\ninterface ClinicSchedule {\n  scheduleId: number;\n  date: string;\n  fromTime: string;\n  toTime: string;\n  clinics: { clinicId: number };\n}\n\n@Component({\n  selector: 'app-appointment-booking',\n  templateUrl: './appointment-booking.component.html',\n  styleUrls: ['./appointment-booking.component.css']\n})\nexport class AppointmentBookingComponent implements OnInit {\n  appointmentForm: FormGroup;\n  showDoctorModal = false;\n  selectedDate: string = '';\n  availableTimeSlots: TimeSlot[] = [];\n  selectedTimeSlot: TimeSlot | null = null;\n  selectedDoctor: Doctor | null = null;\n  clinics: any[] = [];\n  services: any[] = [];\n  \n  constructor(\n    private fb: FormBuilder,\n    private router: Router,\n    private appointmentsService: AppointmentsService\n  ) {\n    this.appointmentForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\\s]+$/)]],\n      lastName: ['', [Validators.required, Validators.pattern(/^[a-zA-Z\\s]+$/)]],\n      email: ['', [Validators.required, Validators.email]],\n      telephone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      preferredService: ['', Validators.required],\n      preferredDate: ['', Validators.required],\n      fromTime: [''],\n      toTime: [''],\n      doctorName: [''],\n      clinicId: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadServices();\n    this.loadClinics();\n  }\n\n  loadServices(): void {\n    this.appointmentsService.getServicesList().subscribe(\n      (services) => {\n        this.services = services;\n      },\n      (error) => {\n        console.error('Error loading services:', error);\n      }\n    );\n  }\n\n  loadClinics(): void {\n    this.appointmentsService.getClinicsList().subscribe(\n      (clinics) => {\n        this.clinics = clinics;\n      },\n      (error) => {\n        console.error('Error loading clinics:', error);\n      }\n    );\n  }\n\n  onDateChange(): void {\n    const selectedDate = this.appointmentForm.get('preferredDate')?.value;\n    if (selectedDate) {\n      this.selectedDate = selectedDate;\n      this.checkScheduleAndGenerateTimeSlots();\n    }\n  }\n\n  checkScheduleAndGenerateTimeSlots(): void {\n    const preferredService = this.appointmentForm.get('preferredService')?.value;\n\n    if (!preferredService) {\n      Swal.fire({\n        title: 'Service Required!',\n        text: 'Please select a preferred service first.',\n        icon: 'warning',\n        confirmButtonText: 'OK'\n      });\n      return;\n    }\n\n    // Find clinics that offer the selected service\n    this.appointmentsService.findClinics(\n      this.selectedDate,\n      '09:00', // Default time for clinic search\n      '', // No specific city filter for now\n      parseInt(preferredService)\n    ).subscribe(\n      (clinicsResponse) => {\n        if (clinicsResponse && clinicsResponse.length > 0) {\n          // Use the first available clinic for now\n          const selectedClinic = clinicsResponse[0];\n          this.appointmentForm.patchValue({\n            clinicId: selectedClinic.clinicId\n          });\n\n          // Get available time slots for this clinic\n          this.getAvailableTimeSlots(selectedClinic.clinicId);\n        } else {\n          Swal.fire({\n            title: 'No Clinics Available!',\n            text: 'No clinics are available for the selected service and date.',\n            icon: 'info',\n            confirmButtonText: 'OK'\n          });\n        }\n      },\n      (error) => {\n        console.error('Error finding clinics:', error);\n        Swal.fire({\n          title: 'Error!',\n          text: 'Failed to find available clinics. Please try again.',\n          icon: 'error',\n          confirmButtonText: 'OK'\n        });\n      }\n    );\n  }\n\n  getAvailableTimeSlots(clinicId: number): void {\n    this.appointmentsService.getAvailableTimeSlots(clinicId, this.selectedDate).subscribe(\n      (response) => {\n        if (response.success) {\n          this.availableTimeSlots = response.timeSlots || [];\n          this.showDoctorModal = true;\n        } else {\n          if (response.needsScheduleCreation) {\n            Swal.fire({\n              title: 'No Schedule Found!',\n              text: `No schedule found for ${response.dayName}. Please contact the clinic to set up a schedule for this day.`,\n              icon: 'info',\n              confirmButtonText: 'OK'\n            });\n          } else {\n            Swal.fire({\n              title: 'No Time Slots!',\n              text: response.message || 'No available time slots for the selected date.',\n              icon: 'info',\n              confirmButtonText: 'OK'\n            });\n          }\n        }\n      },\n      (error) => {\n        console.error('Error getting time slots:', error);\n        Swal.fire({\n          title: 'Error!',\n          text: 'Failed to get available time slots. Please try again.',\n          icon: 'error',\n          confirmButtonText: 'OK'\n        });\n      }\n    );\n  }\n\n  selectDoctorAndTimeSlot(doctor: Doctor, timeSlot: TimeSlot): void {\n    this.selectedDoctor = doctor;\n    this.selectedTimeSlot = timeSlot;\n    \n    // Auto-fill the form\n    this.appointmentForm.patchValue({\n      fromTime: timeSlot.startTime,\n      toTime: timeSlot.endTime,\n      doctorName: `${doctor.title} ${doctor.firstName} ${doctor.lastName}`\n    });\n    \n    this.showDoctorModal = false;\n  }\n\n  closeModal(): void {\n    this.showDoctorModal = false;\n  }\n\n  getTodayDate(): string {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  }\n\n  onSubmit(): void {\n    if (this.appointmentForm.valid && this.selectedDoctor) {\n      const formValues = this.appointmentForm.value;\n\n      const appointment: Appointments = {\n        appointmentId: 0,\n        firstName: formValues.firstName,\n        lastName: formValues.lastName,\n        email: formValues.email,\n        telephone: formValues.telephone,\n        preferredservice: formValues.preferredService,\n        fromDate: formValues.preferredDate,\n        toDate: formValues.preferredDate,\n        fromTime: formValues.fromTime,\n        toTime: formValues.toTime,\n        clinics: { clinicId: formValues.clinicId },\n        doctor: this.selectedDoctor,\n        status: 'Pending',\n        // Other required fields with default values\n        address: '',\n        city: '',\n        state: '',\n        district: '',\n        nearestCity: '',\n        userName: '',\n        password: '',\n        customer: { customerId: 0 } as any\n      } as any;\n\n      this.appointmentsService.saveAppointments(appointment).subscribe(\n        (response) => {\n          Swal.fire({\n            title: 'Success!',\n            text: 'Appointment booked successfully!',\n            icon: 'success',\n            confirmButtonText: 'OK'\n          }).then(() => {\n            this.router.navigate(['/appointments']);\n          });\n        },\n        (error) => {\n          console.error('Error saving appointment:', error);\n          Swal.fire({\n            title: 'Error!',\n            text: 'Failed to book appointment. Please try again.',\n            icon: 'error',\n            confirmButtonText: 'OK'\n          });\n        }\n      );\n    } else {\n      this.appointmentForm.markAllAsTouched();\n      if (!this.selectedDoctor) {\n        Swal.fire({\n          title: 'Missing Selection!',\n          text: 'Please select a doctor and time slot.',\n          icon: 'warning',\n          confirmButtonText: 'OK'\n        });\n      }\n    }\n  }\n}\n", "<app-default-navbar loggedUser=\"Book Appointment\"/>\n\n<div class=\"appointment-booking-container\">\n  <div class=\"header\">\n    <h2>Book Your Appointment</h2>\n    <p>Fill in your details and select your preferred date to book an appointment</p>\n  </div>\n\n  <form [formGroup]=\"appointmentForm\" (ngSubmit)=\"onSubmit()\" class=\"appointment-form\">\n    <!-- Patient Information Section -->\n    <div class=\"form-section\">\n      <h3>Patient Information</h3>\n      <div class=\"row\">\n        <div class=\"col-md-6\">\n          <label for=\"firstName\" class=\"form-label\">First Name *</label>\n          <input \n            type=\"text\" \n            id=\"firstName\" \n            formControlName=\"firstName\" \n            class=\"form-control\"\n            [class.is-invalid]=\"appointmentForm.get('firstName')?.invalid && appointmentForm.get('firstName')?.touched\">\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('firstName')?.invalid && appointmentForm.get('firstName')?.touched\">\n            <small *ngIf=\"appointmentForm.get('firstName')?.errors?.['required']\">First name is required</small>\n            <small *ngIf=\"appointmentForm.get('firstName')?.errors?.['pattern']\">Only letters and spaces allowed</small>\n          </div>\n        </div>\n        \n        <div class=\"col-md-6\">\n          <label for=\"lastName\" class=\"form-label\">Last Name *</label>\n          <input \n            type=\"text\" \n            id=\"lastName\" \n            formControlName=\"lastName\" \n            class=\"form-control\"\n            [class.is-invalid]=\"appointmentForm.get('lastName')?.invalid && appointmentForm.get('lastName')?.touched\">\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('lastName')?.invalid && appointmentForm.get('lastName')?.touched\">\n            <small *ngIf=\"appointmentForm.get('lastName')?.errors?.['required']\">Last name is required</small>\n            <small *ngIf=\"appointmentForm.get('lastName')?.errors?.['pattern']\">Only letters and spaces allowed</small>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"row\">\n        <div class=\"col-md-6\">\n          <label for=\"email\" class=\"form-label\">Email *</label>\n          <input \n            type=\"email\" \n            id=\"email\" \n            formControlName=\"email\" \n            class=\"form-control\"\n            [class.is-invalid]=\"appointmentForm.get('email')?.invalid && appointmentForm.get('email')?.touched\">\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('email')?.invalid && appointmentForm.get('email')?.touched\">\n            <small *ngIf=\"appointmentForm.get('email')?.errors?.['required']\">Email is required</small>\n            <small *ngIf=\"appointmentForm.get('email')?.errors?.['email']\">Please enter a valid email</small>\n          </div>\n        </div>\n        \n        <div class=\"col-md-6\">\n          <label for=\"telephone\" class=\"form-label\">Telephone Number *</label>\n          <input \n            type=\"tel\" \n            id=\"telephone\" \n            formControlName=\"telephone\" \n            class=\"form-control\"\n            [class.is-invalid]=\"appointmentForm.get('telephone')?.invalid && appointmentForm.get('telephone')?.touched\">\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('telephone')?.invalid && appointmentForm.get('telephone')?.touched\">\n            <small *ngIf=\"appointmentForm.get('telephone')?.errors?.['required']\">Telephone number is required</small>\n            <small *ngIf=\"appointmentForm.get('telephone')?.errors?.['pattern']\">Please enter a valid 10-digit phone number</small>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Appointment Details Section -->\n    <div class=\"form-section\">\n      <h3>Appointment Details</h3>\n      <div class=\"row\">\n        <div class=\"col-md-6\">\n          <label for=\"preferredService\" class=\"form-label\">Preferred Service *</label>\n          <select \n            id=\"preferredService\" \n            formControlName=\"preferredService\" \n            class=\"form-control\"\n            [class.is-invalid]=\"appointmentForm.get('preferredService')?.invalid && appointmentForm.get('preferredService')?.touched\">\n            <option value=\"\">Select a service</option>\n            <option *ngFor=\"let service of services\" [value]=\"service.clinicServiceCategoryId\">\n              {{service.serviceName}}\n            </option>\n          </select>\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('preferredService')?.invalid && appointmentForm.get('preferredService')?.touched\">\n            <small>Please select a service</small>\n          </div>\n        </div>\n        \n        <div class=\"col-md-6\">\n          <label for=\"preferredDate\" class=\"form-label\">Preferred Date *</label>\n          <input \n            type=\"date\" \n            id=\"preferredDate\" \n            formControlName=\"preferredDate\" \n            class=\"form-control\"\n            [min]=\"getTodayDate()\"\n            (change)=\"onDateChange()\"\n            [class.is-invalid]=\"appointmentForm.get('preferredDate')?.invalid && appointmentForm.get('preferredDate')?.touched\">\n          <div class=\"invalid-feedback\" *ngIf=\"appointmentForm.get('preferredDate')?.invalid && appointmentForm.get('preferredDate')?.touched\">\n            <small>Please select a date</small>\n          </div>\n        </div>\n      </div>\n\n      <!-- Selected Doctor and Time Display -->\n      <div class=\"row\" *ngIf=\"selectedDoctor && selectedTimeSlot\">\n        <div class=\"col-12\">\n          <div class=\"selected-appointment-info\">\n            <h4>Selected Appointment</h4>\n            <p><strong>Doctor:</strong> {{selectedDoctor.title}} {{selectedDoctor.firstName}} {{selectedDoctor.lastName}}</p>\n            <p><strong>Time:</strong> {{selectedTimeSlot.displayTime}}</p>\n            <p><strong>Date:</strong> {{selectedDate | date:'fullDate'}}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Submit Button -->\n    <div class=\"form-actions\">\n      <button type=\"button\" class=\"btn btn-secondary me-3\" (click)=\"router.navigate(['/appointments'])\">\n        Cancel\n      </button>\n      <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"appointmentForm.invalid || !selectedDoctor\">\n        Book Appointment\n      </button>\n    </div>\n  </form>\n</div>\n\n<!-- Doctor & Time Slot Selection Modal -->\n<div class=\"modal fade show\" [style.display]=\"showDoctorModal ? 'block' : 'none'\" tabindex=\"-1\" *ngIf=\"showDoctorModal\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\">Select Doctor & Time Slot</h5>\n        <button type=\"button\" class=\"btn-close\" (click)=\"closeModal()\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <p>Available appointments for {{selectedDate | date:'fullDate'}}</p>\n        \n        <!-- Time slots will be populated here -->\n        <div class=\"time-slots-container\">\n          <div *ngFor=\"let timeSlot of availableTimeSlots\" class=\"time-slot-group\">\n            <h6>{{timeSlot.displayTime}}</h6>\n            <div class=\"doctors-list\">\n              <div *ngFor=\"let doctor of timeSlot.availableDoctors\" \n                   class=\"doctor-card\" \n                   (click)=\"selectDoctorAndTimeSlot(doctor, timeSlot)\">\n                <div class=\"doctor-info\">\n                  <h6>{{doctor.title}} {{doctor.firstName}} {{doctor.lastName}}</h6>\n                  <p>{{doctor.qualifications}}</p>\n                </div>\n                <button class=\"btn btn-sm btn-primary\">Select</button>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <div *ngIf=\"availableTimeSlots.length === 0\" class=\"no-slots-message\">\n          <p>No available time slots for the selected date. Please choose a different date.</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"modal-backdrop fade show\" *ngIf=\"showDoctorModal\"></div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,OAAOC,IAAI,MAAM,aAAa;;;;;;;ICgBlBC,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACpGH,EAAA,CAAAC,cAAA,YAAqE;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF9GH,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAI,UAAA,IAAAC,mDAAA,oBAAoG;IACpGL,EAAA,CAAAI,UAAA,IAAAE,mDAAA,oBAA4G;IAC9GN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;IAC5Db,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,eAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAA2D;;;;;IAanEb,EAAA,CAAAC,cAAA,YAAqE;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAClGH,EAAA,CAAAC,cAAA,YAAoE;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF7GH,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAI,UAAA,IAAAW,mDAAA,oBAAkG;IAClGf,EAAA,CAAAI,UAAA,IAAAY,mDAAA,oBAA2G;IAC7GhB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAQ,MAAA,CAAAN,eAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;IAC3Db,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAG,MAAA,CAAAN,eAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAA0D;;;;;IAelEb,EAAA,CAAAC,cAAA,YAAkE;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC3FH,EAAA,CAAAC,cAAA,YAA+D;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFnGH,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAI,UAAA,IAAAc,mDAAA,oBAA2F;IAC3FlB,EAAA,CAAAI,UAAA,IAAAe,mDAAA,oBAAiG;IACnGnB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAW,MAAA,CAAAT,eAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IACxDb,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAM,MAAA,CAAAT,eAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAqD;;;;;IAa7Db,EAAA,CAAAC,cAAA,YAAsE;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC1GH,EAAA,CAAAC,cAAA,YAAqE;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFzHH,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAI,UAAA,IAAAiB,mDAAA,oBAA0G;IAC1GrB,EAAA,CAAAI,UAAA,IAAAkB,mDAAA,oBAAuH;IACzHtB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFIH,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAc,MAAA,CAAAZ,eAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;IAC5Db,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAS,MAAA,CAAAZ,eAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAA2D;;;;;IAkBnEb,EAAA,CAAAC,cAAA,iBAAmF;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFgCH,EAAA,CAAAQ,UAAA,UAAAgB,WAAA,CAAAC,uBAAA,CAAyC;IAChFzB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA0B,kBAAA,MAAAF,WAAA,CAAAG,WAAA,MACF;;;;;IAEF3B,EAAA,CAAAC,cAAA,cAA2I;IAClID,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAcxCH,EAAA,CAAAC,cAAA,cAAqI;IAC5HD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAMzCH,EAAA,CAAAC,cAAA,aAA4D;IAGlDD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAiF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjHH,EAAA,CAAAC,cAAA,QAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9DH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAkC;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAFpCH,EAAA,CAAAO,SAAA,GAAiF;IAAjFP,EAAA,CAAA4B,kBAAA,MAAAC,MAAA,CAAAC,cAAA,CAAAC,KAAA,OAAAF,MAAA,CAAAC,cAAA,CAAAE,SAAA,OAAAH,MAAA,CAAAC,cAAA,CAAAG,QAAA,KAAiF;IACnFjC,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA0B,kBAAA,MAAAG,MAAA,CAAAK,gBAAA,CAAAC,WAAA,KAAgC;IAChCnC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA0B,kBAAA,MAAA1B,EAAA,CAAAoC,WAAA,QAAAP,MAAA,CAAAQ,YAAA,kBAAkC;;;;;;IAkC1DrC,EAAA,CAAAC,cAAA,cAEyD;IAApDD,EAAA,CAAAsC,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,YAAA,GAAA7C,EAAA,CAAA8C,aAAA,GAAAF,SAAA;MAAA,MAAAG,OAAA,GAAA/C,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAAgD,WAAA,CAAAD,OAAA,CAAAE,uBAAA,CAAAN,UAAA,EAAAE,YAAA,CAAyC;IAAA,EAAC;IACtD7C,EAAA,CAAAC,cAAA,cAAyB;IACnBD,EAAA,CAAAE,MAAA,GAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAElCH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHhDH,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAA4B,kBAAA,KAAAe,UAAA,CAAAZ,KAAA,OAAAY,UAAA,CAAAX,SAAA,OAAAW,UAAA,CAAAV,QAAA,KAAyD;IAC1DjC,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAkD,iBAAA,CAAAP,UAAA,CAAAQ,cAAA,CAAyB;;;;;IARpCnD,EAAA,CAAAC,cAAA,cAAyE;IACnED,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,UAAA,IAAAgD,wDAAA,kBAQM;IACRpD,EAAA,CAAAG,YAAA,EAAM;;;;IAXFH,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAkD,iBAAA,CAAAL,YAAA,CAAAV,WAAA,CAAwB;IAEFnC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,UAAA,YAAAqC,YAAA,CAAAQ,gBAAA,CAA4B;;;;;IAa1DrD,EAAA,CAAAC,cAAA,cAAsE;IACjED,EAAA,CAAAE,MAAA,qFAA8E;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IA7B/FH,EAAA,CAAAC,cAAA,cAAwH;IAIxFD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,iBAA+D;IAAvBD,EAAA,CAAAsC,UAAA,mBAAAgB,oEAAA;MAAAtD,EAAA,CAAAyC,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAAgD,WAAA,CAAAQ,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAACzD,EAAA,CAAAG,YAAA,EAAS;IAE1EH,EAAA,CAAAC,cAAA,cAAwB;IACnBD,EAAA,CAAAE,MAAA,GAA6D;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGpEH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAI,UAAA,KAAAsD,kDAAA,kBAaM;IACR1D,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAI,UAAA,KAAAuD,kDAAA,kBAEM;IACR3D,EAAA,CAAAG,YAAA,EAAM;;;;IA/BiBH,EAAA,CAAA4D,WAAA,YAAAC,MAAA,CAAAC,eAAA,oBAAoD;IAQtE9D,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAA0B,kBAAA,gCAAA1B,EAAA,CAAAoC,WAAA,QAAAyB,MAAA,CAAAxB,YAAA,kBAA6D;IAIpCrC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,YAAAqD,MAAA,CAAAE,kBAAA,CAAqB;IAgB3C/D,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAqD,MAAA,CAAAE,kBAAA,CAAAC,MAAA,OAAqC;;;;;IAOnDhE,EAAA,CAAAiE,SAAA,cAAoE;;;ADnJpE,MAKaC,2BAA2B;EAUtCC,YACUC,EAAe,EACfC,MAAc,EACdC,mBAAwC;IAFxC,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAX7B,KAAAR,eAAe,GAAG,KAAK;IACvB,KAAAzB,YAAY,GAAW,EAAE;IACzB,KAAA0B,kBAAkB,GAAe,EAAE;IACnC,KAAA7B,gBAAgB,GAAoB,IAAI;IACxC,KAAAJ,cAAc,GAAkB,IAAI;IACpC,KAAAyC,OAAO,GAAU,EAAE;IACnB,KAAAC,QAAQ,GAAU,EAAE;IAOlB,IAAI,CAAC7D,eAAe,GAAG,IAAI,CAACyD,EAAE,CAACK,KAAK,CAAC;MACnCzC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC4E,QAAQ,EAAE5E,UAAU,CAAC6E,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MAC3E1C,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC4E,QAAQ,EAAE5E,UAAU,CAAC6E,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;MAC1EC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9E,UAAU,CAAC4E,QAAQ,EAAE5E,UAAU,CAAC8E,KAAK,CAAC,CAAC;MACpDC,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC/E,UAAU,CAAC4E,QAAQ,EAAE5E,UAAU,CAAC6E,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACzEG,gBAAgB,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAAC4E,QAAQ,CAAC;MAC3CK,aAAa,EAAE,CAAC,EAAE,EAAEjF,UAAU,CAAC4E,QAAQ,CAAC;MACxCM,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAD,YAAYA,CAAA;IACV,IAAI,CAACf,mBAAmB,CAACiB,eAAe,EAAE,CAACC,SAAS,CACjDhB,QAAQ,IAAI;MACX,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,EACAiB,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,CACF;EACH;EAEAH,WAAWA,CAAA;IACT,IAAI,CAAChB,mBAAmB,CAACqB,cAAc,EAAE,CAACH,SAAS,CAChDjB,OAAO,IAAI;MACV,IAAI,CAACA,OAAO,GAAGA,OAAO;IACxB,CAAC,EACAkB,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,CACF;EACH;EAEAG,YAAYA,CAAA;IACV,MAAMvD,YAAY,GAAG,IAAI,CAAC1B,eAAe,CAACC,GAAG,CAAC,eAAe,CAAC,EAAEiF,KAAK;IACrE,IAAIxD,YAAY,EAAE;MAChB,IAAI,CAACA,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACyD,iCAAiC,EAAE;;EAE5C;EAEAA,iCAAiCA,CAAA;IAC/B,MAAMhB,gBAAgB,GAAG,IAAI,CAACnE,eAAe,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEiF,KAAK;IAE5E,IAAI,CAACf,gBAAgB,EAAE;MACrB/E,IAAI,CAACgG,IAAI,CAAC;QACRhE,KAAK,EAAE,mBAAmB;QAC1BiE,IAAI,EAAE,0CAA0C;QAChDC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC;MACF;;IAGF;IACA,IAAI,CAAC5B,mBAAmB,CAAC6B,WAAW,CAClC,IAAI,CAAC9D,YAAY,EACjB,OAAO;IAAE;IACT,EAAE;IAAE;IACJ+D,QAAQ,CAACtB,gBAAgB,CAAC,CAC3B,CAACU,SAAS,CACRa,eAAe,IAAI;MAClB,IAAIA,eAAe,IAAIA,eAAe,CAACrC,MAAM,GAAG,CAAC,EAAE;QACjD;QACA,MAAMsC,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC1F,eAAe,CAAC4F,UAAU,CAAC;UAC9BpB,QAAQ,EAAEmB,cAAc,CAACnB;SAC1B,CAAC;QAEF;QACA,IAAI,CAACqB,qBAAqB,CAACF,cAAc,CAACnB,QAAQ,CAAC;OACpD,MAAM;QACLpF,IAAI,CAACgG,IAAI,CAAC;UACRhE,KAAK,EAAE,uBAAuB;UAC9BiE,IAAI,EAAE,6DAA6D;UACnEC,IAAI,EAAE,MAAM;UACZC,iBAAiB,EAAE;SACpB,CAAC;;IAEN,CAAC,EACAT,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C1F,IAAI,CAACgG,IAAI,CAAC;QACRhE,KAAK,EAAE,QAAQ;QACfiE,IAAI,EAAE,qDAAqD;QAC3DC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF;EACH;EAEAM,qBAAqBA,CAACrB,QAAgB;IACpC,IAAI,CAACb,mBAAmB,CAACkC,qBAAqB,CAACrB,QAAQ,EAAE,IAAI,CAAC9C,YAAY,CAAC,CAACmD,SAAS,CAClFiB,QAAQ,IAAI;MACX,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAI,CAAC3C,kBAAkB,GAAG0C,QAAQ,CAACE,SAAS,IAAI,EAAE;QAClD,IAAI,CAAC7C,eAAe,GAAG,IAAI;OAC5B,MAAM;QACL,IAAI2C,QAAQ,CAACG,qBAAqB,EAAE;UAClC7G,IAAI,CAACgG,IAAI,CAAC;YACRhE,KAAK,EAAE,oBAAoB;YAC3BiE,IAAI,EAAE,yBAAyBS,QAAQ,CAACI,OAAO,gEAAgE;YAC/GZ,IAAI,EAAE,MAAM;YACZC,iBAAiB,EAAE;WACpB,CAAC;SACH,MAAM;UACLnG,IAAI,CAACgG,IAAI,CAAC;YACRhE,KAAK,EAAE,gBAAgB;YACvBiE,IAAI,EAAES,QAAQ,CAACK,OAAO,IAAI,gDAAgD;YAC1Eb,IAAI,EAAE,MAAM;YACZC,iBAAiB,EAAE;WACpB,CAAC;;;IAGR,CAAC,EACAT,KAAK,IAAI;MACRC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1F,IAAI,CAACgG,IAAI,CAAC;QACRhE,KAAK,EAAE,QAAQ;QACfiE,IAAI,EAAE,uDAAuD;QAC7DC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF;EACH;EAEAjD,uBAAuBA,CAAC8D,MAAc,EAAEC,QAAkB;IACxD,IAAI,CAAClF,cAAc,GAAGiF,MAAM;IAC5B,IAAI,CAAC7E,gBAAgB,GAAG8E,QAAQ;IAEhC;IACA,IAAI,CAACrG,eAAe,CAAC4F,UAAU,CAAC;MAC9BvB,QAAQ,EAAEgC,QAAQ,CAACC,SAAS;MAC5BhC,MAAM,EAAE+B,QAAQ,CAACE,OAAO;MACxBhC,UAAU,EAAE,GAAG6B,MAAM,CAAChF,KAAK,IAAIgF,MAAM,CAAC/E,SAAS,IAAI+E,MAAM,CAAC9E,QAAQ;KACnE,CAAC;IAEF,IAAI,CAAC6B,eAAe,GAAG,KAAK;EAC9B;EAEAL,UAAUA,CAAA;IACR,IAAI,CAACK,eAAe,GAAG,KAAK;EAC9B;EAEAqD,YAAYA,CAAA;IACV,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,OAAOD,KAAK,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1C;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7G,eAAe,CAAC8G,KAAK,IAAI,IAAI,CAAC3F,cAAc,EAAE;MACrD,MAAM4F,UAAU,GAAG,IAAI,CAAC/G,eAAe,CAACkF,KAAK;MAE7C,MAAM8B,WAAW,GAAiB;QAChCC,aAAa,EAAE,CAAC;QAChB5F,SAAS,EAAE0F,UAAU,CAAC1F,SAAS;QAC/BC,QAAQ,EAAEyF,UAAU,CAACzF,QAAQ;QAC7B2C,KAAK,EAAE8C,UAAU,CAAC9C,KAAK;QACvBC,SAAS,EAAE6C,UAAU,CAAC7C,SAAS;QAC/BgD,gBAAgB,EAAEH,UAAU,CAAC5C,gBAAgB;QAC7CgD,QAAQ,EAAEJ,UAAU,CAAC3C,aAAa;QAClCgD,MAAM,EAAEL,UAAU,CAAC3C,aAAa;QAChCC,QAAQ,EAAE0C,UAAU,CAAC1C,QAAQ;QAC7BC,MAAM,EAAEyC,UAAU,CAACzC,MAAM;QACzBV,OAAO,EAAE;UAAEY,QAAQ,EAAEuC,UAAU,CAACvC;QAAQ,CAAE;QAC1C4B,MAAM,EAAE,IAAI,CAACjF,cAAc;QAC3BkG,MAAM,EAAE,SAAS;QACjB;QACAC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAC;OACnB;MAER,IAAI,CAACnE,mBAAmB,CAACoE,gBAAgB,CAACf,WAAW,CAAC,CAACnC,SAAS,CAC7DiB,QAAQ,IAAI;QACX1G,IAAI,CAACgG,IAAI,CAAC;UACRhE,KAAK,EAAE,UAAU;UACjBiE,IAAI,EAAE,kCAAkC;UACxCC,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE;SACpB,CAAC,CAACyC,IAAI,CAAC,MAAK;UACX,IAAI,CAACtE,MAAM,CAACuE,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACzC,CAAC,CAAC;MACJ,CAAC,EACAnD,KAAK,IAAI;QACRC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD1F,IAAI,CAACgG,IAAI,CAAC;UACRhE,KAAK,EAAE,QAAQ;UACfiE,IAAI,EAAE,+CAA+C;UACrDC,IAAI,EAAE,OAAO;UACbC,iBAAiB,EAAE;SACpB,CAAC;MACJ,CAAC,CACF;KACF,MAAM;MACL,IAAI,CAACvF,eAAe,CAACkI,gBAAgB,EAAE;MACvC,IAAI,CAAC,IAAI,CAAC/G,cAAc,EAAE;QACxB/B,IAAI,CAACgG,IAAI,CAAC;UACRhE,KAAK,EAAE,oBAAoB;UAC3BiE,IAAI,EAAE,uCAAuC;UAC7CC,IAAI,EAAE,SAAS;UACfC,iBAAiB,EAAE;SACpB,CAAC;;;EAGR;EAAC,QAAA4C,CAAA,G;qBA3OU5E,2BAA2B,EAAAlE,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjJ,EAAA,CAAA+I,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnJ,EAAA,CAAA+I,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BpF,2BAA2B;IAAAqF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC7BxC7J,EAAA,CAAAiE,SAAA,4BAAmD;QAEnDjE,EAAA,CAAAC,cAAA,aAA2C;QAEnCD,EAAA,CAAAE,MAAA,4BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9BH,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,iFAA0E;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGnFH,EAAA,CAAAC,cAAA,cAAqF;QAAjDD,EAAA,CAAAsC,UAAA,sBAAAyH,8DAAA;UAAA,OAAYD,GAAA,CAAAtC,QAAA,EAAU;QAAA,EAAC;QAEzDxH,EAAA,CAAAC,cAAA,aAA0B;QACpBD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,cAAiB;QAE6BD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9DH,EAAA,CAAAiE,SAAA,gBAK8G;QAC9GjE,EAAA,CAAAI,UAAA,KAAA4J,2CAAA,iBAGM;QACRhK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACqBD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5DH,EAAA,CAAAiE,SAAA,iBAK4G;QAC5GjE,EAAA,CAAAI,UAAA,KAAA6J,2CAAA,iBAGM;QACRjK,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAEyBD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrDH,EAAA,CAAAiE,SAAA,iBAKsG;QACtGjE,EAAA,CAAAI,UAAA,KAAA8J,2CAAA,iBAGM;QACRlK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QACsBD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpEH,EAAA,CAAAiE,SAAA,iBAK8G;QAC9GjE,EAAA,CAAAI,UAAA,KAAA+J,2CAAA,iBAGM;QACRnK,EAAA,CAAAG,YAAA,EAAM;QAKVH,EAAA,CAAAC,cAAA,cAA0B;QACpBD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC5BH,EAAA,CAAAC,cAAA,cAAiB;QAEoCD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC5EH,EAAA,CAAAC,cAAA,kBAI4H;QACzGD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC1CH,EAAA,CAAAI,UAAA,KAAAgK,8CAAA,qBAES;QACXpK,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAI,UAAA,KAAAiK,2CAAA,iBAEM;QACRrK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAsB;QAC0BD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtEH,EAAA,CAAAC,cAAA,iBAOsH;QADpHD,EAAA,CAAAsC,UAAA,oBAAAgI,8DAAA;UAAA,OAAUR,GAAA,CAAAlE,YAAA,EAAc;QAAA,EAAC;QAN3B5F,EAAA,CAAAG,YAAA,EAOsH;QACtHH,EAAA,CAAAI,UAAA,KAAAmK,2CAAA,iBAEM;QACRvK,EAAA,CAAAG,YAAA,EAAM;QAIRH,EAAA,CAAAI,UAAA,KAAAoK,2CAAA,mBASM;QACRxK,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAA0B;QAC6BD,EAAA,CAAAsC,UAAA,mBAAAmI,8DAAA;UAAA,OAASX,GAAA,CAAAzF,MAAA,CAAAuE,QAAA,EAAiB,eAAe,EAAE;QAAA,EAAC;QAC/F5I,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAsG;QACpGD,EAAA,CAAAE,MAAA,0BACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAMfH,EAAA,CAAAI,UAAA,KAAAsK,2CAAA,mBAkCM;QACN1K,EAAA,CAAAI,UAAA,KAAAuK,2CAAA,kBAAoE;;;;;;;;;;;;;;;QAnK5D3K,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAQ,UAAA,cAAAsJ,GAAA,CAAAnJ,eAAA,CAA6B;QAYzBX,EAAA,CAAAO,SAAA,GAA2G;QAA3GP,EAAA,CAAA4K,WAAA,iBAAA9J,OAAA,GAAAgJ,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAA+J,OAAA,OAAA/J,OAAA,GAAAgJ,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAAgK,OAAA,EAA2G;QAC9E9K,EAAA,CAAAO,SAAA,GAA4F;QAA5FP,EAAA,CAAAQ,UAAA,WAAAuK,OAAA,GAAAjB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAmK,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAjB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAmK,OAAA,CAAAD,OAAA,EAA4F;QAazH9K,EAAA,CAAAO,SAAA,GAAyG;QAAzGP,EAAA,CAAA4K,WAAA,iBAAAI,OAAA,GAAAlB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,+BAAAoK,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAlB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,+BAAAoK,OAAA,CAAAF,OAAA,EAAyG;QAC5E9K,EAAA,CAAAO,SAAA,GAA0F;QAA1FP,EAAA,CAAAQ,UAAA,WAAAyK,OAAA,GAAAnB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,+BAAAqK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAnB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,+BAAAqK,OAAA,CAAAH,OAAA,EAA0F;QAevH9K,EAAA,CAAAO,SAAA,GAAmG;QAAnGP,EAAA,CAAA4K,WAAA,iBAAAM,OAAA,GAAApB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,4BAAAsK,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAApB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,4BAAAsK,OAAA,CAAAJ,OAAA,EAAmG;QACtE9K,EAAA,CAAAO,SAAA,GAAoF;QAApFP,EAAA,CAAAQ,UAAA,WAAA2K,OAAA,GAAArB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,4BAAAuK,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAArB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,4BAAAuK,OAAA,CAAAL,OAAA,EAAoF;QAajH9K,EAAA,CAAAO,SAAA,GAA2G;QAA3GP,EAAA,CAAA4K,WAAA,iBAAAQ,OAAA,GAAAtB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAwK,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAtB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAwK,OAAA,CAAAN,OAAA,EAA2G;QAC9E9K,EAAA,CAAAO,SAAA,GAA4F;QAA5FP,EAAA,CAAAQ,UAAA,WAAA6K,OAAA,GAAAvB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAyK,OAAA,CAAAR,OAAA,OAAAQ,OAAA,GAAAvB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,gCAAAyK,OAAA,CAAAP,OAAA,EAA4F;QAkBzH9K,EAAA,CAAAO,SAAA,GAAyH;QAAzHP,EAAA,CAAA4K,WAAA,iBAAAU,OAAA,GAAAxB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,uCAAA0K,OAAA,CAAAT,OAAA,OAAAS,OAAA,GAAAxB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,uCAAA0K,OAAA,CAAAR,OAAA,EAAyH;QAE7F9K,EAAA,CAAAO,SAAA,GAAW;QAAXP,EAAA,CAAAQ,UAAA,YAAAsJ,GAAA,CAAAtF,QAAA,CAAW;QAIVxE,EAAA,CAAAO,SAAA,GAA0G;QAA1GP,EAAA,CAAAQ,UAAA,WAAA+K,QAAA,GAAAzB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,uCAAA2K,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAAzB,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,uCAAA2K,QAAA,CAAAT,OAAA,EAA0G;QAcvI9K,EAAA,CAAAO,SAAA,GAAmH;QAAnHP,EAAA,CAAA4K,WAAA,iBAAAY,QAAA,GAAA1B,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,oCAAA4K,QAAA,CAAAX,OAAA,OAAAW,QAAA,GAAA1B,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,oCAAA4K,QAAA,CAAAV,OAAA,EAAmH;QAFnH9K,EAAA,CAAAQ,UAAA,QAAAsJ,GAAA,CAAA3C,YAAA,GAAsB;QAGOnH,EAAA,CAAAO,SAAA,GAAoG;QAApGP,EAAA,CAAAQ,UAAA,WAAAiL,QAAA,GAAA3B,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,oCAAA6K,QAAA,CAAAZ,OAAA,OAAAY,QAAA,GAAA3B,GAAA,CAAAnJ,eAAA,CAAAC,GAAA,oCAAA6K,QAAA,CAAAX,OAAA,EAAoG;QAOrH9K,EAAA,CAAAO,SAAA,GAAwC;QAAxCP,EAAA,CAAAQ,UAAA,SAAAsJ,GAAA,CAAAhI,cAAA,IAAAgI,GAAA,CAAA5H,gBAAA,CAAwC;QAiBZlC,EAAA,CAAAO,SAAA,GAAuD;QAAvDP,EAAA,CAAAQ,UAAA,aAAAsJ,GAAA,CAAAnJ,eAAA,CAAAkK,OAAA,KAAAf,GAAA,CAAAhI,cAAA,CAAuD;QAQV9B,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAQ,UAAA,SAAAsJ,GAAA,CAAAhG,eAAA,CAAqB;QAmC/E9D,EAAA,CAAAO,SAAA,GAAqB;QAArBP,EAAA,CAAAQ,UAAA,SAAAsJ,GAAA,CAAAhG,eAAA,CAAqB;;;;;;SD9I/CI,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}