package com.navitsa.mydent.controller;

import com.navitsa.mydent.entity.UserCategory;
import com.navitsa.mydent.services.UserCategoryService;
import com.navitsa.mydent.services.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpStatus;
import java.util.Map;
import java.util.HashMap;


import java.util.List;

@RestController
public class UserCategoryController {

    private final UserCategoryService userCategoryService;
    private final UserService userService;

    @Autowired
    public UserCategoryController(UserCategoryService userCategoryService, UserService userService) {
        this.userCategoryService = userCategoryService;
        this.userService = userService;
    }

    @GetMapping("/getUserCategoryById/{id}")
    public UserCategory getUserCategoryById(@PathVariable int id) {
        return userCategoryService.getUserCategoryById(id);
    }

    @GetMapping("/userCategoryList")
    public List<UserCategory> getAllUserCategory() {
        return userCategoryService.findAllUserCategories();
    }

    @GetMapping("/verify")
    public ResponseEntity<Map<String, String>> verifyEmail(@RequestParam String token) {
        Map<String, String> response = new HashMap<>();
        try {
            String message = userService.verifyEmail(token);

            response.put("message", message); 
            return ResponseEntity.ok(response);  
        } catch (Exception e) {
            response.put("error", "An unexpected error occurred.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response); 
        }
    }



}
