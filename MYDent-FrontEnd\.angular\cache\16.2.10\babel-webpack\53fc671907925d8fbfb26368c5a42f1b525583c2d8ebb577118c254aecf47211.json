{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT } from '@angular/common';\n\n// This service is based on the `ng2-cookies` package which sadly is not a service and does\n// not use `DOCUMENT` injection and therefore doesn't work well with AoT production builds.\n// Package: https://github.com/BCJTI/ng2-cookies\nclass CookieService {\n  constructor(document,\n  // Get the `PLATFORM_ID` so we can check if we're in a browser.\n  platformId) {\n    this.document = document;\n    this.platformId = platformId;\n    this.documentIsAccessible = isPlatformBrowser(this.platformId);\n  }\n  /**\n   * Get cookie Regular Expression\n   *\n   * @param name Cookie name\n   * @returns property RegExp\n   *\n   * @author: <PERSON>an <PERSON>\n   * @since: 1.0.0\n   */\n  static getCookieRegExp(name) {\n    const escapedName = name.replace(/([\\[\\]{}()|=;+?,.*^$])/gi, '\\\\$1');\n    return new RegExp('(?:^' + escapedName + '|;\\\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');\n  }\n  /**\n   * Gets the unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @param encodedURIComponent A value representing an encoded URI component.\n   *\n   * @returns The unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  static safeDecodeURIComponent(encodedURIComponent) {\n    try {\n      return decodeURIComponent(encodedURIComponent);\n    } catch {\n      // probably it is not uri encoded. return as is\n      return encodedURIComponent;\n    }\n  }\n  /**\n   * Return `true` if {@link Document} is accessible, otherwise return `false`\n   *\n   * @param name Cookie name\n   * @returns boolean - whether cookie with specified name exists\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  check(name) {\n    if (!this.documentIsAccessible) {\n      return false;\n    }\n    name = encodeURIComponent(name);\n    const regExp = CookieService.getCookieRegExp(name);\n    return regExp.test(this.document.cookie);\n  }\n  /**\n   * Get cookies by name\n   *\n   * @param name Cookie name\n   * @returns property value\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  get(name) {\n    if (this.documentIsAccessible && this.check(name)) {\n      name = encodeURIComponent(name);\n      const regExp = CookieService.getCookieRegExp(name);\n      const result = regExp.exec(this.document.cookie);\n      return result[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';\n    } else {\n      return '';\n    }\n  }\n  /**\n   * Get all cookies in JSON format\n   *\n   * @returns all the cookies in json\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  getAll() {\n    if (!this.documentIsAccessible) {\n      return {};\n    }\n    const cookies = {};\n    const document = this.document;\n    if (document.cookie && document.cookie !== '') {\n      document.cookie.split(';').forEach(currentCookie => {\n        const [cookieName, cookieValue] = currentCookie.split('=');\n        cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);\n      });\n    }\n    return cookies;\n  }\n  set(name, value, expiresOrOptions, path, domain, secure, sameSite) {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {\n      const optionsBody = {\n        expires: expiresOrOptions,\n        path,\n        domain,\n        secure,\n        sameSite: sameSite ? sameSite : 'Lax'\n      };\n      this.set(name, value, optionsBody);\n      return;\n    }\n    let cookieString = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';\n    const options = expiresOrOptions ? expiresOrOptions : {};\n    if (options.expires) {\n      if (typeof options.expires === 'number') {\n        const dateExpires = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);\n        cookieString += 'expires=' + dateExpires.toUTCString() + ';';\n      } else {\n        cookieString += 'expires=' + options.expires.toUTCString() + ';';\n      }\n    }\n    if (options.path) {\n      cookieString += 'path=' + options.path + ';';\n    }\n    if (options.domain) {\n      cookieString += 'domain=' + options.domain + ';';\n    }\n    if (options.secure === false && options.sameSite === 'None') {\n      options.secure = true;\n      console.warn(`[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` + `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`);\n    }\n    if (options.secure) {\n      cookieString += 'secure;';\n    }\n    if (!options.sameSite) {\n      options.sameSite = 'Lax';\n    }\n    cookieString += 'sameSite=' + options.sameSite + ';';\n    this.document.cookie = cookieString;\n  }\n  /**\n   * Delete cookie by name\n   *\n   * @param name   Cookie name\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Cookie secure flag\n   * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  delete(name, path, domain, secure, sameSite = 'Lax') {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');\n    this.set(name, '', {\n      expires: expiresDate,\n      path,\n      domain,\n      secure,\n      sameSite\n    });\n  }\n  /**\n   * Delete all cookies\n   *\n   * @param path   Cookie path\n   * @param domain Cookie domain\n   * @param secure Is the Cookie secure\n   * @param sameSite Is the cookie same site\n   *\n   * @author: Stepan Suvorov\n   * @since: 1.0.0\n   */\n  deleteAll(path, domain, secure, sameSite = 'Lax') {\n    if (!this.documentIsAccessible) {\n      return;\n    }\n    const cookies = this.getAll();\n    for (const cookieName in cookies) {\n      if (cookies.hasOwnProperty(cookieName)) {\n        this.delete(cookieName, path, domain, secure, sameSite);\n      }\n    }\n  }\n  static #_ = this.ɵfac = function CookieService_Factory(t) {\n    return new (t || CookieService)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: CookieService,\n    factory: CookieService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CookieService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Document,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n\n/*\n * Public API Surface of ngx-cookie-service\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CookieService };", "map": {"version": 3, "names": ["i0", "PLATFORM_ID", "Injectable", "Inject", "isPlatformBrowser", "DOCUMENT", "CookieService", "constructor", "document", "platformId", "documentIsAccessible", "getCookieRegExp", "name", "<PERSON><PERSON><PERSON>", "replace", "RegExp", "safeDecodeURIComponent", "encodedURIComponent", "decodeURIComponent", "check", "encodeURIComponent", "regExp", "test", "cookie", "get", "result", "exec", "getAll", "cookies", "split", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "cookieName", "cookieValue", "set", "value", "expiresOrOptions", "path", "domain", "secure", "sameSite", "Date", "optionsBody", "expires", "cookieString", "options", "dateExpires", "getTime", "toUTCString", "console", "warn", "delete", "expiresDate", "deleteAll", "hasOwnProperty", "_", "ɵfac", "CookieService_Factory", "t", "ɵɵinject", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "Document", "decorators", "undefined"], "sources": ["E:/Github/MYDent-FrontEnd/node_modules/ngx-cookie-service/fesm2022/ngx-cookie-service.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject } from '@angular/core';\nimport { isPlatformBrowser, DOCUMENT } from '@angular/common';\n\n// This service is based on the `ng2-cookies` package which sadly is not a service and does\n// not use `DOCUMENT` injection and therefore doesn't work well with AoT production builds.\n// Package: https://github.com/BCJTI/ng2-cookies\nclass CookieService {\n    constructor(document, \n    // Get the `PLATFORM_ID` so we can check if we're in a browser.\n    platformId) {\n        this.document = document;\n        this.platformId = platformId;\n        this.documentIsAccessible = isPlatformBrowser(this.platformId);\n    }\n    /**\n     * Get cookie Regular Expression\n     *\n     * @param name Cookie name\n     * @returns property RegExp\n     *\n     * @author: <PERSON>an <PERSON>\n     * @since: 1.0.0\n     */\n    static getCookieRegExp(name) {\n        const escapedName = name.replace(/([\\[\\]{}()|=;+?,.*^$])/gi, '\\\\$1');\n        return new RegExp('(?:^' + escapedName + '|;\\\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');\n    }\n    /**\n     * Gets the unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n     *\n     * @param encodedURIComponent A value representing an encoded URI component.\n     *\n     * @returns The unencoded version of an encoded component of a Uniform Resource Identifier (URI).\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    static safeDecodeURIComponent(encodedURIComponent) {\n        try {\n            return decodeURIComponent(encodedURIComponent);\n        }\n        catch {\n            // probably it is not uri encoded. return as is\n            return encodedURIComponent;\n        }\n    }\n    /**\n     * Return `true` if {@link Document} is accessible, otherwise return `false`\n     *\n     * @param name Cookie name\n     * @returns boolean - whether cookie with specified name exists\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    check(name) {\n        if (!this.documentIsAccessible) {\n            return false;\n        }\n        name = encodeURIComponent(name);\n        const regExp = CookieService.getCookieRegExp(name);\n        return regExp.test(this.document.cookie);\n    }\n    /**\n     * Get cookies by name\n     *\n     * @param name Cookie name\n     * @returns property value\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    get(name) {\n        if (this.documentIsAccessible && this.check(name)) {\n            name = encodeURIComponent(name);\n            const regExp = CookieService.getCookieRegExp(name);\n            const result = regExp.exec(this.document.cookie);\n            return result[1] ? CookieService.safeDecodeURIComponent(result[1]) : '';\n        }\n        else {\n            return '';\n        }\n    }\n    /**\n     * Get all cookies in JSON format\n     *\n     * @returns all the cookies in json\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    getAll() {\n        if (!this.documentIsAccessible) {\n            return {};\n        }\n        const cookies = {};\n        const document = this.document;\n        if (document.cookie && document.cookie !== '') {\n            document.cookie.split(';').forEach((currentCookie) => {\n                const [cookieName, cookieValue] = currentCookie.split('=');\n                cookies[CookieService.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = CookieService.safeDecodeURIComponent(cookieValue);\n            });\n        }\n        return cookies;\n    }\n    set(name, value, expiresOrOptions, path, domain, secure, sameSite) {\n        if (!this.documentIsAccessible) {\n            return;\n        }\n        if (typeof expiresOrOptions === 'number' || expiresOrOptions instanceof Date || path || domain || secure || sameSite) {\n            const optionsBody = {\n                expires: expiresOrOptions,\n                path,\n                domain,\n                secure,\n                sameSite: sameSite ? sameSite : 'Lax',\n            };\n            this.set(name, value, optionsBody);\n            return;\n        }\n        let cookieString = encodeURIComponent(name) + '=' + encodeURIComponent(value) + ';';\n        const options = expiresOrOptions ? expiresOrOptions : {};\n        if (options.expires) {\n            if (typeof options.expires === 'number') {\n                const dateExpires = new Date(new Date().getTime() + options.expires * 1000 * 60 * 60 * 24);\n                cookieString += 'expires=' + dateExpires.toUTCString() + ';';\n            }\n            else {\n                cookieString += 'expires=' + options.expires.toUTCString() + ';';\n            }\n        }\n        if (options.path) {\n            cookieString += 'path=' + options.path + ';';\n        }\n        if (options.domain) {\n            cookieString += 'domain=' + options.domain + ';';\n        }\n        if (options.secure === false && options.sameSite === 'None') {\n            options.secure = true;\n            console.warn(`[ngx-cookie-service] Cookie ${name} was forced with secure flag because sameSite=None.` +\n                `More details : https://github.com/stevermeister/ngx-cookie-service/issues/86#issuecomment-597720130`);\n        }\n        if (options.secure) {\n            cookieString += 'secure;';\n        }\n        if (!options.sameSite) {\n            options.sameSite = 'Lax';\n        }\n        cookieString += 'sameSite=' + options.sameSite + ';';\n        this.document.cookie = cookieString;\n    }\n    /**\n     * Delete cookie by name\n     *\n     * @param name   Cookie name\n     * @param path   Cookie path\n     * @param domain Cookie domain\n     * @param secure Cookie secure flag\n     * @param sameSite Cookie sameSite flag - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Set-Cookie/SameSite\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    delete(name, path, domain, secure, sameSite = 'Lax') {\n        if (!this.documentIsAccessible) {\n            return;\n        }\n        const expiresDate = new Date('Thu, 01 Jan 1970 00:00:01 GMT');\n        this.set(name, '', { expires: expiresDate, path, domain, secure, sameSite });\n    }\n    /**\n     * Delete all cookies\n     *\n     * @param path   Cookie path\n     * @param domain Cookie domain\n     * @param secure Is the Cookie secure\n     * @param sameSite Is the cookie same site\n     *\n     * @author: Stepan Suvorov\n     * @since: 1.0.0\n     */\n    deleteAll(path, domain, secure, sameSite = 'Lax') {\n        if (!this.documentIsAccessible) {\n            return;\n        }\n        const cookies = this.getAll();\n        for (const cookieName in cookies) {\n            if (cookies.hasOwnProperty(cookieName)) {\n                this.delete(cookieName, path, domain, secure, sameSite);\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.1\", ngImport: i0, type: CookieService, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.1\", ngImport: i0, type: CookieService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.1\", ngImport: i0, type: CookieService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\n/*\n * Public API Surface of ngx-cookie-service\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CookieService };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,WAAW,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAC/D,SAASC,iBAAiB,EAAEC,QAAQ,QAAQ,iBAAiB;;AAE7D;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,QAAQ;EACpB;EACAC,UAAU,EAAE;IACR,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,oBAAoB,GAAGN,iBAAiB,CAAC,IAAI,CAACK,UAAU,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOE,eAAeA,CAACC,IAAI,EAAE;IACzB,MAAMC,WAAW,GAAGD,IAAI,CAACE,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC;IACpE,OAAO,IAAIC,MAAM,CAAC,MAAM,GAAGF,WAAW,GAAG,QAAQ,GAAGA,WAAW,GAAG,gBAAgB,EAAE,GAAG,CAAC;EAC5F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOG,sBAAsBA,CAACC,mBAAmB,EAAE;IAC/C,IAAI;MACA,OAAOC,kBAAkB,CAACD,mBAAmB,CAAC;IAClD,CAAC,CACD,MAAM;MACF;MACA,OAAOA,mBAAmB;IAC9B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,KAAKA,CAACP,IAAI,EAAE;IACR,IAAI,CAAC,IAAI,CAACF,oBAAoB,EAAE;MAC5B,OAAO,KAAK;IAChB;IACAE,IAAI,GAAGQ,kBAAkB,CAACR,IAAI,CAAC;IAC/B,MAAMS,MAAM,GAAGf,aAAa,CAACK,eAAe,CAACC,IAAI,CAAC;IAClD,OAAOS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACd,QAAQ,CAACe,MAAM,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACZ,IAAI,EAAE;IACN,IAAI,IAAI,CAACF,oBAAoB,IAAI,IAAI,CAACS,KAAK,CAACP,IAAI,CAAC,EAAE;MAC/CA,IAAI,GAAGQ,kBAAkB,CAACR,IAAI,CAAC;MAC/B,MAAMS,MAAM,GAAGf,aAAa,CAACK,eAAe,CAACC,IAAI,CAAC;MAClD,MAAMa,MAAM,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACe,MAAM,CAAC;MAChD,OAAOE,MAAM,CAAC,CAAC,CAAC,GAAGnB,aAAa,CAACU,sBAAsB,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;IAC3E,CAAC,MACI;MACD,OAAO,EAAE;IACb;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACjB,oBAAoB,EAAE;MAC5B,OAAO,CAAC,CAAC;IACb;IACA,MAAMkB,OAAO,GAAG,CAAC,CAAC;IAClB,MAAMpB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIA,QAAQ,CAACe,MAAM,IAAIf,QAAQ,CAACe,MAAM,KAAK,EAAE,EAAE;MAC3Cf,QAAQ,CAACe,MAAM,CAACM,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAEC,aAAa,IAAK;QAClD,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAGF,aAAa,CAACF,KAAK,CAAC,GAAG,CAAC;QAC1DD,OAAO,CAACtB,aAAa,CAACU,sBAAsB,CAACgB,UAAU,CAAClB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,GAAGR,aAAa,CAACU,sBAAsB,CAACiB,WAAW,CAAC;MACnI,CAAC,CAAC;IACN;IACA,OAAOL,OAAO;EAClB;EACAM,GAAGA,CAACtB,IAAI,EAAEuB,KAAK,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAC/D,IAAI,CAAC,IAAI,CAAC9B,oBAAoB,EAAE;MAC5B;IACJ;IACA,IAAI,OAAO0B,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,YAAYK,IAAI,IAAIJ,IAAI,IAAIC,MAAM,IAAIC,MAAM,IAAIC,QAAQ,EAAE;MAClH,MAAME,WAAW,GAAG;QAChBC,OAAO,EAAEP,gBAAgB;QACzBC,IAAI;QACJC,MAAM;QACNC,MAAM;QACNC,QAAQ,EAAEA,QAAQ,GAAGA,QAAQ,GAAG;MACpC,CAAC;MACD,IAAI,CAACN,GAAG,CAACtB,IAAI,EAAEuB,KAAK,EAAEO,WAAW,CAAC;MAClC;IACJ;IACA,IAAIE,YAAY,GAAGxB,kBAAkB,CAACR,IAAI,CAAC,GAAG,GAAG,GAAGQ,kBAAkB,CAACe,KAAK,CAAC,GAAG,GAAG;IACnF,MAAMU,OAAO,GAAGT,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,CAAC;IACxD,IAAIS,OAAO,CAACF,OAAO,EAAE;MACjB,IAAI,OAAOE,OAAO,CAACF,OAAO,KAAK,QAAQ,EAAE;QACrC,MAAMG,WAAW,GAAG,IAAIL,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACM,OAAO,CAAC,CAAC,GAAGF,OAAO,CAACF,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QAC1FC,YAAY,IAAI,UAAU,GAAGE,WAAW,CAACE,WAAW,CAAC,CAAC,GAAG,GAAG;MAChE,CAAC,MACI;QACDJ,YAAY,IAAI,UAAU,GAAGC,OAAO,CAACF,OAAO,CAACK,WAAW,CAAC,CAAC,GAAG,GAAG;MACpE;IACJ;IACA,IAAIH,OAAO,CAACR,IAAI,EAAE;MACdO,YAAY,IAAI,OAAO,GAAGC,OAAO,CAACR,IAAI,GAAG,GAAG;IAChD;IACA,IAAIQ,OAAO,CAACP,MAAM,EAAE;MAChBM,YAAY,IAAI,SAAS,GAAGC,OAAO,CAACP,MAAM,GAAG,GAAG;IACpD;IACA,IAAIO,OAAO,CAACN,MAAM,KAAK,KAAK,IAAIM,OAAO,CAACL,QAAQ,KAAK,MAAM,EAAE;MACzDK,OAAO,CAACN,MAAM,GAAG,IAAI;MACrBU,OAAO,CAACC,IAAI,CAAE,+BAA8BtC,IAAK,qDAAoD,GAChG,qGAAoG,CAAC;IAC9G;IACA,IAAIiC,OAAO,CAACN,MAAM,EAAE;MAChBK,YAAY,IAAI,SAAS;IAC7B;IACA,IAAI,CAACC,OAAO,CAACL,QAAQ,EAAE;MACnBK,OAAO,CAACL,QAAQ,GAAG,KAAK;IAC5B;IACAI,YAAY,IAAI,WAAW,GAAGC,OAAO,CAACL,QAAQ,GAAG,GAAG;IACpD,IAAI,CAAChC,QAAQ,CAACe,MAAM,GAAGqB,YAAY;EACvC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,MAAMA,CAACvC,IAAI,EAAEyB,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACjD,IAAI,CAAC,IAAI,CAAC9B,oBAAoB,EAAE;MAC5B;IACJ;IACA,MAAM0C,WAAW,GAAG,IAAIX,IAAI,CAAC,+BAA+B,CAAC;IAC7D,IAAI,CAACP,GAAG,CAACtB,IAAI,EAAE,EAAE,EAAE;MAAE+B,OAAO,EAAES,WAAW;MAAEf,IAAI;MAAEC,MAAM;MAAEC,MAAM;MAAEC;IAAS,CAAC,CAAC;EAChF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIa,SAASA,CAAChB,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAE;IAC9C,IAAI,CAAC,IAAI,CAAC9B,oBAAoB,EAAE;MAC5B;IACJ;IACA,MAAMkB,OAAO,GAAG,IAAI,CAACD,MAAM,CAAC,CAAC;IAC7B,KAAK,MAAMK,UAAU,IAAIJ,OAAO,EAAE;MAC9B,IAAIA,OAAO,CAAC0B,cAAc,CAACtB,UAAU,CAAC,EAAE;QACpC,IAAI,CAACmB,MAAM,CAACnB,UAAU,EAAEK,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,CAAC;MAC3D;IACJ;EACJ;EAAC,QAAAe,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFpD,aAAa,EAAvBN,EAAE,CAAA2D,QAAA,CAAuCtD,QAAQ,GAAjDL,EAAE,CAAA2D,QAAA,CAA4D1D,WAAW;EAAA,CAA6C;EAAA,QAAA2D,EAAA,GAC7M,IAAI,CAACC,KAAK,kBAD6E7D,EAAE,CAAA8D,kBAAA;IAAAC,KAAA,EACYzD,aAAa;IAAA0D,OAAA,EAAb1D,aAAa,CAAAkD,IAAA;IAAAS,UAAA,EAAc;EAAM,EAAG;AACtJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGlE,EAAE,CAAAmE,iBAAA,CAGX7D,aAAa,EAAc,CAAC;IAC3G8D,IAAI,EAAElE,UAAU;IAChBmE,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAEE,QAAQ;MAAEC,UAAU,EAAE,CAAC;QAC7DH,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAAChE,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE+D,IAAI,EAAEI,SAAS;MAAED,UAAU,EAAE,CAAC;QAClCH,IAAI,EAAEjE,MAAM;QACZkE,IAAI,EAAE,CAACpE,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASK,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}