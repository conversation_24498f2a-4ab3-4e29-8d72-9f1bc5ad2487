package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Table(name = "clinic_schedule")
public class ClinicSchedule {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "schedule_id")
    private Integer scheduleId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "clinic_id", referencedColumnName = "clinic_id")
    private Clinic clinics;

    @Column(name= "date")
    private String date;

    @Column(name= "from_time")
    private String fromTime;

    @Column(name= "to_time")
    private String toTime;

    @Column (name = "holiday_Date")
    private Boolean holidayDate;

    public ClinicSchedule(){

    }

    public ClinicSchedule(Integer scheduleId,Clinic clinics , String date,String fromTime,String toTime, Boolean holidayDate){
        super();
        this.scheduleId = scheduleId;
        this.clinics = clinics;
        this.date = date;
        this.fromTime = fromTime;
        this.toTime = toTime;
        this.holidayDate = holidayDate;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public Clinic getClinics() {
        return clinics;
    }

    public void setClinics(Clinic clinics) {
        this.clinics = clinics;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getFromTime() {
        return fromTime;
    }

    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    public String getToTime() {
        return toTime;
    }

    public void setToTime(String toTime) {
        this.toTime = toTime;
    }

    public Boolean getHolidayDate() {
        return holidayDate;
    }

    public void setHolidayDate(Boolean holidayDate) {
        this.holidayDate = holidayDate;
    }
}
