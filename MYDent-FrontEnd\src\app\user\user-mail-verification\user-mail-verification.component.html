<div class="page-background">
  <div class="rec1"></div>
  <div class="rec2"></div>
  <div class="verification-container">
    <div class="verification-content">
      <h2>{{ userType }} Email Verification</h2>

      <p *ngIf="loading">Loading...</p>
      
      <p *ngIf="!loading">
        <ng-container *ngIf="message === 'Email verified successfully!'">
          Thank you for verifying your email!<br>
          You can now access your account and start using all the features of our platform.
        </ng-container>
        
        <ng-container *ngIf="message === 'Your email is already verified.'">
          Your email is already verified.<br>
          You can now access your account and start using all the features of our platform.
        </ng-container>
        
        <ng-container *ngIf="message !== 'Email verified successfully!' && message !== 'Your email is already verified.'">
          {{ message }}
        </ng-container>
      </p>
      
      <button *ngIf="message === 'Email verified successfully!' || message === 'Your email is already verified.'" mat-raised-button (click)="goToLogin()">
        Go to Login
      </button>
    </div>
  </div>
</div>
