{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { LaboratorySetup } from '../laboratory';\nimport Swal from 'sweetalert2';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../laboratory.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction AddLaboratoryServicesComponent_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const laboratoryCategory_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", laboratoryCategory_r8);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", laboratoryCategory_r8.laboratoryCategoryName, \" \");\n  }\n}\nfunction AddLaboratoryServicesComponent_div_24_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \" Category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddLaboratoryServicesComponent_div_24_small_1_Template, 2, 0, \"small\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.laboratorySetupForm.get(\"category\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AddLaboratoryServicesComponent_option_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const laboratorySubCategory_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", laboratorySubCategory_r10);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", laboratorySubCategory_r10.laboratorySubCategoryName, \" \");\n  }\n}\nfunction AddLaboratoryServicesComponent_div_30_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \" Sub category is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddLaboratoryServicesComponent_div_30_small_1_Template, 2, 0, \"small\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.laboratorySetupForm.get(\"subCategory\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AddLaboratoryServicesComponent_div_34_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \" Description is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddLaboratoryServicesComponent_div_34_small_1_Template, 2, 0, \"small\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r4.laboratorySetupForm.get(\"description\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction AddLaboratoryServicesComponent_div_38_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \"Price is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_div_38_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \"Please enter a valid price.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AddLaboratoryServicesComponent_div_38_small_1_Template, 2, 0, \"small\", 30);\n    i0.ɵɵtemplate(2, AddLaboratoryServicesComponent_div_38_small_2_Template, 2, 0, \"small\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.laboratorySetupForm.get(\"price\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r5.laboratorySetupForm.get(\"price\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction AddLaboratoryServicesComponent_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Saving...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AddLaboratoryServicesComponent_span_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Add Service\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass AddLaboratoryServicesComponent {\n  constructor(fb, laboratoryService, router) {\n    this.fb = fb;\n    this.laboratoryService = laboratoryService;\n    this.router = router;\n    this.searchTerm = '';\n    this.laboratorySetup = new LaboratorySetup();\n    this.laboratoryCategories = [];\n    this.laboratorySubCategories = [];\n    this.isLoading = false; // Loading state variable\n    this.laboratorySetupForm = this.fb.group({\n      category: [null, Validators.required],\n      subCategory: [null, Validators.required],\n      description: ['', Validators.required],\n      price: ['', [Validators.required, Validators.pattern('^\\\\d+(\\\\.\\\\d{1,2})?$')]]\n    });\n  }\n  ngOnInit() {\n    this.loadLaboratoryCategories();\n  }\n  loadLaboratoryCategories() {\n    this.laboratoryService.getLaboratoryCategoriesList().subscribe(data => {\n      console.log('Received categories:', data);\n      this.laboratoryCategories = data;\n    }, error => {\n      console.error('Error fetching laboratory categories:', error);\n    });\n  }\n  onCategoryChange() {\n    const selectedCategory = this.laboratorySetupForm.get('category')?.value;\n    if (selectedCategory) {\n      this.getLaboratorySubCategories(selectedCategory.laboratoryCategoryId);\n    } else {\n      this.laboratorySubCategories = [];\n    }\n  }\n  getLaboratorySubCategories(categoryId) {\n    if (categoryId) {\n      this.laboratoryService.getLaboratorySubCategoriesList(categoryId).subscribe(data => {\n        this.laboratorySubCategories = data;\n      }, error => {\n        console.error('Error fetching subcategories:', error);\n      });\n    } else {\n      this.laboratorySubCategories = [];\n    }\n  }\n  saveLaboratoryService() {\n    if (this.laboratorySetupForm.valid) {\n      this.isLoading = true;\n      this.laboratorySetup.laboratoryCategoryId = this.laboratorySetupForm.get('category')?.value;\n      this.laboratorySetup.laboratorySubCategoryId = this.laboratorySetupForm.get('subCategory')?.value;\n      this.laboratorySetup.description = this.laboratorySetupForm.get('description')?.value;\n      this.laboratorySetup.price = this.laboratorySetupForm.get('price')?.value;\n      this.laboratorySetup.status = \"Active\";\n      const userIdString = localStorage.getItem('userid');\n      const userId = userIdString ? parseInt(userIdString, 10) : null;\n      if (userId !== null) {\n        this.laboratoryService.saveLaboratorySetup(userId, this.laboratorySetup).subscribe(response => {\n          console.log('Laboratory setup saved successfully:', response);\n          this.isLoading = false;\n          Swal.fire({\n            title: 'Success!',\n            html: '<div style=\"color: #ff7a00;\"><i class=\"fas fa-check-circle fa-3x\"></i></div><p style=\"margin-top: 20px;\">Laboratory setup saved successfully.</p>',\n            confirmButtonText: 'OK',\n            confirmButtonColor: '#ff7a00'\n          }).then(() => {\n            this.laboratorySetupForm.reset();\n          });\n          this.router.navigate(['/add-laboratory-services']);\n        }, error => {\n          console.error('Failed to save laboratory setup:', error);\n          Swal.fire({\n            title: 'Error!',\n            html: '<i class=\"fas fa-exclamation-triangle\" style=\"color: #B93426; font-size: 48px;\"></i><p style=\"margin-top: 20px;\">There was and error with saving laboratory setup. Please try again.</p>',\n            confirmButtonColor: '#B93426'\n          });\n          this.isLoading = false;\n        });\n      } else {\n        console.error('User ID is not available in localStorage.');\n        this.isLoading = false;\n      }\n    } else {\n      console.log('Form is invalid');\n      Swal.fire({\n        title: 'Error!',\n        html: '<i class=\"fas fa-exclamation-triangle\" style=\"color: #B93426; font-size: 48px;\"></i><p style=\"margin-top: 20px;\">There was and error with saving laboratory setup. Please try again.</p>',\n        confirmButtonColor: '#B93426'\n      });\n    }\n  }\n  static #_ = this.ɵfac = function AddLaboratoryServicesComponent_Factory(t) {\n    return new (t || AddLaboratoryServicesComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.LaboratoryService), i0.ɵɵdirectiveInject(i3.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddLaboratoryServicesComponent,\n    selectors: [[\"app-add-laboratory-services\"]],\n    decls: 46,\n    vars: 10,\n    consts: [[1, \"row\", \"bg-white\"], [1, \"col-12\"], [1, \"row\", \"g-0\", \"w-100\", \"bg-white\"], [1, \"container\", \"g-0\"], [1, \"header-row\"], [1, \"header-row-h1\"], [1, \"header-bottom-line\"], [1, \"row\"], [1, \"col\", \"d-flex\"], [1, \"add-service\"], [1, \"col\", \"d-flex\", \"justify-content-end\"], [1, \"bi\", \"bi-pencil-square\"], [1, \"rectangle\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"col-md-6\", \"d-flex\", \"flex-column\"], [\"for\", \"category\"], [\"id\", \"category\", \"name\", \"category\", \"formControlName\", \"category\", 3, \"change\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"for\", \"subCategory\"], [\"id\", \"subCategory\", \"name\", \"subCategory\", \"formControlName\", \"subCategory\"], [\"for\", \"description\", 1, \"mt-2\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Item 01\", \"formControlName\", \"description\", 1, \"description-textarea\"], [\"for\", \"price\", 1, \"mt-2\"], [\"type\", \"text\", \"id\", \"price\", \"name\", \"price\", \"placeholder\", \"LKR\", \"formControlName\", \"price\", 1, \"price-input\"], [1, \"row\", \"mt-4\"], [1, \"d-flex\", \"justify-content-end\", \"gap-3\", \"ms-auto\", \"mt-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [3, \"ngValue\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"text-danger\"]],\n    template: function AddLaboratoryServicesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵtext(6, \"Add Laboratory Services\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(7, \"div\", 6)(8, \"br\");\n        i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9);\n        i0.ɵɵtext(12, \"Add Service\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 10);\n        i0.ɵɵelement(14, \"i\", 11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(15, \"br\");\n        i0.ɵɵelementStart(16, \"div\", 12)(17, \"form\", 13);\n        i0.ɵɵlistener(\"ngSubmit\", function AddLaboratoryServicesComponent_Template_form_ngSubmit_17_listener() {\n          return ctx.saveLaboratoryService();\n        });\n        i0.ɵɵelementStart(18, \"div\", 7)(19, \"div\", 14)(20, \"label\", 15);\n        i0.ɵɵtext(21, \"Select Laboratory Category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"select\", 16);\n        i0.ɵɵlistener(\"change\", function AddLaboratoryServicesComponent_Template_select_change_22_listener() {\n          return ctx.onCategoryChange();\n        });\n        i0.ɵɵtemplate(23, AddLaboratoryServicesComponent_option_23_Template, 2, 2, \"option\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, AddLaboratoryServicesComponent_div_24_Template, 2, 1, \"div\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 14)(26, \"label\", 19);\n        i0.ɵɵtext(27, \"Select Laboratory Sub Category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"select\", 20);\n        i0.ɵɵtemplate(29, AddLaboratoryServicesComponent_option_29_Template, 2, 2, \"option\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(30, AddLaboratoryServicesComponent_div_30_Template, 2, 1, \"div\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"label\", 21);\n        i0.ɵɵtext(32, \"Description\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"textarea\", 22);\n        i0.ɵɵtemplate(34, AddLaboratoryServicesComponent_div_34_Template, 2, 1, \"div\", 18);\n        i0.ɵɵelementStart(35, \"label\", 23);\n        i0.ɵɵtext(36, \"Price\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(37, \"input\", 24);\n        i0.ɵɵtemplate(38, AddLaboratoryServicesComponent_div_38_Template, 3, 2, \"div\", 18);\n        i0.ɵɵelementStart(39, \"div\", 25)(40, \"div\", 26)(41, \"button\", 27);\n        i0.ɵɵtext(42, \"Schedule\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"button\", 28);\n        i0.ɵɵtemplate(44, AddLaboratoryServicesComponent_span_44_Template, 2, 0, \"span\", 18);\n        i0.ɵɵtemplate(45, AddLaboratoryServicesComponent_span_45_Template, 2, 0, \"span\", 18);\n        i0.ɵɵelementEnd()()()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"formGroup\", ctx.laboratorySetupForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.laboratoryCategories);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.laboratorySetupForm.get(\"category\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx.laboratorySetupForm.get(\"category\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx.laboratorySetupForm.get(\"category\")) == null ? null : tmp_2_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.laboratorySubCategories);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.laboratorySetupForm.get(\"subCategory\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx.laboratorySetupForm.get(\"subCategory\")) == null ? null : tmp_4_0.dirty) || ((tmp_4_0 = ctx.laboratorySetupForm.get(\"subCategory\")) == null ? null : tmp_4_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.laboratorySetupForm.get(\"description\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = ctx.laboratorySetupForm.get(\"description\")) == null ? null : tmp_5_0.dirty) || ((tmp_5_0 = ctx.laboratorySetupForm.get(\"description\")) == null ? null : tmp_5_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.laboratorySetupForm.get(\"price\")) == null ? null : tmp_6_0.invalid) && (((tmp_6_0 = ctx.laboratorySetupForm.get(\"price\")) == null ? null : tmp_6_0.dirty) || ((tmp_6_0 = ctx.laboratorySetupForm.get(\"price\")) == null ? null : tmp_6_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".sidebar-container[_ngcontent-%COMP%] {\\n    height: calc(100vh - 80px);\\n    width: 20%;\\n    overflow-y: auto;\\n    display: inline-block;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n    height: calc(100vh - 80px);\\n    width: 80%;\\n    overflow-y: auto;\\n    padding: 20px;\\n    display: inline-block;\\n}\\n\\n@media (max-width: 768px) {\\n    .sidebar-container[_ngcontent-%COMP%], .main-content[_ngcontent-%COMP%] {\\n        width: 100%;\\n        height: auto;\\n        display: block;\\n    }\\n}\\n\\n.header-row[_ngcontent-%COMP%] {\\n    font-weight: bold;\\n    display: flex;\\n    flex-direction: column;\\n    align-items: flex-start;\\n}\\n\\n.header-row-h1[_ngcontent-%COMP%] {\\n    color: black;\\n    font-size: 24px;\\n    font-weight: 600;\\n    line-height: 38.73px;\\n    white-space: nowrap;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n}\\n\\n.header-bottom-line[_ngcontent-%COMP%] {\\n    border-bottom: 2px solid #ccc;\\n    margin-top: 15px;\\n    width: 100%;\\n}\\n\\n.add-service[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n    color: #000;\\n}\\n\\n.rectangle[_ngcontent-%COMP%] {\\n    width: 100%;\\n    max-width: 1090px;\\n    border-radius: 10px;\\n    background: #FFFFFF;\\n    box-shadow: 0px 3px 11.9px -1px #00000040;\\n    padding: 40px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    font-weight: 500;\\n    text-align: left; \\n}\\n\\nselect[_ngcontent-%COMP%], textarea[_ngcontent-%COMP%], input[_ngcontent-%COMP%] {\\n    width: 100%; \\n    padding: 10px; \\n    font-size: 16px; \\n    color: #495057;\\n    border-radius: 4px; \\n    border: 1px solid #b3b3b3; \\n    box-sizing: border-box; \\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:focus {\\n    outline: none;\\n    border-color: #ff7a00;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n    font-weight: 600;\\n    width: 100%; \\n    max-width: 167px;\\n    height: 35px;\\n    border-radius: 18px;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n    border: 1px solid #FB751E;\\n    color: #FB751E;\\n    background: #FFFFFF;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n    border: none;\\n    color: #FFFFFF;\\n    background: linear-gradient(to right, #FB751E, #B93426);\\n}\\n\\n.row[_ngcontent-%COMP%] {\\n    width: 100%;\\n}\\n\\n.d-flex[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-direction: row;\\n    flex-wrap: wrap; \\n    justify-content: flex-start; \\n    gap: 10px;\\n}\\n\\n.d-flex[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%; \\n}\\n\\n@media (max-width: 768px) {\\n    .d-flex[_ngcontent-%COMP%] {\\n        flex-direction: column; \\n        align-items: stretch;\\n    }\\n\\n    .btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n        width: 100%;\\n        max-width: none;\\n    }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { AddLaboratoryServicesComponent };", "map": {"version": 3, "names": ["Validators", "LaboratorySetup", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "laboratoryCategory_r8", "ɵɵadvance", "ɵɵtextInterpolate1", "laboratoryCategoryName", "ɵɵtemplate", "AddLaboratoryServicesComponent_div_24_small_1_Template", "tmp_0_0", "ctx_r1", "laboratorySetupForm", "get", "errors", "laboratorySubCategory_r10", "laboratorySubCategoryName", "AddLaboratoryServicesComponent_div_30_small_1_Template", "ctx_r3", "AddLaboratoryServicesComponent_div_34_small_1_Template", "ctx_r4", "AddLaboratoryServicesComponent_div_38_small_1_Template", "AddLaboratoryServicesComponent_div_38_small_2_Template", "ctx_r5", "tmp_1_0", "AddLaboratoryServicesComponent", "constructor", "fb", "laboratoryService", "router", "searchTerm", "laboratorySetup", "laboratoryCategories", "laboratorySubCategories", "isLoading", "group", "category", "required", "subCategory", "description", "price", "pattern", "ngOnInit", "loadLaboratoryCategories", "getLaboratoryCategoriesList", "subscribe", "data", "console", "log", "error", "onCategoryChange", "selectedCate<PERSON><PERSON>", "value", "getLaboratorySubCategories", "laboratoryCategoryId", "categoryId", "getLaboratorySubCategoriesList", "saveLaboratoryService", "valid", "laboratorySubCategoryId", "status", "userIdString", "localStorage", "getItem", "userId", "parseInt", "saveLaboratorySetup", "response", "fire", "title", "html", "confirmButtonText", "confirmButtonColor", "then", "reset", "navigate", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "LaboratoryService", "i3", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "AddLaboratoryServicesComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "AddLaboratoryServicesComponent_Template_form_ngSubmit_17_listener", "AddLaboratoryServicesComponent_Template_select_change_22_listener", "AddLaboratoryServicesComponent_option_23_Template", "AddLaboratoryServicesComponent_div_24_Template", "AddLaboratoryServicesComponent_option_29_Template", "AddLaboratoryServicesComponent_div_30_Template", "AddLaboratoryServicesComponent_div_34_Template", "AddLaboratoryServicesComponent_div_38_Template", "AddLaboratoryServicesComponent_span_44_Template", "AddLaboratoryServicesComponent_span_45_Template", "tmp_2_0", "invalid", "dirty", "touched", "tmp_4_0", "tmp_5_0", "tmp_6_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\add-laboratory-services\\add-laboratory-services.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\add-laboratory-services\\add-laboratory-services.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Laboratory, LaboratoryCategory, LaboratorySetup, LaboratorySubCategory } from '../laboratory';\r\nimport { Router } from '@angular/router';\r\nimport { LaboratoryService } from '../laboratory.service';\r\nimport Swal from 'sweetalert2';\r\n\r\n@Component({\r\n  selector: 'app-add-laboratory-services',\r\n  templateUrl: './add-laboratory-services.component.html',\r\n  styleUrls: ['./add-laboratory-services.component.css']\r\n})\r\nexport class AddLaboratoryServicesComponent implements OnInit {\r\n  searchTerm: string = '';\r\n  laboratorySetup: LaboratorySetup = new LaboratorySetup();\r\n  laboratorySetupForm: FormGroup;\r\n  laboratoryCategories: LaboratoryCategory[] = [];\r\n  laboratorySubCategories: LaboratorySubCategory[] = [];\r\n  isLoading: boolean = false; // Loading state variable\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private laboratoryService: LaboratoryService,\r\n    private router: Router,\r\n  ) {\r\n    this.laboratorySetupForm = this.fb.group({\r\n      category: [null, Validators.required],\r\n      subCategory: [null, Validators.required],\r\n      description: ['', Validators.required],\r\n      price: ['', [Validators.required, Validators.pattern('^\\\\d+(\\\\.\\\\d{1,2})?$')]],\r\n    });\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loadLaboratoryCategories();\r\n  }\r\n\r\n  loadLaboratoryCategories(): void {\r\n    this.laboratoryService.getLaboratoryCategoriesList().subscribe(\r\n      (data: LaboratoryCategory[]) => {\r\n        console.log('Received categories:', data);\r\n        this.laboratoryCategories = data;\r\n      },\r\n      (error) => {\r\n        console.error('Error fetching laboratory categories:', error);\r\n      }\r\n    );\r\n  }\r\n\r\n  onCategoryChange(): void {\r\n    const selectedCategory = this.laboratorySetupForm.get('category')?.value;\r\n    if (selectedCategory) {\r\n        this.getLaboratorySubCategories(selectedCategory.laboratoryCategoryId);\r\n    } else {\r\n        this.laboratorySubCategories = [];\r\n    }\r\n  }\r\n\r\n  getLaboratorySubCategories(categoryId: number | null): void {\r\n    if (categoryId) {\r\n      this.laboratoryService.getLaboratorySubCategoriesList(categoryId).subscribe(\r\n        (data: LaboratorySubCategory[]) => {\r\n          this.laboratorySubCategories = data;\r\n        },\r\n        (error) => {\r\n          console.error('Error fetching subcategories:', error);\r\n        }\r\n      );\r\n    } else {\r\n      this.laboratorySubCategories = [];\r\n    }\r\n  }\r\n\r\n  saveLaboratoryService(): void {\r\n    if (this.laboratorySetupForm.valid) {\r\n      this.isLoading = true; \r\n  \r\n      this.laboratorySetup.laboratoryCategoryId = this.laboratorySetupForm.get('category')?.value;\r\n      this.laboratorySetup.laboratorySubCategoryId = this.laboratorySetupForm.get('subCategory')?.value;\r\n      this.laboratorySetup.description = this.laboratorySetupForm.get('description')?.value;\r\n      this.laboratorySetup.price = this.laboratorySetupForm.get('price')?.value;\r\n      this.laboratorySetup.status = \"Active\";\r\n      const userIdString = localStorage.getItem('userid');\r\n      const userId = userIdString ? parseInt(userIdString, 10) : null;\r\n  \r\n      if (userId !== null) {\r\n        this.laboratoryService.saveLaboratorySetup(userId, this.laboratorySetup).subscribe(\r\n          (response) => {\r\n            console.log('Laboratory setup saved successfully:', response);\r\n            this.isLoading = false; \r\n  \r\n            Swal.fire({\r\n              title: 'Success!',\r\n              html: '<div style=\"color: #ff7a00;\"><i class=\"fas fa-check-circle fa-3x\"></i></div><p style=\"margin-top: 20px;\">Laboratory setup saved successfully.</p>',\r\n              confirmButtonText: 'OK',\r\n              confirmButtonColor: '#ff7a00'\r\n            }).then(() => {\r\n\r\n              this.laboratorySetupForm.reset();\r\n            });\r\n  \r\n            this.router.navigate(['/add-laboratory-services']);\r\n          },\r\n          (error) => {\r\n            console.error('Failed to save laboratory setup:', error);\r\n            Swal.fire({\r\n              title: 'Error!',\r\n              html: '<i class=\"fas fa-exclamation-triangle\" style=\"color: #B93426; font-size: 48px;\"></i><p style=\"margin-top: 20px;\">There was and error with saving laboratory setup. Please try again.</p>', \r\n              confirmButtonColor: '#B93426' \r\n            });\r\n            this.isLoading = false; \r\n          }\r\n        );\r\n      } else {\r\n        console.error('User ID is not available in localStorage.');\r\n        this.isLoading = false; \r\n      }\r\n    } else {\r\n      console.log('Form is invalid');\r\n      Swal.fire({\r\n        title: 'Error!',\r\n        html: '<i class=\"fas fa-exclamation-triangle\" style=\"color: #B93426; font-size: 48px;\"></i><p style=\"margin-top: 20px;\">There was and error with saving laboratory setup. Please try again.</p>', \r\n        confirmButtonColor: '#B93426' \r\n      });\r\n    }\r\n  }\r\n  \r\n}\r\n", "<div class=\"row bg-white\">\r\n  <div class=\"col-12\">\r\n    <div class=\"row g-0 w-100 bg-white\">\r\n      <div class=\"container g-0\">\r\n        <div class=\"header-row\">\r\n          <div class=\"header-row-h1\">Add Laboratory Services</div>\r\n        </div>\r\n        <div class=\"header-bottom-line\"></div>\r\n        <br />\r\n\r\n        <div class=\"row \">\r\n          <div class=\"col d-flex\">\r\n            <div class=\"add-service\">Add Service</div>\r\n          </div>\r\n          <div class=\"col d-flex justify-content-end\">\r\n            <i class=\"bi bi-pencil-square\"></i>\r\n          </div>\r\n        </div>\r\n        <br />\r\n\r\n        <div class=\"rectangle\">\r\n          <form [formGroup]=\"laboratorySetupForm\" (ngSubmit)=\"saveLaboratoryService()\">\r\n            <div class=\"row\">\r\n              <div class=\"col-md-6 d-flex flex-column\">\r\n                <label for=\"category\">Select Laboratory Category</label>\r\n                <select id=\"category\" name=\"category\" formControlName=\"category\" (change)=\"onCategoryChange()\">\r\n                  <option *ngFor=\"let laboratoryCategory of laboratoryCategories\" [ngValue]=\"laboratoryCategory\">\r\n                    {{ laboratoryCategory.laboratoryCategoryName }}\r\n                  </option>\r\n                </select>\r\n                <div *ngIf=\"laboratorySetupForm.get('category')?.invalid && (laboratorySetupForm.get('category')?.dirty || laboratorySetupForm.get('category')?.touched)\">\r\n                  <small class=\"text-danger\" *ngIf=\"laboratorySetupForm.get('category')?.errors?.['required']\">\r\n                    Category is required.\r\n                  </small>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"col-md-6 d-flex flex-column\">\r\n                <label for=\"subCategory\">Select Laboratory Sub Category</label>\r\n                <select id=\"subCategory\" name=\"subCategory\" formControlName=\"subCategory\">\r\n                  <option *ngFor=\"let laboratorySubCategory of laboratorySubCategories\" [ngValue]=\"laboratorySubCategory\">\r\n                    {{ laboratorySubCategory.laboratorySubCategoryName }}\r\n                  </option>\r\n                </select>\r\n                <div *ngIf=\"laboratorySetupForm.get('subCategory')?.invalid && (laboratorySetupForm.get('subCategory')?.dirty || laboratorySetupForm.get('subCategory')?.touched)\">\r\n                  <small class=\"text-danger\" *ngIf=\"laboratorySetupForm.get('subCategory')?.errors?.['required']\">\r\n                    Sub category is required.\r\n                  </small>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <label for=\"description\" class=\"mt-2\">Description</label>\r\n            <textarea id=\"description\" name=\"description\" placeholder=\"Item 01\" formControlName=\"description\" class=\"description-textarea\"></textarea>\r\n            <div *ngIf=\"laboratorySetupForm.get('description')?.invalid && (laboratorySetupForm.get('description')?.dirty || laboratorySetupForm.get('description')?.touched)\">\r\n              <small class=\"text-danger\" *ngIf=\"laboratorySetupForm.get('description')?.errors?.['required']\">\r\n                Description is required.\r\n              </small>\r\n            </div>\r\n\r\n            <label for=\"price\" class=\"mt-2\">Price</label>\r\n            <input type=\"text\" id=\"price\" name=\"price\" placeholder=\"LKR\" formControlName=\"price\" class=\"price-input\" />\r\n            <div *ngIf=\"laboratorySetupForm.get('price')?.invalid && (laboratorySetupForm.get('price')?.dirty || laboratorySetupForm.get('price')?.touched)\">\r\n              <small class=\"text-danger\" *ngIf=\"laboratorySetupForm.get('price')?.errors?.['required']\">Price is required.</small>\r\n              <small class=\"text-danger\" *ngIf=\"laboratorySetupForm.get('price')?.errors?.['pattern']\">Please enter a valid price.</small>\r\n            </div>\r\n\r\n            <!-- Submit Button Row -->\r\n            <div class=\"row mt-4\">\r\n              <div class=\"d-flex justify-content-end gap-3 ms-auto mt-2\">\r\n                <button class=\"btn btn-secondary\" type=\"button\">Schedule</button>\r\n                <button class=\"btn btn-primary\" type=\"submit\" [disabled]=\"isLoading\">\r\n                  <span *ngIf=\"isLoading\">Saving...</span>\r\n                  <span *ngIf=\"!isLoading\">Add Service</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAyCC,eAAe,QAA+B,eAAe;AAGtG,OAAOC,IAAI,MAAM,aAAa;;;;;;;;ICqBZC,EAAA,CAAAC,cAAA,iBAA+F;IAC7FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFuDH,EAAA,CAAAI,UAAA,YAAAC,qBAAA,CAA8B;IAC5FL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,qBAAA,CAAAG,sBAAA,MACF;;;;;IAGAR,EAAA,CAAAC,cAAA,gBAA6F;IAC3FD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAHVH,EAAA,CAAAC,cAAA,UAA0J;IACxJD,EAAA,CAAAS,UAAA,IAAAC,sDAAA,oBAEQ;IACVV,EAAA,CAAAG,YAAA,EAAM;;;;;IAHwBH,EAAA,CAAAM,SAAA,GAA+D;IAA/DN,EAAA,CAAAI,UAAA,UAAAO,OAAA,GAAAC,MAAA,CAAAC,mBAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA+D;;;;;IAS3Ff,EAAA,CAAAC,cAAA,iBAAwG;IACtGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF6DH,EAAA,CAAAI,UAAA,YAAAY,yBAAA,CAAiC;IACrGhB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAS,yBAAA,CAAAC,yBAAA,MACF;;;;;IAGAjB,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAmK;IACjKD,EAAA,CAAAS,UAAA,IAAAS,sDAAA,oBAEQ;IACVlB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHwBH,EAAA,CAAAM,SAAA,GAAkE;IAAlEN,EAAA,CAAAI,UAAA,UAAAO,OAAA,GAAAQ,MAAA,CAAAN,mBAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkE;;;;;IAUlGf,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAHVH,EAAA,CAAAC,cAAA,UAAmK;IACjKD,EAAA,CAAAS,UAAA,IAAAW,sDAAA,oBAEQ;IACVpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHwBH,EAAA,CAAAM,SAAA,GAAkE;IAAlEN,EAAA,CAAAI,UAAA,UAAAO,OAAA,GAAAU,MAAA,CAAAR,mBAAA,CAAAC,GAAA,kCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkE;;;;;IAQ9Ff,EAAA,CAAAC,cAAA,gBAA0F;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACpHH,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF9HH,EAAA,CAAAC,cAAA,UAAiJ;IAC/ID,EAAA,CAAAS,UAAA,IAAAa,sDAAA,oBAAoH;IACpHtB,EAAA,CAAAS,UAAA,IAAAc,sDAAA,oBAA4H;IAC9HvB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFwBH,EAAA,CAAAM,SAAA,GAA4D;IAA5DN,EAAA,CAAAI,UAAA,UAAAO,OAAA,GAAAa,MAAA,CAAAX,mBAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA4D;IAC5Df,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAqB,OAAA,GAAAD,MAAA,CAAAX,mBAAA,CAAAC,GAAA,4BAAAW,OAAA,CAAAV,MAAA,kBAAAU,OAAA,CAAAV,MAAA,YAA2D;;;;;IAQnFf,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxCH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADlE7D,MAKauB,8BAA8B;EAQzCC,YACUC,EAAe,EACfC,iBAAoC,EACpCC,MAAc;IAFd,KAAAF,EAAE,GAAFA,EAAE;IACF,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IAVhB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAoB,IAAIlC,eAAe,EAAE;IAExD,KAAAmC,oBAAoB,GAAyB,EAAE;IAC/C,KAAAC,uBAAuB,GAA4B,EAAE;IACrD,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAO1B,IAAI,CAACtB,mBAAmB,GAAG,IAAI,CAACe,EAAE,CAACQ,KAAK,CAAC;MACvCC,QAAQ,EAAE,CAAC,IAAI,EAAExC,UAAU,CAACyC,QAAQ,CAAC;MACrCC,WAAW,EAAE,CAAC,IAAI,EAAE1C,UAAU,CAACyC,QAAQ,CAAC;MACxCE,WAAW,EAAE,CAAC,EAAE,EAAE3C,UAAU,CAACyC,QAAQ,CAAC;MACtCG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAACyC,QAAQ,EAAEzC,UAAU,CAAC6C,OAAO,CAAC,sBAAsB,CAAC,CAAC;KAC9E,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAA,wBAAwBA,CAAA;IACtB,IAAI,CAACf,iBAAiB,CAACgB,2BAA2B,EAAE,CAACC,SAAS,CAC3DC,IAA0B,IAAI;MAC7BC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,IAAI,CAAC;MACzC,IAAI,CAACd,oBAAoB,GAAGc,IAAI;IAClC,CAAC,EACAG,KAAK,IAAI;MACRF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,CACF;EACH;EAEAC,gBAAgBA,CAAA;IACd,MAAMC,gBAAgB,GAAG,IAAI,CAACvC,mBAAmB,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEuC,KAAK;IACxE,IAAID,gBAAgB,EAAE;MAClB,IAAI,CAACE,0BAA0B,CAACF,gBAAgB,CAACG,oBAAoB,CAAC;KACzE,MAAM;MACH,IAAI,CAACrB,uBAAuB,GAAG,EAAE;;EAEvC;EAEAoB,0BAA0BA,CAACE,UAAyB;IAClD,IAAIA,UAAU,EAAE;MACd,IAAI,CAAC3B,iBAAiB,CAAC4B,8BAA8B,CAACD,UAAU,CAAC,CAACV,SAAS,CACxEC,IAA6B,IAAI;QAChC,IAAI,CAACb,uBAAuB,GAAGa,IAAI;MACrC,CAAC,EACAG,KAAK,IAAI;QACRF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD,CAAC,CACF;KACF,MAAM;MACL,IAAI,CAAChB,uBAAuB,GAAG,EAAE;;EAErC;EAEAwB,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAC7C,mBAAmB,CAAC8C,KAAK,EAAE;MAClC,IAAI,CAACxB,SAAS,GAAG,IAAI;MAErB,IAAI,CAACH,eAAe,CAACuB,oBAAoB,GAAG,IAAI,CAAC1C,mBAAmB,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEuC,KAAK;MAC3F,IAAI,CAACrB,eAAe,CAAC4B,uBAAuB,GAAG,IAAI,CAAC/C,mBAAmB,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEuC,KAAK;MACjG,IAAI,CAACrB,eAAe,CAACQ,WAAW,GAAG,IAAI,CAAC3B,mBAAmB,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEuC,KAAK;MACrF,IAAI,CAACrB,eAAe,CAACS,KAAK,GAAG,IAAI,CAAC5B,mBAAmB,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEuC,KAAK;MACzE,IAAI,CAACrB,eAAe,CAAC6B,MAAM,GAAG,QAAQ;MACtC,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;MACnD,MAAMC,MAAM,GAAGH,YAAY,GAAGI,QAAQ,CAACJ,YAAY,EAAE,EAAE,CAAC,GAAG,IAAI;MAE/D,IAAIG,MAAM,KAAK,IAAI,EAAE;QACnB,IAAI,CAACpC,iBAAiB,CAACsC,mBAAmB,CAACF,MAAM,EAAE,IAAI,CAACjC,eAAe,CAAC,CAACc,SAAS,CAC/EsB,QAAQ,IAAI;UACXpB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEmB,QAAQ,CAAC;UAC7D,IAAI,CAACjC,SAAS,GAAG,KAAK;UAEtBpC,IAAI,CAACsE,IAAI,CAAC;YACRC,KAAK,EAAE,UAAU;YACjBC,IAAI,EAAE,mJAAmJ;YACzJC,iBAAiB,EAAE,IAAI;YACvBC,kBAAkB,EAAE;WACrB,CAAC,CAACC,IAAI,CAAC,MAAK;YAEX,IAAI,CAAC7D,mBAAmB,CAAC8D,KAAK,EAAE;UAClC,CAAC,CAAC;UAEF,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;QACpD,CAAC,EACA1B,KAAK,IAAI;UACRF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxDnD,IAAI,CAACsE,IAAI,CAAC;YACRC,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,0LAA0L;YAChME,kBAAkB,EAAE;WACrB,CAAC;UACF,IAAI,CAACtC,SAAS,GAAG,KAAK;QACxB,CAAC,CACF;OACF,MAAM;QACLa,OAAO,CAACE,KAAK,CAAC,2CAA2C,CAAC;QAC1D,IAAI,CAACf,SAAS,GAAG,KAAK;;KAEzB,MAAM;MACLa,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9BlD,IAAI,CAACsE,IAAI,CAAC;QACRC,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,0LAA0L;QAChME,kBAAkB,EAAE;OACrB,CAAC;;EAEN;EAAC,QAAAI,CAAA,G;qBAjHUnD,8BAA8B,EAAA1B,EAAA,CAAA8E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhF,EAAA,CAAA8E,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAlF,EAAA,CAAA8E,iBAAA,CAAAK,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA9B3D,8BAA8B;IAAA4D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ3C5F,EAAA,CAAAC,cAAA,aAA0B;QAKWD,EAAA,CAAAE,MAAA,8BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAE1DH,EAAA,CAAA8F,SAAA,aAAsC;QAGtC9F,EAAA,CAAAC,cAAA,aAAkB;QAEWD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAE5CH,EAAA,CAAAC,cAAA,eAA4C;QAC1CD,EAAA,CAAA8F,SAAA,aAAmC;QACrC9F,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAA8F,SAAA,UAAM;QAEN9F,EAAA,CAAAC,cAAA,eAAuB;QACmBD,EAAA,CAAA+F,UAAA,sBAAAC,kEAAA;UAAA,OAAYH,GAAA,CAAAnC,qBAAA,EAAuB;QAAA,EAAC;QAC1E1D,EAAA,CAAAC,cAAA,cAAiB;QAESD,EAAA,CAAAE,MAAA,kCAA0B;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxDH,EAAA,CAAAC,cAAA,kBAA+F;QAA9BD,EAAA,CAAA+F,UAAA,oBAAAE,kEAAA;UAAA,OAAUJ,GAAA,CAAA1C,gBAAA,EAAkB;QAAA,EAAC;QAC5FnD,EAAA,CAAAS,UAAA,KAAAyF,iDAAA,qBAES;QACXlG,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAS,UAAA,KAAA0F,8CAAA,kBAIM;QACRnG,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAAyC;QACdD,EAAA,CAAAE,MAAA,sCAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC/DH,EAAA,CAAAC,cAAA,kBAA0E;QACxED,EAAA,CAAAS,UAAA,KAAA2F,iDAAA,qBAES;QACXpG,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAS,UAAA,KAAA4F,8CAAA,kBAIM;QACRrG,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,iBAAsC;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzDH,EAAA,CAAA8F,SAAA,oBAA0I;QAC1I9F,EAAA,CAAAS,UAAA,KAAA6F,8CAAA,kBAIM;QAENtG,EAAA,CAAAC,cAAA,iBAAgC;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7CH,EAAA,CAAA8F,SAAA,iBAA2G;QAC3G9F,EAAA,CAAAS,UAAA,KAAA8F,8CAAA,kBAGM;QAGNvG,EAAA,CAAAC,cAAA,eAAsB;QAE8BD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACjEH,EAAA,CAAAC,cAAA,kBAAqE;QACnED,EAAA,CAAAS,UAAA,KAAA+F,+CAAA,mBAAwC;QACxCxG,EAAA,CAAAS,UAAA,KAAAgG,+CAAA,mBAA2C;QAC7CzG,EAAA,CAAAG,YAAA,EAAS;;;;;;;QArDTH,EAAA,CAAAM,SAAA,IAAiC;QAAjCN,EAAA,CAAAI,UAAA,cAAAyF,GAAA,CAAAhF,mBAAA,CAAiC;QAKQb,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAA5D,oBAAA,CAAuB;QAI1DjC,EAAA,CAAAM,SAAA,GAAkJ;QAAlJN,EAAA,CAAAI,UAAA,WAAAsG,OAAA,GAAAb,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,+BAAA4F,OAAA,CAAAC,OAAA,QAAAD,OAAA,GAAAb,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,+BAAA4F,OAAA,CAAAE,KAAA,OAAAF,OAAA,GAAAb,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,+BAAA4F,OAAA,CAAAG,OAAA,GAAkJ;QAU5G7G,EAAA,CAAAM,SAAA,GAA0B;QAA1BN,EAAA,CAAAI,UAAA,YAAAyF,GAAA,CAAA3D,uBAAA,CAA0B;QAIhElC,EAAA,CAAAM,SAAA,GAA2J;QAA3JN,EAAA,CAAAI,UAAA,WAAA0G,OAAA,GAAAjB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAgG,OAAA,CAAAH,OAAA,QAAAG,OAAA,GAAAjB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAgG,OAAA,CAAAF,KAAA,OAAAE,OAAA,GAAAjB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAgG,OAAA,CAAAD,OAAA,GAA2J;QAU/J7G,EAAA,CAAAM,SAAA,GAA2J;QAA3JN,EAAA,CAAAI,UAAA,WAAA2G,OAAA,GAAAlB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAiG,OAAA,CAAAJ,OAAA,QAAAI,OAAA,GAAAlB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAiG,OAAA,CAAAH,KAAA,OAAAG,OAAA,GAAAlB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,kCAAAiG,OAAA,CAAAF,OAAA,GAA2J;QAQ3J7G,EAAA,CAAAM,SAAA,GAAyI;QAAzIN,EAAA,CAAAI,UAAA,WAAA4G,OAAA,GAAAnB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,4BAAAkG,OAAA,CAAAL,OAAA,QAAAK,OAAA,GAAAnB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,4BAAAkG,OAAA,CAAAJ,KAAA,OAAAI,OAAA,GAAAnB,GAAA,CAAAhF,mBAAA,CAAAC,GAAA,4BAAAkG,OAAA,CAAAH,OAAA,GAAyI;QAS7F7G,EAAA,CAAAM,SAAA,GAAsB;QAAtBN,EAAA,CAAAI,UAAA,aAAAyF,GAAA,CAAA1D,SAAA,CAAsB;QAC3DnC,EAAA,CAAAM,SAAA,GAAe;QAAfN,EAAA,CAAAI,UAAA,SAAAyF,GAAA,CAAA1D,SAAA,CAAe;QACfnC,EAAA,CAAAM,SAAA,GAAgB;QAAhBN,EAAA,CAAAI,UAAA,UAAAyF,GAAA,CAAA1D,SAAA,CAAgB;;;;;;;SD7D5BT,8BAA8B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}