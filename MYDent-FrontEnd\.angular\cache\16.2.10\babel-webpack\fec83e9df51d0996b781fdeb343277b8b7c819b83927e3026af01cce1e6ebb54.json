{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { ActiveDescendantKeyManager, addAriaReferencedId, removeAriaReferencedId } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { trigger, state, style, transition, group, animate } from '@angular/animations';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport { ESCAPE, hasModifierKey, UP_ARROW, ENTER, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n// Animation values come from\n// https://github.com/material-components/material-components-web/blob/master/packages/mdc-menu-surface/_mixins.scss\n// TODO(mmalerba): Ideally find a way to import the values from MDC's code.\nconst _c0 = [\"panel\"];\nfunction MatAutocomplete_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0, 1);\n    i0.ɵɵlistener(\"@panelAnimation.done\", function MatAutocomplete_ng_template_0_Template_div_animation_panelAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3._animationDone.next($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const formFieldId_r1 = ctx.id;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r0.id)(\"ngClass\", ctx_r0._classList)(\"@panelAnimation\", ctx_r0.isOpen ? \"visible\" : \"hidden\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r0.ariaLabel || null)(\"aria-labelledby\", ctx_r0._getPanelAriaLabelledby(formFieldId_r1));\n  }\n}\nconst _c1 = [\"*\"];\nconst panelAnimation = trigger('panelAnimation', [state('void, hidden', style({\n  opacity: 0,\n  transform: 'scaleY(0.8)'\n})), transition(':enter, hidden => visible', [group([animate('0.03s linear', style({\n  opacity: 1\n})), animate('0.12s cubic-bezier(0, 0, 0.2, 1)', style({\n  transform: 'scaleY(1)'\n}))])]), transition(':leave, visible => hidden', [animate('0.075s linear', style({\n  opacity: 0\n}))])]);\n\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n  constructor( /** Reference to the autocomplete panel that emitted the event. */\n  source, /** Option that was selected. */\n  option) {\n    this.source = source;\n    this.option = option;\n  }\n}\n// Boilerplate for applying mixins to MatAutocomplete.\n/** @docs-private */\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n  providedIn: 'root',\n  factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    autoActiveFirstOption: false,\n    autoSelectActiveOption: false,\n    hideSingleSelectionIndicator: false,\n    requireSelection: false\n  };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n  /** Whether the autocomplete panel is open. */\n  get isOpen() {\n    return this._isOpen && this.showPanel;\n  }\n  /** @docs-private Sets the theme color of the panel. */\n  _setColor(value) {\n    this._color = value;\n    this._setThemeClasses(this._classList);\n  }\n  /**\n   * Whether the first option should be highlighted when the autocomplete panel is opened.\n   * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n   */\n  get autoActiveFirstOption() {\n    return this._autoActiveFirstOption;\n  }\n  set autoActiveFirstOption(value) {\n    this._autoActiveFirstOption = coerceBooleanProperty(value);\n  }\n  /** Whether the active option should be selected as the user is navigating. */\n  get autoSelectActiveOption() {\n    return this._autoSelectActiveOption;\n  }\n  set autoSelectActiveOption(value) {\n    this._autoSelectActiveOption = coerceBooleanProperty(value);\n  }\n  /**\n   * Whether the user is required to make a selection when they're interacting with the\n   * autocomplete. If the user moves away from the autocomplete without selecting an option from\n   * the list, the value will be reset. If the user opens the panel and closes it without\n   * interacting or selecting a value, the initial value will be kept.\n   */\n  get requireSelection() {\n    return this._requireSelection;\n  }\n  set requireSelection(value) {\n    this._requireSelection = coerceBooleanProperty(value);\n  }\n  /**\n   * Takes classes set on the host mat-autocomplete element and applies them to the panel\n   * inside the overlay container to allow for easy styling.\n   */\n  set classList(value) {\n    if (value && value.length) {\n      this._classList = coerceStringArray(value).reduce((classList, className) => {\n        classList[className] = true;\n        return classList;\n      }, {});\n    } else {\n      this._classList = {};\n    }\n    this._setVisibilityClasses(this._classList);\n    this._setThemeClasses(this._classList);\n    this._elementRef.nativeElement.className = '';\n  }\n  constructor(_changeDetectorRef, _elementRef, _defaults, platform) {\n    super();\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    this._defaults = _defaults;\n    this._activeOptionChanges = Subscription.EMPTY;\n    /** Whether the autocomplete panel should be visible, depending on option length. */\n    this.showPanel = false;\n    this._isOpen = false;\n    /** Function that maps an option's control value to its display value in the trigger. */\n    this.displayWith = null;\n    /** Event that is emitted whenever an option from the list is selected. */\n    this.optionSelected = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is opened. */\n    this.opened = new EventEmitter();\n    /** Event that is emitted when the autocomplete panel is closed. */\n    this.closed = new EventEmitter();\n    /** Emits whenever an option is activated. */\n    this.optionActivated = new EventEmitter();\n    this._classList = {};\n    /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n    this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`;\n    // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n    // Safari using VoiceOver. We should occasionally check back to see whether the bug\n    // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n    // option altogether.\n    this.inertGroups = platform?.SAFARI || false;\n    this._autoActiveFirstOption = !!_defaults.autoActiveFirstOption;\n    this._autoSelectActiveOption = !!_defaults.autoSelectActiveOption;\n    this._requireSelection = !!_defaults.requireSelection;\n  }\n  ngAfterContentInit() {\n    this._keyManager = new ActiveDescendantKeyManager(this.options).withWrap().skipPredicate(this._skipPredicate);\n    this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n      if (this.isOpen) {\n        this.optionActivated.emit({\n          source: this,\n          option: this.options.toArray()[index] || null\n        });\n      }\n    });\n    // Set the initial visibility state.\n    this._setVisibility();\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._activeOptionChanges.unsubscribe();\n  }\n  /**\n   * Sets the panel scrollTop. This allows us to manually scroll to display options\n   * above or below the fold, as they are not actually being focused when active.\n   */\n  _setScrollTop(scrollTop) {\n    if (this.panel) {\n      this.panel.nativeElement.scrollTop = scrollTop;\n    }\n  }\n  /** Returns the panel's scrollTop. */\n  _getScrollTop() {\n    return this.panel ? this.panel.nativeElement.scrollTop : 0;\n  }\n  /** Panel should hide itself when the option list is empty. */\n  _setVisibility() {\n    this.showPanel = !!this.options.length;\n    this._setVisibilityClasses(this._classList);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits the `select` event. */\n  _emitSelectEvent(option) {\n    const event = new MatAutocompleteSelectedEvent(this, option);\n    this.optionSelected.emit(event);\n  }\n  /** Gets the aria-labelledby for the autocomplete panel. */\n  _getPanelAriaLabelledby(labelId) {\n    if (this.ariaLabel) {\n      return null;\n    }\n    const labelExpression = labelId ? labelId + ' ' : '';\n    return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n  }\n  /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n  _setVisibilityClasses(classList) {\n    classList[this._visibleClass] = this.showPanel;\n    classList[this._hiddenClass] = !this.showPanel;\n  }\n  /** Sets the theming classes on a classlist based on the theme of the panel. */\n  _setThemeClasses(classList) {\n    classList['mat-primary'] = this._color === 'primary';\n    classList['mat-warn'] = this._color === 'warn';\n    classList['mat-accent'] = this._color === 'accent';\n  }\n  _skipPredicate(option) {\n    return option.disabled;\n  }\n  static #_ = this.ɵfac = function _MatAutocompleteBase_Factory(t) {\n    return new (t || _MatAutocompleteBase)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i1.Platform));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatAutocompleteBase,\n    viewQuery: function _MatAutocompleteBase_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(TemplateRef, 7);\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.panel = _t.first);\n      }\n    },\n    inputs: {\n      ariaLabel: [\"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n      displayWith: \"displayWith\",\n      autoActiveFirstOption: \"autoActiveFirstOption\",\n      autoSelectActiveOption: \"autoSelectActiveOption\",\n      requireSelection: \"requireSelection\",\n      panelWidth: \"panelWidth\",\n      classList: [\"class\", \"classList\"]\n    },\n    outputs: {\n      optionSelected: \"optionSelected\",\n      opened: \"opened\",\n      closed: \"closed\",\n      optionActivated: \"optionActivated\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }, {\n      type: i1.Platform\n    }];\n  }, {\n    template: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    panel: [{\n      type: ViewChild,\n      args: ['panel']\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    displayWith: [{\n      type: Input\n    }],\n    autoActiveFirstOption: [{\n      type: Input\n    }],\n    autoSelectActiveOption: [{\n      type: Input\n    }],\n    requireSelection: [{\n      type: Input\n    }],\n    panelWidth: [{\n      type: Input\n    }],\n    optionSelected: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    closed: [{\n      type: Output\n    }],\n    optionActivated: [{\n      type: Output\n    }],\n    classList: [{\n      type: Input,\n      args: ['class']\n    }]\n  });\n})();\nclass MatAutocomplete extends _MatAutocompleteBase {\n  constructor() {\n    super(...arguments);\n    this._visibleClass = 'mat-mdc-autocomplete-visible';\n    this._hiddenClass = 'mat-mdc-autocomplete-hidden';\n    this._animationDone = new EventEmitter();\n    this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n  }\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n    this._syncParentProperties();\n  }\n  /** Syncs the parent state with the individual options. */\n  _syncParentProperties() {\n    if (this.options) {\n      for (const option of this.options) {\n        option._changeDetectorRef.markForCheck();\n      }\n    }\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._animationDone.complete();\n  }\n  // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n  // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n  // recommendation.\n  //\n  // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n  // makes a few exceptions for compound widgets.\n  //\n  // From [Developing a Keyboard Interface](\n  // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n  //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n  //   Listbox...\"\n  //\n  // The user can focus disabled options using the keyboard, but the user cannot click disabled\n  // options.\n  _skipPredicate(_option) {\n    return false;\n  }\n  static #_ = this.ɵfac = /* @__PURE__ */function () {\n    let ɵMatAutocomplete_BaseFactory;\n    return function MatAutocomplete_Factory(t) {\n      return (ɵMatAutocomplete_BaseFactory || (ɵMatAutocomplete_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocomplete)))(t || MatAutocomplete);\n    };\n  }();\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatAutocomplete,\n    selectors: [[\"mat-autocomplete\"]],\n    contentQueries: function MatAutocomplete_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_OPTGROUP, 5);\n        i0.ɵɵcontentQuery(dirIndex, MatOption, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.optionGroups = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.options = _t);\n      }\n    },\n    hostAttrs: [\"ngSkipHydration\", \"\", 1, \"mat-mdc-autocomplete\"],\n    inputs: {\n      disableRipple: \"disableRipple\",\n      hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\"\n    },\n    exportAs: [\"matAutocomplete\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_OPTION_PARENT_COMPONENT,\n      useExisting: MatAutocomplete\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 0,\n    consts: [[\"role\", \"listbox\", 1, \"mat-mdc-autocomplete-panel\", \"mdc-menu-surface\", \"mdc-menu-surface--open\", 3, \"id\", \"ngClass\"], [\"panel\", \"\"]],\n    template: function MatAutocomplete_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, MatAutocomplete_ng_template_0_Template, 3, 5, \"ng-template\");\n      }\n    },\n    dependencies: [i2.NgClass],\n    styles: [\"div.mat-mdc-autocomplete-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-autocomplete-background-color)}.cdk-high-contrast-active div.mat-mdc-autocomplete-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden}mat-autocomplete{display:none}\"],\n    encapsulation: 2,\n    data: {\n      animation: [panelAnimation]\n    },\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocomplete, [{\n    type: Component,\n    args: [{\n      selector: 'mat-autocomplete',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      exportAs: 'matAutocomplete',\n      inputs: ['disableRipple'],\n      host: {\n        'class': 'mat-mdc-autocomplete',\n        'ngSkipHydration': ''\n      },\n      providers: [{\n        provide: MAT_OPTION_PARENT_COMPONENT,\n        useExisting: MatAutocomplete\n      }],\n      animations: [panelAnimation],\n      template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    [@panelAnimation]=\\\"isOpen ? 'visible' : 'hidden'\\\"\\n    (@panelAnimation.done)=\\\"_animationDone.next($event)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"div.mat-mdc-autocomplete-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-autocomplete-background-color)}.cdk-high-contrast-active div.mat-mdc-autocomplete-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden}mat-autocomplete{display:none}\"]\n    }]\n  }], null, {\n    optionGroups: [{\n      type: ContentChildren,\n      args: [MAT_OPTGROUP, {\n        descendants: true\n      }]\n    }],\n    options: [{\n      type: ContentChildren,\n      args: [MatOption, {\n        descendants: true\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\nclass _MatAutocompleteOriginBase {\n  constructor( /** Reference to the element on which the directive is applied. */\n  elementRef) {\n    this.elementRef = elementRef;\n  }\n  static #_ = this.ɵfac = function _MatAutocompleteOriginBase_Factory(t) {\n    return new (t || _MatAutocompleteOriginBase)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatAutocompleteOriginBase\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteOriginBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, null);\n})();\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {\n  static #_ = this.ɵfac = /* @__PURE__ */function () {\n    let ɵMatAutocompleteOrigin_BaseFactory;\n    return function MatAutocompleteOrigin_Factory(t) {\n      return (ɵMatAutocompleteOrigin_BaseFactory || (ɵMatAutocompleteOrigin_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteOrigin)))(t || MatAutocompleteOrigin);\n    };\n  }();\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAutocompleteOrigin,\n    selectors: [[\"\", \"matAutocompleteOrigin\", \"\"]],\n    exportAs: [\"matAutocompleteOrigin\"],\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteOrigin, [{\n    type: Directive,\n    args: [{\n      selector: '[matAutocompleteOrigin]',\n      exportAs: 'matAutocompleteOrigin'\n    }]\n  }], null, null);\n})();\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatAutocompleteTrigger),\n  multi: true\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n  return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' + 'Make sure that the id passed to the `matAutocomplete` is correct and that ' + \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass _MatAutocompleteTriggerBase {\n  /**\n   * Whether the autocomplete is disabled. When disabled, the element will\n   * act as a regular input and the user won't be able to open the panel.\n   */\n  get autocompleteDisabled() {\n    return this._autocompleteDisabled;\n  }\n  set autocompleteDisabled(value) {\n    this._autocompleteDisabled = coerceBooleanProperty(value);\n  }\n  constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n    this._element = _element;\n    this._overlay = _overlay;\n    this._viewContainerRef = _viewContainerRef;\n    this._zone = _zone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._dir = _dir;\n    this._formField = _formField;\n    this._document = _document;\n    this._viewportRuler = _viewportRuler;\n    this._defaults = _defaults;\n    this._componentDestroyed = false;\n    this._autocompleteDisabled = false;\n    /** Whether or not the label state is being overridden. */\n    this._manuallyFloatingLabel = false;\n    /** Subscription to viewport size changes. */\n    this._viewportSubscription = Subscription.EMPTY;\n    /**\n     * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n     * closed autocomplete from being reopened if the user switches to another browser tab and then\n     * comes back.\n     */\n    this._canOpenOnNextFocus = true;\n    /** Stream of keyboard events that can close the panel. */\n    this._closeKeyEventStream = new Subject();\n    /**\n     * Event handler for when the window is blurred. Needs to be an\n     * arrow function in order to preserve the context.\n     */\n    this._windowBlurHandler = () => {\n      // If the user blurred the window while the autocomplete is focused, it means that it'll be\n      // refocused when they come back. In this case we want to skip the first focus event, if the\n      // pane was closed, in order to avoid reopening it unintentionally.\n      this._canOpenOnNextFocus = this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n    };\n    /** `View -> model callback called when value changes` */\n    this._onChange = () => {};\n    /** `View -> model callback called when autocomplete has been touched` */\n    this._onTouched = () => {};\n    /**\n     * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n     * will render the panel underneath the trigger if there is enough space for it to fit in\n     * the viewport, otherwise the panel will be shown above it. If the position is set to\n     * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n     * whether it fits completely in the viewport.\n     */\n    this.position = 'auto';\n    /**\n     * `autocomplete` attribute to be set on the input element.\n     * @docs-private\n     */\n    this.autocompleteAttribute = 'off';\n    this._overlayAttached = false;\n    /** Stream of changes to the selection state of the autocomplete options. */\n    this.optionSelections = defer(() => {\n      const options = this.autocomplete ? this.autocomplete.options : null;\n      if (options) {\n        return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n      }\n      // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n      // Return a stream that we'll replace with the real one once everything is in place.\n      return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n    });\n    /** Handles keyboard events coming from the overlay panel. */\n    this._handlePanelKeydown = event => {\n      // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n      // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n      if (event.keyCode === ESCAPE && !hasModifierKey(event) || event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey')) {\n        // If the user had typed something in before we autoselected an option, and they decided\n        // to cancel the selection, restore the input value to the one they had typed in.\n        if (this._pendingAutoselectedOption) {\n          this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n          this._pendingAutoselectedOption = null;\n        }\n        this._closeKeyEventStream.next();\n        this._resetActiveItem();\n        // We need to stop propagation, otherwise the event will eventually\n        // reach the input itself and cause the overlay to be reopened.\n        event.stopPropagation();\n        event.preventDefault();\n      }\n    };\n    /**\n     * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n     * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n     * panel. Track the modal we have changed so we can undo the changes on destroy.\n     */\n    this._trackedModal = null;\n    this._scrollStrategy = scrollStrategy;\n  }\n  ngAfterViewInit() {\n    const window = this._getWindow();\n    if (typeof window !== 'undefined') {\n      this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['position'] && this._positionStrategy) {\n      this._setStrategyPositions(this._positionStrategy);\n      if (this.panelOpen) {\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  ngOnDestroy() {\n    const window = this._getWindow();\n    if (typeof window !== 'undefined') {\n      window.removeEventListener('blur', this._windowBlurHandler);\n    }\n    this._viewportSubscription.unsubscribe();\n    this._componentDestroyed = true;\n    this._destroyPanel();\n    this._closeKeyEventStream.complete();\n    this._clearFromModal();\n  }\n  /** Whether or not the autocomplete panel is open. */\n  get panelOpen() {\n    return this._overlayAttached && this.autocomplete.showPanel;\n  }\n  /** Opens the autocomplete suggestion panel. */\n  openPanel() {\n    this._attachOverlay();\n    this._floatLabel();\n    // Add aria-owns attribute when the autocomplete becomes visible.\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n  }\n  /** Closes the autocomplete suggestion panel. */\n  closePanel() {\n    this._resetLabel();\n    if (!this._overlayAttached) {\n      return;\n    }\n    if (this.panelOpen) {\n      // Only emit if the panel was visible.\n      // The `NgZone.onStable` always emits outside of the Angular zone,\n      // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n      // We should manually run in Angular zone to update UI after panel closing.\n      this._zone.run(() => {\n        this.autocomplete.closed.emit();\n      });\n    }\n    this.autocomplete._isOpen = this._overlayAttached = false;\n    this._pendingAutoselectedOption = null;\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n      this._closingActionsSubscription.unsubscribe();\n    }\n    this._updatePanelState();\n    // Note that in some cases this can end up being called after the component is destroyed.\n    // Add a check to ensure that we don't try to run change detection on a destroyed view.\n    if (!this._componentDestroyed) {\n      // We need to trigger change detection manually, because\n      // `fromEvent` doesn't seem to do it at the proper time.\n      // This ensures that the label is reset when the\n      // user clicks outside.\n      this._changeDetectorRef.detectChanges();\n    }\n    // Remove aria-owns attribute when the autocomplete is no longer visible.\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n  }\n  /**\n   * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n   * within the viewport.\n   */\n  updatePosition() {\n    if (this._overlayAttached) {\n      this._overlayRef.updatePosition();\n    }\n  }\n  /**\n   * A stream of actions that should close the autocomplete panel, including\n   * when an option is selected, on blur, and when TAB is pressed.\n   */\n  get panelClosingActions() {\n    return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached)) : of()).pipe(\n    // Normalize the output so we return a consistent type.\n    map(event => event instanceof MatOptionSelectionChange ? event : null));\n  }\n  /** The currently active option, coerced to MatOption type. */\n  get activeOption() {\n    if (this.autocomplete && this.autocomplete._keyManager) {\n      return this.autocomplete._keyManager.activeItem;\n    }\n    return null;\n  }\n  /** Stream of clicks outside of the autocomplete panel. */\n  _getOutsideClickStream() {\n    return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n      // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n      // fall back to check the first element in the path of the click event.\n      const clickTarget = _getEventTarget(event);\n      const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n      const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n      return this._overlayAttached && clickTarget !== this._element.nativeElement &&\n      // Normally focus moves inside `mousedown` so this condition will almost always be\n      // true. Its main purpose is to handle the case where the input is focused from an\n      // outside click which propagates up to the `body` listener within the same sequence\n      // and causes the panel to close immediately (see #3106).\n      this._document.activeElement !== this._element.nativeElement && (!formField || !formField.contains(clickTarget)) && (!customOrigin || !customOrigin.contains(clickTarget)) && !!this._overlayRef && !this._overlayRef.overlayElement.contains(clickTarget);\n    }));\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    Promise.resolve(null).then(() => this._assignOptionValue(value));\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this._element.nativeElement.disabled = isDisabled;\n  }\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const hasModifier = hasModifierKey(event);\n    // Prevent the default action on all escape key presses. This is here primarily to bring IE\n    // in line with other browsers. By default, pressing escape on IE will cause it to revert\n    // the input value to the one that it had on focus, however it won't dispatch any events\n    // which means that the model value will be out of sync with the view.\n    if (keyCode === ESCAPE && !hasModifier) {\n      event.preventDefault();\n    }\n    if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n      this.activeOption._selectViaInteraction();\n      this._resetActiveItem();\n      event.preventDefault();\n    } else if (this.autocomplete) {\n      const prevActiveItem = this.autocomplete._keyManager.activeItem;\n      const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n      if (keyCode === TAB || isArrowKey && !hasModifier && this.panelOpen) {\n        this.autocomplete._keyManager.onKeydown(event);\n      } else if (isArrowKey && this._canOpen()) {\n        this.openPanel();\n      }\n      if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n        this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n        if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n          if (!this._pendingAutoselectedOption) {\n            this._valueBeforeAutoSelection = this._element.nativeElement.value;\n          }\n          this._pendingAutoselectedOption = this.activeOption;\n          this._assignOptionValue(this.activeOption.value);\n        }\n      }\n    }\n  }\n  _handleInput(event) {\n    let target = event.target;\n    let value = target.value;\n    // Based on `NumberValueAccessor` from forms.\n    if (target.type === 'number') {\n      value = value == '' ? null : parseFloat(value);\n    }\n    // If the input has a placeholder, IE will fire the `input` event on page load,\n    // focus and blur, in addition to when the user actually changed the value. To\n    // filter out all of the extra events, we save the value on focus and between\n    // `input` events, and we check whether it changed.\n    // See: https://connect.microsoft.com/IE/feedback/details/885747/\n    if (this._previousValue !== value) {\n      this._previousValue = value;\n      this._pendingAutoselectedOption = null;\n      // If selection is required we don't write to the CVA while the user is typing.\n      // At the end of the selection either the user will have picked something\n      // or we'll reset the value back to null.\n      if (!this.autocomplete || !this.autocomplete.requireSelection) {\n        this._onChange(value);\n      }\n      if (!value) {\n        this._clearPreviousSelectedOption(null, false);\n      }\n      if (this._canOpen() && this._document.activeElement === event.target) {\n        this.openPanel();\n      }\n    }\n  }\n  _handleFocus() {\n    if (!this._canOpenOnNextFocus) {\n      this._canOpenOnNextFocus = true;\n    } else if (this._canOpen()) {\n      this._previousValue = this._element.nativeElement.value;\n      this._attachOverlay();\n      this._floatLabel(true);\n    }\n  }\n  _handleClick() {\n    if (this._canOpen() && !this.panelOpen) {\n      this.openPanel();\n    }\n  }\n  /**\n   * In \"auto\" mode, the label will animate down as soon as focus is lost.\n   * This causes the value to jump when selecting an option with the mouse.\n   * This method manually floats the label until the panel can be closed.\n   * @param shouldAnimate Whether the label should be animated when it is floated.\n   */\n  _floatLabel(shouldAnimate = false) {\n    if (this._formField && this._formField.floatLabel === 'auto') {\n      if (shouldAnimate) {\n        this._formField._animateAndLockLabel();\n      } else {\n        this._formField.floatLabel = 'always';\n      }\n      this._manuallyFloatingLabel = true;\n    }\n  }\n  /** If the label has been manually elevated, return it to its normal state. */\n  _resetLabel() {\n    if (this._manuallyFloatingLabel) {\n      if (this._formField) {\n        this._formField.floatLabel = 'auto';\n      }\n      this._manuallyFloatingLabel = false;\n    }\n  }\n  /**\n   * This method listens to a stream of panel closing actions and resets the\n   * stream every time the option list changes.\n   */\n  _subscribeToClosingActions() {\n    const firstStable = this._zone.onStable.pipe(take(1));\n    const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()),\n    // Defer emitting to the stream until the next tick, because changing\n    // bindings in here will cause \"changed after checked\" errors.\n    delay(0));\n    // When the zone is stable initially, and when the option list changes...\n    return merge(firstStable, optionChanges).pipe(\n    // create a new stream of panelClosingActions, replacing any previous streams\n    // that were created, and flatten it so our stream only emits closing events...\n    switchMap(() => {\n      // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n      // the Angular zone. This will lead to change detection being called outside of the Angular\n      // zone and the `autocomplete.opened` will also emit outside of the Angular.\n      this._zone.run(() => {\n        const wasOpen = this.panelOpen;\n        this._resetActiveItem();\n        this._updatePanelState();\n        this._changeDetectorRef.detectChanges();\n        if (this.panelOpen) {\n          this._overlayRef.updatePosition();\n        }\n        if (wasOpen !== this.panelOpen) {\n          // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n          // `closed` event, because we may not have emitted it. This can happen\n          // - if the users opens the panel and there are no options, but the\n          //   options come in slightly later or as a result of the value changing,\n          // - if the panel is closed after the user entered a string that did not match any\n          //   of the available options,\n          // - if a valid string is entered after an invalid one.\n          if (this.panelOpen) {\n            this._captureValueOnAttach();\n            this._emitOpened();\n          } else {\n            this.autocomplete.closed.emit();\n          }\n        }\n      });\n      return this.panelClosingActions;\n    }),\n    // when the first closing event occurs...\n    take(1))\n    // set the value, close the panel, and complete.\n    .subscribe(event => this._setValueAndClose(event));\n  }\n  /**\n   * Emits the opened event once it's known that the panel will be shown and stores\n   * the state of the trigger right before the opening sequence was finished.\n   */\n  _emitOpened() {\n    this.autocomplete.opened.emit();\n  }\n  /** Intended to be called when the panel is attached. Captures the current value of the input. */\n  _captureValueOnAttach() {\n    this._valueOnAttach = this._element.nativeElement.value;\n  }\n  /** Destroys the autocomplete suggestion panel. */\n  _destroyPanel() {\n    if (this._overlayRef) {\n      this.closePanel();\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n  }\n  _assignOptionValue(value) {\n    const toDisplay = this.autocomplete && this.autocomplete.displayWith ? this.autocomplete.displayWith(value) : value;\n    // Simply falling back to an empty string if the display value is falsy does not work properly.\n    // The display value can also be the number zero and shouldn't fall back to an empty string.\n    this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n  }\n  _updateNativeInputValue(value) {\n    // If it's used within a `MatFormField`, we should set it through the property so it can go\n    // through change detection.\n    if (this._formField) {\n      this._formField._control.value = value;\n    } else {\n      this._element.nativeElement.value = value;\n    }\n    this._previousValue = value;\n  }\n  /**\n   * This method closes the panel, and if a value is specified, also sets the associated\n   * control to that value. It will also mark the control as dirty if this interaction\n   * stemmed from the user.\n   */\n  _setValueAndClose(event) {\n    const panel = this.autocomplete;\n    const toSelect = event ? event.source : this._pendingAutoselectedOption;\n    if (toSelect) {\n      this._clearPreviousSelectedOption(toSelect);\n      this._assignOptionValue(toSelect.value);\n      // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n      // gets reset while the panel is still animating which looks glitchy. It'll likely break\n      // some tests to change it at this point.\n      this._onChange(toSelect.value);\n      panel._emitSelectEvent(toSelect);\n      this._element.nativeElement.focus();\n    } else if (panel.requireSelection && this._element.nativeElement.value !== this._valueOnAttach) {\n      this._clearPreviousSelectedOption(null);\n      this._assignOptionValue(null);\n      // Wait for the animation to finish before clearing the form control value, otherwise\n      // the options might change while the animation is running which looks glitchy.\n      if (panel._animationDone) {\n        panel._animationDone.pipe(take(1)).subscribe(() => this._onChange(null));\n      } else {\n        this._onChange(null);\n      }\n    }\n    this.closePanel();\n  }\n  /**\n   * Clear any previous selected option and emit a selection change event for this option\n   */\n  _clearPreviousSelectedOption(skip, emitEvent) {\n    // Null checks are necessary here, because the autocomplete\n    // or its options may not have been assigned yet.\n    this.autocomplete?.options?.forEach(option => {\n      if (option !== skip && option.selected) {\n        option.deselect(emitEvent);\n      }\n    });\n  }\n  _attachOverlay() {\n    if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatAutocompleteMissingPanelError();\n    }\n    let overlayRef = this._overlayRef;\n    if (!overlayRef) {\n      this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n        id: this._formField?.getLabelId()\n      });\n      overlayRef = this._overlay.create(this._getOverlayConfig());\n      this._overlayRef = overlayRef;\n      this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n        if (this.panelOpen && overlayRef) {\n          overlayRef.updateSize({\n            width: this._getPanelWidth()\n          });\n        }\n      });\n    } else {\n      // Update the trigger, panel width and direction, in case anything has changed.\n      this._positionStrategy.setOrigin(this._getConnectedElement());\n      overlayRef.updateSize({\n        width: this._getPanelWidth()\n      });\n    }\n    if (overlayRef && !overlayRef.hasAttached()) {\n      overlayRef.attach(this._portal);\n      this._closingActionsSubscription = this._subscribeToClosingActions();\n    }\n    const wasOpen = this.panelOpen;\n    this.autocomplete._isOpen = this._overlayAttached = true;\n    this.autocomplete._setColor(this._formField?.color);\n    this._updatePanelState();\n    this._applyModalPanelOwnership();\n    this._captureValueOnAttach();\n    // We need to do an extra `panelOpen` check in here, because the\n    // autocomplete won't be shown if there are no options.\n    if (this.panelOpen && wasOpen !== this.panelOpen) {\n      this._emitOpened();\n    }\n  }\n  /** Updates the panel's visibility state and any trigger state tied to id. */\n  _updatePanelState() {\n    this.autocomplete._setVisibility();\n    // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n    // because the act of subscribing will prevent events from reaching other overlays and\n    // we don't want to block the events if there are no options.\n    if (this.panelOpen) {\n      const overlayRef = this._overlayRef;\n      if (!this._keydownSubscription) {\n        // Use the `keydownEvents` in order to take advantage of\n        // the overlay event targeting provided by the CDK overlay.\n        this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n      }\n      if (!this._outsideClickSubscription) {\n        // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n        // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n        // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n        this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n      }\n    } else {\n      this._keydownSubscription?.unsubscribe();\n      this._outsideClickSubscription?.unsubscribe();\n      this._keydownSubscription = this._outsideClickSubscription = null;\n    }\n  }\n  _getOverlayConfig() {\n    return new OverlayConfig({\n      positionStrategy: this._getOverlayPosition(),\n      scrollStrategy: this._scrollStrategy(),\n      width: this._getPanelWidth(),\n      direction: this._dir ?? undefined,\n      panelClass: this._defaults?.overlayPanelClass\n    });\n  }\n  _getOverlayPosition() {\n    const strategy = this._overlay.position().flexibleConnectedTo(this._getConnectedElement()).withFlexibleDimensions(false).withPush(false);\n    this._setStrategyPositions(strategy);\n    this._positionStrategy = strategy;\n    return strategy;\n  }\n  /** Sets the positions on a position strategy based on the directive's input state. */\n  _setStrategyPositions(positionStrategy) {\n    // Note that we provide horizontal fallback positions, even though by default the dropdown\n    // width matches the input, because consumers can override the width. See #18854.\n    const belowPositions = [{\n      originX: 'start',\n      originY: 'bottom',\n      overlayX: 'start',\n      overlayY: 'top'\n    }, {\n      originX: 'end',\n      originY: 'bottom',\n      overlayX: 'end',\n      overlayY: 'top'\n    }];\n    // The overlay edge connected to the trigger should have squared corners, while\n    // the opposite end has rounded corners. We apply a CSS class to swap the\n    // border-radius based on the overlay position.\n    const panelClass = this._aboveClass;\n    const abovePositions = [{\n      originX: 'start',\n      originY: 'top',\n      overlayX: 'start',\n      overlayY: 'bottom',\n      panelClass\n    }, {\n      originX: 'end',\n      originY: 'top',\n      overlayX: 'end',\n      overlayY: 'bottom',\n      panelClass\n    }];\n    let positions;\n    if (this.position === 'above') {\n      positions = abovePositions;\n    } else if (this.position === 'below') {\n      positions = belowPositions;\n    } else {\n      positions = [...belowPositions, ...abovePositions];\n    }\n    positionStrategy.withPositions(positions);\n  }\n  _getConnectedElement() {\n    if (this.connectedTo) {\n      return this.connectedTo.elementRef;\n    }\n    return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n  }\n  _getPanelWidth() {\n    return this.autocomplete.panelWidth || this._getHostWidth();\n  }\n  /** Returns the width of the input element, so the panel width can match it. */\n  _getHostWidth() {\n    return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n  }\n  /**\n   * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n   * option.\n   *\n   * If the consumer opted-in to automatically activatating the first option, activate the first\n   * *enabled* option.\n   */\n  _resetActiveItem() {\n    const autocomplete = this.autocomplete;\n    if (autocomplete.autoActiveFirstOption) {\n      // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n      // because it activates the first option that passes the skip predicate, rather than the\n      // first *enabled* option.\n      let firstEnabledOptionIndex = -1;\n      for (let index = 0; index < autocomplete.options.length; index++) {\n        const option = autocomplete.options.get(index);\n        if (!option.disabled) {\n          firstEnabledOptionIndex = index;\n          break;\n        }\n      }\n      autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n    } else {\n      autocomplete._keyManager.setActiveItem(-1);\n    }\n  }\n  /** Determines whether the panel can be opened. */\n  _canOpen() {\n    const element = this._element.nativeElement;\n    return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    return this._document?.defaultView || window;\n  }\n  /** Scrolls to a particular option in the list. */\n  _scrollToOption(index) {\n    // Given that we are not actually focusing active options, we must manually adjust scroll\n    // to reveal options below the fold. First, we find the offset of the option from the top\n    // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n    // the panel height + the option height, so the active option will be just visible at the\n    // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n    // will become the offset. If that offset is visible within the panel already, the scrollTop is\n    // not adjusted.\n    const autocomplete = this.autocomplete;\n    const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n    if (index === 0 && labelCount === 1) {\n      // If we've got one group label before the option and we're at the top option,\n      // scroll the list to the top. This is better UX than scrolling the list to the\n      // top of the option, because it allows the user to read the top group's label.\n      autocomplete._setScrollTop(0);\n    } else if (autocomplete.panel) {\n      const option = autocomplete.options.toArray()[index];\n      if (option) {\n        const element = option._getHostElement();\n        const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n        autocomplete._setScrollTop(newScrollPosition);\n      }\n    }\n  }\n  /**\n   * If the autocomplete trigger is inside of an `aria-modal` element, connect\n   * that modal to the options panel with `aria-owns`.\n   *\n   * For some browser + screen reader combinations, when navigation is inside\n   * of an `aria-modal` element, the screen reader treats everything outside\n   * of that modal as hidden or invisible.\n   *\n   * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n   * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n   * from reaching the panel.\n   *\n   * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n   * the options panel. This effectively communicates to assistive technology that the\n   * options panel is part of the same interaction as the modal.\n   *\n   * At time of this writing, this issue is present in VoiceOver.\n   * See https://github.com/angular/components/issues/20694\n   */\n  _applyModalPanelOwnership() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n    // the `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    if (!modal) {\n      // Most commonly, the autocomplete trigger is not inside a modal.\n      return;\n    }\n    const panelId = this.autocomplete.id;\n    if (this._trackedModal) {\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n    }\n    addAriaReferencedId(modal, 'aria-owns', panelId);\n    this._trackedModal = modal;\n  }\n  /** Clears the references to the listbox overlay element from the modal it was added to. */\n  _clearFromModal() {\n    if (this._trackedModal) {\n      const panelId = this.autocomplete.id;\n      removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n      this._trackedModal = null;\n    }\n  }\n  static #_ = this.ɵfac = function _MatAutocompleteTriggerBase_Factory(t) {\n    return new (t || _MatAutocompleteTriggerBase)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 9), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(i3.ViewportRuler), i0.ɵɵdirectiveInject(MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, 8));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatAutocompleteTriggerBase,\n    inputs: {\n      autocomplete: [\"matAutocomplete\", \"autocomplete\"],\n      position: [\"matAutocompletePosition\", \"position\"],\n      connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"],\n      autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"],\n      autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatAutocompleteTriggerBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1$1.Overlay\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2$1.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i4.MatFormField,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_FORM_FIELD]\n      }, {\n        type: Host\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i3.ViewportRuler\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, {\n    autocomplete: [{\n      type: Input,\n      args: ['matAutocomplete']\n    }],\n    position: [{\n      type: Input,\n      args: ['matAutocompletePosition']\n    }],\n    connectedTo: [{\n      type: Input,\n      args: ['matAutocompleteConnectedTo']\n    }],\n    autocompleteAttribute: [{\n      type: Input,\n      args: ['autocomplete']\n    }],\n    autocompleteDisabled: [{\n      type: Input,\n      args: ['matAutocompleteDisabled']\n    }]\n  });\n})();\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n  constructor() {\n    super(...arguments);\n    this._aboveClass = 'mat-mdc-autocomplete-panel-above';\n  }\n  static #_ = this.ɵfac = /* @__PURE__ */function () {\n    let ɵMatAutocompleteTrigger_BaseFactory;\n    return function MatAutocompleteTrigger_Factory(t) {\n      return (ɵMatAutocompleteTrigger_BaseFactory || (ɵMatAutocompleteTrigger_BaseFactory = i0.ɵɵgetInheritedFactory(MatAutocompleteTrigger)))(t || MatAutocompleteTrigger);\n    };\n  }();\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatAutocompleteTrigger,\n    selectors: [[\"input\", \"matAutocomplete\", \"\"], [\"textarea\", \"matAutocomplete\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-autocomplete-trigger\"],\n    hostVars: 7,\n    hostBindings: function MatAutocompleteTrigger_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focusin\", function MatAutocompleteTrigger_focusin_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"blur\", function MatAutocompleteTrigger_blur_HostBindingHandler() {\n          return ctx._onTouched();\n        })(\"input\", function MatAutocompleteTrigger_input_HostBindingHandler($event) {\n          return ctx._handleInput($event);\n        })(\"keydown\", function MatAutocompleteTrigger_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        })(\"click\", function MatAutocompleteTrigger_click_HostBindingHandler() {\n          return ctx._handleClick();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"autocomplete\", ctx.autocompleteAttribute)(\"role\", ctx.autocompleteDisabled ? null : \"combobox\")(\"aria-autocomplete\", ctx.autocompleteDisabled ? null : \"list\")(\"aria-activedescendant\", ctx.panelOpen && ctx.activeOption ? ctx.activeOption.id : null)(\"aria-expanded\", ctx.autocompleteDisabled ? null : ctx.panelOpen.toString())(\"aria-controls\", ctx.autocompleteDisabled || !ctx.panelOpen ? null : ctx.autocomplete == null ? null : ctx.autocomplete.id)(\"aria-haspopup\", ctx.autocompleteDisabled ? null : \"listbox\");\n      }\n    },\n    exportAs: [\"matAutocompleteTrigger\"],\n    features: [i0.ɵɵProvidersFeature([MAT_AUTOCOMPLETE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n      host: {\n        'class': 'mat-mdc-autocomplete-trigger',\n        '[attr.autocomplete]': 'autocompleteAttribute',\n        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n        // a little earlier. This avoids issues where IE delays the focusing of the input.\n        '(focusin)': '_handleFocus()',\n        '(blur)': '_onTouched()',\n        '(input)': '_handleInput($event)',\n        '(keydown)': '_handleKeydown($event)',\n        '(click)': '_handleClick()'\n      },\n      exportAs: 'matAutocompleteTrigger',\n      providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR]\n    }]\n  }], null, null);\n})();\nclass MatAutocompleteModule {\n  static #_ = this.ɵfac = function MatAutocompleteModule_Factory(t) {\n    return new (t || MatAutocompleteModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatAutocompleteModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule, CdkScrollableModule, MatOptionModule, MatCommonModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAutocompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n      exports: [CdkScrollableModule, MatAutocomplete, MatOptionModule, MatCommonModule, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n      providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };", "map": {"version": 3, "names": ["i0", "InjectionToken", "EventEmitter", "TemplateRef", "Directive", "Inject", "ViewChild", "Input", "Output", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "forwardRef", "Optional", "Host", "NgModule", "mixinDisableRipple", "MAT_OPTION_PARENT_COMPONENT", "MAT_OPTGROUP", "MatOption", "MatOptionSelectionChange", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MatOptionModule", "MatCommonModule", "i2", "DOCUMENT", "CommonModule", "i3", "CdkScrollableModule", "i1$1", "Overlay", "OverlayConfig", "OverlayModule", "ActiveDescendantKeyManager", "addAriaReferencedId", "removeAriaReferencedId", "coerceBooleanProperty", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "i1", "_getEventTarget", "trigger", "state", "style", "transition", "group", "animate", "Subscription", "Subject", "defer", "merge", "of", "fromEvent", "ESCAPE", "hasModifierKey", "UP_ARROW", "ENTER", "DOWN_ARROW", "TAB", "TemplatePortal", "NG_VALUE_ACCESSOR", "i4", "MAT_FORM_FIELD", "startWith", "switchMap", "take", "filter", "map", "tap", "delay", "i2$1", "_c0", "MatAutocomplete_ng_template_0_Template", "rf", "ctx", "_r4", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "MatAutocomplete_ng_template_0_Template_div_animation_panelAnimation_done_0_listener", "$event", "ɵɵrestoreView", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "_animationDone", "next", "ɵɵprojection", "ɵɵelementEnd", "formFieldId_r1", "id", "ctx_r0", "ɵɵproperty", "_classList", "isOpen", "ɵɵattribute", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "_c1", "panelAnimation", "opacity", "transform", "_uniqueAutocompleteIdCounter", "MatAutocompleteSelectedEvent", "constructor", "source", "option", "_MatAutocompleteMixinBase", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY", "autoActiveFirstOption", "autoSelectActiveOption", "hideSingleSelectionIndicator", "requireSelection", "_MatAutocompleteBase", "_isOpen", "showPanel", "_setColor", "value", "_color", "_setThemeClasses", "_autoActiveFirstOption", "_autoSelectActiveOption", "_requireSelection", "classList", "length", "reduce", "className", "_setVisibilityClasses", "_elementRef", "nativeElement", "_changeDetectorRef", "_defaults", "platform", "_activeOptionChanges", "EMPTY", "displayWith", "optionSelected", "opened", "closed", "optionActivated", "inertGroups", "SAFARI", "ngAfterContentInit", "_keyManager", "options", "withWrap", "skipPredicate", "_skipPredicate", "change", "subscribe", "index", "emit", "toArray", "_setVisibility", "ngOnDestroy", "destroy", "unsubscribe", "_setScrollTop", "scrollTop", "panel", "_getScrollTop", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_emitSelectEvent", "event", "labelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_visibleClass", "_hiddenClass", "disabled", "_", "ɵfac", "_MatAutocompleteBase_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "Platform", "_2", "ɵdir", "ɵɵdefineDirective", "type", "viewQuery", "_MatAutocompleteBase_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "template", "first", "inputs", "panelWidth", "outputs", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "undefined", "decorators", "args", "static", "MatAutocomplete", "arguments", "_hideSingleSelectionIndicator", "_syncParentProperties", "complete", "_option", "ɵMatAutocomplete_BaseFactory", "MatAutocomplete_Factory", "ɵɵgetInheritedFactory", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "MatAutocomplete_ContentQueries", "dirIndex", "ɵɵcontentQuery", "optionGroups", "hostAttrs", "disable<PERSON><PERSON><PERSON>", "exportAs", "ɵɵProvidersFeature", "provide", "useExisting", "ngContentSelectors", "decls", "vars", "consts", "MatAutocomplete_Template", "ɵɵprojectionDef", "ɵɵtemplate", "dependencies", "Ng<PERSON><PERSON>", "styles", "encapsulation", "data", "animation", "changeDetection", "selector", "None", "OnPush", "host", "providers", "animations", "descendants", "_MatAutocompleteOriginBase", "elementRef", "_MatAutocompleteOriginBase_Factory", "MatAutocompleteOrigin", "ɵMatAutocompleteOrigin_BaseFactory", "MatAutocompleteOrigin_Factory", "MAT_AUTOCOMPLETE_VALUE_ACCESSOR", "MatAutocompleteTrigger", "multi", "getMatAutocompleteMissingPanelError", "Error", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY", "overlay", "scrollStrategies", "reposition", "MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER", "deps", "useFactory", "_MatAutocompleteTriggerBase", "autocompleteDisabled", "_autocompleteDisabled", "_element", "_overlay", "_viewContainerRef", "_zone", "scrollStrategy", "_dir", "_formField", "_document", "_viewportRuler", "_componentDestroyed", "_manuallyFloatingLabel", "_viewportSubscription", "_canOpenOnNextFocus", "_closeKeyEventStream", "_windowBlurHandler", "activeElement", "panelOpen", "_onChange", "_onTouched", "position", "autocompleteAttribute", "_overlayAttached", "optionSelections", "autocomplete", "changes", "pipe", "onSelectionChange", "onStable", "_handlePanelKeydown", "keyCode", "_pendingAutoselectedOption", "_updateNativeInputValue", "_valueBeforeAutoSelection", "_resetActiveItem", "stopPropagation", "preventDefault", "_trackedModal", "_scrollStrategy", "ngAfterViewInit", "window", "_getWindow", "runOutsideAngular", "addEventListener", "ngOnChanges", "_positionStrategy", "_setStrategyPositions", "_overlayRef", "updatePosition", "removeEventListener", "_destroyPanel", "_clearFromModal", "openPanel", "_attachOverlay", "_floatLabel", "panelId", "closePanel", "_resetLabel", "run", "has<PERSON>tta<PERSON>", "detach", "_closingActionsSubscription", "_updatePanelState", "detectChanges", "panelClosingActions", "tabOut", "_getOutsideClickStream", "detachments", "activeOption", "activeItem", "clickTarget", "formField", "customOrigin", "connectedTo", "contains", "overlayElement", "writeValue", "Promise", "resolve", "then", "_assignOptionValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "_handleKeydown", "hasModifier", "_selectViaInteraction", "prevActiveItem", "isArrowKey", "onKeydown", "_canOpen", "_scrollToOption", "activeItemIndex", "_handleInput", "target", "parseFloat", "_previousValue", "_clearPreviousSelectedOption", "_handleFocus", "_handleClick", "shouldAnimate", "floatLabel", "_animateAndLockLabel", "_subscribeToClosingActions", "firstStable", "optionChanges", "reapplyLastPosition", "was<PERSON><PERSON>", "_captureValueOnAttach", "_emitOpened", "_setValueAndClose", "_valueOnAttach", "dispose", "toDisplay", "_control", "toSelect", "focus", "skip", "emitEvent", "for<PERSON>ach", "selected", "deselect", "overlayRef", "_portal", "getLabelId", "create", "_getOverlayConfig", "updateSize", "width", "_get<PERSON><PERSON><PERSON><PERSON>idth", "<PERSON><PERSON><PERSON><PERSON>", "_getConnectedElement", "attach", "color", "_applyModalPanelOwnership", "_keydownSubscription", "keydownEvents", "_outsideClickSubscription", "outsidePointerEvents", "positionStrategy", "_getOverlayPosition", "direction", "panelClass", "overlayPanelClass", "strategy", "flexibleConnectedTo", "withFlexibleDimensions", "with<PERSON><PERSON>", "belowPositions", "originX", "originY", "overlayX", "overlayY", "_aboveClass", "abovePositions", "positions", "withPositions", "getConnectedOverlayOrigin", "_getHostWidth", "getBoundingClientRect", "firstEnabledOptionIndex", "get", "setActiveItem", "element", "readOnly", "defaultView", "labelCount", "_getHostElement", "newScrollPosition", "offsetTop", "offsetHeight", "modal", "closest", "_MatAutocompleteTriggerBase_Factory", "ViewContainerRef", "NgZone", "Directionality", "ViewportRuler", "ɵɵNgOnChangesFeature", "MatFormField", "ɵMatAutocompleteTrigger_BaseFactory", "MatAutocompleteTrigger_Factory", "hostVars", "hostBindings", "MatAutocompleteTrigger_HostBindings", "MatAutocompleteTrigger_focusin_HostBindingHandler", "MatAutocompleteTrigger_blur_HostBindingHandler", "MatAutocompleteTrigger_input_HostBindingHandler", "MatAutocompleteTrigger_keydown_HostBindingHandler", "MatAutocompleteTrigger_click_HostBindingHandler", "toString", "MatAutocompleteModule", "MatAutocompleteModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["E:/Github/MYDent-FrontEnd/node_modules/@angular/material/fesm2022/autocomplete.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, EventEmitter, TemplateRef, Directive, Inject, ViewChild, Input, Output, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, forwardRef, Optional, Host, NgModule } from '@angular/core';\nimport { mixinDisableRipple, MAT_OPTION_PARENT_COMPONENT, MAT_OPTGROUP, MatOption, MatOptionSelectionChange, _countGroupLabelsBeforeOption, _getOptionScrollPosition, MatOptionModule, MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i3 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { ActiveDescendantKeyManager, addAriaReferencedId, removeAriaReferencedId } from '@angular/cdk/a11y';\nimport { coerceBooleanProperty, coerceStringArray } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { _getEventTarget } from '@angular/cdk/platform';\nimport { trigger, state, style, transition, group, animate } from '@angular/animations';\nimport { Subscription, Subject, defer, merge, of, fromEvent } from 'rxjs';\nimport { ESCAPE, hasModifierKey, UP_ARROW, ENTER, DOWN_ARROW, TAB } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD } from '@angular/material/form-field';\nimport { startWith, switchMap, take, filter, map, tap, delay } from 'rxjs/operators';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n// Animation values come from\n// https://github.com/material-components/material-components-web/blob/master/packages/mdc-menu-surface/_mixins.scss\n// TODO(mmalerba): Ideally find a way to import the values from MDC's code.\nconst panelAnimation = trigger('panelAnimation', [\n    state('void, hidden', style({\n        opacity: 0,\n        transform: 'scaleY(0.8)',\n    })),\n    transition(':enter, hidden => visible', [\n        group([\n            animate('0.03s linear', style({ opacity: 1 })),\n            animate('0.12s cubic-bezier(0, 0, 0.2, 1)', style({ transform: 'scaleY(1)' })),\n        ]),\n    ]),\n    transition(':leave, visible => hidden', [animate('0.075s linear', style({ opacity: 0 }))]),\n]);\n\n/**\n * Autocomplete IDs need to be unique across components, so this counter exists outside of\n * the component definition.\n */\nlet _uniqueAutocompleteIdCounter = 0;\n/** Event object that is emitted when an autocomplete option is selected. */\nclass MatAutocompleteSelectedEvent {\n    constructor(\n    /** Reference to the autocomplete panel that emitted the event. */\n    source, \n    /** Option that was selected. */\n    option) {\n        this.source = source;\n        this.option = option;\n    }\n}\n// Boilerplate for applying mixins to MatAutocomplete.\n/** @docs-private */\nconst _MatAutocompleteMixinBase = mixinDisableRipple(class {\n});\n/** Injection token to be used to override the default options for `mat-autocomplete`. */\nconst MAT_AUTOCOMPLETE_DEFAULT_OPTIONS = new InjectionToken('mat-autocomplete-default-options', {\n    providedIn: 'root',\n    factory: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        autoActiveFirstOption: false,\n        autoSelectActiveOption: false,\n        hideSingleSelectionIndicator: false,\n        requireSelection: false,\n    };\n}\n/** Base class with all of the `MatAutocomplete` functionality. */\nclass _MatAutocompleteBase extends _MatAutocompleteMixinBase {\n    /** Whether the autocomplete panel is open. */\n    get isOpen() {\n        return this._isOpen && this.showPanel;\n    }\n    /** @docs-private Sets the theme color of the panel. */\n    _setColor(value) {\n        this._color = value;\n        this._setThemeClasses(this._classList);\n    }\n    /**\n     * Whether the first option should be highlighted when the autocomplete panel is opened.\n     * Can be configured globally through the `MAT_AUTOCOMPLETE_DEFAULT_OPTIONS` token.\n     */\n    get autoActiveFirstOption() {\n        return this._autoActiveFirstOption;\n    }\n    set autoActiveFirstOption(value) {\n        this._autoActiveFirstOption = coerceBooleanProperty(value);\n    }\n    /** Whether the active option should be selected as the user is navigating. */\n    get autoSelectActiveOption() {\n        return this._autoSelectActiveOption;\n    }\n    set autoSelectActiveOption(value) {\n        this._autoSelectActiveOption = coerceBooleanProperty(value);\n    }\n    /**\n     * Whether the user is required to make a selection when they're interacting with the\n     * autocomplete. If the user moves away from the autocomplete without selecting an option from\n     * the list, the value will be reset. If the user opens the panel and closes it without\n     * interacting or selecting a value, the initial value will be kept.\n     */\n    get requireSelection() {\n        return this._requireSelection;\n    }\n    set requireSelection(value) {\n        this._requireSelection = coerceBooleanProperty(value);\n    }\n    /**\n     * Takes classes set on the host mat-autocomplete element and applies them to the panel\n     * inside the overlay container to allow for easy styling.\n     */\n    set classList(value) {\n        if (value && value.length) {\n            this._classList = coerceStringArray(value).reduce((classList, className) => {\n                classList[className] = true;\n                return classList;\n            }, {});\n        }\n        else {\n            this._classList = {};\n        }\n        this._setVisibilityClasses(this._classList);\n        this._setThemeClasses(this._classList);\n        this._elementRef.nativeElement.className = '';\n    }\n    constructor(_changeDetectorRef, _elementRef, _defaults, platform) {\n        super();\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._defaults = _defaults;\n        this._activeOptionChanges = Subscription.EMPTY;\n        /** Whether the autocomplete panel should be visible, depending on option length. */\n        this.showPanel = false;\n        this._isOpen = false;\n        /** Function that maps an option's control value to its display value in the trigger. */\n        this.displayWith = null;\n        /** Event that is emitted whenever an option from the list is selected. */\n        this.optionSelected = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is opened. */\n        this.opened = new EventEmitter();\n        /** Event that is emitted when the autocomplete panel is closed. */\n        this.closed = new EventEmitter();\n        /** Emits whenever an option is activated. */\n        this.optionActivated = new EventEmitter();\n        this._classList = {};\n        /** Unique ID to be used by autocomplete trigger's \"aria-owns\" property. */\n        this.id = `mat-autocomplete-${_uniqueAutocompleteIdCounter++}`;\n        // TODO(crisbeto): the problem that the `inertGroups` option resolves is only present on\n        // Safari using VoiceOver. We should occasionally check back to see whether the bug\n        // wasn't resolved in VoiceOver, and if it has, we can remove this and the `inertGroups`\n        // option altogether.\n        this.inertGroups = platform?.SAFARI || false;\n        this._autoActiveFirstOption = !!_defaults.autoActiveFirstOption;\n        this._autoSelectActiveOption = !!_defaults.autoSelectActiveOption;\n        this._requireSelection = !!_defaults.requireSelection;\n    }\n    ngAfterContentInit() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withWrap()\n            .skipPredicate(this._skipPredicate);\n        this._activeOptionChanges = this._keyManager.change.subscribe(index => {\n            if (this.isOpen) {\n                this.optionActivated.emit({ source: this, option: this.options.toArray()[index] || null });\n            }\n        });\n        // Set the initial visibility state.\n        this._setVisibility();\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._activeOptionChanges.unsubscribe();\n    }\n    /**\n     * Sets the panel scrollTop. This allows us to manually scroll to display options\n     * above or below the fold, as they are not actually being focused when active.\n     */\n    _setScrollTop(scrollTop) {\n        if (this.panel) {\n            this.panel.nativeElement.scrollTop = scrollTop;\n        }\n    }\n    /** Returns the panel's scrollTop. */\n    _getScrollTop() {\n        return this.panel ? this.panel.nativeElement.scrollTop : 0;\n    }\n    /** Panel should hide itself when the option list is empty. */\n    _setVisibility() {\n        this.showPanel = !!this.options.length;\n        this._setVisibilityClasses(this._classList);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits the `select` event. */\n    _emitSelectEvent(option) {\n        const event = new MatAutocompleteSelectedEvent(this, option);\n        this.optionSelected.emit(event);\n    }\n    /** Gets the aria-labelledby for the autocomplete panel. */\n    _getPanelAriaLabelledby(labelId) {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Sets the autocomplete visibility classes on a classlist based on the panel is visible. */\n    _setVisibilityClasses(classList) {\n        classList[this._visibleClass] = this.showPanel;\n        classList[this._hiddenClass] = !this.showPanel;\n    }\n    /** Sets the theming classes on a classlist based on the theme of the panel. */\n    _setThemeClasses(classList) {\n        classList['mat-primary'] = this._color === 'primary';\n        classList['mat-warn'] = this._color === 'warn';\n        classList['mat-accent'] = this._color === 'accent';\n    }\n    _skipPredicate(option) {\n        return option.disabled;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteBase, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS }, { token: i1.Platform }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatAutocompleteBase, inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], displayWith: \"displayWith\", autoActiveFirstOption: \"autoActiveFirstOption\", autoSelectActiveOption: \"autoSelectActiveOption\", requireSelection: \"requireSelection\", panelWidth: \"panelWidth\", classList: [\"class\", \"classList\"] }, outputs: { optionSelected: \"optionSelected\", opened: \"opened\", closed: \"closed\", optionActivated: \"optionActivated\" }, viewQueries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true, static: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }, { type: i1.Platform }]; }, propDecorators: { template: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], displayWith: [{\n                type: Input\n            }], autoActiveFirstOption: [{\n                type: Input\n            }], autoSelectActiveOption: [{\n                type: Input\n            }], requireSelection: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], optionSelected: [{\n                type: Output\n            }], opened: [{\n                type: Output\n            }], closed: [{\n                type: Output\n            }], optionActivated: [{\n                type: Output\n            }], classList: [{\n                type: Input,\n                args: ['class']\n            }] } });\nclass MatAutocomplete extends _MatAutocompleteBase {\n    constructor() {\n        super(...arguments);\n        this._visibleClass = 'mat-mdc-autocomplete-visible';\n        this._hiddenClass = 'mat-mdc-autocomplete-hidden';\n        this._animationDone = new EventEmitter();\n        this._hideSingleSelectionIndicator = this._defaults.hideSingleSelectionIndicator ?? false;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = coerceBooleanProperty(value);\n        this._syncParentProperties();\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._animationDone.complete();\n    }\n    // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n    // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n    // recommendation.\n    //\n    // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n    // makes a few exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    //\n    // The user can focus disabled options using the keyboard, but the user cannot click disabled\n    // options.\n    _skipPredicate(_option) {\n        return false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocomplete, deps: null, target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatAutocomplete, selector: \"mat-autocomplete\", inputs: { disableRipple: \"disableRipple\", hideSingleSelectionIndicator: \"hideSingleSelectionIndicator\" }, host: { attributes: { \"ngSkipHydration\": \"\" }, classAttribute: \"mat-mdc-autocomplete\" }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], queries: [{ propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }], exportAs: [\"matAutocomplete\"], usesInheritance: true, ngImport: i0, template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    [@panelAnimation]=\\\"isOpen ? 'visible' : 'hidden'\\\"\\n    (@panelAnimation.done)=\\\"_animationDone.next($event)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-autocomplete-background-color)}.cdk-high-contrast-active div.mat-mdc-autocomplete-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden}mat-autocomplete{display:none}\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [panelAnimation], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocomplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-autocomplete', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, exportAs: 'matAutocomplete', inputs: ['disableRipple'], host: {\n                        'class': 'mat-mdc-autocomplete',\n                        'ngSkipHydration': '',\n                    }, providers: [{ provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatAutocomplete }], animations: [panelAnimation], template: \"<ng-template let-formFieldId=\\\"id\\\">\\n  <div\\n    class=\\\"mat-mdc-autocomplete-panel mdc-menu-surface mdc-menu-surface--open\\\"\\n    role=\\\"listbox\\\"\\n    [id]=\\\"id\\\"\\n    [ngClass]=\\\"_classList\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby(formFieldId)\\\"\\n    [@panelAnimation]=\\\"isOpen ? 'visible' : 'hidden'\\\"\\n    (@panelAnimation.done)=\\\"_animationDone.next($event)\\\"\\n    #panel>\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\"div.mat-mdc-autocomplete-panel{box-shadow:0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);width:100%;max-height:256px;visibility:hidden;transform-origin:center top;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-autocomplete-background-color)}.cdk-high-contrast-active div.mat-mdc-autocomplete-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above) div.mat-mdc-autocomplete-panel{border-top-left-radius:0;border-top-right-radius:0}.mat-mdc-autocomplete-panel-above div.mat-mdc-autocomplete-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:center bottom}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-visible{visibility:visible}div.mat-mdc-autocomplete-panel.mat-mdc-autocomplete-hidden{visibility:hidden}mat-autocomplete{display:none}\"] }]\n        }], propDecorators: { optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input\n            }] } });\n\n/** Base class containing all of the functionality for `MatAutocompleteOrigin`. */\nclass _MatAutocompleteOriginBase {\n    constructor(\n    /** Reference to the element on which the directive is applied. */\n    elementRef) {\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteOriginBase, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatAutocompleteOriginBase, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteOriginBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; } });\n/**\n * Directive applied to an element to make it usable\n * as a connection point for an autocomplete panel.\n */\nclass MatAutocompleteOrigin extends _MatAutocompleteOriginBase {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteOrigin, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatAutocompleteOrigin, selector: \"[matAutocompleteOrigin]\", exportAs: [\"matAutocompleteOrigin\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteOrigin, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matAutocompleteOrigin]',\n                    exportAs: 'matAutocompleteOrigin',\n                }]\n        }] });\n\n/**\n * Provider that allows the autocomplete to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatAutocompleteTrigger),\n    multi: true,\n};\n/**\n * Creates an error to be thrown when attempting to use an autocomplete trigger without a panel.\n * @docs-private\n */\nfunction getMatAutocompleteMissingPanelError() {\n    return Error('Attempting to open an undefined instance of `mat-autocomplete`. ' +\n        'Make sure that the id passed to the `matAutocomplete` is correct and that ' +\n        \"you're attempting to open it after the ngAfterContentInit hook.\");\n}\n/** Injection token that determines the scroll handling while the autocomplete panel is open. */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY = new InjectionToken('mat-autocomplete-scroll-strategy');\n/** @docs-private */\nfunction MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_AUTOCOMPLETE_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY,\n};\n/** Base class with all of the `MatAutocompleteTrigger` functionality. */\nclass _MatAutocompleteTriggerBase {\n    /**\n     * Whether the autocomplete is disabled. When disabled, the element will\n     * act as a regular input and the user won't be able to open the panel.\n     */\n    get autocompleteDisabled() {\n        return this._autocompleteDisabled;\n    }\n    set autocompleteDisabled(value) {\n        this._autocompleteDisabled = coerceBooleanProperty(value);\n    }\n    constructor(_element, _overlay, _viewContainerRef, _zone, _changeDetectorRef, scrollStrategy, _dir, _formField, _document, _viewportRuler, _defaults) {\n        this._element = _element;\n        this._overlay = _overlay;\n        this._viewContainerRef = _viewContainerRef;\n        this._zone = _zone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._dir = _dir;\n        this._formField = _formField;\n        this._document = _document;\n        this._viewportRuler = _viewportRuler;\n        this._defaults = _defaults;\n        this._componentDestroyed = false;\n        this._autocompleteDisabled = false;\n        /** Whether or not the label state is being overridden. */\n        this._manuallyFloatingLabel = false;\n        /** Subscription to viewport size changes. */\n        this._viewportSubscription = Subscription.EMPTY;\n        /**\n         * Whether the autocomplete can open the next time it is focused. Used to prevent a focused,\n         * closed autocomplete from being reopened if the user switches to another browser tab and then\n         * comes back.\n         */\n        this._canOpenOnNextFocus = true;\n        /** Stream of keyboard events that can close the panel. */\n        this._closeKeyEventStream = new Subject();\n        /**\n         * Event handler for when the window is blurred. Needs to be an\n         * arrow function in order to preserve the context.\n         */\n        this._windowBlurHandler = () => {\n            // If the user blurred the window while the autocomplete is focused, it means that it'll be\n            // refocused when they come back. In this case we want to skip the first focus event, if the\n            // pane was closed, in order to avoid reopening it unintentionally.\n            this._canOpenOnNextFocus =\n                this._document.activeElement !== this._element.nativeElement || this.panelOpen;\n        };\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when autocomplete has been touched` */\n        this._onTouched = () => { };\n        /**\n         * Position of the autocomplete panel relative to the trigger element. A position of `auto`\n         * will render the panel underneath the trigger if there is enough space for it to fit in\n         * the viewport, otherwise the panel will be shown above it. If the position is set to\n         * `above` or `below`, the panel will always be shown above or below the trigger. no matter\n         * whether it fits completely in the viewport.\n         */\n        this.position = 'auto';\n        /**\n         * `autocomplete` attribute to be set on the input element.\n         * @docs-private\n         */\n        this.autocompleteAttribute = 'off';\n        this._overlayAttached = false;\n        /** Stream of changes to the selection state of the autocomplete options. */\n        this.optionSelections = defer(() => {\n            const options = this.autocomplete ? this.autocomplete.options : null;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            // If there are any subscribers before `ngAfterViewInit`, the `autocomplete` will be undefined.\n            // Return a stream that we'll replace with the real one once everything is in place.\n            return this._zone.onStable.pipe(take(1), switchMap(() => this.optionSelections));\n        });\n        /** Handles keyboard events coming from the overlay panel. */\n        this._handlePanelKeydown = (event) => {\n            // Close when pressing ESCAPE or ALT + UP_ARROW, based on the a11y guidelines.\n            // See: https://www.w3.org/TR/wai-aria-practices-1.1/#textbox-keyboard-interaction\n            if ((event.keyCode === ESCAPE && !hasModifierKey(event)) ||\n                (event.keyCode === UP_ARROW && hasModifierKey(event, 'altKey'))) {\n                // If the user had typed something in before we autoselected an option, and they decided\n                // to cancel the selection, restore the input value to the one they had typed in.\n                if (this._pendingAutoselectedOption) {\n                    this._updateNativeInputValue(this._valueBeforeAutoSelection ?? '');\n                    this._pendingAutoselectedOption = null;\n                }\n                this._closeKeyEventStream.next();\n                this._resetActiveItem();\n                // We need to stop propagation, otherwise the event will eventually\n                // reach the input itself and cause the overlay to be reopened.\n                event.stopPropagation();\n                event.preventDefault();\n            }\n        };\n        /**\n         * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n         * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n         * panel. Track the modal we have changed so we can undo the changes on destroy.\n         */\n        this._trackedModal = null;\n        this._scrollStrategy = scrollStrategy;\n    }\n    ngAfterViewInit() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            this._zone.runOutsideAngular(() => window.addEventListener('blur', this._windowBlurHandler));\n        }\n    }\n    ngOnChanges(changes) {\n        if (changes['position'] && this._positionStrategy) {\n            this._setStrategyPositions(this._positionStrategy);\n            if (this.panelOpen) {\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    ngOnDestroy() {\n        const window = this._getWindow();\n        if (typeof window !== 'undefined') {\n            window.removeEventListener('blur', this._windowBlurHandler);\n        }\n        this._viewportSubscription.unsubscribe();\n        this._componentDestroyed = true;\n        this._destroyPanel();\n        this._closeKeyEventStream.complete();\n        this._clearFromModal();\n    }\n    /** Whether or not the autocomplete panel is open. */\n    get panelOpen() {\n        return this._overlayAttached && this.autocomplete.showPanel;\n    }\n    /** Opens the autocomplete suggestion panel. */\n    openPanel() {\n        this._attachOverlay();\n        this._floatLabel();\n        // Add aria-owns attribute when the autocomplete becomes visible.\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            addAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n    }\n    /** Closes the autocomplete suggestion panel. */\n    closePanel() {\n        this._resetLabel();\n        if (!this._overlayAttached) {\n            return;\n        }\n        if (this.panelOpen) {\n            // Only emit if the panel was visible.\n            // The `NgZone.onStable` always emits outside of the Angular zone,\n            // so all the subscriptions from `_subscribeToClosingActions()` are also outside of the Angular zone.\n            // We should manually run in Angular zone to update UI after panel closing.\n            this._zone.run(() => {\n                this.autocomplete.closed.emit();\n            });\n        }\n        this.autocomplete._isOpen = this._overlayAttached = false;\n        this._pendingAutoselectedOption = null;\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n            this._closingActionsSubscription.unsubscribe();\n        }\n        this._updatePanelState();\n        // Note that in some cases this can end up being called after the component is destroyed.\n        // Add a check to ensure that we don't try to run change detection on a destroyed view.\n        if (!this._componentDestroyed) {\n            // We need to trigger change detection manually, because\n            // `fromEvent` doesn't seem to do it at the proper time.\n            // This ensures that the label is reset when the\n            // user clicks outside.\n            this._changeDetectorRef.detectChanges();\n        }\n        // Remove aria-owns attribute when the autocomplete is no longer visible.\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n    }\n    /**\n     * Updates the position of the autocomplete suggestion panel to ensure that it fits all options\n     * within the viewport.\n     */\n    updatePosition() {\n        if (this._overlayAttached) {\n            this._overlayRef.updatePosition();\n        }\n    }\n    /**\n     * A stream of actions that should close the autocomplete panel, including\n     * when an option is selected, on blur, and when TAB is pressed.\n     */\n    get panelClosingActions() {\n        return merge(this.optionSelections, this.autocomplete._keyManager.tabOut.pipe(filter(() => this._overlayAttached)), this._closeKeyEventStream, this._getOutsideClickStream(), this._overlayRef\n            ? this._overlayRef.detachments().pipe(filter(() => this._overlayAttached))\n            : of()).pipe(\n        // Normalize the output so we return a consistent type.\n        map(event => (event instanceof MatOptionSelectionChange ? event : null)));\n    }\n    /** The currently active option, coerced to MatOption type. */\n    get activeOption() {\n        if (this.autocomplete && this.autocomplete._keyManager) {\n            return this.autocomplete._keyManager.activeItem;\n        }\n        return null;\n    }\n    /** Stream of clicks outside of the autocomplete panel. */\n    _getOutsideClickStream() {\n        return merge(fromEvent(this._document, 'click'), fromEvent(this._document, 'auxclick'), fromEvent(this._document, 'touchend')).pipe(filter(event => {\n            // If we're in the Shadow DOM, the event target will be the shadow root, so we have to\n            // fall back to check the first element in the path of the click event.\n            const clickTarget = _getEventTarget(event);\n            const formField = this._formField ? this._formField._elementRef.nativeElement : null;\n            const customOrigin = this.connectedTo ? this.connectedTo.elementRef.nativeElement : null;\n            return (this._overlayAttached &&\n                clickTarget !== this._element.nativeElement &&\n                // Normally focus moves inside `mousedown` so this condition will almost always be\n                // true. Its main purpose is to handle the case where the input is focused from an\n                // outside click which propagates up to the `body` listener within the same sequence\n                // and causes the panel to close immediately (see #3106).\n                this._document.activeElement !== this._element.nativeElement &&\n                (!formField || !formField.contains(clickTarget)) &&\n                (!customOrigin || !customOrigin.contains(clickTarget)) &&\n                !!this._overlayRef &&\n                !this._overlayRef.overlayElement.contains(clickTarget));\n        }));\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        Promise.resolve(null).then(() => this._assignOptionValue(value));\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this._element.nativeElement.disabled = isDisabled;\n    }\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const hasModifier = hasModifierKey(event);\n        // Prevent the default action on all escape key presses. This is here primarily to bring IE\n        // in line with other browsers. By default, pressing escape on IE will cause it to revert\n        // the input value to the one that it had on focus, however it won't dispatch any events\n        // which means that the model value will be out of sync with the view.\n        if (keyCode === ESCAPE && !hasModifier) {\n            event.preventDefault();\n        }\n        if (this.activeOption && keyCode === ENTER && this.panelOpen && !hasModifier) {\n            this.activeOption._selectViaInteraction();\n            this._resetActiveItem();\n            event.preventDefault();\n        }\n        else if (this.autocomplete) {\n            const prevActiveItem = this.autocomplete._keyManager.activeItem;\n            const isArrowKey = keyCode === UP_ARROW || keyCode === DOWN_ARROW;\n            if (keyCode === TAB || (isArrowKey && !hasModifier && this.panelOpen)) {\n                this.autocomplete._keyManager.onKeydown(event);\n            }\n            else if (isArrowKey && this._canOpen()) {\n                this.openPanel();\n            }\n            if (isArrowKey || this.autocomplete._keyManager.activeItem !== prevActiveItem) {\n                this._scrollToOption(this.autocomplete._keyManager.activeItemIndex || 0);\n                if (this.autocomplete.autoSelectActiveOption && this.activeOption) {\n                    if (!this._pendingAutoselectedOption) {\n                        this._valueBeforeAutoSelection = this._element.nativeElement.value;\n                    }\n                    this._pendingAutoselectedOption = this.activeOption;\n                    this._assignOptionValue(this.activeOption.value);\n                }\n            }\n        }\n    }\n    _handleInput(event) {\n        let target = event.target;\n        let value = target.value;\n        // Based on `NumberValueAccessor` from forms.\n        if (target.type === 'number') {\n            value = value == '' ? null : parseFloat(value);\n        }\n        // If the input has a placeholder, IE will fire the `input` event on page load,\n        // focus and blur, in addition to when the user actually changed the value. To\n        // filter out all of the extra events, we save the value on focus and between\n        // `input` events, and we check whether it changed.\n        // See: https://connect.microsoft.com/IE/feedback/details/885747/\n        if (this._previousValue !== value) {\n            this._previousValue = value;\n            this._pendingAutoselectedOption = null;\n            // If selection is required we don't write to the CVA while the user is typing.\n            // At the end of the selection either the user will have picked something\n            // or we'll reset the value back to null.\n            if (!this.autocomplete || !this.autocomplete.requireSelection) {\n                this._onChange(value);\n            }\n            if (!value) {\n                this._clearPreviousSelectedOption(null, false);\n            }\n            if (this._canOpen() && this._document.activeElement === event.target) {\n                this.openPanel();\n            }\n        }\n    }\n    _handleFocus() {\n        if (!this._canOpenOnNextFocus) {\n            this._canOpenOnNextFocus = true;\n        }\n        else if (this._canOpen()) {\n            this._previousValue = this._element.nativeElement.value;\n            this._attachOverlay();\n            this._floatLabel(true);\n        }\n    }\n    _handleClick() {\n        if (this._canOpen() && !this.panelOpen) {\n            this.openPanel();\n        }\n    }\n    /**\n     * In \"auto\" mode, the label will animate down as soon as focus is lost.\n     * This causes the value to jump when selecting an option with the mouse.\n     * This method manually floats the label until the panel can be closed.\n     * @param shouldAnimate Whether the label should be animated when it is floated.\n     */\n    _floatLabel(shouldAnimate = false) {\n        if (this._formField && this._formField.floatLabel === 'auto') {\n            if (shouldAnimate) {\n                this._formField._animateAndLockLabel();\n            }\n            else {\n                this._formField.floatLabel = 'always';\n            }\n            this._manuallyFloatingLabel = true;\n        }\n    }\n    /** If the label has been manually elevated, return it to its normal state. */\n    _resetLabel() {\n        if (this._manuallyFloatingLabel) {\n            if (this._formField) {\n                this._formField.floatLabel = 'auto';\n            }\n            this._manuallyFloatingLabel = false;\n        }\n    }\n    /**\n     * This method listens to a stream of panel closing actions and resets the\n     * stream every time the option list changes.\n     */\n    _subscribeToClosingActions() {\n        const firstStable = this._zone.onStable.pipe(take(1));\n        const optionChanges = this.autocomplete.options.changes.pipe(tap(() => this._positionStrategy.reapplyLastPosition()), \n        // Defer emitting to the stream until the next tick, because changing\n        // bindings in here will cause \"changed after checked\" errors.\n        delay(0));\n        // When the zone is stable initially, and when the option list changes...\n        return (merge(firstStable, optionChanges)\n            .pipe(\n        // create a new stream of panelClosingActions, replacing any previous streams\n        // that were created, and flatten it so our stream only emits closing events...\n        switchMap(() => {\n            // The `NgZone.onStable` always emits outside of the Angular zone, thus we have to re-enter\n            // the Angular zone. This will lead to change detection being called outside of the Angular\n            // zone and the `autocomplete.opened` will also emit outside of the Angular.\n            this._zone.run(() => {\n                const wasOpen = this.panelOpen;\n                this._resetActiveItem();\n                this._updatePanelState();\n                this._changeDetectorRef.detectChanges();\n                if (this.panelOpen) {\n                    this._overlayRef.updatePosition();\n                }\n                if (wasOpen !== this.panelOpen) {\n                    // If the `panelOpen` state changed, we need to make sure to emit the `opened` or\n                    // `closed` event, because we may not have emitted it. This can happen\n                    // - if the users opens the panel and there are no options, but the\n                    //   options come in slightly later or as a result of the value changing,\n                    // - if the panel is closed after the user entered a string that did not match any\n                    //   of the available options,\n                    // - if a valid string is entered after an invalid one.\n                    if (this.panelOpen) {\n                        this._captureValueOnAttach();\n                        this._emitOpened();\n                    }\n                    else {\n                        this.autocomplete.closed.emit();\n                    }\n                }\n            });\n            return this.panelClosingActions;\n        }), \n        // when the first closing event occurs...\n        take(1))\n            // set the value, close the panel, and complete.\n            .subscribe(event => this._setValueAndClose(event)));\n    }\n    /**\n     * Emits the opened event once it's known that the panel will be shown and stores\n     * the state of the trigger right before the opening sequence was finished.\n     */\n    _emitOpened() {\n        this.autocomplete.opened.emit();\n    }\n    /** Intended to be called when the panel is attached. Captures the current value of the input. */\n    _captureValueOnAttach() {\n        this._valueOnAttach = this._element.nativeElement.value;\n    }\n    /** Destroys the autocomplete suggestion panel. */\n    _destroyPanel() {\n        if (this._overlayRef) {\n            this.closePanel();\n            this._overlayRef.dispose();\n            this._overlayRef = null;\n        }\n    }\n    _assignOptionValue(value) {\n        const toDisplay = this.autocomplete && this.autocomplete.displayWith\n            ? this.autocomplete.displayWith(value)\n            : value;\n        // Simply falling back to an empty string if the display value is falsy does not work properly.\n        // The display value can also be the number zero and shouldn't fall back to an empty string.\n        this._updateNativeInputValue(toDisplay != null ? toDisplay : '');\n    }\n    _updateNativeInputValue(value) {\n        // If it's used within a `MatFormField`, we should set it through the property so it can go\n        // through change detection.\n        if (this._formField) {\n            this._formField._control.value = value;\n        }\n        else {\n            this._element.nativeElement.value = value;\n        }\n        this._previousValue = value;\n    }\n    /**\n     * This method closes the panel, and if a value is specified, also sets the associated\n     * control to that value. It will also mark the control as dirty if this interaction\n     * stemmed from the user.\n     */\n    _setValueAndClose(event) {\n        const panel = this.autocomplete;\n        const toSelect = event ? event.source : this._pendingAutoselectedOption;\n        if (toSelect) {\n            this._clearPreviousSelectedOption(toSelect);\n            this._assignOptionValue(toSelect.value);\n            // TODO(crisbeto): this should wait until the animation is done, otherwise the value\n            // gets reset while the panel is still animating which looks glitchy. It'll likely break\n            // some tests to change it at this point.\n            this._onChange(toSelect.value);\n            panel._emitSelectEvent(toSelect);\n            this._element.nativeElement.focus();\n        }\n        else if (panel.requireSelection &&\n            this._element.nativeElement.value !== this._valueOnAttach) {\n            this._clearPreviousSelectedOption(null);\n            this._assignOptionValue(null);\n            // Wait for the animation to finish before clearing the form control value, otherwise\n            // the options might change while the animation is running which looks glitchy.\n            if (panel._animationDone) {\n                panel._animationDone.pipe(take(1)).subscribe(() => this._onChange(null));\n            }\n            else {\n                this._onChange(null);\n            }\n        }\n        this.closePanel();\n    }\n    /**\n     * Clear any previous selected option and emit a selection change event for this option\n     */\n    _clearPreviousSelectedOption(skip, emitEvent) {\n        // Null checks are necessary here, because the autocomplete\n        // or its options may not have been assigned yet.\n        this.autocomplete?.options?.forEach(option => {\n            if (option !== skip && option.selected) {\n                option.deselect(emitEvent);\n            }\n        });\n    }\n    _attachOverlay() {\n        if (!this.autocomplete && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatAutocompleteMissingPanelError();\n        }\n        let overlayRef = this._overlayRef;\n        if (!overlayRef) {\n            this._portal = new TemplatePortal(this.autocomplete.template, this._viewContainerRef, {\n                id: this._formField?.getLabelId(),\n            });\n            overlayRef = this._overlay.create(this._getOverlayConfig());\n            this._overlayRef = overlayRef;\n            this._viewportSubscription = this._viewportRuler.change().subscribe(() => {\n                if (this.panelOpen && overlayRef) {\n                    overlayRef.updateSize({ width: this._getPanelWidth() });\n                }\n            });\n        }\n        else {\n            // Update the trigger, panel width and direction, in case anything has changed.\n            this._positionStrategy.setOrigin(this._getConnectedElement());\n            overlayRef.updateSize({ width: this._getPanelWidth() });\n        }\n        if (overlayRef && !overlayRef.hasAttached()) {\n            overlayRef.attach(this._portal);\n            this._closingActionsSubscription = this._subscribeToClosingActions();\n        }\n        const wasOpen = this.panelOpen;\n        this.autocomplete._isOpen = this._overlayAttached = true;\n        this.autocomplete._setColor(this._formField?.color);\n        this._updatePanelState();\n        this._applyModalPanelOwnership();\n        this._captureValueOnAttach();\n        // We need to do an extra `panelOpen` check in here, because the\n        // autocomplete won't be shown if there are no options.\n        if (this.panelOpen && wasOpen !== this.panelOpen) {\n            this._emitOpened();\n        }\n    }\n    /** Updates the panel's visibility state and any trigger state tied to id. */\n    _updatePanelState() {\n        this.autocomplete._setVisibility();\n        // Note that here we subscribe and unsubscribe based on the panel's visiblity state,\n        // because the act of subscribing will prevent events from reaching other overlays and\n        // we don't want to block the events if there are no options.\n        if (this.panelOpen) {\n            const overlayRef = this._overlayRef;\n            if (!this._keydownSubscription) {\n                // Use the `keydownEvents` in order to take advantage of\n                // the overlay event targeting provided by the CDK overlay.\n                this._keydownSubscription = overlayRef.keydownEvents().subscribe(this._handlePanelKeydown);\n            }\n            if (!this._outsideClickSubscription) {\n                // Subscribe to the pointer events stream so that it doesn't get picked up by other overlays.\n                // TODO(crisbeto): we should switch `_getOutsideClickStream` eventually to use this stream,\n                // but the behvior isn't exactly the same and it ends up breaking some internal tests.\n                this._outsideClickSubscription = overlayRef.outsidePointerEvents().subscribe();\n            }\n        }\n        else {\n            this._keydownSubscription?.unsubscribe();\n            this._outsideClickSubscription?.unsubscribe();\n            this._keydownSubscription = this._outsideClickSubscription = null;\n        }\n    }\n    _getOverlayConfig() {\n        return new OverlayConfig({\n            positionStrategy: this._getOverlayPosition(),\n            scrollStrategy: this._scrollStrategy(),\n            width: this._getPanelWidth(),\n            direction: this._dir ?? undefined,\n            panelClass: this._defaults?.overlayPanelClass,\n        });\n    }\n    _getOverlayPosition() {\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this._getConnectedElement())\n            .withFlexibleDimensions(false)\n            .withPush(false);\n        this._setStrategyPositions(strategy);\n        this._positionStrategy = strategy;\n        return strategy;\n    }\n    /** Sets the positions on a position strategy based on the directive's input state. */\n    _setStrategyPositions(positionStrategy) {\n        // Note that we provide horizontal fallback positions, even though by default the dropdown\n        // width matches the input, because consumers can override the width. See #18854.\n        const belowPositions = [\n            { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },\n            { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },\n        ];\n        // The overlay edge connected to the trigger should have squared corners, while\n        // the opposite end has rounded corners. We apply a CSS class to swap the\n        // border-radius based on the overlay position.\n        const panelClass = this._aboveClass;\n        const abovePositions = [\n            { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom', panelClass },\n            { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom', panelClass },\n        ];\n        let positions;\n        if (this.position === 'above') {\n            positions = abovePositions;\n        }\n        else if (this.position === 'below') {\n            positions = belowPositions;\n        }\n        else {\n            positions = [...belowPositions, ...abovePositions];\n        }\n        positionStrategy.withPositions(positions);\n    }\n    _getConnectedElement() {\n        if (this.connectedTo) {\n            return this.connectedTo.elementRef;\n        }\n        return this._formField ? this._formField.getConnectedOverlayOrigin() : this._element;\n    }\n    _getPanelWidth() {\n        return this.autocomplete.panelWidth || this._getHostWidth();\n    }\n    /** Returns the width of the input element, so the panel width can match it. */\n    _getHostWidth() {\n        return this._getConnectedElement().nativeElement.getBoundingClientRect().width;\n    }\n    /**\n     * Reset the active item to -1. This is so that pressing arrow keys will activate the correct\n     * option.\n     *\n     * If the consumer opted-in to automatically activatating the first option, activate the first\n     * *enabled* option.\n     */\n    _resetActiveItem() {\n        const autocomplete = this.autocomplete;\n        if (autocomplete.autoActiveFirstOption) {\n            // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n            // because it activates the first option that passes the skip predicate, rather than the\n            // first *enabled* option.\n            let firstEnabledOptionIndex = -1;\n            for (let index = 0; index < autocomplete.options.length; index++) {\n                const option = autocomplete.options.get(index);\n                if (!option.disabled) {\n                    firstEnabledOptionIndex = index;\n                    break;\n                }\n            }\n            autocomplete._keyManager.setActiveItem(firstEnabledOptionIndex);\n        }\n        else {\n            autocomplete._keyManager.setActiveItem(-1);\n        }\n    }\n    /** Determines whether the panel can be opened. */\n    _canOpen() {\n        const element = this._element.nativeElement;\n        return !element.readOnly && !element.disabled && !this._autocompleteDisabled;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document?.defaultView || window;\n    }\n    /** Scrolls to a particular option in the list. */\n    _scrollToOption(index) {\n        // Given that we are not actually focusing active options, we must manually adjust scroll\n        // to reveal options below the fold. First, we find the offset of the option from the top\n        // of the panel. If that offset is below the fold, the new scrollTop will be the offset -\n        // the panel height + the option height, so the active option will be just visible at the\n        // bottom of the panel. If that offset is above the top of the visible panel, the new scrollTop\n        // will become the offset. If that offset is visible within the panel already, the scrollTop is\n        // not adjusted.\n        const autocomplete = this.autocomplete;\n        const labelCount = _countGroupLabelsBeforeOption(index, autocomplete.options, autocomplete.optionGroups);\n        if (index === 0 && labelCount === 1) {\n            // If we've got one group label before the option and we're at the top option,\n            // scroll the list to the top. This is better UX than scrolling the list to the\n            // top of the option, because it allows the user to read the top group's label.\n            autocomplete._setScrollTop(0);\n        }\n        else if (autocomplete.panel) {\n            const option = autocomplete.options.toArray()[index];\n            if (option) {\n                const element = option._getHostElement();\n                const newScrollPosition = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, autocomplete._getScrollTop(), autocomplete.panel.nativeElement.offsetHeight);\n                autocomplete._setScrollTop(newScrollPosition);\n            }\n        }\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._element.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = this.autocomplete.id;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the references to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (this._trackedModal) {\n            const panelId = this.autocomplete.id;\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n            this._trackedModal = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteTriggerBase, deps: [{ token: i0.ElementRef }, { token: i1$1.Overlay }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: MAT_AUTOCOMPLETE_SCROLL_STRATEGY }, { token: i2$1.Directionality, optional: true }, { token: MAT_FORM_FIELD, host: true, optional: true }, { token: DOCUMENT, optional: true }, { token: i3.ViewportRuler }, { token: MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatAutocompleteTriggerBase, inputs: { autocomplete: [\"matAutocomplete\", \"autocomplete\"], position: [\"matAutocompletePosition\", \"position\"], connectedTo: [\"matAutocompleteConnectedTo\", \"connectedTo\"], autocompleteAttribute: [\"autocomplete\", \"autocompleteAttribute\"], autocompleteDisabled: [\"matAutocompleteDisabled\", \"autocompleteDisabled\"] }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatAutocompleteTriggerBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1$1.Overlay }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY]\n                }] }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }, {\n                    type: Host\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i3.ViewportRuler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_AUTOCOMPLETE_DEFAULT_OPTIONS]\n                }] }]; }, propDecorators: { autocomplete: [{\n                type: Input,\n                args: ['matAutocomplete']\n            }], position: [{\n                type: Input,\n                args: ['matAutocompletePosition']\n            }], connectedTo: [{\n                type: Input,\n                args: ['matAutocompleteConnectedTo']\n            }], autocompleteAttribute: [{\n                type: Input,\n                args: ['autocomplete']\n            }], autocompleteDisabled: [{\n                type: Input,\n                args: ['matAutocompleteDisabled']\n            }] } });\nclass MatAutocompleteTrigger extends _MatAutocompleteTriggerBase {\n    constructor() {\n        super(...arguments);\n        this._aboveClass = 'mat-mdc-autocomplete-panel-above';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteTrigger, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatAutocompleteTrigger, selector: \"input[matAutocomplete], textarea[matAutocomplete]\", host: { listeners: { \"focusin\": \"_handleFocus()\", \"blur\": \"_onTouched()\", \"input\": \"_handleInput($event)\", \"keydown\": \"_handleKeydown($event)\", \"click\": \"_handleClick()\" }, properties: { \"attr.autocomplete\": \"autocompleteAttribute\", \"attr.role\": \"autocompleteDisabled ? null : \\\"combobox\\\"\", \"attr.aria-autocomplete\": \"autocompleteDisabled ? null : \\\"list\\\"\", \"attr.aria-activedescendant\": \"(panelOpen && activeOption) ? activeOption.id : null\", \"attr.aria-expanded\": \"autocompleteDisabled ? null : panelOpen.toString()\", \"attr.aria-controls\": \"(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id\", \"attr.aria-haspopup\": \"autocompleteDisabled ? null : \\\"listbox\\\"\" }, classAttribute: \"mat-mdc-autocomplete-trigger\" }, providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR], exportAs: [\"matAutocompleteTrigger\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matAutocomplete], textarea[matAutocomplete]`,\n                    host: {\n                        'class': 'mat-mdc-autocomplete-trigger',\n                        '[attr.autocomplete]': 'autocompleteAttribute',\n                        '[attr.role]': 'autocompleteDisabled ? null : \"combobox\"',\n                        '[attr.aria-autocomplete]': 'autocompleteDisabled ? null : \"list\"',\n                        '[attr.aria-activedescendant]': '(panelOpen && activeOption) ? activeOption.id : null',\n                        '[attr.aria-expanded]': 'autocompleteDisabled ? null : panelOpen.toString()',\n                        '[attr.aria-controls]': '(autocompleteDisabled || !panelOpen) ? null : autocomplete?.id',\n                        '[attr.aria-haspopup]': 'autocompleteDisabled ? null : \"listbox\"',\n                        // Note: we use `focusin`, as opposed to `focus`, in order to open the panel\n                        // a little earlier. This avoids issues where IE delays the focusing of the input.\n                        '(focusin)': '_handleFocus()',\n                        '(blur)': '_onTouched()',\n                        '(input)': '_handleInput($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(click)': '_handleClick()',\n                    },\n                    exportAs: 'matAutocompleteTrigger',\n                    providers: [MAT_AUTOCOMPLETE_VALUE_ACCESSOR],\n                }]\n        }] });\n\nclass MatAutocompleteModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteModule, declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin], imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule], exports: [CdkScrollableModule,\n            MatAutocomplete,\n            MatOptionModule,\n            MatCommonModule,\n            MatAutocompleteTrigger,\n            MatAutocompleteOrigin] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteModule, providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule, CdkScrollableModule,\n            MatOptionModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatAutocompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, MatOptionModule, MatCommonModule, CommonModule],\n                    exports: [\n                        CdkScrollableModule,\n                        MatAutocomplete,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatAutocompleteTrigger,\n                        MatAutocompleteOrigin,\n                    ],\n                    declarations: [MatAutocomplete, MatAutocompleteTrigger, MatAutocompleteOrigin],\n                    providers: [MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_AUTOCOMPLETE_DEFAULT_OPTIONS, MAT_AUTOCOMPLETE_DEFAULT_OPTIONS_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY, MAT_AUTOCOMPLETE_SCROLL_STRATEGY_FACTORY_PROVIDER, MAT_AUTOCOMPLETE_VALUE_ACCESSOR, MatAutocomplete, MatAutocompleteModule, MatAutocompleteOrigin, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, _MatAutocompleteBase, _MatAutocompleteOriginBase, _MatAutocompleteTriggerBase, getMatAutocompleteMissingPanelError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AACpO,SAASC,kBAAkB,EAAEC,2BAA2B,EAAEC,YAAY,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,6BAA6B,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AACtO,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AAC5E,SAASC,0BAA0B,EAAEC,mBAAmB,EAAEC,sBAAsB,QAAQ,mBAAmB;AAC3G,SAASC,qBAAqB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAChF,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AACvF,SAASC,YAAY,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,SAAS,QAAQ,MAAM;AACzE,SAASC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,QAAQ,uBAAuB;AAChG,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,EAAE,MAAM,8BAA8B;AAClD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,QAAQ,gBAAgB;AACpF,OAAO,KAAKC,IAAI,MAAM,mBAAmB;;AAEzC;AACA;AACA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAwMoG5E,EAAE,CAAA6E,gBAAA;IAAF7E,EAAE,CAAA8E,cAAA,eAwFu+B,CAAC;IAxF1+B9E,EAAE,CAAA+E,UAAA,kCAAAC,oFAAAC,MAAA;MAAFjF,EAAE,CAAAkF,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFnF,EAAE,CAAAoF,aAAA;MAAA,OAAFpF,EAAE,CAAAqF,WAAA,CAwF87BF,MAAA,CAAAG,cAAA,CAAAC,IAAA,CAAAN,MAA0B,EAAC;IAAA,CAAC,CAAC;IAxF79BjF,EAAE,CAAAwF,YAAA,EAwFsgC,CAAC;IAxFzgCxF,EAAE,CAAAyF,YAAA,CAwFghC,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAgB,cAAA,GAAAf,GAAA,CAAAgB,EAAA;IAAA,MAAAC,MAAA,GAxFnhC5F,EAAE,CAAAoF,aAAA;IAAFpF,EAAE,CAAA6F,UAAA,OAAAD,MAAA,CAAAD,EAwFqtB,CAAC,YAAAC,MAAA,CAAAE,UAAD,CAAC,oBAAAF,MAAA,CAAAG,MAAA,uBAAD,CAAC;IAxFxtB/F,EAAE,CAAAgG,WAAA,eAAAJ,MAAA,CAAAK,SAAA,QAwFgyB,CAAC,oBAAAL,MAAA,CAAAM,uBAAA,CAAAR,cAAA,CAAD,CAAC;EAAA;AAAA;AAAA,MAAAS,GAAA;AA/Rv4B,MAAMC,cAAc,GAAG1D,OAAO,CAAC,gBAAgB,EAAE,CAC7CC,KAAK,CAAC,cAAc,EAAEC,KAAK,CAAC;EACxByD,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,EACHzD,UAAU,CAAC,2BAA2B,EAAE,CACpCC,KAAK,CAAC,CACFC,OAAO,CAAC,cAAc,EAAEH,KAAK,CAAC;EAAEyD,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,EAC9CtD,OAAO,CAAC,kCAAkC,EAAEH,KAAK,CAAC;EAAE0D,SAAS,EAAE;AAAY,CAAC,CAAC,CAAC,CACjF,CAAC,CACL,CAAC,EACFzD,UAAU,CAAC,2BAA2B,EAAE,CAACE,OAAO,CAAC,eAAe,EAAEH,KAAK,CAAC;EAAEyD,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7F,CAAC;;AAEF;AACA;AACA;AACA;AACA,IAAIE,4BAA4B,GAAG,CAAC;AACpC;AACA,MAAMC,4BAA4B,CAAC;EAC/BC,WAAWA,CAAA,CACX;EACAC,MAAM,EACN;EACAC,MAAM,EAAE;IACJ,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;AACJ;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG3F,kBAAkB,CAAC,MAAM,EAC1D,CAAC;AACF;AACA,MAAM4F,gCAAgC,GAAG,IAAI5G,cAAc,CAAC,kCAAkC,EAAE;EAC5F6G,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,wCAAwCA,CAAA,EAAG;EAChD,OAAO;IACHC,qBAAqB,EAAE,KAAK;IAC5BC,sBAAsB,EAAE,KAAK;IAC7BC,4BAA4B,EAAE,KAAK;IACnCC,gBAAgB,EAAE;EACtB,CAAC;AACL;AACA;AACA,MAAMC,oBAAoB,SAAST,yBAAyB,CAAC;EACzD;EACA,IAAIb,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACuB,OAAO,IAAI,IAAI,CAACC,SAAS;EACzC;EACA;EACAC,SAASA,CAACC,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,gBAAgB,CAAC,IAAI,CAAC7B,UAAU,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI,IAAImB,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACW,sBAAsB;EACtC;EACA,IAAIX,qBAAqBA,CAACQ,KAAK,EAAE;IAC7B,IAAI,CAACG,sBAAsB,GAAGtF,qBAAqB,CAACmF,KAAK,CAAC;EAC9D;EACA;EACA,IAAIP,sBAAsBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACW,uBAAuB;EACvC;EACA,IAAIX,sBAAsBA,CAACO,KAAK,EAAE;IAC9B,IAAI,CAACI,uBAAuB,GAAGvF,qBAAqB,CAACmF,KAAK,CAAC;EAC/D;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIL,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACU,iBAAiB;EACjC;EACA,IAAIV,gBAAgBA,CAACK,KAAK,EAAE;IACxB,IAAI,CAACK,iBAAiB,GAAGxF,qBAAqB,CAACmF,KAAK,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,IAAIM,SAASA,CAACN,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAIA,KAAK,CAACO,MAAM,EAAE;MACvB,IAAI,CAAClC,UAAU,GAAGvD,iBAAiB,CAACkF,KAAK,CAAC,CAACQ,MAAM,CAAC,CAACF,SAAS,EAAEG,SAAS,KAAK;QACxEH,SAAS,CAACG,SAAS,CAAC,GAAG,IAAI;QAC3B,OAAOH,SAAS;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,MACI;MACD,IAAI,CAACjC,UAAU,GAAG,CAAC,CAAC;IACxB;IACA,IAAI,CAACqC,qBAAqB,CAAC,IAAI,CAACrC,UAAU,CAAC;IAC3C,IAAI,CAAC6B,gBAAgB,CAAC,IAAI,CAAC7B,UAAU,CAAC;IACtC,IAAI,CAACsC,WAAW,CAACC,aAAa,CAACH,SAAS,GAAG,EAAE;EACjD;EACAzB,WAAWA,CAAC6B,kBAAkB,EAAEF,WAAW,EAAEG,SAAS,EAAEC,QAAQ,EAAE;IAC9D,KAAK,CAAC,CAAC;IACP,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACE,oBAAoB,GAAGzF,YAAY,CAAC0F,KAAK;IAC9C;IACA,IAAI,CAACnB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,OAAO,GAAG,KAAK;IACpB;IACA,IAAI,CAACqB,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI1I,YAAY,CAAC,CAAC;IACxC;IACA,IAAI,CAAC2I,MAAM,GAAG,IAAI3I,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC4I,MAAM,GAAG,IAAI5I,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC6I,eAAe,GAAG,IAAI7I,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC4F,UAAU,GAAG,CAAC,CAAC;IACpB;IACA,IAAI,CAACH,EAAE,GAAI,oBAAmBY,4BAA4B,EAAG,EAAC;IAC9D;IACA;IACA;IACA;IACA,IAAI,CAACyC,WAAW,GAAGR,QAAQ,EAAES,MAAM,IAAI,KAAK;IAC5C,IAAI,CAACrB,sBAAsB,GAAG,CAAC,CAACW,SAAS,CAACtB,qBAAqB;IAC/D,IAAI,CAACY,uBAAuB,GAAG,CAAC,CAACU,SAAS,CAACrB,sBAAsB;IACjE,IAAI,CAACY,iBAAiB,GAAG,CAAC,CAACS,SAAS,CAACnB,gBAAgB;EACzD;EACA8B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,WAAW,GAAG,IAAIhH,0BAA0B,CAAC,IAAI,CAACiH,OAAO,CAAC,CAC1DC,QAAQ,CAAC,CAAC,CACVC,aAAa,CAAC,IAAI,CAACC,cAAc,CAAC;IACvC,IAAI,CAACd,oBAAoB,GAAG,IAAI,CAACU,WAAW,CAACK,MAAM,CAACC,SAAS,CAACC,KAAK,IAAI;MACnE,IAAI,IAAI,CAAC3D,MAAM,EAAE;QACb,IAAI,CAACgD,eAAe,CAACY,IAAI,CAAC;UAAEjD,MAAM,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI,CAACyC,OAAO,CAACQ,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC,IAAI;QAAK,CAAC,CAAC;MAC9F;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACG,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACX,WAAW,EAAEY,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACtB,oBAAoB,CAACuB,WAAW,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIC,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,CAAC9B,aAAa,CAAC6B,SAAS,GAAGA,SAAS;IAClD;EACJ;EACA;EACAE,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACD,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC9B,aAAa,CAAC6B,SAAS,GAAG,CAAC;EAC9D;EACA;EACAL,cAAcA,CAAA,EAAG;IACb,IAAI,CAACtC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC6B,OAAO,CAACpB,MAAM;IACtC,IAAI,CAACG,qBAAqB,CAAC,IAAI,CAACrC,UAAU,CAAC;IAC3C,IAAI,CAACwC,kBAAkB,CAAC+B,YAAY,CAAC,CAAC;EAC1C;EACA;EACAC,gBAAgBA,CAAC3D,MAAM,EAAE;IACrB,MAAM4D,KAAK,GAAG,IAAI/D,4BAA4B,CAAC,IAAI,EAAEG,MAAM,CAAC;IAC5D,IAAI,CAACiC,cAAc,CAACe,IAAI,CAACY,KAAK,CAAC;EACnC;EACA;EACArE,uBAAuBA,CAACsE,OAAO,EAAE;IAC7B,IAAI,IAAI,CAACvE,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAMwE,eAAe,GAAGD,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACE,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGF,OAAO;EAChF;EACA;EACArC,qBAAqBA,CAACJ,SAAS,EAAE;IAC7BA,SAAS,CAAC,IAAI,CAAC4C,aAAa,CAAC,GAAG,IAAI,CAACpD,SAAS;IAC9CQ,SAAS,CAAC,IAAI,CAAC6C,YAAY,CAAC,GAAG,CAAC,IAAI,CAACrD,SAAS;EAClD;EACA;EACAI,gBAAgBA,CAACI,SAAS,EAAE;IACxBA,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAACL,MAAM,KAAK,SAAS;IACpDK,SAAS,CAAC,UAAU,CAAC,GAAG,IAAI,CAACL,MAAM,KAAK,MAAM;IAC9CK,SAAS,CAAC,YAAY,CAAC,GAAG,IAAI,CAACL,MAAM,KAAK,QAAQ;EACtD;EACA6B,cAAcA,CAAC5C,MAAM,EAAE;IACnB,OAAOA,MAAM,CAACkE,QAAQ;EAC1B;EAAC,QAAAC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,6BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5D,oBAAoB,EAA9BrH,EAAE,CAAAkL,iBAAA,CAA8ClL,EAAE,CAACmL,iBAAiB,GAApEnL,EAAE,CAAAkL,iBAAA,CAA+ElL,EAAE,CAACoL,UAAU,GAA9FpL,EAAE,CAAAkL,iBAAA,CAAyGrE,gCAAgC,GAA3I7G,EAAE,CAAAkL,iBAAA,CAAsJ1I,EAAE,CAAC6I,QAAQ;EAAA,CAA4C;EAAA,QAAAC,EAAA,GACtS,IAAI,CAACC,IAAI,kBAD8EvL,EAAE,CAAAwL,iBAAA;IAAAC,IAAA,EACJpE,oBAAoB;IAAAqE,SAAA,WAAAC,2BAAAjH,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADlB1E,EAAE,CAAA4L,WAAA,CACuhBzL,WAAW;QADpiBH,EAAE,CAAA4L,WAAA,CAAApH,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAmH,EAAA;QAAF7L,EAAE,CAAA8L,cAAA,CAAAD,EAAA,GAAF7L,EAAE,CAAA+L,WAAA,QAAApH,GAAA,CAAAqH,QAAA,GAAAH,EAAA,CAAAI,KAAA;QAAFjM,EAAE,CAAA8L,cAAA,CAAAD,EAAA,GAAF7L,EAAE,CAAA+L,WAAA,QAAApH,GAAA,CAAAwF,KAAA,GAAA0B,EAAA,CAAAI,KAAA;MAAA;IAAA;IAAAC,MAAA;MAAAjG,SAAA;MAAAyE,cAAA;MAAA/B,WAAA;MAAA1B,qBAAA;MAAAC,sBAAA;MAAAE,gBAAA;MAAA+E,UAAA;MAAApE,SAAA;IAAA;IAAAqE,OAAA;MAAAxD,cAAA;MAAAC,MAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;IAAAsD,QAAA,GAAFrM,EAAE,CAAAsM,0BAAA;EAAA,EAC+rB;AACryB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvM,EAAE,CAAAwM,iBAAA,CAGXnF,oBAAoB,EAAc,CAAC;IAClHoE,IAAI,EAAErL;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqL,IAAI,EAAEzL,EAAE,CAACmL;IAAkB,CAAC,EAAE;MAAEM,IAAI,EAAEzL,EAAE,CAACoL;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAEgB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACvHjB,IAAI,EAAEpL,MAAM;QACZsM,IAAI,EAAE,CAAC9F,gCAAgC;MAC3C,CAAC;IAAE,CAAC,EAAE;MAAE4E,IAAI,EAAEjJ,EAAE,CAAC6I;IAAS,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEW,QAAQ,EAAE,CAAC;MAC9DP,IAAI,EAAEnL,SAAS;MACfqM,IAAI,EAAE,CAACxM,WAAW,EAAE;QAAEyM,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEzC,KAAK,EAAE,CAAC;MACRsB,IAAI,EAAEnL,SAAS;MACfqM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE1G,SAAS,EAAE,CAAC;MACZwF,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEjC,cAAc,EAAE,CAAC;MACjBe,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEhE,WAAW,EAAE,CAAC;MACd8C,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE0G,qBAAqB,EAAE,CAAC;MACxBwE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE2G,sBAAsB,EAAE,CAAC;MACzBuE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE6G,gBAAgB,EAAE,CAAC;MACnBqE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE4L,UAAU,EAAE,CAAC;MACbV,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEqI,cAAc,EAAE,CAAC;MACjB6C,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEqI,MAAM,EAAE,CAAC;MACT4C,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEsI,MAAM,EAAE,CAAC;MACT2C,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEuI,eAAe,EAAE,CAAC;MAClB0C,IAAI,EAAEjL;IACV,CAAC,CAAC;IAAEuH,SAAS,EAAE,CAAC;MACZ0D,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAME,eAAe,SAASxF,oBAAoB,CAAC;EAC/CZ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGqG,SAAS,CAAC;IACnB,IAAI,CAACnC,aAAa,GAAG,8BAA8B;IACnD,IAAI,CAACC,YAAY,GAAG,6BAA6B;IACjD,IAAI,CAACtF,cAAc,GAAG,IAAIpF,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC6M,6BAA6B,GAAG,IAAI,CAACxE,SAAS,CAACpB,4BAA4B,IAAI,KAAK;EAC7F;EACA;EACA,IAAIA,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAC4F,6BAA6B;EAC7C;EACA,IAAI5F,4BAA4BA,CAACM,KAAK,EAAE;IACpC,IAAI,CAACsF,6BAA6B,GAAGzK,qBAAqB,CAACmF,KAAK,CAAC;IACjE,IAAI,CAACuF,qBAAqB,CAAC,CAAC;EAChC;EACA;EACAA,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC5D,OAAO,EAAE;MACd,KAAK,MAAMzC,MAAM,IAAI,IAAI,CAACyC,OAAO,EAAE;QAC/BzC,MAAM,CAAC2B,kBAAkB,CAAC+B,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACAP,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACxE,cAAc,CAAC2H,QAAQ,CAAC,CAAC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA1D,cAAcA,CAAC2D,OAAO,EAAE;IACpB,OAAO,KAAK;EAChB;EAAC,QAAApC,CAAA,GACQ,IAAI,CAACC,IAAI;IAAA,IAAAoC,4BAAA;IAAA,gBAAAC,wBAAAnC,CAAA;MAAA,QAAAkC,4BAAA,KAAAA,4BAAA,GAvF8EnN,EAAE,CAAAqN,qBAAA,CAuFQR,eAAe,IAAA5B,CAAA,IAAf4B,eAAe;IAAA;EAAA,GAAqD;EAAA,QAAAvB,EAAA,GACrK,IAAI,CAACgC,IAAI,kBAxF8EtN,EAAE,CAAAuN,iBAAA;IAAA9B,IAAA,EAwFJoB,eAAe;IAAAW,SAAA;IAAAC,cAAA,WAAAC,+BAAAhJ,EAAA,EAAAC,GAAA,EAAAgJ,QAAA;MAAA,IAAAjJ,EAAA;QAxFb1E,EAAE,CAAA4N,cAAA,CAAAD,QAAA,EAwFwXxM,YAAY;QAxFtYnB,EAAE,CAAA4N,cAAA,CAAAD,QAAA,EAwFicvM,SAAS;MAAA;MAAA,IAAAsD,EAAA;QAAA,IAAAmH,EAAA;QAxF5c7L,EAAE,CAAA8L,cAAA,CAAAD,EAAA,GAAF7L,EAAE,CAAA+L,WAAA,QAAApH,GAAA,CAAAkJ,YAAA,GAAAhC,EAAA;QAAF7L,EAAE,CAAA8L,cAAA,CAAAD,EAAA,GAAF7L,EAAE,CAAA+L,WAAA,QAAApH,GAAA,CAAAyE,OAAA,GAAAyC,EAAA;MAAA;IAAA;IAAAiC,SAAA,sBAwF8L,EAAE;IAAA5B,MAAA;MAAA6B,aAAA;MAAA5G,4BAAA;IAAA;IAAA6G,QAAA;IAAA3B,QAAA,GAxFlMrM,EAAE,CAAAiO,kBAAA,CAwFyP,CAAC;MAAEC,OAAO,EAAEhN,2BAA2B;MAAEiN,WAAW,EAAEtB;IAAgB,CAAC,CAAC,GAxFnU7M,EAAE,CAAAsM,0BAAA;IAAA8B,kBAAA,EAAAjI,GAAA;IAAAkI,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAvC,QAAA,WAAAwC,yBAAA9J,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF1E,EAAE,CAAAyO,eAAA;QAAFzO,EAAE,CAAA0O,UAAA,IAAAjK,sCAAA,qBAwFgiC,CAAC;MAAA;IAAA;IAAAkK,YAAA,GAAw9BjN,EAAE,CAACkN,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAAsE,CAAC5I,cAAc;IAAC;IAAA6I,eAAA;EAAA,EAAiG;AAChyE;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KA1FoGvM,EAAE,CAAAwM,iBAAA,CA0FXK,eAAe,EAAc,CAAC;IAC7GpB,IAAI,EAAEhL,SAAS;IACfkM,IAAI,EAAE,CAAC;MAAEuC,QAAQ,EAAE,kBAAkB;MAAEJ,aAAa,EAAEpO,iBAAiB,CAACyO,IAAI;MAAEF,eAAe,EAAEtO,uBAAuB,CAACyO,MAAM;MAAEpB,QAAQ,EAAE,iBAAiB;MAAE9B,MAAM,EAAE,CAAC,eAAe,CAAC;MAAEmD,IAAI,EAAE;QACjL,OAAO,EAAE,sBAAsB;QAC/B,iBAAiB,EAAE;MACvB,CAAC;MAAEC,SAAS,EAAE,CAAC;QAAEpB,OAAO,EAAEhN,2BAA2B;QAAEiN,WAAW,EAAEtB;MAAgB,CAAC,CAAC;MAAE0C,UAAU,EAAE,CAACnJ,cAAc,CAAC;MAAE4F,QAAQ,EAAE,ofAAof;MAAE6C,MAAM,EAAE,CAAC,65BAA65B;IAAE,CAAC;EAC3iD,CAAC,CAAC,QAAkB;IAAEhB,YAAY,EAAE,CAAC;MAC7BpC,IAAI,EAAE7K,eAAe;MACrB+L,IAAI,EAAE,CAACxL,YAAY,EAAE;QAAEqO,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEpG,OAAO,EAAE,CAAC;MACVqC,IAAI,EAAE7K,eAAe;MACrB+L,IAAI,EAAE,CAACvL,SAAS,EAAE;QAAEoO,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAErI,4BAA4B,EAAE,CAAC;MAC/BsE,IAAI,EAAElL;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMkP,0BAA0B,CAAC;EAC7BhJ,WAAWA,CAAA,CACX;EACAiJ,UAAU,EAAE;IACR,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EAAC,QAAA5E,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA4E,mCAAA1E,CAAA;IAAA,YAAAA,CAAA,IAAwFwE,0BAA0B,EAjHpCzP,EAAE,CAAAkL,iBAAA,CAiHoDlL,EAAE,CAACoL,UAAU;EAAA,CAA4C;EAAA,QAAAE,EAAA,GACtM,IAAI,CAACC,IAAI,kBAlH8EvL,EAAE,CAAAwL,iBAAA;IAAAC,IAAA,EAkHJgE;EAA0B,EAAiB;AAC7I;AACA;EAAA,QAAAlD,SAAA,oBAAAA,SAAA,KApHoGvM,EAAE,CAAAwM,iBAAA,CAoHXiD,0BAA0B,EAAc,CAAC;IACxHhE,IAAI,EAAErL;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqL,IAAI,EAAEzL,EAAE,CAACoL;IAAW,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7E;AACA;AACA;AACA;AACA,MAAMwE,qBAAqB,SAASH,0BAA0B,CAAC;EAAA,QAAA3E,CAAA,GAClD,IAAI,CAACC,IAAI;IAAA,IAAA8E,kCAAA;IAAA,gBAAAC,8BAAA7E,CAAA;MAAA,QAAA4E,kCAAA,KAAAA,kCAAA,GA5H8E7P,EAAE,CAAAqN,qBAAA,CA4HQuC,qBAAqB,IAAA3E,CAAA,IAArB2E,qBAAqB;IAAA;EAAA,GAAqD;EAAA,QAAAtE,EAAA,GAC3K,IAAI,CAACC,IAAI,kBA7H8EvL,EAAE,CAAAwL,iBAAA;IAAAC,IAAA,EA6HJmE,qBAAqB;IAAApC,SAAA;IAAAQ,QAAA;IAAA3B,QAAA,GA7HnBrM,EAAE,CAAAsM,0BAAA;EAAA,EA6HmI;AACzO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/HoGvM,EAAE,CAAAwM,iBAAA,CA+HXoD,qBAAqB,EAAc,CAAC;IACnHnE,IAAI,EAAErL,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCuC,QAAQ,EAAE,yBAAyB;MACnClB,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM+B,+BAA+B,GAAG;EACpC7B,OAAO,EAAErK,iBAAiB;EAC1BsK,WAAW,EAAEtN,UAAU,CAAC,MAAMmP,sBAAsB,CAAC;EACrDC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAA,EAAG;EAC3C,OAAOC,KAAK,CAAC,kEAAkE,GAC3E,4EAA4E,GAC5E,iEAAiE,CAAC;AAC1E;AACA;AACA,MAAMC,gCAAgC,GAAG,IAAInQ,cAAc,CAAC,kCAAkC,CAAC;AAC/F;AACA,SAASoQ,wCAAwCA,CAACC,OAAO,EAAE;EACvD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAMC,iDAAiD,GAAG;EACtDvC,OAAO,EAAEkC,gCAAgC;EACzCM,IAAI,EAAE,CAAC1O,OAAO,CAAC;EACf2O,UAAU,EAAEN;AAChB,CAAC;AACD;AACA,MAAMO,2BAA2B,CAAC;EAC9B;AACJ;AACA;AACA;EACI,IAAIC,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACC,qBAAqB;EACrC;EACA,IAAID,oBAAoBA,CAACpJ,KAAK,EAAE;IAC5B,IAAI,CAACqJ,qBAAqB,GAAGxO,qBAAqB,CAACmF,KAAK,CAAC;EAC7D;EACAhB,WAAWA,CAACsK,QAAQ,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,KAAK,EAAE5I,kBAAkB,EAAE6I,cAAc,EAAEC,IAAI,EAAEC,UAAU,EAAEC,SAAS,EAAEC,cAAc,EAAEhJ,SAAS,EAAE;IAClJ,IAAI,CAACwI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC5I,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC8I,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAChJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiJ,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACV,qBAAqB,GAAG,KAAK;IAClC;IACA,IAAI,CAACW,sBAAsB,GAAG,KAAK;IACnC;IACA,IAAI,CAACC,qBAAqB,GAAG1O,YAAY,CAAC0F,KAAK;IAC/C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiJ,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI3O,OAAO,CAAC,CAAC;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC4O,kBAAkB,GAAG,MAAM;MAC5B;MACA;MACA;MACA,IAAI,CAACF,mBAAmB,GACpB,IAAI,CAACL,SAAS,CAACQ,aAAa,KAAK,IAAI,CAACf,QAAQ,CAAC1I,aAAa,IAAI,IAAI,CAAC0J,SAAS;IACtF,CAAC;IACD;IACA,IAAI,CAACC,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;IACA,IAAI,CAACC,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,MAAM;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;IACA,IAAI,CAACC,gBAAgB,GAAGnP,KAAK,CAAC,MAAM;MAChC,MAAMkG,OAAO,GAAG,IAAI,CAACkJ,YAAY,GAAG,IAAI,CAACA,YAAY,CAAClJ,OAAO,GAAG,IAAI;MACpE,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACmJ,OAAO,CAACC,IAAI,CAACxO,SAAS,CAACoF,OAAO,CAAC,EAAEnF,SAAS,CAAC,MAAMd,KAAK,CAAC,GAAGiG,OAAO,CAAChF,GAAG,CAACuC,MAAM,IAAIA,MAAM,CAAC8L,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA;MACA;MACA,OAAO,IAAI,CAACvB,KAAK,CAACwB,QAAQ,CAACF,IAAI,CAACtO,IAAI,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC,MAAM,IAAI,CAACoO,gBAAgB,CAAC,CAAC;IACpF,CAAC,CAAC;IACF;IACA,IAAI,CAACM,mBAAmB,GAAIpI,KAAK,IAAK;MAClC;MACA;MACA,IAAKA,KAAK,CAACqI,OAAO,KAAKtP,MAAM,IAAI,CAACC,cAAc,CAACgH,KAAK,CAAC,IAClDA,KAAK,CAACqI,OAAO,KAAKpP,QAAQ,IAAID,cAAc,CAACgH,KAAK,EAAE,QAAQ,CAAE,EAAE;QACjE;QACA;QACA,IAAI,IAAI,CAACsI,0BAA0B,EAAE;UACjC,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAACC,yBAAyB,IAAI,EAAE,CAAC;UAClE,IAAI,CAACF,0BAA0B,GAAG,IAAI;QAC1C;QACA,IAAI,CAACjB,oBAAoB,CAACrM,IAAI,CAAC,CAAC;QAChC,IAAI,CAACyN,gBAAgB,CAAC,CAAC;QACvB;QACA;QACAzI,KAAK,CAAC0I,eAAe,CAAC,CAAC;QACvB1I,KAAK,CAAC2I,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,eAAe,GAAGjC,cAAc;EACzC;EACAkC,eAAeA,CAAA,EAAG;IACd,MAAMC,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,EAAE;MAC/B,IAAI,CAACpC,KAAK,CAACsC,iBAAiB,CAAC,MAAMF,MAAM,CAACG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC5B,kBAAkB,CAAC,CAAC;IAChG;EACJ;EACA6B,WAAWA,CAACnB,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAACoB,iBAAiB,EAAE;MAC/C,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACD,iBAAiB,CAAC;MAClD,IAAI,IAAI,CAAC5B,SAAS,EAAE;QAChB,IAAI,CAAC8B,WAAW,CAACC,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACAhK,WAAWA,CAAA,EAAG;IACV,MAAMwJ,MAAM,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAChC,IAAI,OAAOD,MAAM,KAAK,WAAW,EAAE;MAC/BA,MAAM,CAACS,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAClC,kBAAkB,CAAC;IAC/D;IACA,IAAI,CAACH,qBAAqB,CAAC1H,WAAW,CAAC,CAAC;IACxC,IAAI,CAACwH,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACwC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACpC,oBAAoB,CAAC3E,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACgH,eAAe,CAAC,CAAC;EAC1B;EACA;EACA,IAAIlC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACK,gBAAgB,IAAI,IAAI,CAACE,YAAY,CAAC/K,SAAS;EAC/D;EACA;EACA2M,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,MAAMkB,OAAO,GAAG,IAAI,CAAC/B,YAAY,CAAC3M,EAAE;MACpCvD,mBAAmB,CAAC,IAAI,CAAC+Q,aAAa,EAAE,WAAW,EAAEkB,OAAO,CAAC;IACjE;EACJ;EACA;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACnC,gBAAgB,EAAE;MACxB;IACJ;IACA,IAAI,IAAI,CAACL,SAAS,EAAE;MAChB;MACA;MACA;MACA;MACA,IAAI,CAACb,KAAK,CAACsD,GAAG,CAAC,MAAM;QACjB,IAAI,CAAClC,YAAY,CAACxJ,MAAM,CAACa,IAAI,CAAC,CAAC;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAAC2I,YAAY,CAAChL,OAAO,GAAG,IAAI,CAAC8K,gBAAgB,GAAG,KAAK;IACzD,IAAI,CAACS,0BAA0B,GAAG,IAAI;IACtC,IAAI,IAAI,CAACgB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACY,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAACZ,WAAW,CAACa,MAAM,CAAC,CAAC;MACzB,IAAI,CAACC,2BAA2B,CAAC3K,WAAW,CAAC,CAAC;IAClD;IACA,IAAI,CAAC4K,iBAAiB,CAAC,CAAC;IACxB;IACA;IACA,IAAI,CAAC,IAAI,CAACpD,mBAAmB,EAAE;MAC3B;MACA;MACA;MACA;MACA,IAAI,CAAClJ,kBAAkB,CAACuM,aAAa,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,IAAI,CAAC1B,aAAa,EAAE;MACpB,MAAMkB,OAAO,GAAG,IAAI,CAAC/B,YAAY,CAAC3M,EAAE;MACpCtD,sBAAsB,CAAC,IAAI,CAAC8Q,aAAa,EAAE,WAAW,EAAEkB,OAAO,CAAC;IACpE;EACJ;EACA;AACJ;AACA;AACA;EACIP,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC1B,gBAAgB,EAAE;MACvB,IAAI,CAACyB,WAAW,CAACC,cAAc,CAAC,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIgB,mBAAmBA,CAAA,EAAG;IACtB,OAAO3R,KAAK,CAAC,IAAI,CAACkP,gBAAgB,EAAE,IAAI,CAACC,YAAY,CAACnJ,WAAW,CAAC4L,MAAM,CAACvC,IAAI,CAACrO,MAAM,CAAC,MAAM,IAAI,CAACiO,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACR,oBAAoB,EAAE,IAAI,CAACoD,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAACnB,WAAW,GACxL,IAAI,CAACA,WAAW,CAACoB,WAAW,CAAC,CAAC,CAACzC,IAAI,CAACrO,MAAM,CAAC,MAAM,IAAI,CAACiO,gBAAgB,CAAC,CAAC,GACxEhP,EAAE,CAAC,CAAC,CAAC,CAACoP,IAAI;IAChB;IACApO,GAAG,CAACmG,KAAK,IAAKA,KAAK,YAAYlJ,wBAAwB,GAAGkJ,KAAK,GAAG,IAAK,CAAC,CAAC;EAC7E;EACA;EACA,IAAI2K,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAAC5C,YAAY,IAAI,IAAI,CAACA,YAAY,CAACnJ,WAAW,EAAE;MACpD,OAAO,IAAI,CAACmJ,YAAY,CAACnJ,WAAW,CAACgM,UAAU;IACnD;IACA,OAAO,IAAI;EACf;EACA;EACAH,sBAAsBA,CAAA,EAAG;IACrB,OAAO7R,KAAK,CAACE,SAAS,CAAC,IAAI,CAACiO,SAAS,EAAE,OAAO,CAAC,EAAEjO,SAAS,CAAC,IAAI,CAACiO,SAAS,EAAE,UAAU,CAAC,EAAEjO,SAAS,CAAC,IAAI,CAACiO,SAAS,EAAE,UAAU,CAAC,CAAC,CAACkB,IAAI,CAACrO,MAAM,CAACoG,KAAK,IAAI;MAChJ;MACA;MACA,MAAM6K,WAAW,GAAG3S,eAAe,CAAC8H,KAAK,CAAC;MAC1C,MAAM8K,SAAS,GAAG,IAAI,CAAChE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACjJ,WAAW,CAACC,aAAa,GAAG,IAAI;MACpF,MAAMiN,YAAY,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC7F,UAAU,CAACrH,aAAa,GAAG,IAAI;MACxF,OAAQ,IAAI,CAAC+J,gBAAgB,IACzBgD,WAAW,KAAK,IAAI,CAACrE,QAAQ,CAAC1I,aAAa;MAC3C;MACA;MACA;MACA;MACA,IAAI,CAACiJ,SAAS,CAACQ,aAAa,KAAK,IAAI,CAACf,QAAQ,CAAC1I,aAAa,KAC3D,CAACgN,SAAS,IAAI,CAACA,SAAS,CAACG,QAAQ,CAACJ,WAAW,CAAC,CAAC,KAC/C,CAACE,YAAY,IAAI,CAACA,YAAY,CAACE,QAAQ,CAACJ,WAAW,CAAC,CAAC,IACtD,CAAC,CAAC,IAAI,CAACvB,WAAW,IAClB,CAAC,IAAI,CAACA,WAAW,CAAC4B,cAAc,CAACD,QAAQ,CAACJ,WAAW,CAAC;IAC9D,CAAC,CAAC,CAAC;EACP;EACA;EACAM,UAAUA,CAACjO,KAAK,EAAE;IACdkO,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACrO,KAAK,CAAC,CAAC;EACpE;EACA;EACAsO,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAChE,SAAS,GAAGgE,EAAE;EACvB;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAC/D,UAAU,GAAG+D,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACpF,QAAQ,CAAC1I,aAAa,CAACwC,QAAQ,GAAGsL,UAAU;EACrD;EACAC,cAAcA,CAAC7L,KAAK,EAAE;IAClB,MAAMqI,OAAO,GAAGrI,KAAK,CAACqI,OAAO;IAC7B,MAAMyD,WAAW,GAAG9S,cAAc,CAACgH,KAAK,CAAC;IACzC;IACA;IACA;IACA;IACA,IAAIqI,OAAO,KAAKtP,MAAM,IAAI,CAAC+S,WAAW,EAAE;MACpC9L,KAAK,CAAC2I,cAAc,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAACgC,YAAY,IAAItC,OAAO,KAAKnP,KAAK,IAAI,IAAI,CAACsO,SAAS,IAAI,CAACsE,WAAW,EAAE;MAC1E,IAAI,CAACnB,YAAY,CAACoB,qBAAqB,CAAC,CAAC;MACzC,IAAI,CAACtD,gBAAgB,CAAC,CAAC;MACvBzI,KAAK,CAAC2I,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI,IAAI,IAAI,CAACZ,YAAY,EAAE;MACxB,MAAMiE,cAAc,GAAG,IAAI,CAACjE,YAAY,CAACnJ,WAAW,CAACgM,UAAU;MAC/D,MAAMqB,UAAU,GAAG5D,OAAO,KAAKpP,QAAQ,IAAIoP,OAAO,KAAKlP,UAAU;MACjE,IAAIkP,OAAO,KAAKjP,GAAG,IAAK6S,UAAU,IAAI,CAACH,WAAW,IAAI,IAAI,CAACtE,SAAU,EAAE;QACnE,IAAI,CAACO,YAAY,CAACnJ,WAAW,CAACsN,SAAS,CAAClM,KAAK,CAAC;MAClD,CAAC,MACI,IAAIiM,UAAU,IAAI,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE;QACpC,IAAI,CAACxC,SAAS,CAAC,CAAC;MACpB;MACA,IAAIsC,UAAU,IAAI,IAAI,CAAClE,YAAY,CAACnJ,WAAW,CAACgM,UAAU,KAAKoB,cAAc,EAAE;QAC3E,IAAI,CAACI,eAAe,CAAC,IAAI,CAACrE,YAAY,CAACnJ,WAAW,CAACyN,eAAe,IAAI,CAAC,CAAC;QACxE,IAAI,IAAI,CAACtE,YAAY,CAACpL,sBAAsB,IAAI,IAAI,CAACgO,YAAY,EAAE;UAC/D,IAAI,CAAC,IAAI,CAACrC,0BAA0B,EAAE;YAClC,IAAI,CAACE,yBAAyB,GAAG,IAAI,CAAChC,QAAQ,CAAC1I,aAAa,CAACZ,KAAK;UACtE;UACA,IAAI,CAACoL,0BAA0B,GAAG,IAAI,CAACqC,YAAY;UACnD,IAAI,CAACY,kBAAkB,CAAC,IAAI,CAACZ,YAAY,CAACzN,KAAK,CAAC;QACpD;MACJ;IACJ;EACJ;EACAoP,YAAYA,CAACtM,KAAK,EAAE;IAChB,IAAIuM,MAAM,GAAGvM,KAAK,CAACuM,MAAM;IACzB,IAAIrP,KAAK,GAAGqP,MAAM,CAACrP,KAAK;IACxB;IACA,IAAIqP,MAAM,CAACrL,IAAI,KAAK,QAAQ,EAAE;MAC1BhE,KAAK,GAAGA,KAAK,IAAI,EAAE,GAAG,IAAI,GAAGsP,UAAU,CAACtP,KAAK,CAAC;IAClD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACuP,cAAc,KAAKvP,KAAK,EAAE;MAC/B,IAAI,CAACuP,cAAc,GAAGvP,KAAK;MAC3B,IAAI,CAACoL,0BAA0B,GAAG,IAAI;MACtC;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACP,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAClL,gBAAgB,EAAE;QAC3D,IAAI,CAAC4K,SAAS,CAACvK,KAAK,CAAC;MACzB;MACA,IAAI,CAACA,KAAK,EAAE;QACR,IAAI,CAACwP,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC;MAClD;MACA,IAAI,IAAI,CAACP,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACpF,SAAS,CAACQ,aAAa,KAAKvH,KAAK,CAACuM,MAAM,EAAE;QAClE,IAAI,CAAC5C,SAAS,CAAC,CAAC;MACpB;IACJ;EACJ;EACAgD,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACvF,mBAAmB,EAAE;MAC3B,IAAI,CAACA,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,IAAI,CAAC+E,QAAQ,CAAC,CAAC,EAAE;MACtB,IAAI,CAACM,cAAc,GAAG,IAAI,CAACjG,QAAQ,CAAC1I,aAAa,CAACZ,KAAK;MACvD,IAAI,CAAC0M,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;IAC1B;EACJ;EACA+C,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACT,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC3E,SAAS,EAAE;MACpC,IAAI,CAACmC,SAAS,CAAC,CAAC;IACpB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAACgD,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,IAAI,CAAC/F,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgG,UAAU,KAAK,MAAM,EAAE;MAC1D,IAAID,aAAa,EAAE;QACf,IAAI,CAAC/F,UAAU,CAACiG,oBAAoB,CAAC,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAACjG,UAAU,CAACgG,UAAU,GAAG,QAAQ;MACzC;MACA,IAAI,CAAC5F,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACA;EACA8C,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC9C,sBAAsB,EAAE;MAC7B,IAAI,IAAI,CAACJ,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACgG,UAAU,GAAG,MAAM;MACvC;MACA,IAAI,CAAC5F,sBAAsB,GAAG,KAAK;IACvC;EACJ;EACA;AACJ;AACA;AACA;EACI8F,0BAA0BA,CAAA,EAAG;IACzB,MAAMC,WAAW,GAAG,IAAI,CAACtG,KAAK,CAACwB,QAAQ,CAACF,IAAI,CAACtO,IAAI,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMuT,aAAa,GAAG,IAAI,CAACnF,YAAY,CAAClJ,OAAO,CAACmJ,OAAO,CAACC,IAAI,CAACnO,GAAG,CAAC,MAAM,IAAI,CAACsP,iBAAiB,CAAC+D,mBAAmB,CAAC,CAAC,CAAC;IACpH;IACA;IACApT,KAAK,CAAC,CAAC,CAAC,CAAC;IACT;IACA,OAAQnB,KAAK,CAACqU,WAAW,EAAEC,aAAa,CAAC,CACpCjF,IAAI;IACT;IACA;IACAvO,SAAS,CAAC,MAAM;MACZ;MACA;MACA;MACA,IAAI,CAACiN,KAAK,CAACsD,GAAG,CAAC,MAAM;QACjB,MAAMmD,OAAO,GAAG,IAAI,CAAC5F,SAAS;QAC9B,IAAI,CAACiB,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAAC4B,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACtM,kBAAkB,CAACuM,aAAa,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC9C,SAAS,EAAE;UAChB,IAAI,CAAC8B,WAAW,CAACC,cAAc,CAAC,CAAC;QACrC;QACA,IAAI6D,OAAO,KAAK,IAAI,CAAC5F,SAAS,EAAE;UAC5B;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACA,SAAS,EAAE;YAChB,IAAI,CAAC6F,qBAAqB,CAAC,CAAC;YAC5B,IAAI,CAACC,WAAW,CAAC,CAAC;UACtB,CAAC,MACI;YACD,IAAI,CAACvF,YAAY,CAACxJ,MAAM,CAACa,IAAI,CAAC,CAAC;UACnC;QACJ;MACJ,CAAC,CAAC;MACF,OAAO,IAAI,CAACmL,mBAAmB;IACnC,CAAC,CAAC;IACF;IACA5Q,IAAI,CAAC,CAAC,CAAC;IACH;IAAA,CACCuF,SAAS,CAACc,KAAK,IAAI,IAAI,CAACuN,iBAAiB,CAACvN,KAAK,CAAC,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACIsN,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvF,YAAY,CAACzJ,MAAM,CAACc,IAAI,CAAC,CAAC;EACnC;EACA;EACAiO,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACG,cAAc,GAAG,IAAI,CAAChH,QAAQ,CAAC1I,aAAa,CAACZ,KAAK;EAC3D;EACA;EACAuM,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACH,WAAW,EAAE;MAClB,IAAI,CAACS,UAAU,CAAC,CAAC;MACjB,IAAI,CAACT,WAAW,CAACmE,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACnE,WAAW,GAAG,IAAI;IAC3B;EACJ;EACAiC,kBAAkBA,CAACrO,KAAK,EAAE;IACtB,MAAMwQ,SAAS,GAAG,IAAI,CAAC3F,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC3J,WAAW,GAC9D,IAAI,CAAC2J,YAAY,CAAC3J,WAAW,CAAClB,KAAK,CAAC,GACpCA,KAAK;IACX;IACA;IACA,IAAI,CAACqL,uBAAuB,CAACmF,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,EAAE,CAAC;EACpE;EACAnF,uBAAuBA,CAACrL,KAAK,EAAE;IAC3B;IACA;IACA,IAAI,IAAI,CAAC4J,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC6G,QAAQ,CAACzQ,KAAK,GAAGA,KAAK;IAC1C,CAAC,MACI;MACD,IAAI,CAACsJ,QAAQ,CAAC1I,aAAa,CAACZ,KAAK,GAAGA,KAAK;IAC7C;IACA,IAAI,CAACuP,cAAc,GAAGvP,KAAK;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACIqQ,iBAAiBA,CAACvN,KAAK,EAAE;IACrB,MAAMJ,KAAK,GAAG,IAAI,CAACmI,YAAY;IAC/B,MAAM6F,QAAQ,GAAG5N,KAAK,GAAGA,KAAK,CAAC7D,MAAM,GAAG,IAAI,CAACmM,0BAA0B;IACvE,IAAIsF,QAAQ,EAAE;MACV,IAAI,CAAClB,4BAA4B,CAACkB,QAAQ,CAAC;MAC3C,IAAI,CAACrC,kBAAkB,CAACqC,QAAQ,CAAC1Q,KAAK,CAAC;MACvC;MACA;MACA;MACA,IAAI,CAACuK,SAAS,CAACmG,QAAQ,CAAC1Q,KAAK,CAAC;MAC9B0C,KAAK,CAACG,gBAAgB,CAAC6N,QAAQ,CAAC;MAChC,IAAI,CAACpH,QAAQ,CAAC1I,aAAa,CAAC+P,KAAK,CAAC,CAAC;IACvC,CAAC,MACI,IAAIjO,KAAK,CAAC/C,gBAAgB,IAC3B,IAAI,CAAC2J,QAAQ,CAAC1I,aAAa,CAACZ,KAAK,KAAK,IAAI,CAACsQ,cAAc,EAAE;MAC3D,IAAI,CAACd,4BAA4B,CAAC,IAAI,CAAC;MACvC,IAAI,CAACnB,kBAAkB,CAAC,IAAI,CAAC;MAC7B;MACA;MACA,IAAI3L,KAAK,CAAC7E,cAAc,EAAE;QACtB6E,KAAK,CAAC7E,cAAc,CAACkN,IAAI,CAACtO,IAAI,CAAC,CAAC,CAAC,CAAC,CAACuF,SAAS,CAAC,MAAM,IAAI,CAACuI,SAAS,CAAC,IAAI,CAAC,CAAC;MAC5E,CAAC,MACI;QACD,IAAI,CAACA,SAAS,CAAC,IAAI,CAAC;MACxB;IACJ;IACA,IAAI,CAACsC,UAAU,CAAC,CAAC;EACrB;EACA;AACJ;AACA;EACI2C,4BAA4BA,CAACoB,IAAI,EAAEC,SAAS,EAAE;IAC1C;IACA;IACA,IAAI,CAAChG,YAAY,EAAElJ,OAAO,EAAEmP,OAAO,CAAC5R,MAAM,IAAI;MAC1C,IAAIA,MAAM,KAAK0R,IAAI,IAAI1R,MAAM,CAAC6R,QAAQ,EAAE;QACpC7R,MAAM,CAAC8R,QAAQ,CAACH,SAAS,CAAC;MAC9B;IACJ,CAAC,CAAC;EACN;EACAnE,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC7B,YAAY,KAAK,OAAO/F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACvE,MAAM2D,mCAAmC,CAAC,CAAC;IAC/C;IACA,IAAIwI,UAAU,GAAG,IAAI,CAAC7E,WAAW;IACjC,IAAI,CAAC6E,UAAU,EAAE;MACb,IAAI,CAACC,OAAO,GAAG,IAAI/U,cAAc,CAAC,IAAI,CAAC0O,YAAY,CAACtG,QAAQ,EAAE,IAAI,CAACiF,iBAAiB,EAAE;QAClFtL,EAAE,EAAE,IAAI,CAAC0L,UAAU,EAAEuH,UAAU,CAAC;MACpC,CAAC,CAAC;MACFF,UAAU,GAAG,IAAI,CAAC1H,QAAQ,CAAC6H,MAAM,CAAC,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACjF,WAAW,GAAG6E,UAAU;MAC7B,IAAI,CAAChH,qBAAqB,GAAG,IAAI,CAACH,cAAc,CAAC/H,MAAM,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;QACtE,IAAI,IAAI,CAACsI,SAAS,IAAI2G,UAAU,EAAE;UAC9BA,UAAU,CAACK,UAAU,CAAC;YAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;UAAE,CAAC,CAAC;QAC3D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,IAAI,CAACtF,iBAAiB,CAACuF,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;MAC7DT,UAAU,CAACK,UAAU,CAAC;QAAEC,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC;MAAE,CAAC,CAAC;IAC3D;IACA,IAAIP,UAAU,IAAI,CAACA,UAAU,CAACjE,WAAW,CAAC,CAAC,EAAE;MACzCiE,UAAU,CAACU,MAAM,CAAC,IAAI,CAACT,OAAO,CAAC;MAC/B,IAAI,CAAChE,2BAA2B,GAAG,IAAI,CAAC4C,0BAA0B,CAAC,CAAC;IACxE;IACA,MAAMI,OAAO,GAAG,IAAI,CAAC5F,SAAS;IAC9B,IAAI,CAACO,YAAY,CAAChL,OAAO,GAAG,IAAI,CAAC8K,gBAAgB,GAAG,IAAI;IACxD,IAAI,CAACE,YAAY,CAAC9K,SAAS,CAAC,IAAI,CAAC6J,UAAU,EAAEgI,KAAK,CAAC;IACnD,IAAI,CAACzE,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC0E,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAAC1B,qBAAqB,CAAC,CAAC;IAC5B;IACA;IACA,IAAI,IAAI,CAAC7F,SAAS,IAAI4F,OAAO,KAAK,IAAI,CAAC5F,SAAS,EAAE;MAC9C,IAAI,CAAC8F,WAAW,CAAC,CAAC;IACtB;EACJ;EACA;EACAjD,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACtC,YAAY,CAACzI,cAAc,CAAC,CAAC;IAClC;IACA;IACA;IACA,IAAI,IAAI,CAACkI,SAAS,EAAE;MAChB,MAAM2G,UAAU,GAAG,IAAI,CAAC7E,WAAW;MACnC,IAAI,CAAC,IAAI,CAAC0F,oBAAoB,EAAE;QAC5B;QACA;QACA,IAAI,CAACA,oBAAoB,GAAGb,UAAU,CAACc,aAAa,CAAC,CAAC,CAAC/P,SAAS,CAAC,IAAI,CAACkJ,mBAAmB,CAAC;MAC9F;MACA,IAAI,CAAC,IAAI,CAAC8G,yBAAyB,EAAE;QACjC;QACA;QACA;QACA,IAAI,CAACA,yBAAyB,GAAGf,UAAU,CAACgB,oBAAoB,CAAC,CAAC,CAACjQ,SAAS,CAAC,CAAC;MAClF;IACJ,CAAC,MACI;MACD,IAAI,CAAC8P,oBAAoB,EAAEvP,WAAW,CAAC,CAAC;MACxC,IAAI,CAACyP,yBAAyB,EAAEzP,WAAW,CAAC,CAAC;MAC7C,IAAI,CAACuP,oBAAoB,GAAG,IAAI,CAACE,yBAAyB,GAAG,IAAI;IACrE;EACJ;EACAX,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI7W,aAAa,CAAC;MACrB0X,gBAAgB,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC5CzI,cAAc,EAAE,IAAI,CAACiC,eAAe,CAAC,CAAC;MACtC4F,KAAK,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;MAC5BY,SAAS,EAAE,IAAI,CAACzI,IAAI,IAAI3E,SAAS;MACjCqN,UAAU,EAAE,IAAI,CAACvR,SAAS,EAAEwR;IAChC,CAAC,CAAC;EACN;EACAH,mBAAmBA,CAAA,EAAG;IAClB,MAAMI,QAAQ,GAAG,IAAI,CAAChJ,QAAQ,CACzBkB,QAAQ,CAAC,CAAC,CACV+H,mBAAmB,CAAC,IAAI,CAACd,oBAAoB,CAAC,CAAC,CAAC,CAChDe,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,QAAQ,CAAC,KAAK,CAAC;IACpB,IAAI,CAACvG,qBAAqB,CAACoG,QAAQ,CAAC;IACpC,IAAI,CAACrG,iBAAiB,GAAGqG,QAAQ;IACjC,OAAOA,QAAQ;EACnB;EACA;EACApG,qBAAqBA,CAAC+F,gBAAgB,EAAE;IACpC;IACA;IACA,MAAMS,cAAc,GAAG,CACnB;MAAEC,OAAO,EAAE,OAAO;MAAEC,OAAO,EAAE,QAAQ;MAAEC,QAAQ,EAAE,OAAO;MAAEC,QAAQ,EAAE;IAAM,CAAC,EAC3E;MAAEH,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE,QAAQ;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAC1E;IACD;IACA;IACA;IACA,MAAMV,UAAU,GAAG,IAAI,CAACW,WAAW;IACnC,MAAMC,cAAc,GAAG,CACnB;MAAEL,OAAO,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,QAAQ,EAAE,OAAO;MAAEC,QAAQ,EAAE,QAAQ;MAAEV;IAAW,CAAC,EACvF;MAAEO,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE,KAAK;MAAEC,QAAQ,EAAE,KAAK;MAAEC,QAAQ,EAAE,QAAQ;MAAEV;IAAW,CAAC,CACtF;IACD,IAAIa,SAAS;IACb,IAAI,IAAI,CAACzI,QAAQ,KAAK,OAAO,EAAE;MAC3ByI,SAAS,GAAGD,cAAc;IAC9B,CAAC,MACI,IAAI,IAAI,CAACxI,QAAQ,KAAK,OAAO,EAAE;MAChCyI,SAAS,GAAGP,cAAc;IAC9B,CAAC,MACI;MACDO,SAAS,GAAG,CAAC,GAAGP,cAAc,EAAE,GAAGM,cAAc,CAAC;IACtD;IACAf,gBAAgB,CAACiB,aAAa,CAACD,SAAS,CAAC;EAC7C;EACAxB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5D,WAAW,EAAE;MAClB,OAAO,IAAI,CAACA,WAAW,CAAC7F,UAAU;IACtC;IACA,OAAO,IAAI,CAAC2B,UAAU,GAAG,IAAI,CAACA,UAAU,CAACwJ,yBAAyB,CAAC,CAAC,GAAG,IAAI,CAAC9J,QAAQ;EACxF;EACAkI,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3G,YAAY,CAACnG,UAAU,IAAI,IAAI,CAAC2O,aAAa,CAAC,CAAC;EAC/D;EACA;EACAA,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC3B,oBAAoB,CAAC,CAAC,CAAC9Q,aAAa,CAAC0S,qBAAqB,CAAC,CAAC,CAAC/B,KAAK;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIhG,gBAAgBA,CAAA,EAAG;IACf,MAAMV,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,CAACrL,qBAAqB,EAAE;MACpC;MACA;MACA;MACA,IAAI+T,uBAAuB,GAAG,CAAC,CAAC;MAChC,KAAK,IAAItR,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG4I,YAAY,CAAClJ,OAAO,CAACpB,MAAM,EAAE0B,KAAK,EAAE,EAAE;QAC9D,MAAM/C,MAAM,GAAG2L,YAAY,CAAClJ,OAAO,CAAC6R,GAAG,CAACvR,KAAK,CAAC;QAC9C,IAAI,CAAC/C,MAAM,CAACkE,QAAQ,EAAE;UAClBmQ,uBAAuB,GAAGtR,KAAK;UAC/B;QACJ;MACJ;MACA4I,YAAY,CAACnJ,WAAW,CAAC+R,aAAa,CAACF,uBAAuB,CAAC;IACnE,CAAC,MACI;MACD1I,YAAY,CAACnJ,WAAW,CAAC+R,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9C;EACJ;EACA;EACAxE,QAAQA,CAAA,EAAG;IACP,MAAMyE,OAAO,GAAG,IAAI,CAACpK,QAAQ,CAAC1I,aAAa;IAC3C,OAAO,CAAC8S,OAAO,CAACC,QAAQ,IAAI,CAACD,OAAO,CAACtQ,QAAQ,IAAI,CAAC,IAAI,CAACiG,qBAAqB;EAChF;EACA;EACAyC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjC,SAAS,EAAE+J,WAAW,IAAI/H,MAAM;EAChD;EACA;EACAqD,eAAeA,CAACjN,KAAK,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAM4I,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAMgJ,UAAU,GAAGha,6BAA6B,CAACoI,KAAK,EAAE4I,YAAY,CAAClJ,OAAO,EAAEkJ,YAAY,CAACzE,YAAY,CAAC;IACxG,IAAInE,KAAK,KAAK,CAAC,IAAI4R,UAAU,KAAK,CAAC,EAAE;MACjC;MACA;MACA;MACAhJ,YAAY,CAACrI,aAAa,CAAC,CAAC,CAAC;IACjC,CAAC,MACI,IAAIqI,YAAY,CAACnI,KAAK,EAAE;MACzB,MAAMxD,MAAM,GAAG2L,YAAY,CAAClJ,OAAO,CAACQ,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC;MACpD,IAAI/C,MAAM,EAAE;QACR,MAAMwU,OAAO,GAAGxU,MAAM,CAAC4U,eAAe,CAAC,CAAC;QACxC,MAAMC,iBAAiB,GAAGja,wBAAwB,CAAC4Z,OAAO,CAACM,SAAS,EAAEN,OAAO,CAACO,YAAY,EAAEpJ,YAAY,CAAClI,aAAa,CAAC,CAAC,EAAEkI,YAAY,CAACnI,KAAK,CAAC9B,aAAa,CAACqT,YAAY,CAAC;QACxKpJ,YAAY,CAACrI,aAAa,CAACuR,iBAAiB,CAAC;MACjD;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlC,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMqC,KAAK,GAAG,IAAI,CAAC5K,QAAQ,CAAC1I,aAAa,CAACuT,OAAO,CAAC,mDAAmD,CAAC;IACtG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAMtH,OAAO,GAAG,IAAI,CAAC/B,YAAY,CAAC3M,EAAE;IACpC,IAAI,IAAI,CAACwN,aAAa,EAAE;MACpB9Q,sBAAsB,CAAC,IAAI,CAAC8Q,aAAa,EAAE,WAAW,EAAEkB,OAAO,CAAC;IACpE;IACAjS,mBAAmB,CAACuZ,KAAK,EAAE,WAAW,EAAEtH,OAAO,CAAC;IAChD,IAAI,CAAClB,aAAa,GAAGwI,KAAK;EAC9B;EACA;EACA1H,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACd,aAAa,EAAE;MACpB,MAAMkB,OAAO,GAAG,IAAI,CAAC/B,YAAY,CAAC3M,EAAE;MACpCtD,sBAAsB,CAAC,IAAI,CAAC8Q,aAAa,EAAE,WAAW,EAAEkB,OAAO,CAAC;MAChE,IAAI,CAAClB,aAAa,GAAG,IAAI;IAC7B;EACJ;EAAC,QAAArI,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA8Q,oCAAA5Q,CAAA;IAAA,YAAAA,CAAA,IAAwF2F,2BAA2B,EAj3BrC5Q,EAAE,CAAAkL,iBAAA,CAi3BqDlL,EAAE,CAACoL,UAAU,GAj3BpEpL,EAAE,CAAAkL,iBAAA,CAi3B+EnJ,IAAI,CAACC,OAAO,GAj3B7FhC,EAAE,CAAAkL,iBAAA,CAi3BwGlL,EAAE,CAAC8b,gBAAgB,GAj3B7H9b,EAAE,CAAAkL,iBAAA,CAi3BwIlL,EAAE,CAAC+b,MAAM,GAj3BnJ/b,EAAE,CAAAkL,iBAAA,CAi3B8JlL,EAAE,CAACmL,iBAAiB,GAj3BpLnL,EAAE,CAAAkL,iBAAA,CAi3B+LkF,gCAAgC,GAj3BjOpQ,EAAE,CAAAkL,iBAAA,CAi3B4O3G,IAAI,CAACyX,cAAc,MAj3BjQhc,EAAE,CAAAkL,iBAAA,CAi3B4RnH,cAAc,MAj3B5S/D,EAAE,CAAAkL,iBAAA,CAi3BmVvJ,QAAQ,MAj3B7V3B,EAAE,CAAAkL,iBAAA,CAi3BwXrJ,EAAE,CAACoa,aAAa,GAj3B1Yjc,EAAE,CAAAkL,iBAAA,CAi3BqZrE,gCAAgC;EAAA,CAA4D;EAAA,QAAAyE,EAAA,GAC1kB,IAAI,CAACC,IAAI,kBAl3B8EvL,EAAE,CAAAwL,iBAAA;IAAAC,IAAA,EAk3BJmF,2BAA2B;IAAA1E,MAAA;MAAAoG,YAAA;MAAAJ,QAAA;MAAAqD,WAAA;MAAApD,qBAAA;MAAAtB,oBAAA;IAAA;IAAAxE,QAAA,GAl3BzBrM,EAAE,CAAAkc,oBAAA;EAAA,EAk3BwX;AAC9d;AACA;EAAA,QAAA3P,SAAA,oBAAAA,SAAA,KAp3BoGvM,EAAE,CAAAwM,iBAAA,CAo3BXoE,2BAA2B,EAAc,CAAC;IACzHnF,IAAI,EAAErL;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEqL,IAAI,EAAEzL,EAAE,CAACoL;IAAW,CAAC,EAAE;MAAEK,IAAI,EAAE1J,IAAI,CAACC;IAAQ,CAAC,EAAE;MAAEyJ,IAAI,EAAEzL,EAAE,CAAC8b;IAAiB,CAAC,EAAE;MAAErQ,IAAI,EAAEzL,EAAE,CAAC+b;IAAO,CAAC,EAAE;MAAEtQ,IAAI,EAAEzL,EAAE,CAACmL;IAAkB,CAAC,EAAE;MAAEM,IAAI,EAAEgB,SAAS;MAAEC,UAAU,EAAE,CAAC;QACnMjB,IAAI,EAAEpL,MAAM;QACZsM,IAAI,EAAE,CAACyD,gCAAgC;MAC3C,CAAC;IAAE,CAAC,EAAE;MAAE3E,IAAI,EAAElH,IAAI,CAACyX,cAAc;MAAEtP,UAAU,EAAE,CAAC;QAC5CjB,IAAI,EAAE3K;MACV,CAAC;IAAE,CAAC,EAAE;MAAE2K,IAAI,EAAE3H,EAAE,CAACqY,YAAY;MAAEzP,UAAU,EAAE,CAAC;QACxCjB,IAAI,EAAE3K;MACV,CAAC,EAAE;QACC2K,IAAI,EAAEpL,MAAM;QACZsM,IAAI,EAAE,CAAC5I,cAAc;MACzB,CAAC,EAAE;QACC0H,IAAI,EAAE1K;MACV,CAAC;IAAE,CAAC,EAAE;MAAE0K,IAAI,EAAEgB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCjB,IAAI,EAAE3K;MACV,CAAC,EAAE;QACC2K,IAAI,EAAEpL,MAAM;QACZsM,IAAI,EAAE,CAAChL,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE8J,IAAI,EAAE5J,EAAE,CAACoa;IAAc,CAAC,EAAE;MAAExQ,IAAI,EAAEgB,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DjB,IAAI,EAAE3K;MACV,CAAC,EAAE;QACC2K,IAAI,EAAEpL,MAAM;QACZsM,IAAI,EAAE,CAAC9F,gCAAgC;MAC3C,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEyL,YAAY,EAAE,CAAC;MAC3C7G,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEuF,QAAQ,EAAE,CAAC;MACXzG,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE4I,WAAW,EAAE,CAAC;MACd9J,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEwF,qBAAqB,EAAE,CAAC;MACxB1G,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEkE,oBAAoB,EAAE,CAAC;MACvBpF,IAAI,EAAElL,KAAK;MACXoM,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMqD,sBAAsB,SAASY,2BAA2B,CAAC;EAC7DnK,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGqG,SAAS,CAAC;IACnB,IAAI,CAAC2N,WAAW,GAAG,kCAAkC;EACzD;EAAC,QAAA3P,CAAA,GACQ,IAAI,CAACC,IAAI;IAAA,IAAAqR,mCAAA;IAAA,gBAAAC,+BAAApR,CAAA;MAAA,QAAAmR,mCAAA,KAAAA,mCAAA,GAj6B8Epc,EAAE,CAAAqN,qBAAA,CAi6BQ2C,sBAAsB,IAAA/E,CAAA,IAAtB+E,sBAAsB;IAAA;EAAA,GAAqD;EAAA,QAAA1E,EAAA,GAC5K,IAAI,CAACC,IAAI,kBAl6B8EvL,EAAE,CAAAwL,iBAAA;IAAAC,IAAA,EAk6BJuE,sBAAsB;IAAAxC,SAAA;IAAAM,SAAA;IAAAwO,QAAA;IAAAC,YAAA,WAAAC,oCAAA9X,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAl6BpB1E,EAAE,CAAA+E,UAAA,qBAAA0X,kDAAA;UAAA,OAk6BJ9X,GAAA,CAAAuS,YAAA,CAAa,CAAC;QAAA,oBAAAwF,+CAAA;UAAA,OAAd/X,GAAA,CAAAsN,UAAA,CAAW,CAAC;QAAA,qBAAA0K,gDAAA1X,MAAA;UAAA,OAAZN,GAAA,CAAAkS,YAAA,CAAA5R,MAAmB,CAAC;QAAA,uBAAA2X,kDAAA3X,MAAA;UAAA,OAApBN,GAAA,CAAAyR,cAAA,CAAAnR,MAAqB,CAAC;QAAA,qBAAA4X,gDAAA;UAAA,OAAtBlY,GAAA,CAAAwS,YAAA,CAAa,CAAC;QAAA;MAAA;MAAA,IAAAzS,EAAA;QAl6BZ1E,EAAE,CAAAgG,WAAA,iBAAArB,GAAA,CAAAwN,qBAAA,UAAAxN,GAAA,CAAAkM,oBAAA,2CAAAlM,GAAA,CAAAkM,oBAAA,2CAAAlM,GAAA,CAAAoN,SAAA,IAAApN,GAAA,CAAAuQ,YAAA,GAAAvQ,GAAA,CAAAuQ,YAAA,CAAAvP,EAAA,0BAAAhB,GAAA,CAAAkM,oBAAA,UAAAlM,GAAA,CAAAoN,SAAA,CAAA+K,QAAA,qBAAAnY,GAAA,CAAAkM,oBAAA,KAAAlM,GAAA,CAAAoN,SAAA,UAAApN,GAAA,CAAA2N,YAAA,kBAAA3N,GAAA,CAAA2N,YAAA,CAAA3M,EAAA,mBAAAhB,GAAA,CAAAkM,oBAAA;MAAA;IAAA;IAAA7C,QAAA;IAAA3B,QAAA,GAAFrM,EAAE,CAAAiO,kBAAA,CAk6BuzB,CAAC8B,+BAA+B,CAAC,GAl6B11B/P,EAAE,CAAAsM,0BAAA;EAAA,EAk6Bs6B;AAC5gC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAp6BoGvM,EAAE,CAAAwM,iBAAA,CAo6BXwD,sBAAsB,EAAc,CAAC;IACpHvE,IAAI,EAAErL,SAAS;IACfuM,IAAI,EAAE,CAAC;MACCuC,QAAQ,EAAG,mDAAkD;MAC7DG,IAAI,EAAE;QACF,OAAO,EAAE,8BAA8B;QACvC,qBAAqB,EAAE,uBAAuB;QAC9C,aAAa,EAAE,0CAA0C;QACzD,0BAA0B,EAAE,sCAAsC;QAClE,8BAA8B,EAAE,sDAAsD;QACtF,sBAAsB,EAAE,oDAAoD;QAC5E,sBAAsB,EAAE,gEAAgE;QACxF,sBAAsB,EAAE,yCAAyC;QACjE;QACA;QACA,WAAW,EAAE,gBAAgB;QAC7B,QAAQ,EAAE,cAAc;QACxB,SAAS,EAAE,sBAAsB;QACjC,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE;MACf,CAAC;MACDrB,QAAQ,EAAE,wBAAwB;MAClCsB,SAAS,EAAE,CAACS,+BAA+B;IAC/C,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMgN,qBAAqB,CAAC;EAAA,QAAAjS,CAAA,GACf,IAAI,CAACC,IAAI,YAAAiS,8BAAA/R,CAAA;IAAA,YAAAA,CAAA,IAAwF8R,qBAAqB;EAAA,CAAkD;EAAA,QAAAzR,EAAA,GACxK,IAAI,CAAC2R,IAAI,kBAh8B8Ejd,EAAE,CAAAkd,gBAAA;IAAAzR,IAAA,EAg8BSsR;EAAqB,EAK/F;EAAA,QAAAI,EAAA,GACxB,IAAI,CAACC,IAAI,kBAt8B8Epd,EAAE,CAAAqd,gBAAA;IAAA/N,SAAA,EAs8B2C,CAACmB,iDAAiD,CAAC;IAAA6M,OAAA,GAAYpb,aAAa,EAAEV,eAAe,EAAEC,eAAe,EAAEG,YAAY,EAAEE,mBAAmB,EACtRN,eAAe,EACfC,eAAe;EAAA,EAAI;AAC/B;AACA;EAAA,QAAA8K,SAAA,oBAAAA,SAAA,KA18BoGvM,EAAE,CAAAwM,iBAAA,CA08BXuQ,qBAAqB,EAAc,CAAC;IACnHtR,IAAI,EAAEzK,QAAQ;IACd2L,IAAI,EAAE,CAAC;MACC2Q,OAAO,EAAE,CAACpb,aAAa,EAAEV,eAAe,EAAEC,eAAe,EAAEG,YAAY,CAAC;MACxE2b,OAAO,EAAE,CACLzb,mBAAmB,EACnB+K,eAAe,EACfrL,eAAe,EACfC,eAAe,EACfuO,sBAAsB,EACtBJ,qBAAqB,CACxB;MACD4N,YAAY,EAAE,CAAC3Q,eAAe,EAAEmD,sBAAsB,EAAEJ,qBAAqB,CAAC;MAC9EN,SAAS,EAAE,CAACmB,iDAAiD;IACjE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS5J,gCAAgC,EAAEG,wCAAwC,EAAEoJ,gCAAgC,EAAEC,wCAAwC,EAAEI,iDAAiD,EAAEV,+BAA+B,EAAElD,eAAe,EAAEkQ,qBAAqB,EAAEnN,qBAAqB,EAAEpJ,4BAA4B,EAAEwJ,sBAAsB,EAAE3I,oBAAoB,EAAEoI,0BAA0B,EAAEmB,2BAA2B,EAAEV,mCAAmC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}