package com.navitsa.mydent.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.navitsa.mydent.entity.LaboratoryOrder;
import com.navitsa.mydent.services.LaboratoryOrderService;

@RestController
public class LaboratoryOrderController {

	@Autowired
    private LaboratoryOrderService laboratoryOrderService;
	
	@PostMapping("/saveLaboratoryOrder/{userId}")
	public LaboratoryOrder saveLaboratoryOrder(@PathVariable("userId") int userId, @RequestBody LaboratoryOrder laboratoryOrder) {
	    return laboratoryOrderService.saveLaboratoryOrder(laboratoryOrder, userId);
	}

    @GetMapping("/getLaboratoryOrdersList")
    public List<LaboratoryOrder> getAllLaboratoryOrders() {
        return laboratoryOrderService.findAllLaboratoryOrders();
    }

    @PutMapping("/updateLaboratoryOrder/{id}")
    public ResponseEntity<LaboratoryOrder> updateLaboratoryOrder(@PathVariable int id, @RequestBody LaboratoryOrder laboratoryOrder) {
        return ResponseEntity.ok(laboratoryOrderService.updateLaboratoryOrder(id, laboratoryOrder));
    }

    @GetMapping("/getLaboratoryOrderById/{id}")
    public LaboratoryOrder getLaboratoryOrderById(@PathVariable int id) {
        return laboratoryOrderService.getLaboratoryOrderById(id);
    }

    @DeleteMapping("/deleteLaboratoryOrder/{id}")
    public ResponseEntity<Void> deleteLaboratoryOrder(@PathVariable int id) {
    	laboratoryOrderService.deleteLaboratoryOrder(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/getClinicOrdersByLabUserId/{userId}")
    public List<LaboratoryOrder> getClinicOrdersByLabUserId(@PathVariable Integer userId) {
        return laboratoryOrderService.getClinicOrdersByLabUserId(userId);
    }
    
    @GetMapping("/getLaboratoryOrdersByClinicId/{userId}")
    public List<LaboratoryOrder> getLaboratoryOrdersByClinicId(@PathVariable Integer userId) {
        return laboratoryOrderService.getLaboratoryOrdersByClinicId(userId);
    }

    @PutMapping("/updateOrderStatus/{id}")
    public ResponseEntity<LaboratoryOrder> updateOrderStatus(@PathVariable int id, @RequestBody String status) {
        return ResponseEntity.ok(laboratoryOrderService.updateOrderStatus(id, status));
    }
}
