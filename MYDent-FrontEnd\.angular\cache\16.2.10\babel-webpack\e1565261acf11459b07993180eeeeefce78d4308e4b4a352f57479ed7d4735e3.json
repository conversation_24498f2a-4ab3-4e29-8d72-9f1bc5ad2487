{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../http.service\";\nclass SupplierService {\n  constructor(httpService) {\n    this.httpService = httpService;\n  }\n  saveSupplierInventory(formData) {\n    return this.httpService.request('POST', '/saveSupplierInventoryItem', formData);\n  }\n  getSupplierByUserId(userId) {\n    return this.httpService.request('GET', `/getSupplierByUserId/${userId}`, {});\n  }\n  saveSupplier(supplier) {\n    return this.httpService.request('POST', '/saveSupplier', supplier);\n  }\n  getAllSuppliers() {\n    return this.httpService.request('GET', '/supplierList', {});\n  }\n  getSupplierById(id) {\n    return this.httpService.request('GET', `/getSupplierById/${id}`, {});\n  }\n  updateSupplier(id, supplier) {\n    return this.httpService.request('PUT', `/updateSupplier/${id}`, supplier);\n  }\n  deleteSupplier(id) {\n    return this.httpService.request('DELETE', `/deleteSupplier/${id}`, {});\n  }\n  supplierNameExists(supplierName) {\n    const params = {\n      supplierName\n    };\n    return this.httpService.request('GET', `/check-Supplier`, null, params);\n  }\n  // Supplier-Clinic APIs\n  getOrderRequestBySupplierId(supplierId) {\n    return this.httpService.request('GET', `/getClinicOrdersHeadersFromSupplierId/${supplierId}`, null);\n  }\n  getOrderDetailsBySupplierId(headerId) {\n    return this.httpService.request('GET', `/getClinicOrdersDetailsFromSupplierId/${headerId}`, null);\n  }\n  static #_ = this.ɵfac = function SupplierService_Factory(t) {\n    return new (t || SupplierService)(i0.ɵɵinject(i1.HttpService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: SupplierService,\n    factory: SupplierService.ɵfac,\n    providedIn: 'root'\n  });\n}\nexport { SupplierService };", "map": {"version": 3, "names": ["SupplierService", "constructor", "httpService", "saveSupplierInventory", "formData", "request", "getSupplierByUserId", "userId", "saveSupplier", "supplier", "getAllSuppliers", "getSupplierById", "id", "updateSupplier", "deleteSupplier", "supplierNameExists", "supplierName", "params", "getOrderRequestBySupplierId", "supplierId", "getOrderDetailsBySupplierId", "headerId", "_", "i0", "ɵɵinject", "i1", "HttpService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\supplier\\supplier.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Supplier, SupplierInventory } from './supplier';\r\nimport { Observable } from 'rxjs';\r\nimport { HttpService } from '../http.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class SupplierService {\r\n  constructor(private httpService: HttpService) {}\r\n\r\n  saveSupplierInventory(formData: FormData): Observable<any> {\r\n    return this.httpService.request('POST', '/saveSupplierInventoryItem', formData);\r\n  }\r\n\r\n\r\n  getSupplierByUserId(userId: number): Observable<Supplier> {\r\n    return this.httpService.request('GET', `/getSupplierByUserId/${userId}`, {});\r\n  }\r\n\r\n  saveSupplier(supplier: Supplier): Observable<any> {\r\n    return this.httpService.request('POST', '/saveSupplier', supplier);\r\n  }\r\n\r\n  getAllSuppliers(): Observable<Supplier[]> {\r\n    return this.httpService.request('GET', '/supplierList', {});\r\n  }\r\n\r\n  getSupplierById(id: number): Observable<Supplier> {\r\n    return this.httpService.request('GET', `/getSupplierById/${id}`, {});\r\n  }\r\n\r\n  updateSupplier(id: number, supplier: Supplier): Observable<object> {\r\n    return this.httpService.request('PUT', `/updateSupplier/${id}`, supplier);\r\n  }\r\n\r\n  deleteSupplier(id: number): Observable<any> {\r\n    return this.httpService.request('DELETE', `/deleteSupplier/${id}`, {});\r\n  }\r\n\r\n  supplierNameExists(supplierName: string): Observable<any> {\r\n    const params = { supplierName };\r\n    return this.httpService.request('GET', `/check-Supplier`, null, params);\r\n  }\r\n\r\n  // Supplier-Clinic APIs\r\n  getOrderRequestBySupplierId(supplierId:number) {\r\n    return this.httpService.request('GET',`/getClinicOrdersHeadersFromSupplierId/${supplierId}`,null)\r\n  }\r\n\r\n  getOrderDetailsBySupplierId(headerId:number) {\r\n    return this.httpService.request('GET',`/getClinicOrdersDetailsFromSupplierId/${headerId}`,null)\r\n  }\r\n}\r\n"], "mappings": ";;AAKA,MAGaA,eAAe;EAC1BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,qBAAqBA,CAACC,QAAkB;IACtC,OAAO,IAAI,CAACF,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,4BAA4B,EAAED,QAAQ,CAAC;EACjF;EAGAE,mBAAmBA,CAACC,MAAc;IAChC,OAAO,IAAI,CAACL,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,wBAAwBE,MAAM,EAAE,EAAE,EAAE,CAAC;EAC9E;EAEAC,YAAYA,CAACC,QAAkB;IAC7B,OAAO,IAAI,CAACP,WAAW,CAACG,OAAO,CAAC,MAAM,EAAE,eAAe,EAAEI,QAAQ,CAAC;EACpE;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACR,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,EAAE,CAAC;EAC7D;EAEAM,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACV,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,oBAAoBO,EAAE,EAAE,EAAE,EAAE,CAAC;EACtE;EAEAC,cAAcA,CAACD,EAAU,EAAEH,QAAkB;IAC3C,OAAO,IAAI,CAACP,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,mBAAmBO,EAAE,EAAE,EAAEH,QAAQ,CAAC;EAC3E;EAEAK,cAAcA,CAACF,EAAU;IACvB,OAAO,IAAI,CAACV,WAAW,CAACG,OAAO,CAAC,QAAQ,EAAE,mBAAmBO,EAAE,EAAE,EAAE,EAAE,CAAC;EACxE;EAEAG,kBAAkBA,CAACC,YAAoB;IACrC,MAAMC,MAAM,GAAG;MAAED;IAAY,CAAE;IAC/B,OAAO,IAAI,CAACd,WAAW,CAACG,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAEY,MAAM,CAAC;EACzE;EAEA;EACAC,2BAA2BA,CAACC,UAAiB;IAC3C,OAAO,IAAI,CAACjB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,yCAAyCc,UAAU,EAAE,EAAC,IAAI,CAAC;EACnG;EAEAC,2BAA2BA,CAACC,QAAe;IACzC,OAAO,IAAI,CAACnB,WAAW,CAACG,OAAO,CAAC,KAAK,EAAC,yCAAyCgB,QAAQ,EAAE,EAAC,IAAI,CAAC;EACjG;EAAC,QAAAC,CAAA,G;qBA5CUtB,eAAe,EAAAuB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAf3B,eAAe;IAAA4B,OAAA,EAAf5B,eAAe,CAAA6B,IAAA;IAAAC,UAAA,EAFd;EAAM;;SAEP9B,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}