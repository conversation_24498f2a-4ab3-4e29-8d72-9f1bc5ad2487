{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nclass AppComponent {\n  constructor() {\n    this.title = 'LedgerChimpFrontEnd';\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 1,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [i1.RouterOutlet],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { AppComponent };", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\app.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent {\r\n  title = 'LedgerChimpFrontEnd';\r\n}\r\n", "<router-outlet></router-outlet>\r\n<!-- <div>\r\n  <app-header [pageTitle]=\"'Fleet Management System'\" [logoSrc]=\"'assets/images/logo.png'\" />\r\n  <div class=\"container-fluid\">\r\n    <div class=\"row\">\r\n      <div class=\"col\">\r\n        <app-content/>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div> -->\r\n"], "mappings": ";;AAEA,MAKaA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,qBAAqB;;EAC9B,QAAAC,CAAA,G;qBAFYH,YAAY;EAAA;EAAA,QAAAI,EAAA,G;UAAZJ,YAAY;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzBE,EAAA,CAAAC,SAAA,oBAA+B;;;;;;;SDOlBb,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}