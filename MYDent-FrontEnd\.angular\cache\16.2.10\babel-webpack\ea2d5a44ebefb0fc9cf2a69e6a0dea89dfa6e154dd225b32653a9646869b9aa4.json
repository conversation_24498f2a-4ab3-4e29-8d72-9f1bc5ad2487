{"ast": null, "code": "import * as i0 from \"@angular/core\";\nclass UserManagementModalComponent {\n  static #_ = this.ɵfac = function UserManagementModalComponent_Factory(t) {\n    return new (t || UserManagementModalComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserManagementModalComponent,\n    selectors: [[\"app-user-management-modal\"]],\n    decls: 11,\n    vars: 0,\n    consts: [[1, \"col-12\"], [1, \"row\"], [1, \"col-12\", \"px-5\"], [1, \"d-grid\"], [\"id\", \"exampleModalLabel\", 1, \"modal-title\", \"fs-6\"], [1, \"text-black-50\", 2, \"font-size\", \"13px\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Close\", 1, \"btn-close\"], [1, \"col-12\", 2, \"border-block\", \"1px solid rgb(240, 240, 240)\"]],\n    template: function UserManagementModalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n        i0.ɵɵtext(5, \" User Information \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"p\", 5);\n        i0.ɵɵtext(7, \" A brief overview of the user's profile. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(8, \"button\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(9, \"div\", 7)(10, \"div\", 0);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { UserManagementModalComponent };", "map": {"version": 3, "names": ["UserManagementModalComponent", "_", "_2", "selectors", "decls", "vars", "consts", "template", "UserManagementModalComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\admin\\components\\user-management-modal\\user-management-modal.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\admin\\components\\user-management-modal\\user-management-modal.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-user-management-modal',\r\n  templateUrl: './user-management-modal.component.html',\r\n  styleUrls: ['./user-management-modal.component.css']\r\n})\r\nexport class UserManagementModalComponent {\r\n\r\n}\r\n", "<div class=\"col-12\">\r\n  <div class=\"row\">\r\n    <div class=\"col-12 px-5\">\r\n      <div class=\"d-grid\">\r\n        <h1 class=\"modal-title fs-6\" id=\"exampleModalLabel\">\r\n          User Information\r\n        </h1>\r\n        <p class=\"text-black-50\" style=\"font-size: 13px\">\r\n          A brief overview of the user's profile.\r\n        </p>\r\n      </div>\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn-close\"\r\n        data-bs-dismiss=\"modal\"\r\n        aria-label=\"Close\"\r\n      ></button>\r\n    </div>\r\n    <div\r\n      class=\"col-12\"\r\n      style=\"border-block: 1px solid rgb(240, 240, 240)\"\r\n    ></div>\r\n    <div class=\"col-12\"></div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAEA,MAKaA,4BAA4B;EAAA,QAAAC,CAAA,G;qBAA5BD,4BAA4B;EAAA;EAAA,QAAAE,EAAA,G;UAA5BF,4BAA4B;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzCE,EAAA,CAAAC,cAAA,aAAoB;QAKVD,EAAA,CAAAE,MAAA,yBACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACLH,EAAA,CAAAC,cAAA,WAAiD;QAC/CD,EAAA,CAAAE,MAAA,gDACF;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAI,SAAA,gBAKU;QACZJ,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAI,SAAA,aAGO;QAETJ,EAAA,CAAAG,YAAA,EAAM;;;;;;SDhBKd,4BAA4B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}