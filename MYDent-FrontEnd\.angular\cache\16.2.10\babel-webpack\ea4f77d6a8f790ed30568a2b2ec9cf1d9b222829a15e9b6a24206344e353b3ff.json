{"ast": null, "code": "import { User, UserCategory } from '../../user/user';\nimport { Validators } from '@angular/forms';\nimport { map, mapTo, of, tap } from 'rxjs';\nimport { Laboratory } from '../laboratory';\nimport Swal from 'sweetalert2';\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../user/user.service\";\nimport * as i4 from \"src/app/auth/auth.service\";\nimport * as i5 from \"../laboratory.service\";\nimport * as i6 from \"../../modules/shared-services/shared.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../core/default-navbar/default-navbar.component\";\nconst _c0 = [\"RegisterButton\"];\nfunction LaboratoryRegistrationComponent_div_13_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Laboratory Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_13_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Laboratory Name can only contain letters, numbers, spaces, and \\\".()/,\\\".\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_13_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵtemplate(2, LaboratoryRegistrationComponent_div_13_small_2_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.laboratoryForm.get(\"laboratoryName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.laboratoryForm.get(\"laboratoryName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.laboratoryNameExistsMessage);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_20_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Address is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_20_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.laboratoryForm.get(\"address\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_26_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Email is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_26_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Invalid email format.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_26_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵtemplate(2, LaboratoryRegistrationComponent_div_26_small_2_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.laboratoryForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.laboratoryForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_small_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.userEmailExistsMessage);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_33_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Contact Number is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_33_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Invalid Contact number.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_33_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵtemplate(2, LaboratoryRegistrationComponent_div_33_small_2_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.laboratoryForm.get(\"tele\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r5.laboratoryForm.get(\"tele\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_38_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Contact Person is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_38_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.laboratoryForm.get(\"contactPerson\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_44_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Password is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_44_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Password must be at least 8 characters long.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_44_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_44_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵtemplate(2, LaboratoryRegistrationComponent_div_44_small_2_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵtemplate(4, LaboratoryRegistrationComponent_div_44_small_4_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r7.laboratoryForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r7.laboratoryForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r7.laboratoryForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_div_49_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Please re-enter the password.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LaboratoryRegistrationComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LaboratoryRegistrationComponent_div_49_small_1_Template, 2, 0, \"small\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.laboratoryForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction LaboratoryRegistrationComponent_small_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1, \"Password do not match.\");\n    i0.ɵɵelementEnd();\n  }\n}\nclass LaboratoryRegistrationComponent {\n  constructor(fb, router, userService, authService, laboratoryService, sharedService) {\n    this.fb = fb;\n    this.router = router;\n    this.userService = userService;\n    this.authService = authService;\n    this.laboratoryService = laboratoryService;\n    this.sharedService = sharedService;\n    this.laboratory = new Laboratory();\n    this.user = new User();\n    this.userCategory = new UserCategory();\n    this.isEmailRegistered = false;\n    this.isLaboratoryRegistered = false;\n    this.userEmailExistsMessage = '';\n    this.laboratoryNameExistsMessage = '';\n    this.districts = [];\n    this.cities = [];\n    // User temp\n    this.userTemp = new UserTemp();\n    this.laboratoryForm = this.fb.group({\n      laboratoryName: ['', [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')]],\n      address: ['', Validators.required],\n      // city: ['', Validators.required],\n      // district: ['', Validators.required],\n      tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],\n      contactPerson: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$')]],\n      confirmPassword: ['', Validators.required]\n    }, {\n      validator: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    localStorage.clear();\n    this.userService.getUserCategoryById(6).subscribe(response => {\n      this.userCategory = response;\n    });\n    this.districts = this.sharedService.getDistricts();\n    this.userTemp.userTempType = UserTempType.LABORATORY;\n  }\n  // onDistrictChange(event: Event): void {\n  //   const selectedDistrict = (event.target as HTMLSelectElement).value;\n  //   if (selectedDistrict) {\n  //     this.laboratoryForm.get('city')?.enable();\n  //     this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\n  //   } else {\n  //     this.laboratoryForm.get('city')?.disable();\n  //     this.cities = [];\n  //   }\n  //   this.laboratoryForm.get('city')?.setValue('');\n  // }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    // Only apply mismatch validation if both fields are touched or dirty\n    if (password?.value && confirmPassword?.value && (confirmPassword.dirty || confirmPassword.touched)) {\n      return password.value === confirmPassword.value ? null : {\n        mismatch: true\n      };\n    }\n    // If the fields are not touched or dirty, return null (no error)\n    return null;\n  }\n  updateEmail() {\n    this.user.username = this.laboratory.email;\n  }\n  onUserRegister() {\n    this.user.userCategoryId = this.userCategory;\n    this.user.firstName = this.laboratory.name;\n    return this.userService.register(this.user).pipe(tap(response => {\n      this.user.userId = response.id;\n      this.laboratory.userId = this.user;\n    }, error => {\n      console.log(error);\n    }), mapTo(void 0));\n  }\n  onLaboratoryRegister() {\n    return this.laboratoryService.saveLaboratory(this.laboratory).pipe(tap(() => {\n      this.router.navigate(['/user-login']);\n    }, error => {\n      console.log(error);\n    }), mapTo(void 0));\n  }\n  // UserTemp Saving\n  onUserTempRegister() {\n    if (this.laboratoryForm.invalid) {\n      this.laboratoryForm.markAllAsTouched();\n      return;\n    }\n    // Disable the register button and show a loading indicator\n    this.registerButton.nativeElement.disabled = true;\n    this.registerButton.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\n    // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\n    // this.authService\n    //   .checkUserTempAvailability(this.userTemp.userEmail)\n    //   .subscribe((resp) => {\n    //     if (resp !=null) {\n    //       Swal.fire({\n    //         title: 'Registration Already Exists!',\n    //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\n    //         icon: 'info',\n    //         confirmButtonText: 'OK',\n    //       });\n    //       // Reset the button state\n    //       this.registerButton.nativeElement.disabled = false;\n    //       this.registerButton.nativeElement.innerHTML = 'Register';\n    //       return;\n    //     }\n    this.authService.saveUserTemp(this.userTemp).subscribe(userTempSaved => {\n      console.log('Full userTempSaved object:', userTempSaved);\n      const receivedUserTemp = userTempSaved;\n      let title = 'Registration Completed!';\n      let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\n      let iconName = 'success';\n      if (!receivedUserTemp) {\n        title = 'Registration Failed!';\n        message = 'An error occurred while registering. Please try again.';\n        iconName = 'error';\n      }\n      Swal.fire({\n        title: title,\n        text: message,\n        icon: iconName,\n        confirmButtonText: 'OK'\n      }).then(result => {\n        if (result.isConfirmed && receivedUserTemp) {\n          // Navigate to login page after successful registration\n          this.router.navigate(['/user-login']);\n        }\n      });\n      // Reset button state\n      this.registerButton.nativeElement.disabled = false;\n      this.registerButton.nativeElement.innerHTML = 'Register';\n    }, error => {\n      console.error('Registration error:', error);\n      // Extract error message from backend\n      let errorMessage = 'An error occurred during registration. Please try again later.';\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      Swal.fire({\n        title: 'Registration Failed!',\n        text: errorMessage,\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n      this.registerButton.nativeElement.disabled = false;\n      this.registerButton.nativeElement.innerHTML = 'Register';\n    });\n    // }); // COMMENTED OUT FOR TESTING\n  }\n\n  onSubmitRegister() {\n    Swal.fire({\n      title: \"Wait until approval!\",\n      text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\n      icon: 'success',\n      confirmButtonText: 'OK'\n    });\n    if (this.laboratoryForm.invalid) {\n      this.laboratoryForm.markAllAsTouched();\n      return;\n    }\n    this.checkLaboratoryName().subscribe(isLaboratoryRegistered => {\n      if (!isLaboratoryRegistered) {\n        this.checkUserEmail().subscribe(isEmailRegistered => {\n          if (!isEmailRegistered) {\n            this.onUserRegister().subscribe(() => {\n              this.onLaboratoryRegister().subscribe(() => {\n                // SweetAlert for successful registration\n                Swal.fire({\n                  icon: 'success',\n                  title: 'Registered Successfully!',\n                  text: 'Thank you for registering! Please verify your email to complete the login process.',\n                  confirmButtonText: 'OK'\n                }).then(() => {\n                  // Redirect after confirmation\n                  this.router.navigate(['/user-login']);\n                });\n              });\n            });\n          } else {\n            Swal.fire({\n              icon: 'error',\n              title: 'Email Already Registered',\n              text: this.userEmailExistsMessage,\n              confirmButtonText: 'OK'\n            });\n          }\n        });\n      } else {\n        Swal.fire({\n          icon: 'error',\n          title: 'Laboratory Name Already Taken',\n          text: this.laboratoryNameExistsMessage,\n          confirmButtonText: 'OK'\n        });\n      }\n    });\n  }\n  checkUserEmail() {\n    if (this.laboratoryForm.get('email')?.valid) {\n      const userEmail = this.laboratoryForm.get('email')?.value;\n      return this.userService.checkUser(userEmail).pipe(map(data => {\n        if (data) {\n          this.isEmailRegistered = true;\n          this.userEmailExistsMessage = 'Email already registered. Try another.';\n        } else {\n          this.isEmailRegistered = false;\n          this.userEmailExistsMessage = '';\n        }\n        return this.isEmailRegistered;\n      }));\n    } else {\n      this.isEmailRegistered = false;\n      this.userEmailExistsMessage = '';\n      return of(this.isEmailRegistered);\n    }\n  }\n  checkLaboratoryName() {\n    if (this.laboratoryForm.get('laboratoryName')?.valid) {\n      const laboratoryName = this.laboratoryForm.get('laboratoryName')?.value;\n      return this.laboratoryService.checkLaboratoryName(laboratoryName).pipe(map(data => {\n        if (data) {\n          this.isLaboratoryRegistered = true;\n          this.laboratoryNameExistsMessage = 'That name is taken. Try another.';\n        } else {\n          this.isLaboratoryRegistered = false;\n          this.laboratoryNameExistsMessage = '';\n        }\n        return this.isLaboratoryRegistered;\n      }));\n    } else {\n      this.isLaboratoryRegistered = false;\n      this.laboratoryNameExistsMessage = '';\n      return of(this.isLaboratoryRegistered);\n    }\n  }\n  navigateUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  static #_ = this.ɵfac = function LaboratoryRegistrationComponent_Factory(t) {\n    return new (t || LaboratoryRegistrationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.LaboratoryService), i0.ɵɵdirectiveInject(i6.SharedService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LaboratoryRegistrationComponent,\n    selectors: [[\"app-laboratory-registration\"]],\n    viewQuery: function LaboratoryRegistrationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.registerButton = _t.first);\n      }\n    },\n    decls: 54,\n    vars: 17,\n    consts: [[\"loggedUser\", \"Hello Laboratory\"], [1, \"registration-page\"], [1, \"registration-card\"], [1, \"backtoselection-button\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"lab-name\"], [\"type\", \"text\", \"id\", \"lab-name\", \"name\", \"lab-name\", \"formControlName\", \"laboratoryName\", 3, \"ngModel\", \"ngModelChange\"], [4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"address\"], [\"type\", \"text\", \"id\", \"address\", \"name\", \"address\", \"formControlName\", \"address\", \"rows\", \"1\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"formControlName\", \"email\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"contactNumber\"], [\"type\", \"text\", \"id\", \"contactNumber\", \"name\", \"contactNumber\", \"formControlName\", \"tele\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"contactPerson\"], [\"type\", \"text\", \"id\", \"contactPerson\", \"name\", \"contactPerson\", \"formControlName\", \"contactPerson\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"formControlName\", \"password\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"confirmPassword\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"name\", \"confirmPassword\", \"formControlName\", \"confirmPassword\"], [\"type\", \"submit\", 1, \"register-button\"], [\"RegisterButton\", \"\"], [1, \"text-danger\"]],\n    template: function LaboratoryRegistrationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-default-navbar\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\");\n        i0.ɵɵtext(4, \"Laboratory Registration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function LaboratoryRegistrationComponent_Template_button_click_5_listener() {\n          return ctx.navigateUserSelection();\n        });\n        i0.ɵɵtext(6, \"Selection Menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function LaboratoryRegistrationComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onUserTempRegister();\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"label\", 7);\n        i0.ɵɵtext(11, \"Laboratory Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"input\", 8);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_12_listener($event) {\n          return ctx.userTemp.mainName = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(13, LaboratoryRegistrationComponent_div_13_Template, 3, 2, \"div\", 9);\n        i0.ɵɵtemplate(14, LaboratoryRegistrationComponent_small_14_Template, 2, 1, \"small\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 5)(16, \"div\", 6)(17, \"label\", 11);\n        i0.ɵɵtext(18, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"textarea\", 12);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_textarea_ngModelChange_19_listener($event) {\n          return ctx.userTemp.address = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(20, LaboratoryRegistrationComponent_div_20_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 5)(22, \"div\", 6)(23, \"label\", 13);\n        i0.ɵɵtext(24, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"input\", 14);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_25_listener($event) {\n          return ctx.userTemp.userEmail = $event;\n        })(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_25_listener() {\n          return ctx.updateEmail();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(26, LaboratoryRegistrationComponent_div_26_Template, 3, 2, \"div\", 9);\n        i0.ɵɵtemplate(27, LaboratoryRegistrationComponent_small_27_Template, 2, 1, \"small\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"div\", 5)(29, \"div\", 6)(30, \"label\", 15);\n        i0.ɵɵtext(31, \"Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"input\", 16);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_32_listener($event) {\n          return ctx.userTemp.contactNumber = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(33, LaboratoryRegistrationComponent_div_33_Template, 3, 2, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 6)(35, \"label\", 17);\n        i0.ɵɵtext(36, \"Contact Person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"input\", 18);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_37_listener($event) {\n          return ctx.userTemp.contactPerson = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(38, LaboratoryRegistrationComponent_div_38_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"div\", 5)(40, \"div\", 6)(41, \"label\", 19);\n        i0.ɵɵtext(42, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"input\", 20);\n        i0.ɵɵlistener(\"ngModelChange\", function LaboratoryRegistrationComponent_Template_input_ngModelChange_43_listener($event) {\n          return ctx.userTemp.userPassword = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(44, LaboratoryRegistrationComponent_div_44_Template, 5, 3, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"div\", 6)(46, \"label\", 21);\n        i0.ɵɵtext(47, \"Re-enter Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(48, \"input\", 22);\n        i0.ɵɵtemplate(49, LaboratoryRegistrationComponent_div_49_Template, 2, 1, \"div\", 9);\n        i0.ɵɵtemplate(50, LaboratoryRegistrationComponent_small_50_Template, 2, 0, \"small\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"button\", 23, 24);\n        i0.ɵɵtext(53, \"Register\");\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_5_0;\n        let tmp_7_0;\n        let tmp_10_0;\n        let tmp_12_0;\n        let tmp_14_0;\n        let tmp_15_0;\n        let tmp_16_0;\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"formGroup\", ctx.laboratoryForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.mainName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.laboratoryForm.get(\"laboratoryName\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx.laboratoryForm.get(\"laboratoryName\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx.laboratoryForm.get(\"laboratoryName\")) == null ? null : tmp_2_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLaboratoryRegistered);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.address);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.laboratoryForm.get(\"address\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = ctx.laboratoryForm.get(\"address\")) == null ? null : tmp_5_0.dirty) || ((tmp_5_0 = ctx.laboratoryForm.get(\"address\")) == null ? null : tmp_5_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userEmail);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.laboratoryForm.get(\"email\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = ctx.laboratoryForm.get(\"email\")) == null ? null : tmp_7_0.dirty) || ((tmp_7_0 = ctx.laboratoryForm.get(\"email\")) == null ? null : tmp_7_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEmailRegistered);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.contactNumber);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.laboratoryForm.get(\"tele\")) == null ? null : tmp_10_0.invalid) && (((tmp_10_0 = ctx.laboratoryForm.get(\"tele\")) == null ? null : tmp_10_0.dirty) || ((tmp_10_0 = ctx.laboratoryForm.get(\"tele\")) == null ? null : tmp_10_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.contactPerson);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.laboratoryForm.get(\"contactPerson\")) == null ? null : tmp_12_0.invalid) && (((tmp_12_0 = ctx.laboratoryForm.get(\"contactPerson\")) == null ? null : tmp_12_0.dirty) || ((tmp_12_0 = ctx.laboratoryForm.get(\"contactPerson\")) == null ? null : tmp_12_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userPassword);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.laboratoryForm.get(\"password\")) == null ? null : tmp_14_0.invalid) && (((tmp_14_0 = ctx.laboratoryForm.get(\"password\")) == null ? null : tmp_14_0.dirty) || ((tmp_14_0 = ctx.laboratoryForm.get(\"password\")) == null ? null : tmp_14_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx.laboratoryForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.invalid) && (((tmp_15_0 = ctx.laboratoryForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.dirty) || ((tmp_15_0 = ctx.laboratoryForm.get(\"confirmPassword\")) == null ? null : tmp_15_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.laboratoryForm.errors == null ? null : ctx.laboratoryForm.errors[\"mismatch\"]) && ((tmp_16_0 = ctx.laboratoryForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.dirty));\n      }\n    },\n    dependencies: [i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i8.DefaultNavbarComponent],\n    styles: [\".registration-page[_ngcontent-%COMP%] {\\n  background-image: url('/assets/images/background.png');\\n  background-size: cover;\\n  background-position: center;\\n  background-color: #f9f9f9;\\n  min-height: 80vh;\\n  padding: 40px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: flex-start;\\n}\\n\\n.registration-card[_ngcontent-%COMP%] {\\n  width: 620px;\\n  height: auto;\\n  padding: 40px;\\n  border-radius: 25px;\\n  border: 1px solid #fb751e;\\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n  background-color: white;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n\\nh3[_ngcontent-%COMP%] {\\n  font-family: 'Inter', sans-serif;\\n  font-size: 30px;\\n  font-weight: 700;\\n  color: #fb751e;\\n  text-align: center;\\n  margin-bottom: 28.5px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 10px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-size: 16px;\\n  font-weight: 400;\\n  text-align: left;\\n  margin-bottom: 5px;\\n  color: #000000;\\n  display: block;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 33px;\\n  padding: 5px;\\n  font-size: 14px;\\n  color: #495057;\\n  border-radius: 4px;\\n  border: 1px solid #b3b3b3;\\n  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),\\n    linear-gradient(135deg, #ff7a00 50%, transparent 50%);\\n  background-position: calc(100% - 20px) center,\\n    calc(100% - 15px) center;\\n  background-size: 5px 5px, 5px 5px;\\n  background-repeat: no-repeat;\\n  appearance: none;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%], input[type=\\\"email\\\"][_ngcontent-%COMP%], input[type=\\\"password\\\"][_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 5px;\\n  border: 1px solid #b3b3b3;\\n  border-radius: 4px;\\n}\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"email\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"password\\\"][_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\ntextarea[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n  width: 100%;\\n  padding: 5px;\\n  border: 1px solid #b3b3b3;\\n  border-radius: 4px;\\n  \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, #fb751e, #333333);\\n  border: none;\\n  border-radius: 20px;\\n  color: white;\\n  font-size: 16px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n  margin-top: 30px;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(to left, #fb751e, #333333);\\n}\\n\\n.backtoselection-button[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 40px;\\n  background: none;\\n  border: 2px solid #fb751e;\\n  color: #fb751e;\\n  font-size: 16px;\\n  font-weight: bold;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  \\n\\n\\n\\n\\n  justify-content: center;\\n    margin-bottom: 20px;\\n    margin-top: 20px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .registration-page[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  .header-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n\\n  .contact-link[_ngcontent-%COMP%] {\\n    margin-bottom: 10px;\\n  }\\n\\n  .login-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 10px;\\n  }\\n\\n  .registration-card[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 20px;\\n  }\\n\\n  .registration-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n    margin-bottom: 20px;\\n  }\\n\\n  .form-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 15px;\\n  }\\n\\n  .form-control[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n\\n  .register-button[_ngcontent-%COMP%] {\\n    padding: 12px;\\n    font-size: 16px;\\n  }\\n    \\n\\n  \\n}\\n\\n@media (min-width: 1024px) {\\n  \\n\\n  .backtoselection-button[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 40px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 16px;\\n    font-weight: bold;\\n    border-radius: 20px;\\n    cursor: pointer;\\n    position: absolute;\\n    left: 0;\\n    top: 107px;\\n    margin-left: 5%;\\n  }\\n\\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { LaboratoryRegistrationComponent };", "map": {"version": 3, "names": ["User", "UserCategory", "Validators", "map", "mapTo", "of", "tap", "Laboratory", "<PERSON><PERSON>", "UserTemp", "UserTempType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LaboratoryRegistrationComponent_div_13_small_1_Template", "LaboratoryRegistrationComponent_div_13_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "laboratoryForm", "get", "errors", "tmp_1_0", "ɵɵtextInterpolate", "ctx_r1", "laboratoryNameExistsMessage", "LaboratoryRegistrationComponent_div_20_small_1_Template", "ctx_r2", "LaboratoryRegistrationComponent_div_26_small_1_Template", "LaboratoryRegistrationComponent_div_26_small_2_Template", "ctx_r3", "ctx_r4", "userEmailExistsMessage", "LaboratoryRegistrationComponent_div_33_small_1_Template", "LaboratoryRegistrationComponent_div_33_small_2_Template", "ctx_r5", "LaboratoryRegistrationComponent_div_38_small_1_Template", "ctx_r6", "LaboratoryRegistrationComponent_div_44_small_1_Template", "LaboratoryRegistrationComponent_div_44_small_2_Template", "ɵɵelement", "LaboratoryRegistrationComponent_div_44_small_4_Template", "ctx_r7", "tmp_2_0", "LaboratoryRegistrationComponent_div_49_small_1_Template", "ctx_r8", "LaboratoryRegistrationComponent", "constructor", "fb", "router", "userService", "authService", "laboratoryService", "sharedService", "laboratory", "user", "userCategory", "isEmailRegistered", "isLaboratoryRegistered", "districts", "cities", "userTemp", "group", "laboratoryName", "required", "pattern", "address", "tele", "<PERSON><PERSON><PERSON>", "email", "password", "<PERSON><PERSON><PERSON><PERSON>", "confirmPassword", "validator", "passwordMatchValidator", "ngOnInit", "localStorage", "clear", "getUserCategoryById", "subscribe", "response", "getDistricts", "userTempType", "LABORATORY", "form", "value", "dirty", "touched", "mismatch", "updateEmail", "username", "onUserRegister", "userCategoryId", "firstName", "name", "register", "pipe", "userId", "id", "error", "console", "log", "onLaboratoryRegister", "saveLaboratory", "navigate", "onUserTempRegister", "invalid", "mark<PERSON>llAsTouched", "registerButton", "nativeElement", "disabled", "innerHTML", "saveUserTemp", "userTempSaved", "receivedUserTemp", "title", "message", "iconName", "fire", "text", "icon", "confirmButtonText", "then", "result", "isConfirmed", "errorMessage", "onSubmitRegister", "checkLaboratoryName", "checkUserEmail", "valid", "userEmail", "checkUser", "data", "navigateUserSelection", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "UserService", "i4", "AuthService", "i5", "LaboratoryService", "i6", "SharedService", "_2", "selectors", "viewQuery", "LaboratoryRegistrationComponent_Query", "rf", "ctx", "ɵɵlistener", "LaboratoryRegistrationComponent_Template_button_click_5_listener", "LaboratoryRegistrationComponent_Template_form_ngSubmit_7_listener", "LaboratoryRegistrationComponent_Template_input_ngModelChange_12_listener", "$event", "mainName", "LaboratoryRegistrationComponent_div_13_Template", "LaboratoryRegistrationComponent_small_14_Template", "LaboratoryRegistrationComponent_Template_textarea_ngModelChange_19_listener", "LaboratoryRegistrationComponent_div_20_Template", "LaboratoryRegistrationComponent_Template_input_ngModelChange_25_listener", "LaboratoryRegistrationComponent_div_26_Template", "LaboratoryRegistrationComponent_small_27_Template", "LaboratoryRegistrationComponent_Template_input_ngModelChange_32_listener", "contactNumber", "LaboratoryRegistrationComponent_div_33_Template", "LaboratoryRegistrationComponent_Template_input_ngModelChange_37_listener", "LaboratoryRegistrationComponent_div_38_Template", "LaboratoryRegistrationComponent_Template_input_ngModelChange_43_listener", "userPassword", "LaboratoryRegistrationComponent_div_44_Template", "LaboratoryRegistrationComponent_div_49_Template", "LaboratoryRegistrationComponent_small_50_Template", "tmp_5_0", "tmp_7_0", "tmp_10_0", "tmp_12_0", "tmp_14_0", "tmp_15_0", "tmp_16_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\laboratory-registration\\laboratory-registration.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\laboratory\\laboratory-registration\\laboratory-registration.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { User, UserCategory } from '../../user/user';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from '../../user/user.service';\r\nimport { map, mapTo, Observable, of, tap } from 'rxjs';\r\nimport { SharedService } from '../../modules/shared-services/shared.service';\r\nimport { Laboratory } from '../laboratory';\r\nimport { LaboratoryService } from '../laboratory.service';\r\nimport Swal from 'sweetalert2';\r\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\r\nimport { AuthService } from 'src/app/auth/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-laboratory-registration',\r\n  templateUrl: './laboratory-registration.component.html',\r\n  styleUrls: ['./laboratory-registration.component.css'],\r\n})\r\nexport class LaboratoryRegistrationComponent implements OnInit {\r\n  laboratory: Laboratory = new Laboratory();\r\n  user: User = new User();\r\n  userCategory: UserCategory = new UserCategory();\r\n  laboratoryForm: FormGroup;\r\n  isEmailRegistered: boolean = false;\r\n  isLaboratoryRegistered: boolean = false;\r\n  userEmailExistsMessage: string = '';\r\n  laboratoryNameExistsMessage: string = '';\r\n\r\n  districts: string[] = [];\r\n  cities: String[] = [];\r\n\r\n  // User temp\r\n  protected userTemp: UserTemp = new UserTemp();\r\n  @ViewChild('RegisterButton') registerButton!: ElementRef<HTMLButtonElement>;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private authService: AuthService,\r\n    private laboratoryService: LaboratoryService,\r\n    private sharedService: SharedService\r\n  ) {\r\n    this.laboratoryForm = this.fb.group(\r\n      {\r\n        laboratoryName: [\r\n          '',\r\n          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],\r\n        ],\r\n        address: ['', Validators.required],\r\n        // city: ['', Validators.required],\r\n        // district: ['', Validators.required],\r\n        tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],\r\n        contactPerson: ['', Validators.required],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        password: [\r\n          '',\r\n          [\r\n            Validators.required,\r\n            Validators.minLength(8),\r\n            Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$'),\r\n          ],\r\n        ],\r\n        confirmPassword: ['', Validators.required],\r\n      },\r\n      { validator: this.passwordMatchValidator }\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    localStorage.clear();\r\n    this.userService.getUserCategoryById(6).subscribe((response) => {\r\n      this.userCategory = response;\r\n    });\r\n    this.districts = this.sharedService.getDistricts();\r\n    this.userTemp.userTempType = UserTempType.LABORATORY;\r\n  }\r\n\r\n  // onDistrictChange(event: Event): void {\r\n  //   const selectedDistrict = (event.target as HTMLSelectElement).value;\r\n\r\n  //   if (selectedDistrict) {\r\n  //     this.laboratoryForm.get('city')?.enable();\r\n  //     this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\r\n  //   } else {\r\n  //     this.laboratoryForm.get('city')?.disable();\r\n  //     this.cities = [];\r\n  //   }\r\n  //   this.laboratoryForm.get('city')?.setValue('');\r\n  // }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const password = form.get('password');\r\n    const confirmPassword = form.get('confirmPassword');\r\n\r\n    // Only apply mismatch validation if both fields are touched or dirty\r\n    if (\r\n      password?.value &&\r\n      confirmPassword?.value &&\r\n      (confirmPassword.dirty || confirmPassword.touched)\r\n    ) {\r\n      return password.value === confirmPassword.value\r\n        ? null\r\n        : { mismatch: true };\r\n    }\r\n\r\n    // If the fields are not touched or dirty, return null (no error)\r\n    return null;\r\n  }\r\n\r\n  updateEmail() {\r\n    this.user.username = this.laboratory.email;\r\n  }\r\n\r\n  onUserRegister(): Observable<void> {\r\n    this.user.userCategoryId = this.userCategory;\r\n    this.user.firstName = this.laboratory.name;\r\n    return this.userService.register(this.user).pipe(\r\n      tap(\r\n        (response) => {\r\n          this.user.userId = response.id;\r\n          this.laboratory.userId = this.user;\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      ),\r\n      mapTo(void 0)\r\n    );\r\n  }\r\n\r\n  onLaboratoryRegister(): Observable<void> {\r\n    return this.laboratoryService.saveLaboratory(this.laboratory).pipe(\r\n      tap(\r\n        () => {\r\n          this.router.navigate(['/user-login']);\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      ),\r\n      mapTo(void 0)\r\n    );\r\n  }\r\n\r\n\r\n\r\n  // UserTemp Saving\r\n  onUserTempRegister() {\r\n    if (this.laboratoryForm.invalid) {\r\n      this.laboratoryForm.markAllAsTouched();\r\n      return;\r\n    }\r\n\r\n    // Disable the register button and show a loading indicator\r\n    this.registerButton.nativeElement.disabled = true;\r\n    this.registerButton.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\r\n\r\n    // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\r\n    // this.authService\r\n    //   .checkUserTempAvailability(this.userTemp.userEmail)\r\n    //   .subscribe((resp) => {\r\n\r\n    //     if (resp !=null) {\r\n    //       Swal.fire({\r\n    //         title: 'Registration Already Exists!',\r\n    //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\r\n    //         icon: 'info',\r\n    //         confirmButtonText: 'OK',\r\n    //       });\r\n\r\n    //       // Reset the button state\r\n    //       this.registerButton.nativeElement.disabled = false;\r\n    //       this.registerButton.nativeElement.innerHTML = 'Register';\r\n    //       return;\r\n    //     }\r\n\r\n        this.authService.saveUserTemp(this.userTemp).subscribe(\r\n          (userTempSaved: UserTemp) => {\r\n            console.log('Full userTempSaved object:', userTempSaved);\r\n\r\n            const receivedUserTemp: UserTemp = userTempSaved;\r\n            let title = 'Registration Completed!';\r\n            let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\r\n            let iconName:\r\n              | 'success'\r\n              | 'info'\r\n              | 'error'\r\n              | 'warning'\r\n              | 'question' = 'success';\r\n\r\n            if (!receivedUserTemp) {\r\n              title = 'Registration Failed!';\r\n              message ='An error occurred while registering. Please try again.';\r\n              iconName = 'error';\r\n            }\r\n\r\n            Swal.fire({\r\n              title: title,\r\n              text: message,\r\n              icon: iconName,\r\n              confirmButtonText: 'OK',\r\n                }).then((result) => {\r\n              if (result.isConfirmed && receivedUserTemp) {\r\n                // Navigate to login page after successful registration\r\n                this.router.navigate(['/user-login']);\r\n              }\r\n            });\r\n\r\n            // Reset button state\r\n            this.registerButton.nativeElement.disabled = false;\r\n            this.registerButton.nativeElement.innerHTML = 'Register';\r\n          },\r\n          (error) => {\r\n             console.error('Registration error:', error);\r\n\r\n            // Extract error message from backend\r\n            let errorMessage = 'An error occurred during registration. Please try again later.';\r\n            if (error.error && error.error.message) {\r\n              errorMessage = error.error.message;\r\n            } else if (error.message) {\r\n              errorMessage = error.message;\r\n            }\r\n\r\n            Swal.fire({\r\n              title: 'Registration Failed!',\r\n               text: errorMessage,\r\n              icon: 'error',\r\n              confirmButtonText: 'OK',\r\n            });\r\n\r\n            this.registerButton.nativeElement.disabled = false;\r\n            this.registerButton.nativeElement.innerHTML = 'Register';\r\n          }\r\n        );\r\n      // }); // COMMENTED OUT FOR TESTING\r\n  }\r\n\r\n\r\n  onSubmitRegister() {\r\n\r\n    Swal.fire({\r\n      title: \"Wait until approval!\",\r\n      text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\r\n      icon: 'success',\r\n      confirmButtonText: 'OK',\r\n    });\r\n\r\n    if (this.laboratoryForm.invalid) {\r\n      this.laboratoryForm.markAllAsTouched();\r\n      return;\r\n    }\r\n    this.checkLaboratoryName().subscribe((isLaboratoryRegistered) => {\r\n      if (!isLaboratoryRegistered) {\r\n        this.checkUserEmail().subscribe((isEmailRegistered) => {\r\n          if (!isEmailRegistered) {\r\n            this.onUserRegister().subscribe(() => {\r\n              this.onLaboratoryRegister().subscribe(() => {\r\n                // SweetAlert for successful registration\r\n                Swal.fire({\r\n                  icon: 'success',\r\n                  title: 'Registered Successfully!',\r\n                  text: 'Thank you for registering! Please verify your email to complete the login process.',\r\n                  confirmButtonText: 'OK',\r\n                }).then(() => {\r\n                  // Redirect after confirmation\r\n                  this.router.navigate(['/user-login']);\r\n                });\r\n              });\r\n            });\r\n          } else {\r\n            Swal.fire({\r\n              icon: 'error',\r\n              title: 'Email Already Registered',\r\n              text: this.userEmailExistsMessage,\r\n              confirmButtonText: 'OK',\r\n            });\r\n          }\r\n        });\r\n      } else {\r\n        Swal.fire({\r\n          icon: 'error',\r\n          title: 'Laboratory Name Already Taken',\r\n          text: this.laboratoryNameExistsMessage,\r\n          confirmButtonText: 'OK',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  checkUserEmail(): Observable<boolean> {\r\n    if (this.laboratoryForm.get('email')?.valid) {\r\n      const userEmail = this.laboratoryForm.get('email')?.value;\r\n      return this.userService.checkUser(userEmail).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isEmailRegistered = true;\r\n            this.userEmailExistsMessage =\r\n              'Email already registered. Try another.';\r\n          } else {\r\n            this.isEmailRegistered = false;\r\n            this.userEmailExistsMessage = '';\r\n          }\r\n          return this.isEmailRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isEmailRegistered = false;\r\n      this.userEmailExistsMessage = '';\r\n      return of(this.isEmailRegistered);\r\n    }\r\n  }\r\n\r\n  checkLaboratoryName(): Observable<boolean> {\r\n    if (this.laboratoryForm.get('laboratoryName')?.valid) {\r\n      const laboratoryName = this.laboratoryForm.get('laboratoryName')?.value;\r\n      return this.laboratoryService.checkLaboratoryName(laboratoryName).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isLaboratoryRegistered = true;\r\n            this.laboratoryNameExistsMessage =\r\n              'That name is taken. Try another.';\r\n          } else {\r\n            this.isLaboratoryRegistered = false;\r\n            this.laboratoryNameExistsMessage = '';\r\n          }\r\n          return this.isLaboratoryRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isLaboratoryRegistered = false;\r\n      this.laboratoryNameExistsMessage = '';\r\n      return of(this.isLaboratoryRegistered);\r\n    }\r\n  }\r\n\r\n  navigateUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n", "<app-default-navbar loggedUser=\"Hello Laboratory\"/>\r\n<div class=\"registration-page\">\r\n  <div class=\"registration-card\">\r\n    <h3>Laboratory Registration</h3>\r\n    <button class=\"backtoselection-button\" (click)=\"navigateUserSelection()\">Selection Menu</button>\r\n    <form [formGroup]=\"laboratoryForm\" (ngSubmit)=\"onUserTempRegister()\">\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"lab-name\">Laboratory Name</label>\r\n          <input type=\"text\" id=\"lab-name\" name=\"lab-name\" formControlName=\"laboratoryName\" [(ngModel)]=\"userTemp.mainName\"/>\r\n          <div *ngIf=\"laboratoryForm.get('laboratoryName')?.invalid && (laboratoryForm.get('laboratoryName')?.dirty || laboratoryForm.get('laboratoryName')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('laboratoryName')?.errors?.['required']\">Laboratory Name is required.</small>\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('laboratoryName')?.errors?.['pattern']\">Laboratory Name can only contain letters, numbers, spaces, and \".()/,\".</small>\r\n          </div>\r\n          <small class=\"text-danger\" *ngIf=\"isLaboratoryRegistered\">{{ laboratoryNameExistsMessage }}</small>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"address\">Address</label>\r\n          <textarea type=\"text\" id=\"address\" name=\"address\" formControlName=\"address\" [(ngModel)]=\"userTemp.address\" rows=\"1\"></textarea>\r\n          <div *ngIf=\"laboratoryForm.get('address')?.invalid && (laboratoryForm.get('address')?.dirty || laboratoryForm.get('address')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('address')?.errors?.['required']\">Address is required.</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"email\">Email Address</label>\r\n          <input type=\"email\" id=\"email\" name=\"email\" formControlName=\"email\" [(ngModel)]=\"userTemp.userEmail\" (ngModelChange)=\"updateEmail()\"/>\r\n          <div *ngIf=\"laboratoryForm.get('email')?.invalid && (laboratoryForm.get('email')?.dirty || laboratoryForm.get('email')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('email')?.errors?.['required']\">Email is required.</small>\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('email')?.errors?.['email']\">Invalid email format.</small>\r\n          </div>\r\n          <small class=\"text-danger\" *ngIf=\"isEmailRegistered\">{{ userEmailExistsMessage }}</small>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"district\">District</label>\r\n          <select name=\"district\" id=\"district\" formControlName=\"district\" [(ngModel)]=\"laboratory.state\" (change)=\"onDistrictChange($event)\">\r\n            <option *ngFor=\"let district of districts\" [value]=\"district\">{{ district }}</option>\r\n          </select>\r\n          <div *ngIf=\"laboratoryForm.get('district')?.invalid && (laboratoryForm.get('district')?.dirty || laboratoryForm.get('district')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('district')?.errors?.['required']\">District is required.</small>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"city\">City</label>\r\n          <select name=\"city\" id=\"city\" formControlName=\"city\" [(ngModel)]=\"laboratory.city\">\r\n            <option *ngFor=\"let city of cities\" [value]=\"city\">{{ city }}</option>\r\n          </select>\r\n          <div *ngIf=\"laboratoryForm.get('city')?.invalid && (laboratoryForm.get('city')?.dirty || laboratoryForm.get('city')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('city')?.errors?.['required']\">City is required.</small>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"contactNumber\">Contact Number</label>\r\n          <input type=\"text\" id=\"contactNumber\" name=\"contactNumber\" formControlName=\"tele\" [(ngModel)]=\"userTemp.contactNumber\"/>\r\n          <div *ngIf=\"laboratoryForm.get('tele')?.invalid && (laboratoryForm.get('tele')?.dirty || laboratoryForm.get('tele')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('tele')?.errors?.['required']\">Contact Number is required.</small>\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('tele')?.errors?.['pattern']\">Invalid Contact number.</small>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"contactPerson\">Contact Person</label>\r\n          <input type=\"text\" id=\"contactPerson\" name=\"contactPerson\" formControlName=\"contactPerson\" [(ngModel)]=\"userTemp.contactPerson\"/>\r\n          <div *ngIf=\"laboratoryForm.get('contactPerson')?.invalid && (laboratoryForm.get('contactPerson')?.dirty || laboratoryForm.get('contactPerson')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('contactPerson')?.errors?.['required']\">Contact Person is required.</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"form-row\">\r\n        <div class=\"form-group\">\r\n          <label for=\"password\">Password</label>\r\n          <input type=\"password\" id=\"password\" name=\"password\" formControlName=\"password\" [(ngModel)]=\"userTemp.userPassword\"/>\r\n          <div *ngIf=\"laboratoryForm.get('password')?.invalid && (laboratoryForm.get('password')?.dirty || laboratoryForm.get('password')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('password')?.errors?.['required']\">Password is required.</small>\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('password')?.errors?.['minlength']\">Password must be at least 8 characters long.</small><br>\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('password')?.errors?.['pattern']\">Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.</small>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"form-group\">\r\n          <label for=\"confirmPassword\">Re-enter Password</label>\r\n          <input type=\"password\" id=\"confirmPassword\" name=\"confirmPassword\" formControlName=\"confirmPassword\"/>\r\n          <div *ngIf=\"laboratoryForm.get('confirmPassword')?.invalid && (laboratoryForm.get('confirmPassword')?.dirty || laboratoryForm.get('confirmPassword')?.touched)\">\r\n            <small class=\"text-danger\" *ngIf=\"laboratoryForm.get('confirmPassword')?.errors?.['required']\">Please re-enter the password.</small>\r\n          </div>\r\n          <small class=\"text-danger\" *ngIf=\"laboratoryForm.errors?.['mismatch'] && laboratoryForm.get('confirmPassword')?.dirty\">Password do not match.</small>\r\n        </div>\r\n      </div>\r\n      <button type=\"submit\" #RegisterButton class=\"register-button\">Register</button>\r\n    </form>\r\n  </div>\r\n\r\n</div>\r\n"], "mappings": "AACA,SAASA,IAAI,EAAEC,YAAY,QAAQ,iBAAiB;AACpD,SAAiCC,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,GAAG,EAAEC,KAAK,EAAcC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAEtD,SAASC,UAAU,QAAQ,eAAe;AAE1C,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,QAAQ,EAAEC,YAAY,QAAQ,mBAAmB;;;;;;;;;;;;;ICC9CC,EAAA,CAAAC,cAAA,gBAA8F;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAClIH,EAAA,CAAAC,cAAA,gBAA6F;IAAAD,EAAA,CAAAE,MAAA,gFAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF9KH,EAAA,CAAAC,cAAA,UAA6J;IAC3JD,EAAA,CAAAI,UAAA,IAAAC,uDAAA,oBAAkI;IAClIL,EAAA,CAAAI,UAAA,IAAAE,uDAAA,oBAA4K;IAC9KN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFwBH,EAAA,CAAAO,SAAA,GAAgE;IAAhEP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,cAAA,CAAAC,GAAA,qCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAgE;IAChEb,EAAA,CAAAO,SAAA,GAA+D;IAA/DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,cAAA,CAAAC,GAAA,qCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAA+D;;;;;IAE7Fb,EAAA,CAAAC,cAAA,gBAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAzCH,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAC,2BAAA,CAAiC;;;;;IASzFjB,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADrHH,EAAA,CAAAC,cAAA,UAAwI;IACtID,EAAA,CAAAI,UAAA,IAAAc,uDAAA,oBAAmH;IACrHlB,EAAA,CAAAG,YAAA,EAAM;;;;;IADwBH,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAU,MAAA,CAAAR,cAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;;;;;IAUrFb,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC/GH,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFjHH,EAAA,CAAAC,cAAA,UAAkI;IAChID,EAAA,CAAAI,UAAA,IAAAgB,uDAAA,oBAA+G;IAC/GpB,EAAA,CAAAI,UAAA,IAAAiB,uDAAA,oBAA+G;IACjHrB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFwBH,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAa,MAAA,CAAAX,cAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAuD;IACvDb,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAQ,MAAA,CAAAX,cAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAoD;;;;;IAElFb,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAApCH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAe,iBAAA,CAAAQ,MAAA,CAAAC,sBAAA,CAA4B;;;;;IA+B/ExB,EAAA,CAAAC,cAAA,gBAAoF;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACvHH,EAAA,CAAAC,cAAA,gBAAmF;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFpHH,EAAA,CAAAC,cAAA,UAA+H;IAC7HD,EAAA,CAAAI,UAAA,IAAAqB,uDAAA,oBAAuH;IACvHzB,EAAA,CAAAI,UAAA,IAAAsB,uDAAA,oBAAkH;IACpH1B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFwBH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAkB,MAAA,CAAAhB,cAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAsD;IACtDb,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAa,MAAA,CAAAhB,cAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAAqD;;;;;IAQjFb,EAAA,CAAAC,cAAA,gBAA6F;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADlIH,EAAA,CAAAC,cAAA,UAA0J;IACxJD,EAAA,CAAAI,UAAA,IAAAwB,uDAAA,oBAAgI;IAClI5B,EAAA,CAAAG,YAAA,EAAM;;;;;IADwBH,EAAA,CAAAO,SAAA,GAA+D;IAA/DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAoB,MAAA,CAAAlB,cAAA,CAAAC,GAAA,oCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA+D;;;;;IAU3Fb,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACrHH,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAC7IH,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,uHAAgH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAHjNH,EAAA,CAAAC,cAAA,UAA2I;IACzID,EAAA,CAAAI,UAAA,IAAA0B,uDAAA,oBAAqH;IACrH9B,EAAA,CAAAI,UAAA,IAAA2B,uDAAA,oBAA6I;IAAA/B,EAAA,CAAAgC,SAAA,SAAI;IACjJhC,EAAA,CAAAI,UAAA,IAAA6B,uDAAA,oBAA+M;IACjNjC,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAHwBH,EAAA,CAAAO,SAAA,GAA0D;IAA1DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAyB,MAAA,CAAAvB,cAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA0D;IAC1Db,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAoB,MAAA,CAAAvB,cAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAA2D;IAC3Db,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAQ,UAAA,UAAA2B,OAAA,GAAAD,MAAA,CAAAvB,cAAA,CAAAC,GAAA,+BAAAuB,OAAA,CAAAtB,MAAA,kBAAAsB,OAAA,CAAAtB,MAAA,YAAyD;;;;;IAQrFb,EAAA,CAAAC,cAAA,gBAA+F;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IADtIH,EAAA,CAAAC,cAAA,UAAgK;IAC9JD,EAAA,CAAAI,UAAA,IAAAgC,uDAAA,oBAAoI;IACtIpC,EAAA,CAAAG,YAAA,EAAM;;;;;IADwBH,EAAA,CAAAO,SAAA,GAAiE;IAAjEP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAA4B,MAAA,CAAA1B,cAAA,CAAAC,GAAA,sCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAiE;;;;;IAE/Fb,EAAA,CAAAC,cAAA,gBAAuH;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;ADrF/J,MAKamC,+BAA+B;EAiB1CC,YACUC,EAAe,EACfC,MAAc,EACdC,WAAwB,EACxBC,WAAwB,EACxBC,iBAAoC,EACpCC,aAA4B;IAL5B,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IAtBvB,KAAAC,UAAU,GAAe,IAAIlD,UAAU,EAAE;IACzC,KAAAmD,IAAI,GAAS,IAAI1D,IAAI,EAAE;IACvB,KAAA2D,YAAY,GAAiB,IAAI1D,YAAY,EAAE;IAE/C,KAAA2D,iBAAiB,GAAY,KAAK;IAClC,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAA1B,sBAAsB,GAAW,EAAE;IACnC,KAAAP,2BAA2B,GAAW,EAAE;IAExC,KAAAkC,SAAS,GAAa,EAAE;IACxB,KAAAC,MAAM,GAAa,EAAE;IAErB;IACU,KAAAC,QAAQ,GAAa,IAAIvD,QAAQ,EAAE;IAW3C,IAAI,CAACa,cAAc,GAAG,IAAI,CAAC6B,EAAE,CAACc,KAAK,CACjC;MACEC,cAAc,EAAE,CACd,EAAE,EACF,CAAChE,UAAU,CAACiE,QAAQ,EAAEjE,UAAU,CAACkE,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAClE;MACDC,OAAO,EAAE,CAAC,EAAE,EAAEnE,UAAU,CAACiE,QAAQ,CAAC;MAClC;MACA;MACAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAACiE,QAAQ,EAAEjE,UAAU,CAACkE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACpEG,aAAa,EAAE,CAAC,EAAE,EAAErE,UAAU,CAACiE,QAAQ,CAAC;MACxCK,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtE,UAAU,CAACiE,QAAQ,EAAEjE,UAAU,CAACsE,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CACR,EAAE,EACF,CACEvE,UAAU,CAACiE,QAAQ,EACnBjE,UAAU,CAACwE,SAAS,CAAC,CAAC,CAAC,EACvBxE,UAAU,CAACkE,OAAO,CAAC,8CAA8C,CAAC,CACnE,CACF;MACDO,eAAe,EAAE,CAAC,EAAE,EAAEzE,UAAU,CAACiE,QAAQ;KAC1C,EACD;MAAES,SAAS,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC3C;EACH;EAEAC,QAAQA,CAAA;IACNC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAAC3B,WAAW,CAAC4B,mBAAmB,CAAC,CAAC,CAAC,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC7D,IAAI,CAACxB,YAAY,GAAGwB,QAAQ;IAC9B,CAAC,CAAC;IACF,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACN,aAAa,CAAC4B,YAAY,EAAE;IAClD,IAAI,CAACpB,QAAQ,CAACqB,YAAY,GAAG3E,YAAY,CAAC4E,UAAU;EACtD;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAT,sBAAsBA,CAACU,IAAe;IACpC,MAAMd,QAAQ,GAAGc,IAAI,CAAChE,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMoD,eAAe,GAAGY,IAAI,CAAChE,GAAG,CAAC,iBAAiB,CAAC;IAEnD;IACA,IACEkD,QAAQ,EAAEe,KAAK,IACfb,eAAe,EAAEa,KAAK,KACrBb,eAAe,CAACc,KAAK,IAAId,eAAe,CAACe,OAAO,CAAC,EAClD;MACA,OAAOjB,QAAQ,CAACe,KAAK,KAAKb,eAAe,CAACa,KAAK,GAC3C,IAAI,GACJ;QAAEG,QAAQ,EAAE;MAAI,CAAE;;IAGxB;IACA,OAAO,IAAI;EACb;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClC,IAAI,CAACmC,QAAQ,GAAG,IAAI,CAACpC,UAAU,CAACe,KAAK;EAC5C;EAEAsB,cAAcA,CAAA;IACZ,IAAI,CAACpC,IAAI,CAACqC,cAAc,GAAG,IAAI,CAACpC,YAAY;IAC5C,IAAI,CAACD,IAAI,CAACsC,SAAS,GAAG,IAAI,CAACvC,UAAU,CAACwC,IAAI;IAC1C,OAAO,IAAI,CAAC5C,WAAW,CAAC6C,QAAQ,CAAC,IAAI,CAACxC,IAAI,CAAC,CAACyC,IAAI,CAC9C7F,GAAG,CACA6E,QAAQ,IAAI;MACX,IAAI,CAACzB,IAAI,CAAC0C,MAAM,GAAGjB,QAAQ,CAACkB,EAAE;MAC9B,IAAI,CAAC5C,UAAU,CAAC2C,MAAM,GAAG,IAAI,CAAC1C,IAAI;IACpC,CAAC,EACA4C,KAAK,IAAI;MACRC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB,CAAC,CACF,EACDlG,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAEAqG,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAClD,iBAAiB,CAACmD,cAAc,CAAC,IAAI,CAACjD,UAAU,CAAC,CAAC0C,IAAI,CAChE7F,GAAG,CACD,MAAK;MACH,IAAI,CAAC8C,MAAM,CAACuD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC,CAAC,EACAL,KAAK,IAAI;MACRC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IACpB,CAAC,CACF,EACDlG,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAIA;EACAwG,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACtF,cAAc,CAACuF,OAAO,EAAE;MAC/B,IAAI,CAACvF,cAAc,CAACwF,gBAAgB,EAAE;MACtC;;IAGF;IACA,IAAI,CAACC,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,IAAI;IACjD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,yCAAyC;IAEvF;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEI,IAAI,CAAC5D,WAAW,CAAC6D,YAAY,CAAC,IAAI,CAACnD,QAAQ,CAAC,CAACkB,SAAS,CACnDkC,aAAuB,IAAI;MAC1Bb,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEY,aAAa,CAAC;MAExD,MAAMC,gBAAgB,GAAaD,aAAa;MAChD,IAAIE,KAAK,GAAG,yBAAyB;MACrC,IAAIC,OAAO,GAAG,8JAA8J;MAC5K,IAAIC,QAAQ,GAKK,SAAS;MAE1B,IAAI,CAACH,gBAAgB,EAAE;QACrBC,KAAK,GAAG,sBAAsB;QAC9BC,OAAO,GAAE,wDAAwD;QACjEC,QAAQ,GAAG,OAAO;;MAGpBhH,IAAI,CAACiH,IAAI,CAAC;QACRH,KAAK,EAAEA,KAAK;QACZI,IAAI,EAAEH,OAAO;QACbI,IAAI,EAAEH,QAAQ;QACdI,iBAAiB,EAAE;OAChB,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAI;QACrB,IAAIA,MAAM,CAACC,WAAW,IAAIV,gBAAgB,EAAE;UAC1C;UACA,IAAI,CAACjE,MAAM,CAACuD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;MAEzC,CAAC,CAAC;MAEF;MACA,IAAI,CAACI,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,KAAK;MAClD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,UAAU;IAC1D,CAAC,EACAZ,KAAK,IAAI;MACPC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAE5C;MACA,IAAI0B,YAAY,GAAG,gEAAgE;MACnF,IAAI1B,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACiB,OAAO,EAAE;QACtCS,YAAY,GAAG1B,KAAK,CAACA,KAAK,CAACiB,OAAO;OACnC,MAAM,IAAIjB,KAAK,CAACiB,OAAO,EAAE;QACxBS,YAAY,GAAG1B,KAAK,CAACiB,OAAO;;MAG9B/G,IAAI,CAACiH,IAAI,CAAC;QACRH,KAAK,EAAE,sBAAsB;QAC5BI,IAAI,EAAEM,YAAY;QACnBL,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;MAEF,IAAI,CAACb,cAAc,CAACC,aAAa,CAACC,QAAQ,GAAG,KAAK;MAClD,IAAI,CAACF,cAAc,CAACC,aAAa,CAACE,SAAS,GAAG,UAAU;IAC1D,CAAC,CACF;IACH;EACJ;;EAGAe,gBAAgBA,CAAA;IAEdzH,IAAI,CAACiH,IAAI,CAAC;MACRH,KAAK,EAAE,sBAAsB;MAC7BI,IAAI,EAAE,yHAAyH;MAC/HC,IAAI,EAAE,SAAS;MACfC,iBAAiB,EAAE;KACpB,CAAC;IAEF,IAAI,IAAI,CAACtG,cAAc,CAACuF,OAAO,EAAE;MAC/B,IAAI,CAACvF,cAAc,CAACwF,gBAAgB,EAAE;MACtC;;IAEF,IAAI,CAACoB,mBAAmB,EAAE,CAAChD,SAAS,CAAErB,sBAAsB,IAAI;MAC9D,IAAI,CAACA,sBAAsB,EAAE;QAC3B,IAAI,CAACsE,cAAc,EAAE,CAACjD,SAAS,CAAEtB,iBAAiB,IAAI;UACpD,IAAI,CAACA,iBAAiB,EAAE;YACtB,IAAI,CAACkC,cAAc,EAAE,CAACZ,SAAS,CAAC,MAAK;cACnC,IAAI,CAACuB,oBAAoB,EAAE,CAACvB,SAAS,CAAC,MAAK;gBACzC;gBACA1E,IAAI,CAACiH,IAAI,CAAC;kBACRE,IAAI,EAAE,SAAS;kBACfL,KAAK,EAAE,0BAA0B;kBACjCI,IAAI,EAAE,oFAAoF;kBAC1FE,iBAAiB,EAAE;iBACpB,CAAC,CAACC,IAAI,CAAC,MAAK;kBACX;kBACA,IAAI,CAACzE,MAAM,CAACuD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;gBACvC,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,CAAC;WACH,MAAM;YACLnG,IAAI,CAACiH,IAAI,CAAC;cACRE,IAAI,EAAE,OAAO;cACbL,KAAK,EAAE,0BAA0B;cACjCI,IAAI,EAAE,IAAI,CAACvF,sBAAsB;cACjCyF,iBAAiB,EAAE;aACpB,CAAC;;QAEN,CAAC,CAAC;OACH,MAAM;QACLpH,IAAI,CAACiH,IAAI,CAAC;UACRE,IAAI,EAAE,OAAO;UACbL,KAAK,EAAE,+BAA+B;UACtCI,IAAI,EAAE,IAAI,CAAC9F,2BAA2B;UACtCgG,iBAAiB,EAAE;SACpB,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAO,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7G,cAAc,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE6G,KAAK,EAAE;MAC3C,MAAMC,SAAS,GAAG,IAAI,CAAC/G,cAAc,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEiE,KAAK;MACzD,OAAO,IAAI,CAACnC,WAAW,CAACiF,SAAS,CAACD,SAAS,CAAC,CAAClC,IAAI,CAC/ChG,GAAG,CAAEoI,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC3E,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAACzB,sBAAsB,GACzB,wCAAwC;SAC3C,MAAM;UACL,IAAI,CAACyB,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAACzB,sBAAsB,GAAG,EAAE;;QAElC,OAAO,IAAI,CAACyB,iBAAiB;MAC/B,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACzB,sBAAsB,GAAG,EAAE;MAChC,OAAO9B,EAAE,CAAC,IAAI,CAACuD,iBAAiB,CAAC;;EAErC;EAEAsE,mBAAmBA,CAAA;IACjB,IAAI,IAAI,CAAC5G,cAAc,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAE6G,KAAK,EAAE;MACpD,MAAMlE,cAAc,GAAG,IAAI,CAAC5C,cAAc,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAEiE,KAAK;MACvE,OAAO,IAAI,CAACjC,iBAAiB,CAAC2E,mBAAmB,CAAChE,cAAc,CAAC,CAACiC,IAAI,CACpEhG,GAAG,CAAEoI,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC1E,sBAAsB,GAAG,IAAI;UAClC,IAAI,CAACjC,2BAA2B,GAC9B,kCAAkC;SACrC,MAAM;UACL,IAAI,CAACiC,sBAAsB,GAAG,KAAK;UACnC,IAAI,CAACjC,2BAA2B,GAAG,EAAE;;QAEvC,OAAO,IAAI,CAACiC,sBAAsB;MACpC,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACjC,2BAA2B,GAAG,EAAE;MACrC,OAAOvB,EAAE,CAAC,IAAI,CAACwD,sBAAsB,CAAC;;EAE1C;EAEA2E,qBAAqBA,CAAA;IACnB,IAAI,CAACpF,MAAM,CAACuD,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAAC,QAAA8B,CAAA,G;qBAhUUxF,+BAA+B,EAAAtC,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAA+H,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAvI,EAAA,CAAA+H,iBAAA,CAAAS,EAAA,CAAAC,iBAAA,GAAAzI,EAAA,CAAA+H,iBAAA,CAAAW,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA/BtG,+BAA+B;IAAAuG,SAAA;IAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QClB5ChJ,EAAA,CAAAgC,SAAA,4BAAmD;QACnDhC,EAAA,CAAAC,cAAA,aAA+B;QAEvBD,EAAA,CAAAE,MAAA,8BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChCH,EAAA,CAAAC,cAAA,gBAAyE;QAAlCD,EAAA,CAAAkJ,UAAA,mBAAAC,iEAAA;UAAA,OAASF,GAAA,CAAApB,qBAAA,EAAuB;QAAA,EAAC;QAAC7H,EAAA,CAAAE,MAAA,qBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAChGH,EAAA,CAAAC,cAAA,cAAqE;QAAlCD,EAAA,CAAAkJ,UAAA,sBAAAE,kEAAA;UAAA,OAAYH,GAAA,CAAAhD,kBAAA,EAAoB;QAAA,EAAC;QAClEjG,EAAA,CAAAC,cAAA,aAAsB;QAEID,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7CH,EAAA,CAAAC,cAAA,gBAAmH;QAAjCD,EAAA,CAAAkJ,UAAA,2BAAAG,yEAAAC,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAkG,QAAA,GAAAD,MAAA;QAAA,EAA+B;QAAjHtJ,EAAA,CAAAG,YAAA,EAAmH;QACnHH,EAAA,CAAAI,UAAA,KAAAoJ,+CAAA,iBAGM;QACNxJ,EAAA,CAAAI,UAAA,KAAAqJ,iDAAA,oBAAmG;QACrGzJ,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAEGD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpCH,EAAA,CAAAC,cAAA,oBAAoH;QAAxCD,EAAA,CAAAkJ,UAAA,2BAAAQ,4EAAAJ,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAK,OAAA,GAAA4F,MAAA;QAAA,EAA8B;QAAUtJ,EAAA,CAAAG,YAAA,EAAW;QAC/HH,EAAA,CAAAI,UAAA,KAAAuJ,+CAAA,iBAEM;QACR3J,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAECD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxCH,EAAA,CAAAC,cAAA,iBAAsI;QAAlED,EAAA,CAAAkJ,UAAA,2BAAAU,yEAAAN,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,GAAA4B,MAAA;QAAA,EAAgC,2BAAAM,yEAAA;UAAA,OAAkBX,GAAA,CAAAhE,WAAA,EAAa;QAAA,EAA/B;QAApGjF,EAAA,CAAAG,YAAA,EAAsI;QACtIH,EAAA,CAAAI,UAAA,KAAAyJ,+CAAA,iBAGM;QACN7J,EAAA,CAAAI,UAAA,KAAA0J,iDAAA,oBAAyF;QAC3F9J,EAAA,CAAAG,YAAA,EAAM;QAyBRH,EAAA,CAAAC,cAAA,cAAsB;QAESD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjDH,EAAA,CAAAC,cAAA,iBAAwH;QAAtCD,EAAA,CAAAkJ,UAAA,2BAAAa,yEAAAT,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAA2G,aAAA,GAAAV,MAAA;QAAA,EAAoC;QAAtHtJ,EAAA,CAAAG,YAAA,EAAwH;QACxHH,EAAA,CAAAI,UAAA,KAAA6J,+CAAA,iBAGM;QACRjK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAwB;QACKD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjDH,EAAA,CAAAC,cAAA,iBAAiI;QAAtCD,EAAA,CAAAkJ,UAAA,2BAAAgB,yEAAAZ,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAO,aAAA,GAAA0F,MAAA;QAAA,EAAoC;QAA/HtJ,EAAA,CAAAG,YAAA,EAAiI;QACjIH,EAAA,CAAAI,UAAA,KAAA+J,+CAAA,iBAEM;QACRnK,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAsB;QAEID,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtCH,EAAA,CAAAC,cAAA,iBAAqH;QAArCD,EAAA,CAAAkJ,UAAA,2BAAAkB,yEAAAd,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAgH,YAAA,GAAAf,MAAA;QAAA,EAAmC;QAAnHtJ,EAAA,CAAAG,YAAA,EAAqH;QACrHH,EAAA,CAAAI,UAAA,KAAAkK,+CAAA,iBAIM;QACRtK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,cAAwB;QACOD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtDH,EAAA,CAAAgC,SAAA,iBAAsG;QACtGhC,EAAA,CAAAI,UAAA,KAAAmK,+CAAA,iBAEM;QACNvK,EAAA,CAAAI,UAAA,KAAAoK,iDAAA,oBAAqJ;QACvJxK,EAAA,CAAAG,YAAA,EAAM;QAERH,EAAA,CAAAC,cAAA,sBAA8D;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;QAhG3EH,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAQ,UAAA,cAAAyI,GAAA,CAAAtI,cAAA,CAA4B;QAIsDX,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAkG,QAAA,CAA+B;QAC3GvJ,EAAA,CAAAO,SAAA,GAAqJ;QAArJP,EAAA,CAAAQ,UAAA,WAAA2B,OAAA,GAAA8G,GAAA,CAAAtI,cAAA,CAAAC,GAAA,qCAAAuB,OAAA,CAAA+D,OAAA,QAAA/D,OAAA,GAAA8G,GAAA,CAAAtI,cAAA,CAAAC,GAAA,qCAAAuB,OAAA,CAAA2C,KAAA,OAAA3C,OAAA,GAAA8G,GAAA,CAAAtI,cAAA,CAAAC,GAAA,qCAAAuB,OAAA,CAAA4C,OAAA,GAAqJ;QAI/H/E,EAAA,CAAAO,SAAA,GAA4B;QAA5BP,EAAA,CAAAQ,UAAA,SAAAyI,GAAA,CAAA/F,sBAAA,CAA4B;QAOoBlD,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAK,OAAA,CAA8B;QACpG1D,EAAA,CAAAO,SAAA,GAAgI;QAAhIP,EAAA,CAAAQ,UAAA,WAAAiK,OAAA,GAAAxB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,8BAAA6J,OAAA,CAAAvE,OAAA,QAAAuE,OAAA,GAAAxB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,8BAAA6J,OAAA,CAAA3F,KAAA,OAAA2F,OAAA,GAAAxB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,8BAAA6J,OAAA,CAAA1F,OAAA,GAAgI;QASlE/E,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,CAAgC;QAC9F1H,EAAA,CAAAO,SAAA,GAA0H;QAA1HP,EAAA,CAAAQ,UAAA,WAAAkK,OAAA,GAAAzB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,4BAAA8J,OAAA,CAAAxE,OAAA,QAAAwE,OAAA,GAAAzB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,4BAAA8J,OAAA,CAAA5F,KAAA,OAAA4F,OAAA,GAAAzB,GAAA,CAAAtI,cAAA,CAAAC,GAAA,4BAAA8J,OAAA,CAAA3F,OAAA,GAA0H;QAIpG/E,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAQ,UAAA,SAAAyI,GAAA,CAAAhG,iBAAA,CAAuB;QA6B+BjD,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAA2G,aAAA,CAAoC;QAChHhK,EAAA,CAAAO,SAAA,GAAuH;QAAvHP,EAAA,CAAAQ,UAAA,WAAAmK,QAAA,GAAA1B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,2BAAA+J,QAAA,CAAAzE,OAAA,QAAAyE,QAAA,GAAA1B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,2BAAA+J,QAAA,CAAA7F,KAAA,OAAA6F,QAAA,GAAA1B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,2BAAA+J,QAAA,CAAA5F,OAAA,GAAuH;QAQlC/E,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAO,aAAA,CAAoC;QACzH5D,EAAA,CAAAO,SAAA,GAAkJ;QAAlJP,EAAA,CAAAQ,UAAA,WAAAoK,QAAA,GAAA3B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,oCAAAgK,QAAA,CAAA1E,OAAA,QAAA0E,QAAA,GAAA3B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,oCAAAgK,QAAA,CAAA9F,KAAA,OAAA8F,QAAA,GAAA3B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,oCAAAgK,QAAA,CAAA7F,OAAA,GAAkJ;QASxE/E,EAAA,CAAAO,SAAA,GAAmC;QAAnCP,EAAA,CAAAQ,UAAA,YAAAyI,GAAA,CAAA5F,QAAA,CAAAgH,YAAA,CAAmC;QAC7GrK,EAAA,CAAAO,SAAA,GAAmI;QAAnIP,EAAA,CAAAQ,UAAA,WAAAqK,QAAA,GAAA5B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,+BAAAiK,QAAA,CAAA3E,OAAA,QAAA2E,QAAA,GAAA5B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,+BAAAiK,QAAA,CAAA/F,KAAA,OAAA+F,QAAA,GAAA5B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,+BAAAiK,QAAA,CAAA9F,OAAA,GAAmI;QAUnI/E,EAAA,CAAAO,SAAA,GAAwJ;QAAxJP,EAAA,CAAAQ,UAAA,WAAAsK,QAAA,GAAA7B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,sCAAAkK,QAAA,CAAA5E,OAAA,QAAA4E,QAAA,GAAA7B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,sCAAAkK,QAAA,CAAAhG,KAAA,OAAAgG,QAAA,GAAA7B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,sCAAAkK,QAAA,CAAA/F,OAAA,GAAwJ;QAGlI/E,EAAA,CAAAO,SAAA,GAAyF;QAAzFP,EAAA,CAAAQ,UAAA,UAAAyI,GAAA,CAAAtI,cAAA,CAAAE,MAAA,kBAAAoI,GAAA,CAAAtI,cAAA,CAAAE,MAAA,mBAAAkK,QAAA,GAAA9B,GAAA,CAAAtI,cAAA,CAAAC,GAAA,sCAAAmK,QAAA,CAAAjG,KAAA,EAAyF;;;;;;;SDhFlHxC,+BAA+B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}