package com.navitsa.mydent.entity;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "ClinicAdd_Service")
public class ClinicAddService {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ClinicAddService_id")
    private Integer clinicAddServiceId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "user_id", referencedColumnName = "user_id")
    private User userId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "clinicServicecategory_id", referencedColumnName = "clinicServicecategory_id")
    private ClinicServicesCategory clinicServiceCategoryId;

    @Column(name = "addService_date")
    private String addServiceDate;


    public ClinicAddService() {
    }


    public ClinicAddService(Integer clinicAddServiceId, User userId, ClinicServicesCategory clinicServiceCategoryId, String addServiceDate) {
        this.clinicAddServiceId = clinicAddServiceId;
        this.userId = userId;
        this.clinicServiceCategoryId = clinicServiceCategoryId;
        this.addServiceDate = addServiceDate;
    }

 

    public Integer getClinicAddServiceId() {
        return clinicAddServiceId;
    }

    public void setClinicAddServiceId(Integer clinicAddServiceId) {
        this.clinicAddServiceId = clinicAddServiceId;
    }

    public User getUserId() {
        return userId;
    }

    public void setUserId(User userId) {
        this.userId = userId;
    }

    public ClinicServicesCategory getClinicServiceCategoryId() {
        return clinicServiceCategoryId;
    }

    public void setClinicServiceCategoryId(ClinicServicesCategory clinicServiceCategoryId) {
        this.clinicServiceCategoryId = clinicServiceCategoryId;
    }

    public String getAddServiceDate() {
        return addServiceDate;
    }

    public void setAddServiceDate(String addServiceDate) {
        this.addServiceDate = addServiceDate;
    }
}
