package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.math.BigDecimal;

@Entity
@Table(name = "laboratory_setup")
public class LaboratorySetup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "laboratory_setup_id")
    private Integer laboratorySetupId;

    @Column(name = "description")
    private String description;

    @Column(name = "price")
    private BigDecimal price;
    
    @Column(name = "status")
    private String status;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "laboratory_id", referencedColumnName = "laboratory_id")
    private Laboratory laboratoryId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "laboratory_category_id", referencedColumnName = "laboratory_category_id")
    private LaboratoryCategories laboratoryCategoryId;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "laboratory_sub_category_id", referencedColumnName = "laboratory_sub_category_id")
    private LaboratorySubCategories laboratorySubCategoryId;


    public LaboratorySetup() {}

    public LaboratorySetup(Integer laboratorySetupId, String description, BigDecimal price, String status, Laboratory laboratoryId, LaboratoryCategories laboratoryCategoryId, LaboratorySubCategories laboratorySubCategoryId) {
        super();
        this.laboratorySetupId = laboratorySetupId;
        this.description = description;
        this.price = price;
        this.status = status;
        this.laboratoryId = laboratoryId;
        this.laboratoryCategoryId = laboratoryCategoryId;
        this.laboratorySubCategoryId = laboratorySubCategoryId;

    }

    public Integer getLaboratorySetupId() {
        return laboratorySetupId;
    }

    public void setLaboratorySetupId(Integer laboratorySetupId) {
        this.laboratorySetupId = laboratorySetupId;
    }
    
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Laboratory getLaboratoryId() {
        return laboratoryId;
    }

    public void setLaboratoryId(Laboratory laboratoryId) {
        this.laboratoryId = laboratoryId;
    }
    
    public LaboratoryCategories getLaboratoryCategoryId() {
        return laboratoryCategoryId;
    }

    public void setLaboratoryCategoryId(LaboratoryCategories laboratoryCategoryId) {
        this.laboratoryCategoryId = laboratoryCategoryId;
    }
    
    public LaboratorySubCategories getLaboratorySubCategoryId() {
        return laboratorySubCategoryId;
    }

    public void setLaboratorySubCategoryId(LaboratorySubCategories laboratorySubCategoryId) {
        this.laboratorySubCategoryId = laboratorySubCategoryId;
    }
}
