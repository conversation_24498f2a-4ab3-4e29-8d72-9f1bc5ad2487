export class UserTemp {
  userTempId: number =0;
  mainName: string = '';
  additionalName?: string;
  userEmail: string = '';
  userPassword: string = '';
  userTempType?: UserTempType
  userTempStatus?: UserTempStatus = UserTempStatus.CREATED;
  userTitle?: string;
  contactNumber?: string;
  contactPerson?: string;
  contactPersonDesignation?: string;
  registrationNumber?: string;
  address?: string;
  district?: string;
  city?: string;
  state?: string;
  createDateTime?: Date = new Date();
  verificationToken?:string = "0"
}

export enum UserTempType {
  DOCTOR = 'DOCTOR',
  CLINIC = 'CLINIC',
  LABORATORY = 'LABORATORY',
  SUPPLIER = 'SUPPLIER',
  FUTURE_DENTIST = 'FUTURE_DENTIST',
  ADMIN = 'ADMIN',
  CUSTOMER = 'CUSTOMER'
}

export enum UserTempStatus {
  CREATED = 'CREATED',
  SENT_USER_VERIFICATION = 'SENT_USER_VERIFICATION',
  USER_VERIFIED = 'USER_VERIFIED',
  ADMIN_APPROVED = 'ADMIN_APPROVED',
  ADMIN_REJECTED = 'ADMIN_REJECTED'
}
