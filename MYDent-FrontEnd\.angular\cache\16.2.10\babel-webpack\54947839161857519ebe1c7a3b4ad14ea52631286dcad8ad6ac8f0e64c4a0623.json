{"ast": null, "code": "import { User, UserCategory } from '../../user/user';\nimport { Validators } from '@angular/forms';\nimport { map, mapTo, of, tap } from 'rxjs';\nimport { Clinic } from '../clinic';\nimport Swal from 'sweetalert2';\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../user/user.service\";\nimport * as i4 from \"../clinic.service\";\nimport * as i5 from \"../../modules/shared-services/shared.service\";\nimport * as i6 from \"src/app/auth/auth.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../core/default-navbar/default-navbar.component\";\nimport * as i9 from \"../../core/primary-action-button/primary-action-button.component\";\nconst _c0 = [\"RegisterButton\"];\nfunction ClinicRegistrationComponent_div_18_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Clinic Name is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_18_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \"Clinic Name can only contain letters, numbers, spaces, and \\\".()/,\\\".\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_18_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵtemplate(2, ClinicRegistrationComponent_div_18_small_2_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.clinicForm.get(\"clinicName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.clinicForm.get(\"clinicName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction ClinicRegistrationComponent_small_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.clinicNameExistsMessage, \" \");\n  }\n}\nfunction ClinicRegistrationComponent_div_26_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Address is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_26_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.clinicForm.get(\"address\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicRegistrationComponent_div_32_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Email is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_32_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Invalid email format. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_32_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵtemplate(2, ClinicRegistrationComponent_div_32_small_2_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.clinicForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.clinicForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction ClinicRegistrationComponent_small_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.userEmailExistsMessage, \" \");\n  }\n}\nfunction ClinicRegistrationComponent_option_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const district_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", district_r21);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", district_r21, \" \");\n  }\n}\nfunction ClinicRegistrationComponent_div_42_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" District is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_42_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.clinicForm.get(\"district\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicRegistrationComponent_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r23);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", city_r23, \" \");\n  }\n}\nfunction ClinicRegistrationComponent_div_50_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" City is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_50_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r8.clinicForm.get(\"city\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicRegistrationComponent_div_56_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Contact Number is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_56_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Invalid Contact number. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_56_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵtemplate(2, ClinicRegistrationComponent_div_56_small_2_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.clinicForm.get(\"tele\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r9.clinicForm.get(\"tele\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]);\n  }\n}\nfunction ClinicRegistrationComponent_div_61_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \"Contact Person is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_61_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r10.clinicForm.get(\"contactPerson\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicRegistrationComponent_div_69_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Password is required. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_69_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Password must be at least 8 characters, including an uppercase letter, lowercase letter, digit, and special character. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_69_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵtemplate(2, ClinicRegistrationComponent_div_69_small_2_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r11.clinicForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r11.clinicForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]) || ((tmp_1_0 = ctx_r11.clinicForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"pattern\"]));\n  }\n}\nfunction ClinicRegistrationComponent_div_74_small_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \" Please re-enter the password. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicRegistrationComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, ClinicRegistrationComponent_div_74_small_1_Template, 2, 0, \"small\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r12.clinicForm.get(\"rePassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction ClinicRegistrationComponent_small_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 44);\n    i0.ɵɵtext(1, \"Password do not match.\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nclass ClinicRegistrationComponent {\n  togglePasswordVisibility() {\n    this.passwordVisible = !this.passwordVisible;\n  }\n  onDistrictChange(event) {\n    const selectedDistrict = event.target.value;\n    if (selectedDistrict) {\n      this.clinicForm.get('city')?.enable();\n      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\n      console.log(this.cities);\n    } else {\n      this.clinicForm.get('city')?.disable();\n      this.cities = [];\n    }\n    this.clinicForm.get('city')?.setValue('');\n  }\n  constructor(fb, router, userService, clinicService, sharedService, authService) {\n    this.fb = fb;\n    this.router = router;\n    this.userService = userService;\n    this.clinicService = clinicService;\n    this.sharedService = sharedService;\n    this.authService = authService;\n    this.clinic = new Clinic();\n    this.user = new User();\n    this.userCategory = new UserCategory();\n    this.passwordDoNotMatch = false;\n    this.isEmailRegistered = false;\n    this.userEmailExistsMessage = '';\n    this.isClinicRegistered = false;\n    this.isCityDisabled = true;\n    this.clinicNameExistsMessage = '';\n    this.userTemp = new UserTemp();\n    this.districts = [];\n    this.cities = [];\n    this.passwordVisible = false;\n    this.clinicForm = this.fb.group({\n      clinicName: ['', [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')]],\n      address: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      district: ['', Validators.required],\n      city: [{\n        value: '',\n        disabled: true\n      }, Validators.required],\n      tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],\n      contactPerson: ['', Validators.required],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$')]],\n      rePassword: ['', Validators.required]\n    }, {\n      validator: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const rePassword = form.get('rePassword');\n    if (password?.value && rePassword?.value && (rePassword.dirty || rePassword.touched)) {\n      return password.value === rePassword.value ? null : {\n        mismatch: true\n      };\n    }\n    return null;\n  }\n  ngOnInit() {\n    localStorage.clear();\n    this.userService.getUserCategoryById(3).subscribe(response => {\n      this.userCategory = response;\n    });\n    this.districts = this.sharedService.getDistricts();\n    this.userTemp.userTempType = UserTempType.CLINIC;\n  }\n  updateEmail() {\n    this.user.username = this.clinic.email;\n  }\n  onSubmitRegister() {\n    if (this.clinicForm.invalid) {\n      this.clinicForm.markAllAsTouched();\n      return;\n    }\n    this.checkClinicName().subscribe(isClinicRegistered => {\n      if (!isClinicRegistered) {\n        this.checkUserEmail().subscribe(isEmailRegistered => {\n          if (!isEmailRegistered) {\n            this.onUserRegister().subscribe(() => {\n              this.onClinicRegister().subscribe(() => {\n                Swal.fire({\n                  title: \"Wait until approval!\",\n                  text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\n                  icon: 'success',\n                  confirmButtonText: 'OK'\n                });\n              });\n            });\n          }\n        });\n      }\n    });\n  }\n  onUserRegister() {\n    this.user.userCategoryId = this.userCategory;\n    this.user.firstName = this.clinic.name;\n    return this.userService.register(this.user).pipe(tap(response => {\n      this.user.userId = response.id;\n      this.clinic.userId = this.user;\n    }, error => {\n      console.log(error);\n    }), mapTo(void 0));\n  }\n  onClinicRegister() {\n    return this.clinicService.saveClinic(this.clinic).pipe(tap(() => {\n      Swal.fire({\n        title: 'Registration Successful!',\n        text: 'Thank you for registering! Please verify your email to complete the login process.',\n        icon: 'success',\n        confirmButtonText: 'OK'\n      }).then(result => {\n        if (result.isConfirmed) {\n          this.router.navigate(['/user-login']);\n        }\n      });\n    }, error => {\n      console.log(error);\n      Swal.fire({\n        title: 'Error',\n        text: 'An error occurred during registration. Please try again later.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    }), mapTo(void 0));\n  }\n  // UserTemp Saving\n  onUserTempRegister() {\n    if (this.clinicForm.invalid) {\n      this.clinicForm.markAllAsTouched();\n      return;\n    }\n    // Log the userTemp object to check what's being sent\n    console.log('UserTemp object before sending:', this.userTemp);\n    // Disable the register button and show a loading indicator\n    this.registerButton.buttonInstance.nativeElement.disabled = true;\n    this.registerButton.buttonInstance.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\n    // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\n    // this.authService\n    //   .checkUserTempAvailability(this.userTemp.userEmail)\n    //   .subscribe((resp) => {\n    //     if (resp !=null) {\n    //       Swal.fire({\n    //         title: 'Registration Already Exists!',\n    //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\n    //         icon: 'info',\n    //         confirmButtonText: 'OK',\n    //       });\n    //       // Reset the button state\n    //       this.registerButton.buttonInstance.nativeElement.disabled = false;\n    //       this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\n    //       return;\n    //     }\n    this.authService.saveUserTemp(this.userTemp).subscribe(userTempSaved => {\n      console.log('Full userTempSaved object:', userTempSaved);\n      const receivedUserTemp = userTempSaved;\n      let title = 'Registration Completed!';\n      let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\n      let iconName = 'success';\n      if (!receivedUserTemp) {\n        title = 'Registration Failed!';\n        message = 'An error occurred while registering. Please try again.';\n        iconName = 'error';\n      }\n      Swal.fire({\n        title: title,\n        text: message,\n        icon: iconName,\n        confirmButtonText: 'OK'\n      });\n      // Reset button state\n      this.registerButton.buttonInstance.nativeElement.disabled = false;\n      this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\n    }, error => {\n      Swal.fire({\n        title: 'Registration Failed!',\n        text: 'An error occurred during registration. Please try again later.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n      this.registerButton.buttonInstance.nativeElement.disabled = false;\n      this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\n    });\n    // }); // COMMENTED OUT FOR TESTING\n  }\n\n  checkUserEmail() {\n    if (this.clinicForm.get('email')?.valid) {\n      const userEmail = this.clinicForm.get('email')?.value;\n      return this.userService.checkUser(userEmail).pipe(map(data => {\n        if (data) {\n          this.isEmailRegistered = true;\n          this.userEmailExistsMessage = 'Email already registered. Try another.';\n        } else {\n          this.isEmailRegistered = false;\n          this.userEmailExistsMessage = '';\n        }\n        return this.isEmailRegistered;\n      }));\n    } else {\n      this.isEmailRegistered = false;\n      this.userEmailExistsMessage = '';\n      return of(this.isEmailRegistered);\n    }\n  }\n  checkClinicName() {\n    if (this.clinicForm.get('clinicName')?.valid) {\n      const clinicName = this.clinicForm.get('clinicName')?.value;\n      return this.clinicService.checkClinicName(clinicName).pipe(map(data => {\n        if (data) {\n          this.isClinicRegistered = true;\n          this.clinicNameExistsMessage = 'That name is taken. Try another.';\n        } else {\n          this.isClinicRegistered = false;\n          this.clinicNameExistsMessage = '';\n        }\n        return this.isClinicRegistered;\n      }));\n    } else {\n      this.isClinicRegistered = false;\n      this.clinicNameExistsMessage = '';\n      return of(this.isClinicRegistered);\n    }\n  }\n  navigateUserSelection() {\n    this.router.navigate(['/user-selection']);\n  }\n  static #_ = this.ɵfac = function ClinicRegistrationComponent_Factory(t) {\n    return new (t || ClinicRegistrationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.ClinicService), i0.ɵɵdirectiveInject(i5.SharedService), i0.ɵɵdirectiveInject(i6.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClinicRegistrationComponent,\n    selectors: [[\"app-clinic-registration\"]],\n    viewQuery: function ClinicRegistrationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.registerButton = _t.first);\n      }\n    },\n    decls: 81,\n    vars: 29,\n    consts: [[1, \"container-fluid\", \"base-background\", 2, \"height\", \"100dvh\", \"overflow-y\", \"auto\"], [1, \"row\", \"h-100\"], [1, \"col-12\", 2, \"height\", \"80px !important\"], [\"loggedUser\", \"Hello Doctor\"], [1, \"col-12\", 2, \"height\", \"calc(100% - 80px) !important\"], [1, \"col-11\", \"m-auto\", \"py-5\"], [1, \"row\"], [1, \"col-12\", \"col-sm-10\", \"col-lg-8\", \"col-xl-6\", \"col-xxl-5\", \"mx-auto\", \"bg-white\", \"p-5\", \"bordered-container\"], [1, \"fs-4\", \"m-0\", \"header\"], [1, \"col-12\", \"base-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"col-12\"], [\"for\", \"clinic-name\", 1, \"input-label\"], [\"type\", \"text\", \"id\", \"clinic-name\", \"name\", \"clinic-name\", \"formControlName\", \"clinicName\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"px-1\", \"style\", \"font-weight: 500;\", 4, \"ngIf\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"for\", \"address\", 1, \"input-label\"], [\"type\", \"text\", \"id\", \"address\", \"name\", \"address\", \"formControlName\", \"address\", \"rows\", \"1\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email-address\", 1, \"input-label\"], [\"type\", \"email\", \"id\", \"email-address\", \"name\", \"email-address\", \"formControlName\", \"email\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"col-xl-6\", \"d-grid\"], [\"for\", \"district\", 1, \"input-label\"], [\"name\", \"district\", \"id\", \"district\", \"formControlName\", \"district\", 1, \"\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\", \"disabled\", \"\", \"selected\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"col-xl-6\", \"d-grid\", \"mt-3\", \"mt-xl-0\"], [\"for\", \"city\", 1, \"input-label\"], [\"name\", \"city\", \"id\", \"city\", \"formControlName\", \"city\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"col-xl-6\"], [\"for\", \"contact-number\", 1, \"input-label\"], [\"type\", \"text\", \"id\", \"contact-number\", \"name\", \"contact-number\", \"formControlName\", \"tele\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"col-xl-6\", \"mt-3\", \"mt-xl-0\"], [\"for\", \"contact-person\", 1, \"input-label\"], [\"type\", \"text\", \"id\", \"contact-person\", \"name\", \"contact-person\", \"formControlName\", \"contactPerson\", 3, \"ngModel\", \"ngModelChange\"], [1, \"col-12\", \"col-xl-6\", \"position-relative\"], [\"for\", \"password\", 1, \"input-label\"], [\"id\", \"password\", \"name\", \"password\", \"formControlName\", \"password\", 1, \"form-control\", 3, \"type\", \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"type\", \"button\", 1, \"show-password\", 2, \"position\", \"absolute\", \"right\", \"25px\", \"top\", \"48px\", \"transform\", \"translateY(-50%)\", \"padding\", \"0\", \"border\", \"none\", \"background\", \"none\", \"color\", \"#6c757d\", \"cursor\", \"pointer\", 3, \"click\"], [3, \"ngClass\"], [\"for\", \"repassword\", 1, \"input-label\"], [\"type\", \"password\", \"id\", \"repassword\", \"name\", \"repassword\", \"formControlName\", \"rePassword\"], [\"buttonType\", \"submit\", \"buttonUI\", \"primary\", \"buttonText\", \"Register\", 1, \"d-grid\", \"mt-4\", 3, \"buttonHeight\"], [\"RegisterButton\", \"\"], [\"type\", \"submit\", 1, \"register-button\", \"d-none\"], [1, \"px-1\", 2, \"font-weight\", \"500\"], [1, \"text-danger\"], [3, \"value\"]],\n    template: function ClinicRegistrationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-default-navbar\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 1)(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 6)(10, \"h3\", 8);\n        i0.ɵɵtext(11, \"Clinic Registration\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"form\", 9);\n        i0.ɵɵlistener(\"ngSubmit\", function ClinicRegistrationComponent_Template_form_ngSubmit_12_listener() {\n          return ctx.onUserTempRegister();\n        });\n        i0.ɵɵelementStart(13, \"div\", 6)(14, \"div\", 10)(15, \"label\", 11);\n        i0.ɵɵtext(16, \"Clinic Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"input\", 12);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_17_listener($event) {\n          return ctx.userTemp.mainName = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(18, ClinicRegistrationComponent_div_18_Template, 3, 2, \"div\", 13);\n        i0.ɵɵtemplate(19, ClinicRegistrationComponent_small_19_Template, 2, 1, \"small\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 6)(21, \"div\", 10)(22, \"label\", 15);\n        i0.ɵɵtext(23, \"Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"textarea\", 16);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_textarea_ngModelChange_24_listener($event) {\n          return ctx.userTemp.address = $event;\n        });\n        i0.ɵɵtext(25, \"                      \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(26, ClinicRegistrationComponent_div_26_Template, 2, 1, \"div\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 6)(28, \"div\", 10)(29, \"label\", 17);\n        i0.ɵɵtext(30, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"input\", 18);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_31_listener($event) {\n          return ctx.userTemp.userEmail = $event;\n        })(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_31_listener() {\n          return ctx.updateEmail();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, ClinicRegistrationComponent_div_32_Template, 3, 2, \"div\", 13);\n        i0.ɵɵtemplate(33, ClinicRegistrationComponent_small_33_Template, 2, 1, \"small\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 6)(35, \"div\", 19)(36, \"label\", 20);\n        i0.ɵɵtext(37, \"District\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"select\", 21);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_select_ngModelChange_38_listener($event) {\n          return ctx.userTemp.district = $event;\n        })(\"change\", function ClinicRegistrationComponent_Template_select_change_38_listener($event) {\n          return ctx.onDistrictChange($event);\n        });\n        i0.ɵɵelementStart(39, \"option\", 22);\n        i0.ɵɵtext(40, \"Select\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(41, ClinicRegistrationComponent_option_41_Template, 2, 2, \"option\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(42, ClinicRegistrationComponent_div_42_Template, 2, 1, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 24)(44, \"label\", 25);\n        i0.ɵɵtext(45, \"City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"select\", 26);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_select_ngModelChange_46_listener($event) {\n          return ctx.userTemp.city = $event;\n        });\n        i0.ɵɵelementStart(47, \"option\", 22);\n        i0.ɵɵtext(48, \"Select District\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(49, ClinicRegistrationComponent_option_49_Template, 2, 2, \"option\", 23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(50, ClinicRegistrationComponent_div_50_Template, 2, 1, \"div\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(51, \"div\", 6)(52, \"div\", 27)(53, \"label\", 28);\n        i0.ɵɵtext(54, \"Contact Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(55, \"input\", 29);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_55_listener($event) {\n          return ctx.userTemp.contactNumber = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(56, ClinicRegistrationComponent_div_56_Template, 3, 2, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"div\", 30)(58, \"label\", 31);\n        i0.ɵɵtext(59, \"Contact Person\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"input\", 32);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_60_listener($event) {\n          return ctx.userTemp.contactPerson = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(61, ClinicRegistrationComponent_div_61_Template, 2, 1, \"div\", 13);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(62, \"div\", 6)(63, \"div\", 33)(64, \"label\", 34);\n        i0.ɵɵtext(65, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"input\", 35);\n        i0.ɵɵlistener(\"ngModelChange\", function ClinicRegistrationComponent_Template_input_ngModelChange_66_listener($event) {\n          return ctx.userTemp.userPassword = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(67, \"button\", 36);\n        i0.ɵɵlistener(\"click\", function ClinicRegistrationComponent_Template_button_click_67_listener() {\n          return ctx.togglePasswordVisibility();\n        });\n        i0.ɵɵelement(68, \"i\", 37);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(69, ClinicRegistrationComponent_div_69_Template, 3, 2, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 30)(71, \"label\", 38);\n        i0.ɵɵtext(72, \"Re-enter Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(73, \"input\", 39);\n        i0.ɵɵtemplate(74, ClinicRegistrationComponent_div_74_Template, 2, 1, \"div\", 13);\n        i0.ɵɵtemplate(75, ClinicRegistrationComponent_small_75_Template, 2, 0, \"small\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(76, \"app-primary-action-button\", 40, 41);\n        i0.ɵɵelementStart(78, \"button\", 42, 41);\n        i0.ɵɵtext(80, \"Register\");\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_2_0;\n        let tmp_5_0;\n        let tmp_7_0;\n        let tmp_11_0;\n        let tmp_14_0;\n        let tmp_16_0;\n        let tmp_18_0;\n        let tmp_21_0;\n        let tmp_23_0;\n        let tmp_24_0;\n        let tmp_25_0;\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"formGroup\", ctx.clinicForm);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.mainName);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.clinicForm.get(\"clinicName\")) == null ? null : tmp_2_0.invalid) && (((tmp_2_0 = ctx.clinicForm.get(\"clinicName\")) == null ? null : tmp_2_0.dirty) || ((tmp_2_0 = ctx.clinicForm.get(\"clinicName\")) == null ? null : tmp_2_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isClinicRegistered);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.address);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.clinicForm.get(\"address\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = ctx.clinicForm.get(\"address\")) == null ? null : tmp_5_0.dirty) || ((tmp_5_0 = ctx.clinicForm.get(\"address\")) == null ? null : tmp_5_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userEmail);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.clinicForm.get(\"email\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = ctx.clinicForm.get(\"email\")) == null ? null : tmp_7_0.dirty) || ((tmp_7_0 = ctx.clinicForm.get(\"email\")) == null ? null : tmp_7_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEmailRegistered);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.district);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.districts);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.clinicForm.get(\"district\")) == null ? null : tmp_11_0.invalid) && (((tmp_11_0 = ctx.clinicForm.get(\"district\")) == null ? null : tmp_11_0.dirty) || ((tmp_11_0 = ctx.clinicForm.get(\"district\")) == null ? null : tmp_11_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.city);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.cities);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx.clinicForm.get(\"city\")) == null ? null : tmp_14_0.invalid) && (((tmp_14_0 = ctx.clinicForm.get(\"city\")) == null ? null : tmp_14_0.dirty) || ((tmp_14_0 = ctx.clinicForm.get(\"city\")) == null ? null : tmp_14_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.contactNumber);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.clinicForm.get(\"tele\")) == null ? null : tmp_16_0.invalid) && (((tmp_16_0 = ctx.clinicForm.get(\"tele\")) == null ? null : tmp_16_0.dirty) || ((tmp_16_0 = ctx.clinicForm.get(\"tele\")) == null ? null : tmp_16_0.touched)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.contactPerson);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.clinicForm.get(\"contactPerson\")) == null ? null : tmp_18_0.invalid) && (((tmp_18_0 = ctx.clinicForm.get(\"contactPerson\")) == null ? null : tmp_18_0.dirty) || ((tmp_18_0 = ctx.clinicForm.get(\"contactPerson\")) == null ? null : tmp_18_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵpropertyInterpolate(\"type\", ctx.passwordVisible ? \"text\" : \"password\");\n        i0.ɵɵproperty(\"ngModel\", ctx.userTemp.userPassword)(\"ngClass\", i0.ɵɵpureFunction1(27, _c1, ((tmp_21_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_21_0.invalid) && (((tmp_21_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_21_0.dirty) || ((tmp_21_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_21_0.touched))));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", ctx.passwordVisible ? \"bi bi-eye-fill\" : \"bi bi-eye-slash-fill\");\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_23_0.invalid) && (((tmp_23_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_23_0.dirty) || ((tmp_23_0 = ctx.clinicForm.get(\"password\")) == null ? null : tmp_23_0.touched)));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = ctx.clinicForm.get(\"rePassword\")) == null ? null : tmp_24_0.invalid) && (((tmp_24_0 = ctx.clinicForm.get(\"rePassword\")) == null ? null : tmp_24_0.dirty) || ((tmp_24_0 = ctx.clinicForm.get(\"rePassword\")) == null ? null : tmp_24_0.touched)));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", (ctx.clinicForm.errors == null ? null : ctx.clinicForm.errors[\"mismatch\"]) && ((tmp_25_0 = ctx.clinicForm.get(\"rePassword\")) == null ? null : tmp_25_0.dirty));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"buttonHeight\", 45);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i8.DefaultNavbarComponent, i9.PrimaryActionButtonComponent],\n    styles: [\".base-background[_ngcontent-%COMP%]{\\n  background: url('/assets/images/background.png') center center no-repeat;\\n  background-size: cover;\\n}\\n.bordered-container[_ngcontent-%COMP%]{\\n  border: 1px solid #fb751e;\\n  border-radius: 10px;\\n\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding-inline: 2px;\\n  border-radius: 25px;\\n  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n  \\n\\n  \\n\\n  \\n\\n  border: 1px solid #fb751e;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  width: 100%;\\n  height: auto;\\n  padding-inline: 10px;\\n  padding-top: 10px;\\n  padding-block: 20px;\\n}\\n\\nh3[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-size: 30px;\\n  font-weight: 700;\\n  text-align: center;\\n  color: #fb751e;\\n  margin-bottom: 8.5px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n  margin-right: 10px;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.form-row[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-size: 16px;\\n  font-weight: 400;\\n  text-align: left;\\n  margin-bottom: 5px;\\n  color: #000000;\\n  display: block;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 33px;\\n  padding: 5px;\\n  font-size: 14px;\\n  color: #495057;\\n  border-radius: 4px;\\n  border: 1px solid #b3b3b3;\\n  background-image: linear-gradient(45deg, transparent 50%, #ff7a00 50%),\\n    linear-gradient(135deg, #ff7a00 50%, transparent 50%);\\n  background-position: calc(100% - 20px) center,\\n    calc(100% - 15px) center;\\n  background-size: 5px 5px, 5px 5px;\\n  background-repeat: no-repeat;\\n  appearance: none;\\n}\\n\\n.header[_ngcontent-%COMP%]{\\n  color: transparent;\\n  background: linear-gradient(to right, #fb751e, #b93426);\\n  -webkit-background-clip: text;\\n          background-clip: text;\\n  box-shadow: none;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%], input[type=\\\"email\\\"][_ngcontent-%COMP%], input[type=\\\"password\\\"][_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 5px 10px !important;\\n  border: 1px solid rgb(220,220,220) !important;\\n  height: 40px !important;\\n  border-radius: 4px;\\n  background: transparent !important;\\n}\\n\\nselect[_ngcontent-%COMP%]{\\n  width: 100%;\\n  padding: 5px 10px !important;\\n  border: 1px solid rgb(220,220,220) !important;\\n  height: 40px !important;\\n  border-radius: 4px;\\n  background-color: white;\\n}\\n\\nselect[_ngcontent-%COMP%]:disabled{\\n  background: rgba(221, 221, 221, 0.226) !important;\\n  cursor: not-allowed;\\n}\\n\\n.input-label[_ngcontent-%COMP%]{\\n  font-size: 14px !important;\\n  font-weight: 500 !important;\\n  color: rgb(100,100,100) !important;\\n  margin-bottom: 5px !important;\\n}\\n\\n.base-form[_ngcontent-%COMP%]   div.row[_ngcontent-%COMP%]{\\n  margin-bottom: 16px;\\n}\\n\\n\\ninput[type=\\\"text\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"email\\\"][_ngcontent-%COMP%]:focus, input[type=\\\"password\\\"][_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:not(:disabled):focus {\\n  outline: none;\\n  box-shadow: none;\\n  border-color: #ff7a00 !important;\\n}\\n\\ntextarea[_ngcontent-%COMP%] {\\n  font-family: \\\"Inter\\\";\\n  font-weight: 400;\\n  font-size: 14px;\\n  color: #495057;\\n  width: 100%;\\n  height: 40px;\\n  padding: 5px 10px;\\n  border: 1px solid rgb(220,220,220);;\\n  border-radius: 4px;\\n  \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #ff7a00;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, #fb751e, #333333);\\n  border: none;\\n  border-radius: 20px;\\n  color: white;\\n  font-size: 16px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n  margin-top: 30px;\\n}\\n\\n.register-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(to left, #fb751e, #333333);\\n}\\n\\n.backtoselection-button[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 40px;\\n  background: none;\\n  border: 2px solid #fb751e;\\n  color: #fb751e;\\n  font-size: 16px;\\n  font-weight: bold;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n  justify-content: center;\\n  margin-bottom: 20px;\\n  margin-top: 20px;\\n}\\n\\n@media (min-width: 768px) {\\n  .background-container[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .form-container[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding-inline: 2px;\\n    border-radius: 25px;\\n    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n    \\n\\n    \\n\\n    \\n\\n    border: 1px solid #fb751e;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    width: 100%;\\n    height: auto;\\n    padding-inline: 20px;\\n    padding-top: 10px;\\n    padding-block: 20px;\\n  }\\n}\\n\\n@media (min-width: 1024px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    background-color: white;\\n    padding: 30px;\\n    border-radius: 25px;\\n    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);\\n    \\n\\n    \\n\\n    \\n\\n    border: 1px solid #fb751e;\\n    display: flex;\\n    flex-direction: column;\\n    justify-content: space-between;\\n    width: 620px;\\n    height: auto;\\n  }\\n\\n  .background-container[_ngcontent-%COMP%] {\\n    padding: 30px;\\n  }\\n\\n  .backtoselection-button[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 40px;\\n    background: none;\\n    border: 2px solid #fb751e;\\n    color: #fb751e;\\n    font-size: 16px;\\n    font-weight: bold;\\n    border-radius: 20px;\\n    cursor: pointer;\\n    position: absolute;\\n    left: 0;\\n    top: 107px;\\n    margin-left: 5%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { ClinicRegistrationComponent };", "map": {"version": 3, "names": ["User", "UserCategory", "Validators", "map", "mapTo", "of", "tap", "Clinic", "<PERSON><PERSON>", "UserTemp", "UserTempType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ClinicRegistrationComponent_div_18_small_1_Template", "ClinicRegistrationComponent_div_18_small_2_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "clinicForm", "get", "errors", "tmp_1_0", "ɵɵtextInterpolate1", "ctx_r1", "clinicNameExistsMessage", "ClinicRegistrationComponent_div_26_small_1_Template", "ctx_r2", "ClinicRegistrationComponent_div_32_small_1_Template", "ClinicRegistrationComponent_div_32_small_2_Template", "ctx_r3", "ctx_r4", "userEmailExistsMessage", "district_r21", "ClinicRegistrationComponent_div_42_small_1_Template", "ctx_r6", "city_r23", "ClinicRegistrationComponent_div_50_small_1_Template", "ctx_r8", "ClinicRegistrationComponent_div_56_small_1_Template", "ClinicRegistrationComponent_div_56_small_2_Template", "ctx_r9", "ClinicRegistrationComponent_div_61_small_1_Template", "ctx_r10", "ClinicRegistrationComponent_div_69_small_1_Template", "ClinicRegistrationComponent_div_69_small_2_Template", "ctx_r11", "ClinicRegistrationComponent_div_74_small_1_Template", "ctx_r12", "ClinicRegistrationComponent", "togglePasswordVisibility", "passwordVisible", "onDistrictChange", "event", "selectedDistrict", "target", "value", "enable", "cities", "sharedService", "getCitiesByDistrict", "console", "log", "disable", "setValue", "constructor", "fb", "router", "userService", "clinicService", "authService", "clinic", "user", "userCategory", "passwordDoNotMatch", "isEmailRegistered", "isClinicRegistered", "isCityDisabled", "userTemp", "districts", "group", "clinicName", "required", "pattern", "address", "email", "district", "city", "disabled", "tele", "<PERSON><PERSON><PERSON>", "password", "<PERSON><PERSON><PERSON><PERSON>", "rePassword", "validator", "passwordMatchValidator", "form", "dirty", "touched", "mismatch", "ngOnInit", "localStorage", "clear", "getUserCategoryById", "subscribe", "response", "getDistricts", "userTempType", "CLINIC", "updateEmail", "username", "onSubmitRegister", "invalid", "mark<PERSON>llAsTouched", "checkClinicName", "checkUserEmail", "onUserRegister", "onClinicRegister", "fire", "title", "text", "icon", "confirmButtonText", "userCategoryId", "firstName", "name", "register", "pipe", "userId", "id", "error", "saveClinic", "then", "result", "isConfirmed", "navigate", "onUserTempRegister", "registerButton", "buttonInstance", "nativeElement", "innerHTML", "saveUserTemp", "userTempSaved", "receivedUserTemp", "message", "iconName", "valid", "userEmail", "checkUser", "data", "navigateUserSelection", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "UserService", "i4", "ClinicService", "i5", "SharedService", "i6", "AuthService", "_2", "selectors", "viewQuery", "ClinicRegistrationComponent_Query", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "ClinicRegistrationComponent_Template_form_ngSubmit_12_listener", "ClinicRegistrationComponent_Template_input_ngModelChange_17_listener", "$event", "mainName", "ClinicRegistrationComponent_div_18_Template", "ClinicRegistrationComponent_small_19_Template", "ClinicRegistrationComponent_Template_textarea_ngModelChange_24_listener", "ClinicRegistrationComponent_div_26_Template", "ClinicRegistrationComponent_Template_input_ngModelChange_31_listener", "ClinicRegistrationComponent_div_32_Template", "ClinicRegistrationComponent_small_33_Template", "ClinicRegistrationComponent_Template_select_ngModelChange_38_listener", "ClinicRegistrationComponent_Template_select_change_38_listener", "ClinicRegistrationComponent_option_41_Template", "ClinicRegistrationComponent_div_42_Template", "ClinicRegistrationComponent_Template_select_ngModelChange_46_listener", "ClinicRegistrationComponent_option_49_Template", "ClinicRegistrationComponent_div_50_Template", "ClinicRegistrationComponent_Template_input_ngModelChange_55_listener", "contactNumber", "ClinicRegistrationComponent_div_56_Template", "ClinicRegistrationComponent_Template_input_ngModelChange_60_listener", "ClinicRegistrationComponent_div_61_Template", "ClinicRegistrationComponent_Template_input_ngModelChange_66_listener", "userPassword", "ClinicRegistrationComponent_Template_button_click_67_listener", "ClinicRegistrationComponent_div_69_Template", "ClinicRegistrationComponent_div_74_Template", "ClinicRegistrationComponent_small_75_Template", "tmp_2_0", "tmp_5_0", "tmp_7_0", "tmp_11_0", "tmp_14_0", "tmp_16_0", "tmp_18_0", "ɵɵpropertyInterpolate", "ɵɵpureFunction1", "_c1", "tmp_21_0", "tmp_23_0", "tmp_24_0", "tmp_25_0"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-registration\\clinic-registration.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-registration\\clinic-registration.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit,ViewChild } from '@angular/core';\r\nimport { User, UserCategory } from '../../user/user';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { UserService } from '../../user/user.service';\r\nimport { map, mapTo, Observable, of, tap } from 'rxjs';\r\nimport { SharedService } from '../../modules/shared-services/shared.service';\r\nimport { Clinic } from '../clinic';\r\nimport { ClinicService } from '../clinic.service';\r\nimport Swal from 'sweetalert2';\r\nimport { AuthService } from 'src/app/auth/auth.service';\r\nimport { UserTemp, UserTempType } from 'src/app/auth/auth';\r\nimport { PrimaryActionButtonComponent } from 'src/app/core/primary-action-button/primary-action-button.component';\r\n\r\n\r\n@Component({\r\n  selector: 'app-clinic-registration',\r\n  templateUrl: './clinic-registration.component.html',\r\n  styleUrls: ['./clinic-registration.component.css'],\r\n})\r\n\r\nexport class ClinicRegistrationComponent implements OnInit {\r\n  clinic: Clinic = new Clinic();\r\n  user: User = new User();\r\n  userCategory: UserCategory = new UserCategory();\r\n  clinicForm: FormGroup;\r\n  passwordDoNotMatch = false;\r\n  isEmailRegistered: boolean = false;\r\n  userEmailExistsMessage: string = '';\r\n  isClinicRegistered: boolean = false;\r\n  isCityDisabled: boolean = true;\r\n  clinicNameExistsMessage: string = '';\r\n\r\n  userTemp:UserTemp = new UserTemp();\r\n  @ViewChild('RegisterButton') registerButton!: PrimaryActionButtonComponent;\r\n\r\n  districts: string[] = [];\r\n  cities: String[] = [];\r\n\r\n  passwordVisible: boolean = false;\r\n\r\n  togglePasswordVisibility() {\r\n    this.passwordVisible = !this.passwordVisible;\r\n  }\r\n\r\n  onDistrictChange(event: Event): void {\r\n    const selectedDistrict = (event.target as HTMLSelectElement).value;\r\n    if (selectedDistrict) {\r\n      this.clinicForm.get('city')?.enable();\r\n      this.cities = this.sharedService.getCitiesByDistrict(selectedDistrict);\r\n      console.log(this.cities);\r\n    } else {\r\n      this.clinicForm.get('city')?.disable();\r\n      this.cities = [];\r\n    }\r\n    this.clinicForm.get('city')?.setValue('');\r\n  }\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private clinicService: ClinicService,\r\n    private sharedService: SharedService,\r\n    private authService: AuthService\r\n  ) {\r\n    this.clinicForm = this.fb.group(\r\n      {\r\n        clinicName: [\r\n          '',\r\n          [Validators.required, Validators.pattern('^[a-zA-Z0-9 .()/,]*$')],\r\n        ],\r\n        address: ['', Validators.required],\r\n        email: ['', [Validators.required, Validators.email]],\r\n        district: ['', Validators.required],\r\n        city: [{ value: '', disabled: true }, Validators.required],\r\n        tele: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],\r\n        contactPerson: ['', Validators.required],\r\n        password: [\r\n          '',\r\n          [\r\n            Validators.required,\r\n            Validators.minLength(8),\r\n            Validators.pattern('^(?=.*\\\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\\\\W).*$'),\r\n          ],\r\n        ],\r\n        rePassword: ['', Validators.required],\r\n      },\r\n      { validator: this.passwordMatchValidator }\r\n    );\r\n  }\r\n\r\n  passwordMatchValidator(form: FormGroup) {\r\n    const password = form.get('password');\r\n    const rePassword = form.get('rePassword');\r\n\r\n    if (\r\n      password?.value &&\r\n      rePassword?.value &&\r\n      (rePassword.dirty || rePassword.touched)\r\n    ) {\r\n      return password.value === rePassword.value ? null : { mismatch: true };\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    localStorage.clear();\r\n    this.userService.getUserCategoryById(3).subscribe((response) => {\r\n      this.userCategory = response;\r\n    });\r\n    this.districts = this.sharedService.getDistricts();\r\n    this.userTemp.userTempType = UserTempType.CLINIC;\r\n  }\r\n\r\n  updateEmail() {\r\n    this.user.username = this.clinic.email;\r\n  }\r\n\r\n  onSubmitRegister() {\r\n    if (this.clinicForm.invalid) {\r\n      this.clinicForm.markAllAsTouched();\r\n      return;\r\n    }\r\n    this.checkClinicName().subscribe((isClinicRegistered) => {\r\n      if (!isClinicRegistered) {\r\n        this.checkUserEmail().subscribe((isEmailRegistered) => {\r\n          if (!isEmailRegistered) {\r\n\r\n            this.onUserRegister().subscribe(() => {\r\n              this.onClinicRegister().subscribe(() => {\r\n                Swal.fire({\r\n                  title: \"Wait until approval!\",\r\n                  text: \"Thank you for registering! Your account is under review. Please wait until it’s approved to complete the login process.\",\r\n                  icon: 'success',\r\n                  confirmButtonText: 'OK',\r\n                });\r\n              });\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  onUserRegister(): Observable<void> {\r\n    this.user.userCategoryId = this.userCategory;\r\n    this.user.firstName = this.clinic.name;\r\n    return this.userService.register(this.user).pipe(\r\n      tap(\r\n        (response) => {\r\n          this.user.userId = response.id;\r\n          this.clinic.userId = this.user;\r\n        },\r\n        (error) => {\r\n          console.log(error);\r\n        }\r\n      ),\r\n      mapTo(void 0)\r\n    );\r\n  }\r\n\r\nonClinicRegister(): Observable<void> {\r\n  return this.clinicService.saveClinic(this.clinic).pipe(\r\n    tap(\r\n      () => {\r\n        Swal.fire({\r\n          title: 'Registration Successful!',\r\n          text: 'Thank you for registering! Please verify your email to complete the login process.',\r\n          icon: 'success',\r\n          confirmButtonText: 'OK',\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            this.router.navigate(['/user-login']);\r\n          }\r\n        });\r\n      },\r\n      (error) => {\r\n        console.log(error);\r\n        Swal.fire({\r\n          title: 'Error',\r\n          text: 'An error occurred during registration. Please try again later.',\r\n          icon: 'error',\r\n          confirmButtonText: 'OK'\r\n        });\r\n      }\r\n    ),\r\n    mapTo(void 0)\r\n  );\r\n}\r\n\r\n// UserTemp Saving\r\nonUserTempRegister() {\r\n\r\n  if (this.clinicForm.invalid) {\r\n    this.clinicForm.markAllAsTouched();\r\n    return;\r\n  }\r\n\r\n  // Log the userTemp object to check what's being sent\r\n  console.log('UserTemp object before sending:', this.userTemp);\r\n\r\n  // Disable the register button and show a loading indicator\r\n  this.registerButton.buttonInstance.nativeElement.disabled = true;\r\n  this.registerButton.buttonInstance.nativeElement.innerHTML = `<img src=\"/assets/icons/more-30.png\" />`;\r\n\r\n  // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times\r\n  // this.authService\r\n  //   .checkUserTempAvailability(this.userTemp.userEmail)\r\n  //   .subscribe((resp) => {\r\n\r\n  //     if (resp !=null) {\r\n  //       Swal.fire({\r\n  //         title: 'Registration Already Exists!',\r\n  //         text: 'You have already registered. Our team is processing your account, and you will receive an email once it’s ready for use.',\r\n  //         icon: 'info',\r\n  //         confirmButtonText: 'OK',\r\n  //       });\r\n\r\n  //       // Reset the button state\r\n  //       this.registerButton.buttonInstance.nativeElement.disabled = false;\r\n  //       this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\r\n  //       return;\r\n  //     }\r\n\r\n      this.authService.saveUserTemp(this.userTemp).subscribe(\r\n        (userTempSaved: UserTemp) => {\r\n          console.log('Full userTempSaved object:', userTempSaved);\r\n\r\n          const receivedUserTemp: UserTemp = userTempSaved;\r\n          let title = 'Registration Completed!';\r\n          let message = 'Thank you for registering! We’ve sent you a verification email. Please check your inbox to verify your account and complete the login process once approved.';\r\n          let iconName:\r\n            | 'success'\r\n            | 'info'\r\n            | 'error'\r\n            | 'warning'\r\n            | 'question' = 'success';\r\n\r\n          if (!receivedUserTemp) {\r\n            title = 'Registration Failed!';\r\n            message ='An error occurred while registering. Please try again.';\r\n            iconName = 'error';\r\n          }\r\n\r\n          Swal.fire({\r\n            title: title,\r\n            text: message,\r\n            icon: iconName,\r\n            confirmButtonText: 'OK',\r\n          });\r\n\r\n          // Reset button state\r\n          this.registerButton.buttonInstance.nativeElement.disabled = false;\r\n          this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\r\n        },\r\n        (error) => {\r\n          Swal.fire({\r\n            title: 'Registration Failed!',\r\n            text: 'An error occurred during registration. Please try again later.',\r\n            icon: 'error',\r\n            confirmButtonText: 'OK',\r\n          });\r\n\r\n          this.registerButton.buttonInstance.nativeElement.disabled = false;\r\n          this.registerButton.buttonInstance.nativeElement.innerHTML = 'Register';\r\n        }\r\n      );\r\n    // }); // COMMENTED OUT FOR TESTING\r\n}\r\n\r\n  checkUserEmail(): Observable<boolean> {\r\n    if (this.clinicForm.get('email')?.valid) {\r\n      const userEmail = this.clinicForm.get('email')?.value;\r\n      return this.userService.checkUser(userEmail).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isEmailRegistered = true;\r\n            this.userEmailExistsMessage =\r\n              'Email already registered. Try another.';\r\n          } else {\r\n            this.isEmailRegistered = false;\r\n            this.userEmailExistsMessage = '';\r\n          }\r\n          return this.isEmailRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isEmailRegistered = false;\r\n      this.userEmailExistsMessage = '';\r\n      return of(this.isEmailRegistered);\r\n    }\r\n  }\r\n\r\n  checkClinicName(): Observable<boolean> {\r\n    if (this.clinicForm.get('clinicName')?.valid) {\r\n      const clinicName = this.clinicForm.get('clinicName')?.value;\r\n      return this.clinicService.checkClinicName(clinicName).pipe(\r\n        map((data) => {\r\n          if (data) {\r\n            this.isClinicRegistered = true;\r\n            this.clinicNameExistsMessage = 'That name is taken. Try another.';\r\n          } else {\r\n            this.isClinicRegistered = false;\r\n            this.clinicNameExistsMessage = '';\r\n          }\r\n          return this.isClinicRegistered;\r\n        })\r\n      );\r\n    } else {\r\n      this.isClinicRegistered = false;\r\n      this.clinicNameExistsMessage = '';\r\n      return of(this.isClinicRegistered);\r\n    }\r\n  }\r\n\r\n  navigateUserSelection() {\r\n    this.router.navigate(['/user-selection']);\r\n  }\r\n}\r\n", "<div class=\"container-fluid base-background\" style=\"height: 100dvh;overflow-y: auto;\">\r\n  <div class=\"row h-100\">\r\n    <div class=\"col-12\" style=\"height: 80px !important;\">\r\n      <app-default-navbar loggedUser=\"Hello Doctor\" />\r\n    </div>\r\n    <div class=\"col-12\" style=\"height: calc(100% - 80px) !important;\">\r\n      <div class=\"row h-100\">\r\n        <div class=\"col-11 m-auto py-5\">\r\n          <div class=\"row\">\r\n            <div class=\"col-12 col-sm-10 col-lg-8 col-xl-6 col-xxl-5 mx-auto bg-white p-5 bordered-container\">\r\n              <div class=\"row\">\r\n                <h3 class=\"fs-4 m-0 header\">Clinic Registration</h3>\r\n                <form [formGroup]=\"clinicForm\" class=\"col-12 base-form\" (ngSubmit)=\"onUserTempRegister()\">\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12\">\r\n                      <label class=\"input-label\" for=\"clinic-name\">Clinic Name</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"clinic-name\"\r\n                        name=\"clinic-name\"\r\n                        formControlName=\"clinicName\"\r\n                        [(ngModel)]=\"userTemp.mainName\"\r\n                        required\r\n\r\n                      />\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('clinicName')?.invalid &&\r\n                          (clinicForm.get('clinicName')?.dirty ||\r\n                            clinicForm.get('clinicName')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('clinicName')?.errors?.['required']\"\r\n                        >\r\n                          Clinic Name is required.\r\n                        </small>\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('clinicName')?.errors?.['pattern']\"\r\n                          >Clinic Name can only contain letters, numbers, spaces, and \".()/,\".</small\r\n                        >\r\n                      </div>\r\n                      <small *ngIf=\"isClinicRegistered\" class=\"text-danger\">\r\n                        {{ clinicNameExistsMessage }}\r\n                      </small>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12\">\r\n                      <label class=\"input-label\" for=\"address\">Address</label>\r\n                      <textarea\r\n                        type=\"text\"\r\n                        id=\"address\"\r\n                        name=\"address\"\r\n                        formControlName=\"address\"\r\n                        [(ngModel)]=\"userTemp.address\"\r\n                        rows=\"1\">\r\n                      </textarea>\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('address')?.invalid &&\r\n                          (clinicForm.get('address')?.dirty ||\r\n                            clinicForm.get('address')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('address')?.errors?.['required']\"\r\n                        >\r\n                          Address is required.\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12\">\r\n                      <label class=\"input-label\" for=\"email-address\">Email Address</label>\r\n                      <input\r\n                        type=\"email\"\r\n                        id=\"email-address\"\r\n                        name=\"email-address\"\r\n                        formControlName=\"email\"\r\n                        [(ngModel)]=\"userTemp.userEmail\"\r\n                        (ngModelChange)=\"updateEmail()\"\r\n                      />\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('email')?.invalid &&\r\n                          (clinicForm.get('email')?.dirty ||\r\n                            clinicForm.get('email')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('email')?.errors?.['required']\"\r\n                        >\r\n                          Email is required.\r\n                        </small>\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('email')?.errors?.['email']\"\r\n                        >\r\n                          Invalid email format.\r\n                        </small>\r\n                      </div>\r\n                      <small *ngIf=\"isEmailRegistered\" class=\"text-danger\">\r\n                        {{ userEmailExistsMessage }}\r\n                      </small>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12 col-xl-6 d-grid\">\r\n                      <label class=\"input-label\" for=\"district\">District</label>\r\n                      <select\r\n                        name=\"district\"\r\n                        id=\"district\"\r\n                        formControlName=\"district\"\r\n                        [(ngModel)]=\"userTemp.district\"\r\n                        (change)=\"onDistrictChange($event)\"\r\n                        class=\"\"\r\n                      >\r\n                        <option value=\"\" disabled selected>Select</option>\r\n                        <option *ngFor=\"let district of districts\" [value]=\"district\">\r\n                          {{ district }}\r\n                        </option>\r\n                      </select>\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('district')?.invalid &&\r\n                          (clinicForm.get('district')?.dirty ||\r\n                            clinicForm.get('district')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('district')?.errors?.['required']\"\r\n                        >\r\n                          District is required.\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"col-12 col-xl-6 d-grid mt-3 mt-xl-0\">\r\n                      <label class=\"input-label\" for=\"city\">City</label>\r\n                      <select\r\n                        name=\"city\"\r\n                        id=\"city\"\r\n                        formControlName=\"city\"\r\n                        [(ngModel)]=\"userTemp.city\"\r\n                      >\r\n                        <option value=\"\" disabled selected>Select District</option>\r\n                        <option *ngFor=\"let city of cities\" [value]=\"city\">\r\n                          {{ city }}\r\n                        </option>\r\n                      </select>\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('city')?.invalid &&\r\n                          (clinicForm.get('city')?.dirty || clinicForm.get('city')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('city')?.errors?.['required']\"\r\n                        >\r\n                          City is required.\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12 col-xl-6\">\r\n                      <label for=\"contact-number\" class=\"input-label\">Contact Number</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"contact-number\"\r\n                        name=\"contact-number\"\r\n                        formControlName=\"tele\"\r\n                        [(ngModel)]=\"userTemp.contactNumber\"\r\n                      />\r\n                      <div\r\n                      class=\"px-1\"\r\n                      style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('tele')?.invalid &&\r\n                          (clinicForm.get('tele')?.dirty || clinicForm.get('tele')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('tele')?.errors?.['required']\"\r\n                        >\r\n                          Contact Number is required.\r\n                        </small>\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('tele')?.errors?.['pattern']\"\r\n                        >\r\n                          Invalid Contact number.\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"col-12 col-xl-6 mt-3 mt-xl-0\">\r\n                      <label for=\"contact-person\" class=\"input-label\">Contact Person</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        id=\"contact-person\"\r\n                        name=\"contact-person\"\r\n                        formControlName=\"contactPerson\"\r\n                        [(ngModel)]=\"userTemp.contactPerson\"\r\n                      />\r\n                      <div class=\"px-1\"\r\n                      style=\"font-weight: 500;\" *ngIf=\"clinicForm.get('contactPerson')?.invalid && (clinicForm.get('contactPerson')?.dirty || clinicForm.get('contactPerson')?.touched)\">\r\n                        <small class=\"text-danger\" *ngIf=\"clinicForm.get('contactPerson')?.errors?.['required']\">Contact Person is required.</small>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div class=\"row\">\r\n                    <div class=\"col-12 col-xl-6 position-relative\">\r\n                      <label for=\"password\" class=\"input-label\">Password</label>\r\n                      <input\r\n                        type=\"{{ passwordVisible ? 'text' : 'password' }}\"\r\n                        id=\"password\"\r\n                        name=\"password\"\r\n                        formControlName=\"password\"\r\n                        [(ngModel)]=\"userTemp.userPassword\"\r\n                        class=\"form-control\"\r\n                        [ngClass]=\"{\r\n                  'is-invalid': clinicForm.get('password')?.invalid &&\r\n                  (clinicForm.get('password')?.dirty || clinicForm.get('password')?.touched)\r\n                }\"\r\n                      />\r\n\r\n                      <!-- Password visibility toggle button -->\r\n                      <button\r\n                        type=\"button\"\r\n                        class=\"show-password\"\r\n                        (click)=\"togglePasswordVisibility()\"\r\n                        style=\"\r\n                              position: absolute;\r\n                              right: 25px;\r\n                              top: 48px;\r\n                              transform: translateY(-50%);\r\n                              padding: 0;\r\n                              border: none;\r\n                              background: none;\r\n                              color: #6c757d;\r\n                              cursor: pointer;\r\n                              \"\r\n                      >\r\n                        <i [ngClass]=\"passwordVisible ? 'bi bi-eye-fill' : 'bi bi-eye-slash-fill'\"></i>\r\n                      </button>\r\n\r\n                      <!-- Error messages for validation -->\r\n                      <div class=\"px-1\" style=\"font-weight: 500;\" *ngIf=\"clinicForm.get('password')?.invalid && (clinicForm.get('password')?.dirty || clinicForm.get('password')?.touched)\">\r\n                        <small class=\"text-danger\" *ngIf=\"clinicForm.get('password')?.errors?.['required']\">\r\n                          Password is required.\r\n                        </small>\r\n                        <small class=\"text-danger\" *ngIf=\"clinicForm.get('password')?.errors?.['minlength'] || clinicForm.get('password')?.errors?.['pattern']\">\r\n                          Password must be at least 8 characters, including an uppercase letter, lowercase letter, digit, and special character.\r\n                        </small>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div class=\"col-12 col-xl-6 mt-3 mt-xl-0\">\r\n                      <label for=\"repassword\" class=\"input-label\">Re-enter Password</label>\r\n                      <input\r\n                        type=\"password\"\r\n                        id=\"repassword\"\r\n                        name=\"repassword\"\r\n                        formControlName=\"rePassword\"\r\n                      />\r\n                      <div\r\n                      class=\"px-1\" style=\"font-weight: 500;\"\r\n                        *ngIf=\"\r\n                          clinicForm.get('rePassword')?.invalid &&\r\n                          (clinicForm.get('rePassword')?.dirty ||\r\n                            clinicForm.get('rePassword')?.touched)\r\n                        \"\r\n                      >\r\n                        <small\r\n                          class=\"text-danger\"\r\n                          *ngIf=\"clinicForm.get('rePassword')?.errors?.['required']\"\r\n                        >\r\n                          Please re-enter the password.\r\n                        </small>\r\n                      </div>\r\n                      <small\r\n                        class=\"text-danger\"\r\n                        *ngIf=\"clinicForm.errors?.['mismatch'] && clinicForm.get('rePassword')?.dirty\"\r\n                        >Password do not match.</small\r\n                      >\r\n                    </div>\r\n                  </div>\r\n\r\n                  <app-primary-action-button buttonType=\"submit\" class=\"d-grid mt-4\" [buttonHeight]=\"45\" buttonUI=\"primary\" buttonText=\"Register\" #RegisterButton />\r\n                  <button type=\"submit\" #RegisterButton class=\"register-button d-none\">Register</button>\r\n                </form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AACA,SAASA,IAAI,EAAEC,YAAY,QAAQ,iBAAiB;AACpD,SAAiCC,UAAU,QAAQ,gBAAgB;AAGnE,SAASC,GAAG,EAAEC,KAAK,EAAcC,EAAE,EAAEC,GAAG,QAAQ,MAAM;AAEtD,SAASC,MAAM,QAAQ,WAAW;AAElC,OAAOC,IAAI,MAAM,aAAa;AAE9B,SAASC,QAAQ,EAAEC,YAAY,QAAQ,mBAAmB;;;;;;;;;;;;;;ICsBlCC,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,4EAAmE;IAAAF,EAAA,CAAAG,YAAA,EACrE;;;;;IAlBHH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAC,mDAAA,oBAKQ;IACRL,EAAA,CAAAI,UAAA,IAAAE,mDAAA,oBAIC;IACHN,EAAA,CAAAG,YAAA,EAAM;;;;;;IATDH,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IAMxDb,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,UAAA,CAAAC,GAAA,iCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAAuD;;;;;IAI5Db,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAC,MAAA,CAAAC,uBAAA,MACF;;;;;IAuBEjB,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAbVH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAc,mDAAA,oBAKQ;IACVlB,EAAA,CAAAG,YAAA,EAAM;;;;;IAJDH,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAU,MAAA,CAAAR,UAAA,CAAAC,GAAA,8BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;;;;;IA2BxDb,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAnBVH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAgB,mDAAA,oBAKQ;IACRpB,EAAA,CAAAI,UAAA,IAAAiB,mDAAA,oBAKQ;IACVrB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAVDH,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAa,MAAA,CAAAX,UAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAmD;IAMnDb,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAQ,MAAA,CAAAX,UAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAgD;;;;;IAKrDb,EAAA,CAAAC,cAAA,gBAAqD;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IADNH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAQ,MAAA,CAAAC,sBAAA,MACF;;;;;IAgBExB,EAAA,CAAAC,cAAA,iBAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFkCH,EAAA,CAAAQ,UAAA,UAAAiB,YAAA,CAAkB;IAC3DzB,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAU,YAAA,MACF;;;;;IAUAzB,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAbVH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAsB,mDAAA,oBAKQ;IACV1B,EAAA,CAAAG,YAAA,EAAM;;;;;IAJDH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAkB,MAAA,CAAAhB,UAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAsD;;;;;IAezDb,EAAA,CAAAC,cAAA,iBAAmD;IACjDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF2BH,EAAA,CAAAQ,UAAA,UAAAoB,QAAA,CAAc;IAChD5B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAe,kBAAA,MAAAa,QAAA,MACF;;;;;IASA5B,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAZVH,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAI,UAAA,IAAAyB,mDAAA,oBAKQ;IACV7B,EAAA,CAAAG,YAAA,EAAM;;;;;IAJDH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAqB,MAAA,CAAAnB,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkD;;;;;IA0BrDb,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAnBVH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAA2B,mDAAA,oBAKQ;IACR/B,EAAA,CAAAI,UAAA,IAAA4B,mDAAA,oBAKQ;IACVhC,EAAA,CAAAG,YAAA,EAAM;;;;;;IAVDH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAAwB,MAAA,CAAAtB,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAkD;IAMlDb,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,UAAA,UAAAM,OAAA,GAAAmB,MAAA,CAAAtB,UAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,YAAiD;;;;;IAiBpDb,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAF9HH,EAAA,CAAAC,cAAA,cACmK;IACjKD,EAAA,CAAAI,UAAA,IAAA8B,mDAAA,oBAA4H;IAC9HlC,EAAA,CAAAG,YAAA,EAAM;;;;;IADwBH,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAA0B,OAAA,CAAAxB,UAAA,CAAAC,GAAA,oCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA2D;;;;;IA2CvFb,EAAA,CAAAC,cAAA,gBAAoF;IAClFD,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IACRH,EAAA,CAAAC,cAAA,gBAAwI;IACtID,EAAA,CAAAE,MAAA,+HACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IANVH,EAAA,CAAAC,cAAA,cAAsK;IACpKD,EAAA,CAAAI,UAAA,IAAAgC,mDAAA,oBAEQ;IACRpC,EAAA,CAAAI,UAAA,IAAAiC,mDAAA,oBAEQ;IACVrC,EAAA,CAAAG,YAAA,EAAM;;;;;;IANwBH,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAA6B,OAAA,CAAA3B,UAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAsD;IAGtDb,EAAA,CAAAO,SAAA,GAA0G;IAA1GP,EAAA,CAAAQ,UAAA,WAAAM,OAAA,GAAAwB,OAAA,CAAA3B,UAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,oBAAAC,OAAA,GAAAwB,OAAA,CAAA3B,UAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,aAA0G;;;;;IAsBtIb,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAbVH,EAAA,CAAAC,cAAA,cAOC;IACCD,EAAA,CAAAI,UAAA,IAAAmC,mDAAA,oBAKQ;IACVvC,EAAA,CAAAG,YAAA,EAAM;;;;;IAJDH,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAQ,UAAA,UAAAC,OAAA,GAAA+B,OAAA,CAAA7B,UAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;;;;;IAK7Db,EAAA,CAAAC,cAAA,gBAGG;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EACxB;;;;;;;;AD/RvB,MAMasC,2BAA2B;EAoBtCC,wBAAwBA,CAAA;IACtB,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAC,gBAAgBA,CAACC,KAAY;IAC3B,MAAMC,gBAAgB,GAAID,KAAK,CAACE,MAA4B,CAACC,KAAK;IAClE,IAAIF,gBAAgB,EAAE;MACpB,IAAI,CAACnC,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEqC,MAAM,EAAE;MACrC,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACC,mBAAmB,CAACN,gBAAgB,CAAC;MACtEO,OAAO,CAACC,GAAG,CAAC,IAAI,CAACJ,MAAM,CAAC;KACzB,MAAM;MACL,IAAI,CAACvC,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE2C,OAAO,EAAE;MACtC,IAAI,CAACL,MAAM,GAAG,EAAE;;IAElB,IAAI,CAACvC,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE4C,QAAQ,CAAC,EAAE,CAAC;EAC3C;EAEAC,YACUC,EAAe,EACfC,MAAc,EACdC,WAAwB,EACxBC,aAA4B,EAC5BV,aAA4B,EAC5BW,WAAwB;IALxB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAV,aAAa,GAAbA,aAAa;IACb,KAAAW,WAAW,GAAXA,WAAW;IA1CrB,KAAAC,MAAM,GAAW,IAAInE,MAAM,EAAE;IAC7B,KAAAoE,IAAI,GAAS,IAAI3E,IAAI,EAAE;IACvB,KAAA4E,YAAY,GAAiB,IAAI3E,YAAY,EAAE;IAE/C,KAAA4E,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAA3C,sBAAsB,GAAW,EAAE;IACnC,KAAA4C,kBAAkB,GAAY,KAAK;IACnC,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAApD,uBAAuB,GAAW,EAAE;IAEpC,KAAAqD,QAAQ,GAAY,IAAIxE,QAAQ,EAAE;IAGlC,KAAAyE,SAAS,GAAa,EAAE;IACxB,KAAArB,MAAM,GAAa,EAAE;IAErB,KAAAP,eAAe,GAAY,KAAK;IA2B9B,IAAI,CAAChC,UAAU,GAAG,IAAI,CAAC+C,EAAE,CAACc,KAAK,CAC7B;MACEC,UAAU,EAAE,CACV,EAAE,EACF,CAAClF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAClE;MACDC,OAAO,EAAE,CAAC,EAAE,EAAErF,UAAU,CAACmF,QAAQ,CAAC;MAClCG,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtF,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACsF,KAAK,CAAC,CAAC;MACpDC,QAAQ,EAAE,CAAC,EAAE,EAAEvF,UAAU,CAACmF,QAAQ,CAAC;MACnCK,IAAI,EAAE,CAAC;QAAE/B,KAAK,EAAE,EAAE;QAAEgC,QAAQ,EAAE;MAAI,CAAE,EAAEzF,UAAU,CAACmF,QAAQ,CAAC;MAC1DO,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC1F,UAAU,CAACmF,QAAQ,EAAEnF,UAAU,CAACoF,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MACpEO,aAAa,EAAE,CAAC,EAAE,EAAE3F,UAAU,CAACmF,QAAQ,CAAC;MACxCS,QAAQ,EAAE,CACR,EAAE,EACF,CACE5F,UAAU,CAACmF,QAAQ,EACnBnF,UAAU,CAAC6F,SAAS,CAAC,CAAC,CAAC,EACvB7F,UAAU,CAACoF,OAAO,CAAC,8CAA8C,CAAC,CACnE,CACF;MACDU,UAAU,EAAE,CAAC,EAAE,EAAE9F,UAAU,CAACmF,QAAQ;KACrC,EACD;MAAEY,SAAS,EAAE,IAAI,CAACC;IAAsB,CAAE,CAC3C;EACH;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAML,QAAQ,GAAGK,IAAI,CAAC5E,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMyE,UAAU,GAAGG,IAAI,CAAC5E,GAAG,CAAC,YAAY,CAAC;IAEzC,IACEuE,QAAQ,EAAEnC,KAAK,IACfqC,UAAU,EAAErC,KAAK,KAChBqC,UAAU,CAACI,KAAK,IAAIJ,UAAU,CAACK,OAAO,CAAC,EACxC;MACA,OAAOP,QAAQ,CAACnC,KAAK,KAAKqC,UAAU,CAACrC,KAAK,GAAG,IAAI,GAAG;QAAE2C,QAAQ,EAAE;MAAI,CAAE;;IAGxE,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACNC,YAAY,CAACC,KAAK,EAAE;IACpB,IAAI,CAAClC,WAAW,CAACmC,mBAAmB,CAAC,CAAC,CAAC,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC7D,IAAI,CAAChC,YAAY,GAAGgC,QAAQ;IAC9B,CAAC,CAAC;IACF,IAAI,CAAC1B,SAAS,GAAG,IAAI,CAACpB,aAAa,CAAC+C,YAAY,EAAE;IAClD,IAAI,CAAC5B,QAAQ,CAAC6B,YAAY,GAAGpG,YAAY,CAACqG,MAAM;EAClD;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACrC,IAAI,CAACsC,QAAQ,GAAG,IAAI,CAACvC,MAAM,CAACc,KAAK;EACxC;EAEA0B,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC5F,UAAU,CAAC6F,OAAO,EAAE;MAC3B,IAAI,CAAC7F,UAAU,CAAC8F,gBAAgB,EAAE;MAClC;;IAEF,IAAI,CAACC,eAAe,EAAE,CAACV,SAAS,CAAE5B,kBAAkB,IAAI;MACtD,IAAI,CAACA,kBAAkB,EAAE;QACvB,IAAI,CAACuC,cAAc,EAAE,CAACX,SAAS,CAAE7B,iBAAiB,IAAI;UACpD,IAAI,CAACA,iBAAiB,EAAE;YAEtB,IAAI,CAACyC,cAAc,EAAE,CAACZ,SAAS,CAAC,MAAK;cACnC,IAAI,CAACa,gBAAgB,EAAE,CAACb,SAAS,CAAC,MAAK;gBACrCnG,IAAI,CAACiH,IAAI,CAAC;kBACRC,KAAK,EAAE,sBAAsB;kBAC7BC,IAAI,EAAE,yHAAyH;kBAC/HC,IAAI,EAAE,SAAS;kBACfC,iBAAiB,EAAE;iBACpB,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,CAAC;;QAEN,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAN,cAAcA,CAAA;IACZ,IAAI,CAAC5C,IAAI,CAACmD,cAAc,GAAG,IAAI,CAAClD,YAAY;IAC5C,IAAI,CAACD,IAAI,CAACoD,SAAS,GAAG,IAAI,CAACrD,MAAM,CAACsD,IAAI;IACtC,OAAO,IAAI,CAACzD,WAAW,CAAC0D,QAAQ,CAAC,IAAI,CAACtD,IAAI,CAAC,CAACuD,IAAI,CAC9C5H,GAAG,CACAsG,QAAQ,IAAI;MACX,IAAI,CAACjC,IAAI,CAACwD,MAAM,GAAGvB,QAAQ,CAACwB,EAAE;MAC9B,IAAI,CAAC1D,MAAM,CAACyD,MAAM,GAAG,IAAI,CAACxD,IAAI;IAChC,CAAC,EACA0D,KAAK,IAAI;MACRrE,OAAO,CAACC,GAAG,CAACoE,KAAK,CAAC;IACpB,CAAC,CACF,EACDjI,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAEFoH,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAChD,aAAa,CAAC8D,UAAU,CAAC,IAAI,CAAC5D,MAAM,CAAC,CAACwD,IAAI,CACpD5H,GAAG,CACD,MAAK;MACHE,IAAI,CAACiH,IAAI,CAAC;QACRC,KAAK,EAAE,0BAA0B;QACjCC,IAAI,EAAE,oFAAoF;QAC1FC,IAAI,EAAE,SAAS;QACfC,iBAAiB,EAAE;OACpB,CAAC,CAACU,IAAI,CAAEC,MAAM,IAAI;QACjB,IAAIA,MAAM,CAACC,WAAW,EAAE;UACtB,IAAI,CAACnE,MAAM,CAACoE,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;;MAEzC,CAAC,CAAC;IACJ,CAAC,EACAL,KAAK,IAAI;MACRrE,OAAO,CAACC,GAAG,CAACoE,KAAK,CAAC;MAClB7H,IAAI,CAACiH,IAAI,CAAC;QACRC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE,gEAAgE;QACtEC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;IACJ,CAAC,CACF,EACDzH,KAAK,CAAC,KAAK,CAAC,CAAC,CACd;EACH;EAEA;EACAuI,kBAAkBA,CAAA;IAEhB,IAAI,IAAI,CAACrH,UAAU,CAAC6F,OAAO,EAAE;MAC3B,IAAI,CAAC7F,UAAU,CAAC8F,gBAAgB,EAAE;MAClC;;IAGF;IACApD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAACgB,QAAQ,CAAC;IAE7D;IACA,IAAI,CAAC2D,cAAc,CAACC,cAAc,CAACC,aAAa,CAACnD,QAAQ,GAAG,IAAI;IAChE,IAAI,CAACiD,cAAc,CAACC,cAAc,CAACC,aAAa,CAACC,SAAS,GAAG,yCAAyC;IAEtG;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEI,IAAI,CAACtE,WAAW,CAACuE,YAAY,CAAC,IAAI,CAAC/D,QAAQ,CAAC,CAAC0B,SAAS,CACnDsC,aAAuB,IAAI;MAC1BjF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgF,aAAa,CAAC;MAExD,MAAMC,gBAAgB,GAAaD,aAAa;MAChD,IAAIvB,KAAK,GAAG,yBAAyB;MACrC,IAAIyB,OAAO,GAAG,8JAA8J;MAC5K,IAAIC,QAAQ,GAKK,SAAS;MAE1B,IAAI,CAACF,gBAAgB,EAAE;QACrBxB,KAAK,GAAG,sBAAsB;QAC9ByB,OAAO,GAAE,wDAAwD;QACjEC,QAAQ,GAAG,OAAO;;MAGpB5I,IAAI,CAACiH,IAAI,CAAC;QACRC,KAAK,EAAEA,KAAK;QACZC,IAAI,EAAEwB,OAAO;QACbvB,IAAI,EAAEwB,QAAQ;QACdvB,iBAAiB,EAAE;OACpB,CAAC;MAEF;MACA,IAAI,CAACe,cAAc,CAACC,cAAc,CAACC,aAAa,CAACnD,QAAQ,GAAG,KAAK;MACjE,IAAI,CAACiD,cAAc,CAACC,cAAc,CAACC,aAAa,CAACC,SAAS,GAAG,UAAU;IACzE,CAAC,EACAV,KAAK,IAAI;MACR7H,IAAI,CAACiH,IAAI,CAAC;QACRC,KAAK,EAAE,sBAAsB;QAC7BC,IAAI,EAAE,gEAAgE;QACtEC,IAAI,EAAE,OAAO;QACbC,iBAAiB,EAAE;OACpB,CAAC;MAEF,IAAI,CAACe,cAAc,CAACC,cAAc,CAACC,aAAa,CAACnD,QAAQ,GAAG,KAAK;MACjE,IAAI,CAACiD,cAAc,CAACC,cAAc,CAACC,aAAa,CAACC,SAAS,GAAG,UAAU;IACzE,CAAC,CACF;IACH;EACJ;;EAEEzB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChG,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAE8H,KAAK,EAAE;MACvC,MAAMC,SAAS,GAAG,IAAI,CAAChI,UAAU,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEoC,KAAK;MACrD,OAAO,IAAI,CAACY,WAAW,CAACgF,SAAS,CAACD,SAAS,CAAC,CAACpB,IAAI,CAC/C/H,GAAG,CAAEqJ,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAAC1E,iBAAiB,GAAG,IAAI;UAC7B,IAAI,CAAC3C,sBAAsB,GACzB,wCAAwC;SAC3C,MAAM;UACL,IAAI,CAAC2C,iBAAiB,GAAG,KAAK;UAC9B,IAAI,CAAC3C,sBAAsB,GAAG,EAAE;;QAElC,OAAO,IAAI,CAAC2C,iBAAiB;MAC/B,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAAC3C,sBAAsB,GAAG,EAAE;MAChC,OAAO9B,EAAE,CAAC,IAAI,CAACyE,iBAAiB,CAAC;;EAErC;EAEAuC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC/F,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE8H,KAAK,EAAE;MAC5C,MAAMjE,UAAU,GAAG,IAAI,CAAC9D,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEoC,KAAK;MAC3D,OAAO,IAAI,CAACa,aAAa,CAAC6C,eAAe,CAACjC,UAAU,CAAC,CAAC8C,IAAI,CACxD/H,GAAG,CAAEqJ,IAAI,IAAI;QACX,IAAIA,IAAI,EAAE;UACR,IAAI,CAACzE,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAACnD,uBAAuB,GAAG,kCAAkC;SAClE,MAAM;UACL,IAAI,CAACmD,kBAAkB,GAAG,KAAK;UAC/B,IAAI,CAACnD,uBAAuB,GAAG,EAAE;;QAEnC,OAAO,IAAI,CAACmD,kBAAkB;MAChC,CAAC,CAAC,CACH;KACF,MAAM;MACL,IAAI,CAACA,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnD,uBAAuB,GAAG,EAAE;MACjC,OAAOvB,EAAE,CAAC,IAAI,CAAC0E,kBAAkB,CAAC;;EAEtC;EAEA0E,qBAAqBA,CAAA;IACnB,IAAI,CAACnF,MAAM,CAACoE,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;EAC3C;EAAC,QAAAgB,CAAA,G;qBA1SUtG,2BAA2B,EAAAzC,EAAA,CAAAgJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlJ,EAAA,CAAAgJ,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApJ,EAAA,CAAAgJ,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtJ,EAAA,CAAAgJ,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAxJ,EAAA,CAAAgJ,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAA1J,EAAA,CAAAgJ,iBAAA,CAAAW,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BpH,2BAA2B;IAAAqH,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCrBxCjK,EAAA,CAAAC,cAAA,aAAsF;QAGhFD,EAAA,CAAAmK,SAAA,4BAAgD;QAClDnK,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAkE;QAM1BD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpDH,EAAA,CAAAC,cAAA,eAA0F;QAAlCD,EAAA,CAAAoK,UAAA,sBAAAC,+DAAA;UAAA,OAAYH,GAAA,CAAAlC,kBAAA,EAAoB;QAAA,EAAC;QACvFhI,EAAA,CAAAC,cAAA,cAAiB;QAEgCD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChEH,EAAA,CAAAC,cAAA,iBAQE;QAHAD,EAAA,CAAAoK,UAAA,2BAAAE,qEAAAC,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAkG,QAAA,GAAAD,MAAA;QAAA,EAA+B;QALjCvK,EAAA,CAAAG,YAAA,EAQE;QACFH,EAAA,CAAAI,UAAA,KAAAqK,2CAAA,kBAmBM;QACNzK,EAAA,CAAAI,UAAA,KAAAsK,6CAAA,oBAEQ;QACV1K,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAE4BD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACxDH,EAAA,CAAAC,cAAA,oBAMW;QADTD,EAAA,CAAAoK,UAAA,2BAAAO,wEAAAJ,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAM,OAAA,GAAA2F,MAAA;QAAA,EAA8B;QAEhCvK,EAAA,CAAAE,MAAA;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACXH,EAAA,CAAAI,UAAA,KAAAwK,2CAAA,kBAcM;QACR5K,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAEkCD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACpEH,EAAA,CAAAC,cAAA,iBAOE;QAFAD,EAAA,CAAAoK,UAAA,2BAAAS,qEAAAN,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,GAAA4B,MAAA;QAAA,EAAgC,2BAAAM,qEAAA;UAAA,OACfX,GAAA,CAAA7D,WAAA,EAAa;QAAA,EADE;QALlCrG,EAAA,CAAAG,YAAA,EAOE;QACFH,EAAA,CAAAI,UAAA,KAAA0K,2CAAA,kBAoBM;QACN9K,EAAA,CAAAI,UAAA,KAAA2K,6CAAA,oBAEQ;QACV/K,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAE6BD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC1DH,EAAA,CAAAC,cAAA,kBAOC;QAHCD,EAAA,CAAAoK,UAAA,2BAAAY,sEAAAT,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAQ,QAAA,GAAAyF,MAAA;QAAA,EAA+B,oBAAAU,+DAAAV,MAAA;UAAA,OACrBL,GAAA,CAAAtH,gBAAA,CAAA2H,MAAA,CAAwB;QAAA,EADH;QAI/BvK,EAAA,CAAAC,cAAA,kBAAmC;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAClDH,EAAA,CAAAI,UAAA,KAAA8K,8CAAA,qBAES;QACXlL,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAI,UAAA,KAAA+K,2CAAA,kBAcM;QACRnL,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAAiD;QACTD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAClDH,EAAA,CAAAC,cAAA,kBAKC;QADCD,EAAA,CAAAoK,UAAA,2BAAAgB,sEAAAb,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAS,IAAA,GAAAwF,MAAA;QAAA,EAA2B;QAE3BvK,EAAA,CAAAC,cAAA,kBAAmC;QAAAD,EAAA,CAAAE,MAAA,uBAAe;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC3DH,EAAA,CAAAI,UAAA,KAAAiL,8CAAA,qBAES;QACXrL,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAI,UAAA,KAAAkL,2CAAA,kBAaM;QACRtL,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAEmCD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtEH,EAAA,CAAAC,cAAA,iBAME;QADAD,EAAA,CAAAoK,UAAA,2BAAAmB,qEAAAhB,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAkH,aAAA,GAAAjB,MAAA;QAAA,EAAoC;QALtCvK,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAI,UAAA,KAAAqL,2CAAA,kBAoBM;QACRzL,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAA0C;QACQD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACtEH,EAAA,CAAAC,cAAA,iBAME;QADAD,EAAA,CAAAoK,UAAA,2BAAAsB,qEAAAnB,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAY,aAAA,GAAAqF,MAAA;QAAA,EAAoC;QALtCvK,EAAA,CAAAG,YAAA,EAME;QACFH,EAAA,CAAAI,UAAA,KAAAuL,2CAAA,kBAGM;QACR3L,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAC,cAAA,cAAiB;QAE6BD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC1DH,EAAA,CAAAC,cAAA,iBAWE;QANAD,EAAA,CAAAoK,UAAA,2BAAAwB,qEAAArB,MAAA;UAAA,OAAAL,GAAA,CAAA5F,QAAA,CAAAuH,YAAA,GAAAtB,MAAA;QAAA,EAAmC;QALrCvK,EAAA,CAAAG,YAAA,EAWE;QAGFH,EAAA,CAAAC,cAAA,kBAeC;QAZCD,EAAA,CAAAoK,UAAA,mBAAA0B,8DAAA;UAAA,OAAS5B,GAAA,CAAAxH,wBAAA,EAA0B;QAAA,EAAC;QAapC1C,EAAA,CAAAmK,SAAA,aAA+E;QACjFnK,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAI,UAAA,KAAA2L,2CAAA,kBAOM;QACR/L,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAA0C;QACID,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACrEH,EAAA,CAAAmK,SAAA,iBAKE;QACFnK,EAAA,CAAAI,UAAA,KAAA4L,2CAAA,kBAcM;QACNhM,EAAA,CAAAI,UAAA,KAAA6L,6CAAA,oBAIC;QACHjM,EAAA,CAAAG,YAAA,EAAM;QAGRH,EAAA,CAAAmK,SAAA,yCAAkJ;QAClJnK,EAAA,CAAAC,cAAA,sBAAqE;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;;;;QAvSlFH,EAAA,CAAAO,SAAA,IAAwB;QAAxBP,EAAA,CAAAQ,UAAA,cAAA0J,GAAA,CAAAvJ,UAAA,CAAwB;QAStBX,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAkG,QAAA,CAA+B;QAM9BxK,EAAA,CAAAO,SAAA,GAIF;QAJEP,EAAA,CAAAQ,UAAA,WAAA0L,OAAA,GAAAhC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAsL,OAAA,CAAA1F,OAAA,QAAA0F,OAAA,GAAAhC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAsL,OAAA,CAAAzG,KAAA,OAAAyG,OAAA,GAAAhC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAsL,OAAA,CAAAxG,OAAA,GAIF;QAcO1F,EAAA,CAAAO,SAAA,GAAwB;QAAxBP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAA9F,kBAAA,CAAwB;QAc9BpE,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAM,OAAA,CAA8B;QAK7B5E,EAAA,CAAAO,SAAA,GAIF;QAJEP,EAAA,CAAAQ,UAAA,WAAA2L,OAAA,GAAAjC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,8BAAAuL,OAAA,CAAA3F,OAAA,QAAA2F,OAAA,GAAAjC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,8BAAAuL,OAAA,CAAA1G,KAAA,OAAA0G,OAAA,GAAAjC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,8BAAAuL,OAAA,CAAAzG,OAAA,GAIF;QAoBC1F,EAAA,CAAAO,SAAA,GAAgC;QAAhCP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAqE,SAAA,CAAgC;QAK/B3I,EAAA,CAAAO,SAAA,GAIF;QAJEP,EAAA,CAAAQ,UAAA,WAAA4L,OAAA,GAAAlC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,4BAAAwL,OAAA,CAAA5F,OAAA,QAAA4F,OAAA,GAAAlC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,4BAAAwL,OAAA,CAAA3G,KAAA,OAAA2G,OAAA,GAAAlC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,4BAAAwL,OAAA,CAAA1G,OAAA,GAIF;QAeO1F,EAAA,CAAAO,SAAA,GAAuB;QAAvBP,EAAA,CAAAQ,UAAA,SAAA0J,GAAA,CAAA/F,iBAAA,CAAuB;QAa7BnE,EAAA,CAAAO,SAAA,GAA+B;QAA/BP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAQ,QAAA,CAA+B;QAKF9E,EAAA,CAAAO,SAAA,GAAY;QAAZP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA3F,SAAA,CAAY;QAMxCvE,EAAA,CAAAO,SAAA,GAIF;QAJEP,EAAA,CAAAQ,UAAA,WAAA6L,QAAA,GAAAnC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAyL,QAAA,CAAA7F,OAAA,QAAA6F,QAAA,GAAAnC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAyL,QAAA,CAAA5G,KAAA,OAAA4G,QAAA,GAAAnC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAyL,QAAA,CAAA3G,OAAA,GAIF;QAgBC1F,EAAA,CAAAO,SAAA,GAA2B;QAA3BP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAS,IAAA,CAA2B;QAGF/E,EAAA,CAAAO,SAAA,GAAS;QAATP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAAhH,MAAA,CAAS;QAMjClD,EAAA,CAAAO,SAAA,GAGD;QAHCP,EAAA,CAAAQ,UAAA,WAAA8L,QAAA,GAAApC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA0L,QAAA,CAAA9F,OAAA,QAAA8F,QAAA,GAAApC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA0L,QAAA,CAAA7G,KAAA,OAAA6G,QAAA,GAAApC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA0L,QAAA,CAAA5G,OAAA,GAGD;QAoBA1F,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAkH,aAAA,CAAoC;QAKnCxL,EAAA,CAAAO,SAAA,GAGD;QAHCP,EAAA,CAAAQ,UAAA,WAAA+L,QAAA,GAAArC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA2L,QAAA,CAAA/F,OAAA,QAAA+F,QAAA,GAAArC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA2L,QAAA,CAAA9G,KAAA,OAAA8G,QAAA,GAAArC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,2BAAA2L,QAAA,CAAA7G,OAAA,GAGD;QAuBA1F,EAAA,CAAAO,SAAA,GAAoC;QAApCP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAY,aAAA,CAAoC;QAGXlF,EAAA,CAAAO,SAAA,GAAsI;QAAtIP,EAAA,CAAAQ,UAAA,WAAAgM,QAAA,GAAAtC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,oCAAA4L,QAAA,CAAAhG,OAAA,QAAAgG,QAAA,GAAAtC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,oCAAA4L,QAAA,CAAA/G,KAAA,OAAA+G,QAAA,GAAAtC,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,oCAAA4L,QAAA,CAAA9G,OAAA,GAAsI;QAU/J1F,EAAA,CAAAO,SAAA,GAAkD;QAAlDP,EAAA,CAAAyM,qBAAA,SAAAvC,GAAA,CAAAvH,eAAA,uBAAkD;QAIlD3C,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAA5F,QAAA,CAAAuH,YAAA,CAAmC,YAAA7L,EAAA,CAAA0M,eAAA,KAAAC,GAAA,IAAAC,QAAA,GAAA1C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAgM,QAAA,CAAApG,OAAA,QAAAoG,QAAA,GAAA1C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAgM,QAAA,CAAAnH,KAAA,OAAAmH,QAAA,GAAA1C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAgM,QAAA,CAAAlH,OAAA;QAyBhC1F,EAAA,CAAAO,SAAA,GAAuE;QAAvEP,EAAA,CAAAQ,UAAA,YAAA0J,GAAA,CAAAvH,eAAA,6CAAuE;QAI/B3C,EAAA,CAAAO,SAAA,GAAuH;QAAvHP,EAAA,CAAAQ,UAAA,WAAAqM,QAAA,GAAA3C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAiM,QAAA,CAAArG,OAAA,QAAAqG,QAAA,GAAA3C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAiM,QAAA,CAAApH,KAAA,OAAAoH,QAAA,GAAA3C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,+BAAAiM,QAAA,CAAAnH,OAAA,GAAuH;QAoBjK1F,EAAA,CAAAO,SAAA,GAIF;QAJEP,EAAA,CAAAQ,UAAA,WAAAsM,QAAA,GAAA5C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAkM,QAAA,CAAAtG,OAAA,QAAAsG,QAAA,GAAA5C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAkM,QAAA,CAAArH,KAAA,OAAAqH,QAAA,GAAA5C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAkM,QAAA,CAAApH,OAAA,GAIF;QAWE1F,EAAA,CAAAO,SAAA,GAA4E;QAA5EP,EAAA,CAAAQ,UAAA,UAAA0J,GAAA,CAAAvJ,UAAA,CAAAE,MAAA,kBAAAqJ,GAAA,CAAAvJ,UAAA,CAAAE,MAAA,mBAAAkM,QAAA,GAAA7C,GAAA,CAAAvJ,UAAA,CAAAC,GAAA,iCAAAmM,QAAA,CAAAtH,KAAA,EAA4E;QAMhBzF,EAAA,CAAAO,SAAA,GAAmB;QAAnBP,EAAA,CAAAQ,UAAA,oBAAmB;;;;;;;SD7R3FiC,2BAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}