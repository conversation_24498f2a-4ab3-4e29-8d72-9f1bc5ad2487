{"ast": null, "code": "import { Company } from \"./company\";\nexport class User {\n  constructor() {\n    this.userId = 0;\n    this.firstName = '';\n    this.lastName = '';\n    this.password = '';\n    this.username = '';\n    this.email = '';\n    this.userType = '';\n    this.companyId = new Company();\n    this.userCategoryId = new UserCategory();\n    this.userVerified = UserStatus.INACTIVE;\n  }\n}\nexport class UserCategory {\n  constructor() {\n    this.userCategoryId = 0;\n    this.userCategory = '';\n    this.routerPath = '';\n  }\n}\nexport var UserStatus;\n(function (UserStatus) {\n  UserStatus[UserStatus[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  UserStatus[UserStatus[\"INACTIVE\"] = 1] = \"INACTIVE\";\n  UserStatus[UserStatus[\"OTHER\"] = 2] = \"OTHER\";\n})(UserStatus || (UserStatus = {}));\nexport class SignUpDto {\n  constructor() {\n    this.firstName = '';\n    this.lastName = '';\n    this.password = '';\n    this.username = '';\n    this.userCategoryId = new UserCategory();\n  }\n}", "map": {"version": 3, "names": ["Company", "User", "constructor", "userId", "firstName", "lastName", "password", "username", "email", "userType", "companyId", "userCategoryId", "UserCategory", "userVerified", "UserStatus", "INACTIVE", "userCategory", "routerPath", "SignUpDto"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\user\\user.ts"], "sourcesContent": ["import { Company } from \"./company\";\r\n\r\nexport class User {\r\n  userId: number = 0;\r\n  firstName: string = '';\r\n  lastName: string = '';\r\n  password: string = '';\r\n  username: string = '';\r\n  email: string = '';\r\n  userType: string = '';\r\n  companyId: Company = new Company();\r\n  userCategoryId: UserCategory = new UserCategory();\r\n  userVerified:UserStatus = UserStatus.INACTIVE;\r\n}\r\n\r\nexport class UserCategory {\r\n  userCategoryId: number = 0;\r\n  userCategory: string = '';\r\n  routerPath: string = '';\r\n}\r\n\r\nexport enum UserStatus{\r\n ACTIVE,INACTIVE,OTHER\r\n}\r\n\r\nexport class SignUpDto{\r\n  firstName: string = '';\r\n  lastName: string = '';\r\n  password: string = '';\r\n  username: string = '';\r\n  userCategoryId: UserCategory = new UserCategory();\r\n}\r\n\r\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AAEnC,OAAM,MAAOC,IAAI;EAAjBC,YAAA;IACE,KAAAC,MAAM,GAAW,CAAC;IAClB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAY,IAAIV,OAAO,EAAE;IAClC,KAAAW,cAAc,GAAiB,IAAIC,YAAY,EAAE;IACjD,KAAAC,YAAY,GAAcC,UAAU,CAACC,QAAQ;EAC/C;;AAEA,OAAM,MAAOH,YAAY;EAAzBV,YAAA;IACE,KAAAS,cAAc,GAAW,CAAC;IAC1B,KAAAK,YAAY,GAAW,EAAE;IACzB,KAAAC,UAAU,GAAW,EAAE;EACzB;;AAEA,WAAYH,UAEX;AAFD,WAAYA,UAAU;EACrBA,UAAA,CAAAA,UAAA,0BAAM;EAACA,UAAA,CAAAA,UAAA,8BAAQ;EAACA,UAAA,CAAAA,UAAA,wBAAK;AACtB,CAAC,EAFWA,UAAU,KAAVA,UAAU;AAItB,OAAM,MAAOI,SAAS;EAAtBhB,YAAA;IACE,KAAAE,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAI,cAAc,GAAiB,IAAIC,YAAY,EAAE;EACnD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}