package com.navitsa.mydent.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.navitsa.mydent.entity.Supplier;
import com.navitsa.mydent.services.SupplierService;

@RestController
public class SupplierController {
	    private final SupplierService supplierService;
	 
	    @Autowired
	    public SupplierController(SupplierService supplierService) {
	        this.supplierService = supplierService;
	    }

	    @PostMapping("/saveSupplier")
	    public Supplier saveSupplier(@RequestBody Supplier supplier) {
	        return supplierService.saveSupplier(supplier);
	    }

	    @GetMapping("/supplierList")
	    public List<Supplier> getAllSuppliers() {
	        return supplierService.findAllSuppliers();
	    }

	    @GetMapping("/getSupplierById/{id}")
	    public Supplier getSupplierById(@PathVariable int id) {
	        return supplierService.getSupplierById(id);
	    }

	    @PutMapping("/updateSupplier/{id}")
	    public ResponseEntity<Supplier> updateSupplier(@PathVariable int id, @RequestBody Supplier supplierDetails) {
	        return ResponseEntity.ok(supplierService.updateSupplier(id, supplierDetails));
	    }

	    @DeleteMapping("/deleteSupplier/{id}")
	    public ResponseEntity<Void> deleteSupplier(@PathVariable int id) {
	        supplierService.deleteSupplier(id);
	        return ResponseEntity.noContent().build();
	    }
	    
	    @GetMapping("/check-Supplier")
	    public ResponseEntity<Boolean> supplierNameExists(@RequestParam String supplierName){
	        boolean isSupplierNameExists = supplierService.supplierNameExists(supplierName);
	        return ResponseEntity.ok(isSupplierNameExists);
	    }
	 
	    
	    @GetMapping("/getSupplierByUserId/{userId}")
	    public ResponseEntity<Supplier> getSupplierByUserId(@PathVariable int userId) {
	        try {
	        	Supplier supplier = supplierService.getSupplierByUserId(userId);
	            return ResponseEntity.ok(supplier);
	        } catch (RuntimeException ex) {
	            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
	        } catch (Exception ex) {
	            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
	        }
	    }
}
