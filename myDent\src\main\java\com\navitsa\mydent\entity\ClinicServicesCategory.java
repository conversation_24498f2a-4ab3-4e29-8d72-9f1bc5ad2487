
package com.navitsa.mydent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "ClinicServices_category")
public class ClinicServicesCategory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "clinicServicecategory_id")
    private Integer clinicServiceCategoryId;

    @Column(name = "clinicServicecategory_name")
    private String clinicServiceCategoryName;


    public ClinicServicesCategory() {
    }

    public ClinicServicesCategory(Integer clinicServiceCategoryId, String clinicServiceCategoryName) {
    	super();
        this.clinicServiceCategoryId = clinicServiceCategoryId;
        this.clinicServiceCategoryName = clinicServiceCategoryName;
    }


    public Integer getClinicServiceCategoryId() {
        return clinicServiceCategoryId;
    }

    public void setClinicServiceCategoryId(Integer clinicServiceCategoryId) {
        this.clinicServiceCategoryId = clinicServiceCategoryId;
    }

    public String getClinicServiceCategoryName() {
        return clinicServiceCategoryName;
    }

    public void setClinicServiceCategoryName(String clinicServiceCategoryName) {
        this.clinicServiceCategoryName = clinicServiceCategoryName;
    }
}