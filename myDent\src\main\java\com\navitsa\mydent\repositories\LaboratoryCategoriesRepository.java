package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.LaboratoryCategories;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LaboratoryCategoriesRepository extends JpaRepository<LaboratoryCategories, Integer> {
	
	LaboratoryCategories findByLaboratoryCategoryName(String laboratoryCategoryName);
	
	LaboratoryCategories findByLaboratoryCategoryId(int laboratoryCategoryId);
	
	LaboratoryCategories findById(int laboratoryCategoryId);
}


