{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Directive, Input, Output, ViewChild, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport * as i1 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatRipple, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate'\n  };\n}\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\n// Boilerplate for applying mixins to MatCheckbox.\n/** @docs-private */\nconst _MatCheckboxMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(mixinDisabled(class {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n}))));\nclass _MatCheckboxBase extends _MatCheckboxMixinBase {\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  /** Whether the checkbox is required. */\n  get required() {\n    return this._required;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  constructor(idPrefix, elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n    super(elementRef);\n    this._changeDetectorRef = _changeDetectorRef;\n    this._ngZone = _ngZone;\n    this._animationMode = _animationMode;\n    this._options = _options;\n    /**\n     * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n     * take precedence so this may be omitted.\n     */\n    this.ariaLabel = '';\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    this.ariaLabelledby = null;\n    /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n    this.labelPosition = 'after';\n    /** Name value will be applied to the input element if present */\n    this.name = null;\n    /** Event emitted when the checkbox's `checked` value changes. */\n    this.change = new EventEmitter();\n    /** Event emitted when the checkbox's `indeterminate` value changes. */\n    this.indeterminateChange = new EventEmitter();\n    /**\n     * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n     * @docs-private\n     */\n    this._onTouched = () => {};\n    this._currentAnimationClass = '';\n    this._currentCheckState = 0 /* TransitionCheckState.Init */;\n    this._controlValueAccessorChangeFn = () => {};\n    this._checked = false;\n    this._disabled = false;\n    this._indeterminate = false;\n    this._options = this._options || defaults;\n    this.color = this.defaultColor = this._options.color || defaults.color;\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = `${idPrefix}${++nextUniqueId}`;\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    const checked = coerceBooleanProperty(value);\n    if (checked != this.checked) {\n      this._checked = checked;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Whether the checkbox is disabled. This fully overrides the implementation provided by\n   * mixinDisabled, but the mixin is still required because mixinTabIndex requires it.\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n    if (newValue !== this.disabled) {\n      this._disabled = newValue;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate;\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = coerceBooleanProperty(value);\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(3 /* TransitionCheckState.Indeterminate */);\n      } else {\n        this._transitionCheckState(this.checked ? 1 /* TransitionCheckState.Checked */ : 2 /* TransitionCheckState.Unchecked */);\n      }\n\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n    this._syncIndeterminate(this._indeterminate);\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? 1 /* TransitionCheckState.Checked */ : 2 /* TransitionCheckState.Unchecked */);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (!this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n    switch (oldState) {\n      case 0 /* TransitionCheckState.Init */:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === 1 /* TransitionCheckState.Checked */) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == 3 /* TransitionCheckState.Indeterminate */) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case 2 /* TransitionCheckState.Unchecked */:\n        return newState === 1 /* TransitionCheckState.Checked */ ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case 1 /* TransitionCheckState.Checked */:\n        return newState === 2 /* TransitionCheckState.Unchecked */ ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case 3 /* TransitionCheckState.Indeterminate */:\n        return newState === 1 /* TransitionCheckState.Checked */ ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  static #_ = this.ɵfac = function _MatCheckboxBase_Factory(t) {\n    i0.ɵɵinvalidFactory();\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: _MatCheckboxBase,\n    viewQuery: function _MatCheckboxBase_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(MatRipple, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n      }\n    },\n    inputs: {\n      ariaLabel: [\"aria-label\", \"ariaLabel\"],\n      ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"],\n      ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"],\n      id: \"id\",\n      required: \"required\",\n      labelPosition: \"labelPosition\",\n      name: \"name\",\n      value: \"value\",\n      checked: \"checked\",\n      disabled: \"disabled\",\n      indeterminate: \"indeterminate\"\n    },\n    outputs: {\n      change: \"change\",\n      indeterminateChange: \"indeterminateChange\"\n    },\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxBase, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: undefined\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }, {\n      type: undefined\n    }];\n  }, {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }],\n    checked: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    indeterminate: [{\n      type: Input\n    }]\n  });\n})();\nclass MatCheckbox extends _MatCheckboxBase {\n  constructor(elementRef, changeDetectorRef, ngZone, tabIndex, animationMode, options) {\n    super('mat-mdc-checkbox-', elementRef, changeDetectorRef, ngZone, tabIndex, animationMode, options);\n    this._animationClasses = {\n      uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n      uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n      checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n      checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n      indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n      indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n    };\n  }\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  _onInputClick() {\n    super._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    super._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static #_ = this.ɵfac = function MatCheckbox_Factory(t) {\n    return new (t || MatCheckbox)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_CHECKBOX_DEFAULT_OPTIONS, 8));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatCheckbox,\n    selectors: [[\"mat-checkbox\"]],\n    hostAttrs: [1, \"mat-mdc-checkbox\"],\n    hostVars: 12,\n    hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n        i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked);\n      }\n    },\n    inputs: {\n      disableRipple: \"disableRipple\",\n      color: \"color\",\n      tabIndex: \"tabIndex\"\n    },\n    exportAs: [\"matCheckbox\"],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c2,\n    decls: 15,\n    vars: 20,\n    consts: [[1, \"mdc-form-field\", 3, \"click\"], [1, \"mdc-checkbox\"], [\"checkbox\", \"\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\", \"blur\", \"click\", \"change\"], [\"input\", \"\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"], [\"label\", \"\"]],\n    template: function MatCheckbox_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n          return ctx._preventBubblingFromLabel($event);\n        });\n        i0.ɵɵelementStart(1, \"div\", 1, 2)(3, \"div\", 3);\n        i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n          return ctx._onTouchTargetClick();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"input\", 4, 5);\n        i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n          return ctx._onBlur();\n        })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n          return ctx._onInputClick();\n        })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n          return ctx._onInteractionEvent($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(6, \"div\", 6);\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(8, \"svg\", 8);\n        i0.ɵɵelement(9, \"path\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵnamespaceHTML();\n        i0.ɵɵelement(10, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(11, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"label\", 12, 13);\n        i0.ɵɵprojection(14);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(2);\n        i0.ɵɵclassProp(\"mdc-form-field--align-end\", ctx.labelPosition == \"before\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n        i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.tabIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"name\", ctx.name)(\"value\", ctx.value);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"matRippleTrigger\", _r0)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"for\", ctx.inputId);\n      }\n    },\n    dependencies: [i1.MatRipple],\n    styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}html{--mdc-checkbox-disabled-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-state-layer-opacity:0.16;--mdc-checkbox-selected-hover-state-layer-opacity:0.04;--mdc-checkbox-selected-pressed-state-layer-opacity:0.16;--mdc-checkbox-unselected-focus-state-layer-opacity:0.16;--mdc-checkbox-unselected-hover-state-layer-opacity:0.04;--mdc-checkbox-unselected-pressed-state-layer-opacity:0.16}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked'\n      },\n      providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR],\n      inputs: ['disableRipple', 'color', 'tabIndex'],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\"\\n     (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}html{--mdc-checkbox-disabled-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-state-layer-opacity:0.16;--mdc-checkbox-selected-hover-state-layer-opacity:0.04;--mdc-checkbox-selected-pressed-state-layer-opacity:0.16;--mdc-checkbox-unselected-focus-state-layer-opacity:0.16;--mdc-checkbox-unselected-hover-state-layer-opacity:0.04;--mdc-checkbox-unselected-pressed-state-layer-opacity:0.16}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['tabindex']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ANIMATION_MODULE_TYPE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [MAT_CHECKBOX_DEFAULT_OPTIONS]\n      }]\n    }];\n  }, null);\n})();\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n  static #_ = this.ɵfac = /* @__PURE__ */function () {\n    let ɵMatCheckboxRequiredValidator_BaseFactory;\n    return function MatCheckboxRequiredValidator_Factory(t) {\n      return (ɵMatCheckboxRequiredValidator_BaseFactory || (ɵMatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatCheckboxRequiredValidator)))(t || MatCheckboxRequiredValidator);\n    };\n  }();\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatCheckboxRequiredValidator,\n    selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n    features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n      providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR]\n    }]\n  }], null, null);\n})();\n\n/** This module is used by both original and MDC-based checkbox implementations. */\nclass _MatCheckboxRequiredValidatorModule {\n  static #_ = this.ɵfac = function _MatCheckboxRequiredValidatorModule_Factory(t) {\n    return new (t || _MatCheckboxRequiredValidatorModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: _MatCheckboxRequiredValidatorModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      exports: [MatCheckboxRequiredValidator],\n      declarations: [MatCheckboxRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatCheckboxModule {\n  static #_ = this.ɵfac = function MatCheckboxModule_Factory(t) {\n    return new (t || MatCheckboxModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatCheckboxModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatRippleModule, _MatCheckboxRequiredValidatorModule, MatCommonModule, _MatCheckboxRequiredValidatorModule]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, _MatCheckboxRequiredValidatorModule],\n      exports: [MatCheckbox, MatCommonModule, _MatCheckboxRequiredValidatorModule],\n      declarations: [MatCheckbox]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, _MatCheckboxBase, _MatCheckboxRequiredValidatorModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "EventEmitter", "Directive", "Input", "Output", "ViewChild", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Optional", "Inject", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "i1", "mixinTabIndex", "mixinColor", "mixinDisableRipple", "mixinDisabled", "<PERSON><PERSON><PERSON><PERSON>", "MatCommonModule", "MatRippleModule", "ANIMATION_MODULE_TYPE", "coerceBooleanProperty", "_c0", "_c1", "_c2", "MAT_CHECKBOX_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY", "color", "clickAction", "MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatCheckbox", "multi", "MatCheckboxChange", "nextUniqueId", "defaults", "_MatCheckboxMixinBase", "constructor", "_elementRef", "_MatCheckboxBase", "inputId", "id", "_uniqueId", "required", "_required", "value", "idPrefix", "elementRef", "_changeDetectorRef", "_ngZone", "tabIndex", "_animationMode", "_options", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelPosition", "name", "change", "indeterminateChange", "_onTouched", "_currentAnimationClass", "_currentCheckState", "_controlValueAccessorChangeFn", "_checked", "_disabled", "_indeterminate", "defaultColor", "parseInt", "ngAfterViewInit", "_syncIndeterminate", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "newValue", "indeterminate", "changed", "_transitionCheckState", "emit", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "_onLabelTextChange", "detectChanges", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "newState", "oldState", "element", "_getAnimationTargetElement", "classList", "remove", "_getAnimationClassForCheckStateTransition", "length", "add", "animationClass", "runOutsideAngular", "setTimeout", "_emitChangeEvent", "_createChangeEvent", "_inputElement", "nativeElement", "toggle", "_handleInputClick", "Promise", "resolve", "then", "_onInteractionEvent", "event", "stopPropagation", "_onBlur", "_animationClasses", "uncheckedToChecked", "checkedToIndeterminate", "uncheckedToIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "indeterminateToChecked", "indeterminate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeCheckbox", "_", "ɵfac", "_MatCheckboxBase_Factory", "t", "ɵɵinvalidFactory", "_2", "ɵdir", "ɵɵdefineDirective", "type", "viewQuery", "_MatCheckboxBase_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "_labelElement", "ripple", "inputs", "aria<PERSON><PERSON><PERSON><PERSON>", "outputs", "features", "ɵɵInheritDefinitionFeature", "ngDevMode", "ɵsetClassMetadata", "undefined", "ElementRef", "ChangeDetectorRef", "NgZone", "args", "changeDetectorRef", "ngZone", "animationMode", "options", "focus", "isChecked", "source", "_onInputClick", "_onTouchTargetClick", "_preventBubblingFromLabel", "target", "contains", "MatCheckbox_Factory", "ɵɵdirectiveInject", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatCheckbox_HostBindings", "ɵɵhostProperty", "ɵɵattribute", "ɵɵclassProp", "exportAs", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatCheckbox_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵlistener", "MatCheckbox_Template_div_click_0_listener", "$event", "MatCheckbox_Template_div_click_3_listener", "ɵɵelementEnd", "MatCheckbox_Template_input_blur_4_listener", "MatCheckbox_Template_input_click_4_listener", "MatCheckbox_Template_input_change_4_listener", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵprojection", "_r0", "ɵɵreference", "ɵɵadvance", "ɵɵproperty", "dependencies", "styles", "encapsulation", "changeDetection", "selector", "host", "providers", "None", "OnPush", "decorators", "MAT_CHECKBOX_REQUIRED_VALIDATOR", "MatCheckboxRequiredValidator", "ɵMatCheckboxRequiredValidator_BaseFactory", "MatCheckboxRequiredValidator_Factory", "ɵɵgetInheritedFactory", "_MatCheckboxRequiredValidatorModule", "_MatCheckboxRequiredValidatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "exports", "declarations", "MatCheckboxModule", "MatCheckboxModule_Factory", "imports"], "sources": ["E:/Github/MYDent-FrontEnd/node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, Directive, Input, Output, ViewChild, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport * as i1 from '@angular/material/core';\nimport { mixinTabIndex, mixinColor, mixinDisableRipple, mixinDisabled, MatRipple, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        clickAction: 'check-indeterminate',\n    };\n}\n\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatCheckbox),\n    multi: true,\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\n// Boilerplate for applying mixins to MatCheckbox.\n/** @docs-private */\nconst _MatCheckboxMixinBase = mixinTabIndex(mixinColor(mixinDisableRipple(mixinDisabled(class {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n}))));\nclass _MatCheckboxBase extends _MatCheckboxMixinBase {\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    /** Whether the checkbox is required. */\n    get required() {\n        return this._required;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    constructor(idPrefix, elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n        super(elementRef);\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._animationMode = _animationMode;\n        this._options = _options;\n        /**\n         * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n         * take precedence so this may be omitted.\n         */\n        this.ariaLabel = '';\n        /**\n         * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n         */\n        this.ariaLabelledby = null;\n        /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n        this.labelPosition = 'after';\n        /** Name value will be applied to the input element if present */\n        this.name = null;\n        /** Event emitted when the checkbox's `checked` value changes. */\n        this.change = new EventEmitter();\n        /** Event emitted when the checkbox's `indeterminate` value changes. */\n        this.indeterminateChange = new EventEmitter();\n        /**\n         * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n         * @docs-private\n         */\n        this._onTouched = () => { };\n        this._currentAnimationClass = '';\n        this._currentCheckState = 0 /* TransitionCheckState.Init */;\n        this._controlValueAccessorChangeFn = () => { };\n        this._checked = false;\n        this._disabled = false;\n        this._indeterminate = false;\n        this._options = this._options || defaults;\n        this.color = this.defaultColor = this._options.color || defaults.color;\n        this.tabIndex = parseInt(tabIndex) || 0;\n        this.id = this._uniqueId = `${idPrefix}${++nextUniqueId}`;\n    }\n    ngAfterViewInit() {\n        this._syncIndeterminate(this._indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        const checked = coerceBooleanProperty(value);\n        if (checked != this.checked) {\n            this._checked = checked;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Whether the checkbox is disabled. This fully overrides the implementation provided by\n     * mixinDisabled, but the mixin is still required because mixinTabIndex requires it.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this.disabled) {\n            this._disabled = newValue;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n        return this._indeterminate;\n    }\n    set indeterminate(value) {\n        const changed = value != this._indeterminate;\n        this._indeterminate = coerceBooleanProperty(value);\n        if (changed) {\n            if (this._indeterminate) {\n                this._transitionCheckState(3 /* TransitionCheckState.Indeterminate */);\n            }\n            else {\n                this._transitionCheckState(this.checked ? 1 /* TransitionCheckState.Checked */ : 2 /* TransitionCheckState.Unchecked */);\n            }\n            this.indeterminateChange.emit(this._indeterminate);\n        }\n        this._syncIndeterminate(this._indeterminate);\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n        // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n        // component will be only marked for check, but no actual change detection runs automatically.\n        // Instead of going back into the zone in order to trigger a change detection which causes\n        // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n        // an explicit change detection for the checkbox view and its children.\n        this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    _transitionCheckState(newState) {\n        let oldState = this._currentCheckState;\n        let element = this._getAnimationTargetElement();\n        if (oldState === newState || !element) {\n            return;\n        }\n        if (this._currentAnimationClass) {\n            element.classList.remove(this._currentAnimationClass);\n        }\n        this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n        this._currentCheckState = newState;\n        if (this._currentAnimationClass.length > 0) {\n            element.classList.add(this._currentAnimationClass);\n            // Remove the animation class to avoid animation when the checkbox is moved between containers\n            const animationClass = this._currentAnimationClass;\n            this._ngZone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    element.classList.remove(animationClass);\n                }, 1000);\n            });\n        }\n    }\n    _emitChangeEvent() {\n        this._controlValueAccessorChangeFn(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n        // Assigning the value again here is redundant, but we have to do it in case it was\n        // changed inside the `change` listener which will cause the input to be out of sync.\n        if (this._inputElement) {\n            this._inputElement.nativeElement.checked = this.checked;\n        }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n        this.checked = !this.checked;\n        this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n        const clickAction = this._options?.clickAction;\n        // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n        if (!this.disabled && clickAction !== 'noop') {\n            // When user manually click on the checkbox, `indeterminate` is set to false.\n            if (this.indeterminate && clickAction !== 'check') {\n                Promise.resolve().then(() => {\n                    this._indeterminate = false;\n                    this.indeterminateChange.emit(this._indeterminate);\n                });\n            }\n            this._checked = !this._checked;\n            this._transitionCheckState(this._checked ? 1 /* TransitionCheckState.Checked */ : 2 /* TransitionCheckState.Unchecked */);\n            // Emit our custom change event if the native input emitted one.\n            // It is important to only emit it, if the native input triggered one, because\n            // we don't want to trigger a change event, when the `checked` variable changes for example.\n            this._emitChangeEvent();\n        }\n        else if (!this.disabled && clickAction === 'noop') {\n            // Reset native input when clicked with noop. The native checkbox becomes checked after\n            // click, reset it to be align with `checked` value of `mat-checkbox`.\n            this._inputElement.nativeElement.checked = this.checked;\n            this._inputElement.nativeElement.indeterminate = this.indeterminate;\n        }\n    }\n    _onInteractionEvent(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n    }\n    _onBlur() {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state change\n        // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n        // Don't transition if animations are disabled.\n        if (this._animationMode === 'NoopAnimations') {\n            return '';\n        }\n        switch (oldState) {\n            case 0 /* TransitionCheckState.Init */:\n                // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n                // [checked] bound to it.\n                if (newState === 1 /* TransitionCheckState.Checked */) {\n                    return this._animationClasses.uncheckedToChecked;\n                }\n                else if (newState == 3 /* TransitionCheckState.Indeterminate */) {\n                    return this._checked\n                        ? this._animationClasses.checkedToIndeterminate\n                        : this._animationClasses.uncheckedToIndeterminate;\n                }\n                break;\n            case 2 /* TransitionCheckState.Unchecked */:\n                return newState === 1 /* TransitionCheckState.Checked */\n                    ? this._animationClasses.uncheckedToChecked\n                    : this._animationClasses.uncheckedToIndeterminate;\n            case 1 /* TransitionCheckState.Checked */:\n                return newState === 2 /* TransitionCheckState.Unchecked */\n                    ? this._animationClasses.checkedToUnchecked\n                    : this._animationClasses.checkedToIndeterminate;\n            case 3 /* TransitionCheckState.Indeterminate */:\n                return newState === 1 /* TransitionCheckState.Checked */\n                    ? this._animationClasses.indeterminateToChecked\n                    : this._animationClasses.indeterminateToUnchecked;\n        }\n        return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n        const nativeCheckbox = this._inputElement;\n        if (nativeCheckbox) {\n            nativeCheckbox.nativeElement.indeterminate = value;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxBase, deps: \"invalid\", target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: _MatCheckboxBase, inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], id: \"id\", required: \"required\", labelPosition: \"labelPosition\", name: \"name\", value: \"value\", checked: \"checked\", disabled: \"disabled\", indeterminate: \"indeterminate\" }, outputs: { change: \"change\", indeterminateChange: \"indeterminateChange\" }, viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_labelElement\", first: true, predicate: [\"label\"], descendants: true }, { propertyName: \"ripple\", first: true, predicate: MatRipple, descendants: true }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxBase, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: undefined }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined }, { type: undefined }, { type: undefined }]; }, propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], id: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], indeterminateChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _labelElement: [{\n                type: ViewChild,\n                args: ['label']\n            }], ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }], checked: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], indeterminate: [{\n                type: Input\n            }] } });\nclass MatCheckbox extends _MatCheckboxBase {\n    constructor(elementRef, changeDetectorRef, ngZone, tabIndex, animationMode, options) {\n        super('mat-mdc-checkbox-', elementRef, changeDetectorRef, ngZone, tabIndex, animationMode, options);\n        this._animationClasses = {\n            uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n            uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n            checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n            checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n            indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n            indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n        };\n    }\n    /** Focuses the checkbox. */\n    focus() {\n        this._inputElement.nativeElement.focus();\n    }\n    _createChangeEvent(isChecked) {\n        const event = new MatCheckboxChange();\n        event.source = this;\n        event.checked = isChecked;\n        return event;\n    }\n    _getAnimationTargetElement() {\n        return this._inputElement?.nativeElement;\n    }\n    _onInputClick() {\n        super._handleInputClick();\n    }\n    _onTouchTargetClick() {\n        super._handleInputClick();\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n        if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n            event.stopPropagation();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckbox, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: 'tabindex', attribute: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_CHECKBOX_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCheckbox, selector: \"mat-checkbox\", inputs: { disableRipple: \"disableRipple\", color: \"color\", tabIndex: \"tabIndex\" }, host: { properties: { \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"class._mat-animation-noopable\": \"_animationMode === 'NoopAnimations'\", \"class.mdc-checkbox--disabled\": \"disabled\", \"id\": \"id\", \"class.mat-mdc-checkbox-disabled\": \"disabled\", \"class.mat-mdc-checkbox-checked\": \"checked\" }, classAttribute: \"mat-mdc-checkbox\" }, providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR], exportAs: [\"matCheckbox\"], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\"\\n     (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}html{--mdc-checkbox-disabled-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-state-layer-opacity:0.16;--mdc-checkbox-selected-hover-state-layer-opacity:0.04;--mdc-checkbox-selected-pressed-state-layer-opacity:0.16;--mdc-checkbox-unselected-focus-state-layer-opacity:0.16;--mdc-checkbox-unselected-hover-state-layer-opacity:0.04;--mdc-checkbox-unselected-pressed-state-layer-opacity:0.16}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: i1.MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-checkbox', host: {\n                        'class': 'mat-mdc-checkbox',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n                        '[class.mdc-checkbox--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Add classes that users can use to more easily target disabled or checked checkboxes.\n                        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n                        '[class.mat-mdc-checkbox-checked]': 'checked',\n                    }, providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR], inputs: ['disableRipple', 'color', 'tabIndex'], exportAs: 'matCheckbox', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, template: \"<div class=\\\"mdc-form-field\\\"\\n     [class.mdc-form-field--align-end]=\\\"labelPosition == 'before'\\\"\\n     (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}html{--mdc-checkbox-disabled-selected-checkmark-color:#fff;--mdc-checkbox-selected-focus-state-layer-opacity:0.16;--mdc-checkbox-selected-hover-state-layer-opacity:0.04;--mdc-checkbox-selected-pressed-state-layer-opacity:0.16;--mdc-checkbox-unselected-focus-state-layer-opacity:0.16;--mdc-checkbox-unselected-hover-state-layer-opacity:0.04;--mdc-checkbox-unselected-pressed-state-layer-opacity:0.16}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_CHECKBOX_DEFAULT_OPTIONS]\n                }] }]; } });\n\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: MatCheckboxRequiredValidator, selector: \"mat-checkbox[required][formControlName],\\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]\", providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n                    providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n                }]\n        }] });\n\n/** This module is used by both original and MDC-based checkbox implementations. */\nclass _MatCheckboxRequiredValidatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, declarations: [MatCheckboxRequiredValidator], exports: [MatCheckboxRequiredValidator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [MatCheckboxRequiredValidator],\n                    declarations: [MatCheckboxRequiredValidator],\n                }]\n        }] });\nclass MatCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxModule, declarations: [MatCheckbox], imports: [MatCommonModule, MatRippleModule, _MatCheckboxRequiredValidatorModule], exports: [MatCheckbox, MatCommonModule, _MatCheckboxRequiredValidatorModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxModule, imports: [MatCommonModule, MatRippleModule, _MatCheckboxRequiredValidatorModule, MatCommonModule, _MatCheckboxRequiredValidatorModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: MatCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, _MatCheckboxRequiredValidatorModule],\n                    exports: [MatCheckbox, MatCommonModule, _MatCheckboxRequiredValidatorModule],\n                    declarations: [MatCheckbox],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, _MatCheckboxBase, _MatCheckboxRequiredValidatorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC3M,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5F,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,aAAa,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAClJ,SAASC,qBAAqB,QAAQ,sCAAsC;AAC5E,SAASC,qBAAqB,QAAQ,uBAAuB;;AAE7D;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,4BAA4B,GAAG,IAAI9B,cAAc,CAAC,8BAA8B,EAAE;EACpF+B,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACjB,CAAC;AACL;AAEA,MAAMC,mCAAmC,GAAG;EACxCC,OAAO,EAAEvB,iBAAiB;EAC1BwB,WAAW,EAAErC,UAAU,CAAC,MAAMsC,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,iBAAiB,CAAC;AAExB;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,QAAQ,GAAGV,oCAAoC,CAAC,CAAC;AACvD;AACA;AACA,MAAMW,qBAAqB,GAAG1B,aAAa,CAACC,UAAU,CAACC,kBAAkB,CAACC,aAAa,CAAC,MAAM;EAC1FwB,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;AACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AACL,MAAMC,gBAAgB,SAASH,qBAAqB,CAAC;EACjD;EACA,IAAII,OAAOA,CAAA,EAAG;IACV,OAAQ,GAAE,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAU,QAAO;EAC/C;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAG1B,qBAAqB,CAAC2B,KAAK,CAAC;EACjD;EACAR,WAAWA,CAACS,QAAQ,EAAEC,UAAU,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC/F,KAAK,CAACL,UAAU,CAAC;IACjB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;AACR;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,MAAM,GAAG,IAAI/D,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACgE,mBAAmB,GAAG,IAAIhE,YAAY,CAAC,CAAC;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACiE,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIjB,QAAQ;IACzC,IAAI,CAACT,KAAK,GAAG,IAAI,CAACwC,YAAY,GAAG,IAAI,CAACd,QAAQ,CAAC1B,KAAK,IAAIS,QAAQ,CAACT,KAAK;IACtE,IAAI,CAACwB,QAAQ,GAAGiB,QAAQ,CAACjB,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI,CAACT,EAAE,GAAG,IAAI,CAACC,SAAS,GAAI,GAAEI,QAAS,GAAE,EAAEZ,YAAa,EAAC;EAC7D;EACAkC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACJ,cAAc,CAAC;EAChD;EACA;EACA,IAAIK,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,QAAQ;EACxB;EACA,IAAIO,OAAOA,CAACzB,KAAK,EAAE;IACf,MAAMyB,OAAO,GAAGpD,qBAAqB,CAAC2B,KAAK,CAAC;IAC5C,IAAIyB,OAAO,IAAI,IAAI,CAACA,OAAO,EAAE;MACzB,IAAI,CAACP,QAAQ,GAAGO,OAAO;MACvB,IAAI,CAACtB,kBAAkB,CAACuB,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,SAAS;EACzB;EACA,IAAIQ,QAAQA,CAAC3B,KAAK,EAAE;IAChB,MAAM4B,QAAQ,GAAGvD,qBAAqB,CAAC2B,KAAK,CAAC;IAC7C,IAAI4B,QAAQ,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC5B,IAAI,CAACR,SAAS,GAAGS,QAAQ;MACzB,IAAI,CAACzB,kBAAkB,CAACuB,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIG,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACT,cAAc;EAC9B;EACA,IAAIS,aAAaA,CAAC7B,KAAK,EAAE;IACrB,MAAM8B,OAAO,GAAG9B,KAAK,IAAI,IAAI,CAACoB,cAAc;IAC5C,IAAI,CAACA,cAAc,GAAG/C,qBAAqB,CAAC2B,KAAK,CAAC;IAClD,IAAI8B,OAAO,EAAE;MACT,IAAI,IAAI,CAACV,cAAc,EAAE;QACrB,IAAI,CAACW,qBAAqB,CAAC,CAAC,CAAC,wCAAwC,CAAC;MAC1E,CAAC,MACI;QACD,IAAI,CAACA,qBAAqB,CAAC,IAAI,CAACN,OAAO,GAAG,CAAC,CAAC,qCAAqC,CAAC,CAAC,oCAAoC,CAAC;MAC5H;;MACA,IAAI,CAACZ,mBAAmB,CAACmB,IAAI,CAAC,IAAI,CAACZ,cAAc,CAAC;IACtD;IACA,IAAI,CAACI,kBAAkB,CAAC,IAAI,CAACJ,cAAc,CAAC;EAChD;EACAa,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,aAAa,IAAI,IAAI,CAACP,QAAQ;EAC9C;EACA;EACAQ,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAChC,kBAAkB,CAACiC,aAAa,CAAC,CAAC;EAC3C;EACA;EACAC,UAAUA,CAACrC,KAAK,EAAE;IACd,IAAI,CAACyB,OAAO,GAAG,CAAC,CAACzB,KAAK;EAC1B;EACA;EACAsC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACtB,6BAA6B,GAAGsB,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACzB,UAAU,GAAGyB,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACf,QAAQ,GAAGe,UAAU;EAC9B;EACAX,qBAAqBA,CAACY,QAAQ,EAAE;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAAC5B,kBAAkB;IACtC,IAAI6B,OAAO,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;IAC/C,IAAIF,QAAQ,KAAKD,QAAQ,IAAI,CAACE,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAAC9B,sBAAsB,EAAE;MAC7B8B,OAAO,CAACE,SAAS,CAACC,MAAM,CAAC,IAAI,CAACjC,sBAAsB,CAAC;IACzD;IACA,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACkC,yCAAyC,CAACL,QAAQ,EAAED,QAAQ,CAAC;IAChG,IAAI,CAAC3B,kBAAkB,GAAG2B,QAAQ;IAClC,IAAI,IAAI,CAAC5B,sBAAsB,CAACmC,MAAM,GAAG,CAAC,EAAE;MACxCL,OAAO,CAACE,SAAS,CAACI,GAAG,CAAC,IAAI,CAACpC,sBAAsB,CAAC;MAClD;MACA,MAAMqC,cAAc,GAAG,IAAI,CAACrC,sBAAsB;MAClD,IAAI,CAACX,OAAO,CAACiD,iBAAiB,CAAC,MAAM;QACjCC,UAAU,CAAC,MAAM;UACbT,OAAO,CAACE,SAAS,CAACC,MAAM,CAACI,cAAc,CAAC;QAC5C,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;EACAG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACtC,6BAA6B,CAAC,IAAI,CAACQ,OAAO,CAAC;IAChD,IAAI,CAACb,MAAM,CAACoB,IAAI,CAAC,IAAI,CAACwB,kBAAkB,CAAC,IAAI,CAAC/B,OAAO,CAAC,CAAC;IACvD;IACA;IACA,IAAI,IAAI,CAACgC,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACjC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3D;EACJ;EACA;EACAkC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAClC,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACR,6BAA6B,CAAC,IAAI,CAACQ,OAAO,CAAC;EACpD;EACAmC,iBAAiBA,CAAA,EAAG;IAChB,MAAM9E,WAAW,GAAG,IAAI,CAACyB,QAAQ,EAAEzB,WAAW;IAC9C;IACA,IAAI,CAAC,IAAI,CAAC6C,QAAQ,IAAI7C,WAAW,KAAK,MAAM,EAAE;MAC1C;MACA,IAAI,IAAI,CAAC+C,aAAa,IAAI/C,WAAW,KAAK,OAAO,EAAE;QAC/C+E,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAAC3C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,mBAAmB,CAACmB,IAAI,CAAC,IAAI,CAACZ,cAAc,CAAC;QACtD,CAAC,CAAC;MACN;MACA,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACa,qBAAqB,CAAC,IAAI,CAACb,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC,CAAC,oCAAoC,CAAC;MACzH;MACA;MACA;MACA,IAAI,CAACqC,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI,CAAC,IAAI,CAAC5B,QAAQ,IAAI7C,WAAW,KAAK,MAAM,EAAE;MAC/C;MACA;MACA,IAAI,CAAC2E,aAAa,CAACC,aAAa,CAACjC,OAAO,GAAG,IAAI,CAACA,OAAO;MACvD,IAAI,CAACgC,aAAa,CAACC,aAAa,CAAC7B,aAAa,GAAG,IAAI,CAACA,aAAa;IACvE;EACJ;EACAmC,mBAAmBA,CAACC,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACC,eAAe,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACAN,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACjD,UAAU,CAAC,CAAC;MACjB,IAAI,CAACX,kBAAkB,CAACuB,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACAuB,yCAAyCA,CAACL,QAAQ,EAAED,QAAQ,EAAE;IAC1D;IACA,IAAI,IAAI,CAACrC,cAAc,KAAK,gBAAgB,EAAE;MAC1C,OAAO,EAAE;IACb;IACA,QAAQsC,QAAQ;MACZ,KAAK,CAAC,CAAC;QACH;QACA;QACA,IAAID,QAAQ,KAAK,CAAC,CAAC,oCAAoC;UACnD,OAAO,IAAI,CAACyB,iBAAiB,CAACC,kBAAkB;QACpD,CAAC,MACI,IAAI1B,QAAQ,IAAI,CAAC,CAAC,0CAA0C;UAC7D,OAAO,IAAI,CAACzB,QAAQ,GACd,IAAI,CAACkD,iBAAiB,CAACE,sBAAsB,GAC7C,IAAI,CAACF,iBAAiB,CAACG,wBAAwB;QACzD;QACA;MACJ,KAAK,CAAC,CAAC;QACH,OAAO5B,QAAQ,KAAK,CAAC,CAAC,qCAChB,IAAI,CAACyB,iBAAiB,CAACC,kBAAkB,GACzC,IAAI,CAACD,iBAAiB,CAACG,wBAAwB;MACzD,KAAK,CAAC,CAAC;QACH,OAAO5B,QAAQ,KAAK,CAAC,CAAC,uCAChB,IAAI,CAACyB,iBAAiB,CAACI,kBAAkB,GACzC,IAAI,CAACJ,iBAAiB,CAACE,sBAAsB;MACvD,KAAK,CAAC,CAAC;QACH,OAAO3B,QAAQ,KAAK,CAAC,CAAC,qCAChB,IAAI,CAACyB,iBAAiB,CAACK,sBAAsB,GAC7C,IAAI,CAACL,iBAAiB,CAACM,wBAAwB;IAC7D;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,kBAAkBA,CAACxB,KAAK,EAAE;IACtB,MAAM2E,cAAc,GAAG,IAAI,CAAClB,aAAa;IACzC,IAAIkB,cAAc,EAAE;MAChBA,cAAc,CAACjB,aAAa,CAAC7B,aAAa,GAAG7B,KAAK;IACtD;EACJ;EAAC,QAAA4E,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;IAA8ErI,EAAE,CAAAsI,gBAAA;EAAA,CAAkF;EAAA,QAAAC,EAAA,GAC3K,IAAI,CAACC,IAAI,kBAD8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EACJ1F,gBAAgB;IAAA2F,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADd7I,EAAE,CAAA+I,WAAA,CAAAnH,GAAA;QAAF5B,EAAE,CAAA+I,WAAA,CAAAlH,GAAA;QAAF7B,EAAE,CAAA+I,WAAA,CACwpBxH,SAAS;MAAA;MAAA,IAAAsH,EAAA;QAAA,IAAAG,EAAA;QADnqBhJ,EAAE,CAAAiJ,cAAA,CAAAD,EAAA,GAAFhJ,EAAE,CAAAkJ,WAAA,QAAAJ,GAAA,CAAA/B,aAAA,GAAAiC,EAAA,CAAAG,KAAA;QAAFnJ,EAAE,CAAAiJ,cAAA,CAAAD,EAAA,GAAFhJ,EAAE,CAAAkJ,WAAA,QAAAJ,GAAA,CAAAM,aAAA,GAAAJ,EAAA,CAAAG,KAAA;QAAFnJ,EAAE,CAAAiJ,cAAA,CAAAD,EAAA,GAAFhJ,EAAE,CAAAkJ,WAAA,QAAAJ,GAAA,CAAAO,MAAA,GAAAL,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAG,MAAA;MAAAxF,SAAA;MAAAC,cAAA;MAAAwF,eAAA;MAAArG,EAAA;MAAAE,QAAA;MAAAY,aAAA;MAAAC,IAAA;MAAAX,KAAA;MAAAyB,OAAA;MAAAE,QAAA;MAAAE,aAAA;IAAA;IAAAqE,OAAA;MAAAtF,MAAA;MAAAC,mBAAA;IAAA;IAAAsF,QAAA,GAAFzJ,EAAE,CAAA0J,0BAAA;EAAA,EAC+tB;AACr0B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG3J,EAAE,CAAA4J,iBAAA,CAGX5G,gBAAgB,EAAc,CAAC;IAC9G0F,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEsI,IAAI,EAAEmB;IAAU,CAAC,EAAE;MAAEnB,IAAI,EAAE1I,EAAE,CAAC8J;IAAW,CAAC,EAAE;MAAEpB,IAAI,EAAE1I,EAAE,CAAC+J;IAAkB,CAAC,EAAE;MAAErB,IAAI,EAAE1I,EAAE,CAACgK;IAAO,CAAC,EAAE;MAAEtB,IAAI,EAAEmB;IAAU,CAAC,EAAE;MAAEnB,IAAI,EAAEmB;IAAU,CAAC,EAAE;MAAEnB,IAAI,EAAEmB;IAAU,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE/F,SAAS,EAAE,CAAC;MACtO4E,IAAI,EAAErI,KAAK;MACX4J,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAElG,cAAc,EAAE,CAAC;MACjB2E,IAAI,EAAErI,KAAK;MACX4J,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEV,eAAe,EAAE,CAAC;MAClBb,IAAI,EAAErI,KAAK;MACX4J,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE/G,EAAE,EAAE,CAAC;MACLwF,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXsF,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE2D,aAAa,EAAE,CAAC;MAChB0E,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE4D,IAAI,EAAE,CAAC;MACPyE,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE6D,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAE6D,mBAAmB,EAAE,CAAC;MACtBuE,IAAI,EAAEpI;IACV,CAAC,CAAC;IAAEgD,KAAK,EAAE,CAAC;MACRoF,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE0G,aAAa,EAAE,CAAC;MAChB2B,IAAI,EAAEnI,SAAS;MACf0J,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEb,aAAa,EAAE,CAAC;MAChBV,IAAI,EAAEnI,SAAS;MACf0J,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEZ,MAAM,EAAE,CAAC;MACTX,IAAI,EAAEnI,SAAS;MACf0J,IAAI,EAAE,CAAC1I,SAAS;IACpB,CAAC,CAAC;IAAEwD,OAAO,EAAE,CAAC;MACV2D,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE4E,QAAQ,EAAE,CAAC;MACXyD,IAAI,EAAErI;IACV,CAAC,CAAC;IAAE8E,aAAa,EAAE,CAAC;MAChBuD,IAAI,EAAErI;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMmC,WAAW,SAASQ,gBAAgB,CAAC;EACvCF,WAAWA,CAACU,UAAU,EAAE0G,iBAAiB,EAAEC,MAAM,EAAExG,QAAQ,EAAEyG,aAAa,EAAEC,OAAO,EAAE;IACjF,KAAK,CAAC,mBAAmB,EAAE7G,UAAU,EAAE0G,iBAAiB,EAAEC,MAAM,EAAExG,QAAQ,EAAEyG,aAAa,EAAEC,OAAO,CAAC;IACnG,IAAI,CAAC3C,iBAAiB,GAAG;MACrBC,kBAAkB,EAAE,sCAAsC;MAC1DE,wBAAwB,EAAE,4CAA4C;MACtEC,kBAAkB,EAAE,sCAAsC;MAC1DF,sBAAsB,EAAE,0CAA0C;MAClEG,sBAAsB,EAAE,0CAA0C;MAClEC,wBAAwB,EAAE;IAC9B,CAAC;EACL;EACA;EACAsC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACvD,aAAa,CAACC,aAAa,CAACsD,KAAK,CAAC,CAAC;EAC5C;EACAxD,kBAAkBA,CAACyD,SAAS,EAAE;IAC1B,MAAMhD,KAAK,GAAG,IAAI7E,iBAAiB,CAAC,CAAC;IACrC6E,KAAK,CAACiD,MAAM,GAAG,IAAI;IACnBjD,KAAK,CAACxC,OAAO,GAAGwF,SAAS;IACzB,OAAOhD,KAAK;EAChB;EACAnB,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACW,aAAa,EAAEC,aAAa;EAC5C;EACAyD,aAAaA,CAAA,EAAG;IACZ,KAAK,CAACvD,iBAAiB,CAAC,CAAC;EAC7B;EACAwD,mBAAmBA,CAAA,EAAG;IAClB,KAAK,CAACxD,iBAAiB,CAAC,CAAC;IACzB,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAAC8B,aAAa,CAACC,aAAa,CAACsD,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,yBAAyBA,CAACpD,KAAK,EAAE;IAC7B,IAAI,CAAC,CAACA,KAAK,CAACqD,MAAM,IAAI,IAAI,CAACxB,aAAa,CAACpC,aAAa,CAAC6D,QAAQ,CAACtD,KAAK,CAACqD,MAAM,CAAC,EAAE;MAC3ErD,KAAK,CAACC,eAAe,CAAC,CAAC;IAC3B;EACJ;EAAC,QAAAU,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA2C,oBAAAzC,CAAA;IAAA,YAAAA,CAAA,IAAwF7F,WAAW,EA5FrBxC,EAAE,CAAA+K,iBAAA,CA4FqC/K,EAAE,CAAC8J,UAAU,GA5FpD9J,EAAE,CAAA+K,iBAAA,CA4F+D/K,EAAE,CAAC+J,iBAAiB,GA5FrF/J,EAAE,CAAA+K,iBAAA,CA4FgG/K,EAAE,CAACgK,MAAM,GA5F3GhK,EAAE,CAAAgL,iBAAA,CA4FsH,UAAU,GA5FlIhL,EAAE,CAAA+K,iBAAA,CA4F8JrJ,qBAAqB,MA5FrL1B,EAAE,CAAA+K,iBAAA,CA4FgNhJ,4BAA4B;EAAA,CAA4D;EAAA,QAAAwG,EAAA,GACjY,IAAI,CAAC0C,IAAI,kBA7F8EjL,EAAE,CAAAkL,iBAAA;IAAAxC,IAAA,EA6FJlG,WAAW;IAAA2I,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,yBAAA1C,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7FT7I,EAAE,CAAAwL,cAAA,OAAA1C,GAAA,CAAA5F,EAAA;QAAFlD,EAAE,CAAAyL,WAAA;QAAFzL,EAAE,CAAA0L,WAAA,4BAAA5C,GAAA,CAAAlF,cAAA,iDAAAkF,GAAA,CAAA7D,QAAA,+BAAA6D,GAAA,CAAA7D,QAAA,8BAAA6D,GAAA,CAAA/D,OAAA;MAAA;IAAA;IAAAuE,MAAA;MAAA9D,aAAA;MAAArD,KAAA;MAAAwB,QAAA;IAAA;IAAAgI,QAAA;IAAAlC,QAAA,GAAFzJ,EAAE,CAAA4L,kBAAA,CA6F8e,CAACvJ,mCAAmC,CAAC,GA7FrhBrC,EAAE,CAAA0J,0BAAA;IAAAmC,kBAAA,EAAA/J,GAAA;IAAAgK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAArD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF7I,EAAE,CAAAmM,eAAA;QAAFnM,EAAE,CAAAoM,cAAA,YA6FuvB,CAAC;QA7F1vBpM,EAAE,CAAAqM,UAAA,mBAAAC,0CAAAC,MAAA;UAAA,OA6FotBzD,GAAA,CAAA6B,yBAAA,CAAA4B,MAAgC,CAAC;QAAA,CAAC,CAAC;QA7FzvBvM,EAAE,CAAAoM,cAAA,eA6FiyB,CAAC,YAAD,CAAC;QA7FpyBpM,EAAE,CAAAqM,UAAA,mBAAAG,0CAAA;UAAA,OA6F+5B1D,GAAA,CAAA4B,mBAAA,CAAoB,CAAC;QAAA,CAAC,CAAC;QA7Fx7B1K,EAAE,CAAAyM,YAAA,CA6F47B,CAAC;QA7F/7BzM,EAAE,CAAAoM,cAAA,iBA6FytD,CAAC;QA7F5tDpM,EAAE,CAAAqM,UAAA,kBAAAK,2CAAA;UAAA,OA6FgnD5D,GAAA,CAAArB,OAAA,CAAQ,CAAC;QAAA,CAAC,CAAC,mBAAAkF,4CAAA;UAAA,OAAuB7D,GAAA,CAAA2B,aAAA,CAAc,CAAC;QAAA,CAAvC,CAAC,oBAAAmC,6CAAAL,MAAA;UAAA,OAAgEzD,GAAA,CAAAxB,mBAAA,CAAAiF,MAA0B,CAAC;QAAA,CAA5F,CAAC;QA7F7nDvM,EAAE,CAAAyM,YAAA,CA6FytD,CAAC;QA7F5tDzM,EAAE,CAAA6M,SAAA,YA6FywD,CAAC;QA7F5wD7M,EAAE,CAAAoM,cAAA,YA6FuzD,CAAC;QA7F1zDpM,EAAE,CAAA8M,cAAA,CA6Fy8D,CAAC;QA7F58D9M,EAAE,CAAAoM,cAAA,YA6Fy8D,CAAC;QA7F58DpM,EAAE,CAAA6M,SAAA,aA6FolE,CAAC;QA7FvlE7M,EAAE,CAAAyM,YAAA,CA6FkmE,CAAC;QA7FrmEzM,EAAE,CAAA+M,eAAA,CA6FipE,CAAC;QA7FppE/M,EAAE,CAAA6M,SAAA,cA6FupE,CAAC;QA7F1pE7M,EAAE,CAAAyM,YAAA,CA6FmqE,CAAC;QA7FtqEzM,EAAE,CAAA6M,SAAA,cA6F63E,CAAC;QA7Fh4E7M,EAAE,CAAAyM,YAAA,CA6Fu4E,CAAC;QA7F14EzM,EAAE,CAAAoM,cAAA,oBA6FotF,CAAC;QA7FvtFpM,EAAE,CAAAgN,YAAA,GA6FmvF,CAAC;QA7FtvFhN,EAAE,CAAAyM,YAAA,CA6F+vF,CAAC,CAAD,CAAC;MAAA;MAAA,IAAA5D,EAAA;QAAA,MAAAoE,GAAA,GA7FlwFjN,EAAE,CAAAkN,WAAA;QAAFlN,EAAE,CAAA0L,WAAA,8BAAA5C,GAAA,CAAA9E,aAAA,YA6FksB,CAAC;QA7FrsBhE,EAAE,CAAAmN,SAAA,EA6FulC,CAAC;QA7F1lCnN,EAAE,CAAA0L,WAAA,2BAAA5C,GAAA,CAAA/D,OA6FulC,CAAC;QA7F1lC/E,EAAE,CAAAoN,UAAA,YAAAtE,GAAA,CAAA/D,OA6Fk6C,CAAC,kBAAA+D,GAAA,CAAA3D,aAAD,CAAC,aAAA2D,GAAA,CAAA7D,QAAD,CAAC,OAAA6D,GAAA,CAAA7F,OAAD,CAAC,aAAA6F,GAAA,CAAA1F,QAAD,CAAC,aAAA0F,GAAA,CAAAnF,QAAD,CAAC;QA7Fr6C3D,EAAE,CAAAyL,WAAA,eAAA3C,GAAA,CAAAhF,SAAA,QA6F2oC,CAAC,oBAAAgF,GAAA,CAAA/E,cAAD,CAAC,qBAAA+E,GAAA,CAAAS,eAAD,CAAC,iBAAAT,GAAA,CAAA3D,aAAA,iBAAD,CAAC,SAAA2D,GAAA,CAAA7E,IAAD,CAAC,UAAA6E,GAAA,CAAAxF,KAAD,CAAC;QA7F9oCtD,EAAE,CAAAmN,SAAA,EA6FyxE,CAAC;QA7F5xEnN,EAAE,CAAAoN,UAAA,qBAAAH,GA6FyxE,CAAC,sBAAAnE,GAAA,CAAAtD,aAAA,IAAAsD,GAAA,CAAA7D,QAAD,CAAC,0BAAD,CAAC;QA7F5xEjF,EAAE,CAAAmN,SAAA,EA6FmtF,CAAC;QA7FttFnN,EAAE,CAAAoN,UAAA,QAAAtE,GAAA,CAAA7F,OA6FmtF,CAAC;MAAA;IAAA;IAAAoK,YAAA,GAA0sqBnM,EAAE,CAACK,SAAS;IAAA+L,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA,EAA6T;AAC70wB;AACA;EAAA,QAAA7D,SAAA,oBAAAA,SAAA,KA/FoG3J,EAAE,CAAA4J,iBAAA,CA+FXpH,WAAW,EAAc,CAAC;IACzGkG,IAAI,EAAElI,SAAS;IACfyJ,IAAI,EAAE,CAAC;MAAEwD,QAAQ,EAAE,cAAc;MAAEC,IAAI,EAAE;QAC7B,OAAO,EAAE,kBAAkB;QAC3B,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,iCAAiC,EAAG,qCAAoC;QACxE,gCAAgC,EAAE,UAAU;QAC5C,MAAM,EAAE,IAAI;QACZ;QACA,mCAAmC,EAAE,UAAU;QAC/C,kCAAkC,EAAE;MACxC,CAAC;MAAEC,SAAS,EAAE,CAACtL,mCAAmC,CAAC;MAAEiH,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC;MAAEqC,QAAQ,EAAE,aAAa;MAAE4B,aAAa,EAAE9M,iBAAiB,CAACmN,IAAI;MAAEJ,eAAe,EAAE9M,uBAAuB,CAACmN,MAAM;MAAE5B,QAAQ,EAAE,4qEAA4qE;MAAEqB,MAAM,EAAE,CAAC,2lqBAA2lqB;IAAE,CAAC;EACjgvB,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE5E,IAAI,EAAE1I,EAAE,CAAC8J;IAAW,CAAC,EAAE;MAAEpB,IAAI,EAAE1I,EAAE,CAAC+J;IAAkB,CAAC,EAAE;MAAErB,IAAI,EAAE1I,EAAE,CAACgK;IAAO,CAAC,EAAE;MAAEtB,IAAI,EAAEmB,SAAS;MAAEiE,UAAU,EAAE,CAAC;QAC5IpF,IAAI,EAAE/H,SAAS;QACfsJ,IAAI,EAAE,CAAC,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAEmB,SAAS;MAAEiE,UAAU,EAAE,CAAC;QAClCpF,IAAI,EAAE9H;MACV,CAAC,EAAE;QACC8H,IAAI,EAAE7H,MAAM;QACZoJ,IAAI,EAAE,CAACvI,qBAAqB;MAChC,CAAC;IAAE,CAAC,EAAE;MAAEgH,IAAI,EAAEmB,SAAS;MAAEiE,UAAU,EAAE,CAAC;QAClCpF,IAAI,EAAE9H;MACV,CAAC,EAAE;QACC8H,IAAI,EAAE7H,MAAM;QACZoJ,IAAI,EAAE,CAAClI,4BAA4B;MACvC,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMgM,+BAA+B,GAAG;EACpCzL,OAAO,EAAEtB,aAAa;EACtBuB,WAAW,EAAErC,UAAU,CAAC,MAAM8N,4BAA4B,CAAC;EAC3DvL,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMuL,4BAA4B,SAAS/M,yBAAyB,CAAC;EAAA,QAAAiH,CAAA,GACxD,IAAI,CAACC,IAAI;IAAA,IAAA8F,yCAAA;IAAA,gBAAAC,qCAAA7F,CAAA;MAAA,QAAA4F,yCAAA,KAAAA,yCAAA,GAvI8EjO,EAAE,CAAAmO,qBAAA,CAuIQH,4BAA4B,IAAA3F,CAAA,IAA5B2F,4BAA4B;IAAA;EAAA,GAAqD;EAAA,QAAAzF,EAAA,GAClL,IAAI,CAACC,IAAI,kBAxI8ExI,EAAE,CAAAyI,iBAAA;IAAAC,IAAA,EAwIJsF,4BAA4B;IAAA7C,SAAA;IAAA1B,QAAA,GAxI1BzJ,EAAE,CAAA4L,kBAAA,CAwI8K,CAACmC,+BAA+B,CAAC,GAxIjN/N,EAAE,CAAA0J,0BAAA;EAAA,EAwIuP;AAC7V;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1IoG3J,EAAE,CAAA4J,iBAAA,CA0IXoE,4BAA4B,EAAc,CAAC;IAC1HtF,IAAI,EAAEtI,SAAS;IACf6J,IAAI,EAAE,CAAC;MACCwD,QAAQ,EAAG;AAC/B,kFAAkF;MAC9DE,SAAS,EAAE,CAACI,+BAA+B;IAC/C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMK,mCAAmC,CAAC;EAAA,QAAAlG,CAAA,GAC7B,IAAI,CAACC,IAAI,YAAAkG,4CAAAhG,CAAA;IAAA,YAAAA,CAAA,IAAwF+F,mCAAmC;EAAA,CAAkD;EAAA,QAAA7F,EAAA,GACtL,IAAI,CAAC+F,IAAI,kBAtJ8EtO,EAAE,CAAAuO,gBAAA;IAAA7F,IAAA,EAsJS0F;EAAmC,EAA0F;EAAA,QAAAI,EAAA,GAC/N,IAAI,CAACC,IAAI,kBAvJ8EzO,EAAE,CAAA0O,gBAAA,IAuJ+C;AACrJ;AACA;EAAA,QAAA/E,SAAA,oBAAAA,SAAA,KAzJoG3J,EAAE,CAAA4J,iBAAA,CAyJXwE,mCAAmC,EAAc,CAAC;IACjI1F,IAAI,EAAE5H,QAAQ;IACdmJ,IAAI,EAAE,CAAC;MACC0E,OAAO,EAAE,CAACX,4BAA4B,CAAC;MACvCY,YAAY,EAAE,CAACZ,4BAA4B;IAC/C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMa,iBAAiB,CAAC;EAAA,QAAA3G,CAAA,GACX,IAAI,CAACC,IAAI,YAAA2G,0BAAAzG,CAAA;IAAA,YAAAA,CAAA,IAAwFwG,iBAAiB;EAAA,CAAkD;EAAA,QAAAtG,EAAA,GACpK,IAAI,CAAC+F,IAAI,kBAlK8EtO,EAAE,CAAAuO,gBAAA;IAAA7F,IAAA,EAkKSmG;EAAiB,EAAgM;EAAA,QAAAL,EAAA,GACnT,IAAI,CAACC,IAAI,kBAnK8EzO,EAAE,CAAA0O,gBAAA;IAAAK,OAAA,GAmKsCvN,eAAe,EAAEC,eAAe,EAAE2M,mCAAmC,EAAE5M,eAAe,EAAE4M,mCAAmC;EAAA,EAAI;AAC3Q;AACA;EAAA,QAAAzE,SAAA,oBAAAA,SAAA,KArKoG3J,EAAE,CAAA4J,iBAAA,CAqKXiF,iBAAiB,EAAc,CAAC;IAC/GnG,IAAI,EAAE5H,QAAQ;IACdmJ,IAAI,EAAE,CAAC;MACC8E,OAAO,EAAE,CAACvN,eAAe,EAAEC,eAAe,EAAE2M,mCAAmC,CAAC;MAChFO,OAAO,EAAE,CAACnM,WAAW,EAAEhB,eAAe,EAAE4M,mCAAmC,CAAC;MAC5EQ,YAAY,EAAE,CAACpM,WAAW;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,mCAAmC,EAAEN,4BAA4B,EAAEG,oCAAoC,EAAE6L,+BAA+B,EAAEvL,WAAW,EAAEE,iBAAiB,EAAEmM,iBAAiB,EAAEb,4BAA4B,EAAEhL,gBAAgB,EAAEoL,mCAAmC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}