import { Injectable } from '@angular/core';
import { HttpService } from '../http.service';
import { UserTemp, UserTempStatus, UserTempType } from './auth';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(private httpService: HttpService) {}

  checkUserTempAvailability(userTempEmail: string): Observable<UserTemp> {
    return this.httpService.requestWithoutToken(
      'GET',
      '/auth/user_temp_avb',
      null,
      { userTempEmail }
    );
  }

  saveUserTemp(userTemp: UserTemp): Observable<UserTemp> {
    return this.httpService.requestWithoutToken(
      'POST',
      '/auth/user_temp_register',
      userTemp
    );
  }

  isLoggedIn(): boolean {
    return !!localStorage.getItem('auth_token');
  }

}
