{"ast": null, "code": "import * as i0 from \"@angular/core\";\nclass DoctorSelectComponent {\n  constructor() {\n    this.selectedDoctor = '';\n    this.selectedTime = '';\n    this.selectedTimeId = '';\n    this.timeSlots = [{\n      id: 'slot-1',\n      label: '7:00 AM - 8:00 AM',\n      status: 'disabled'\n    }, {\n      id: 'slot-2',\n      label: '8:00 AM - 9:00 AM',\n      status: 'disabled'\n    }, {\n      id: 'slot-3',\n      label: '9:00 AM - 10:00 AM',\n      status: 'available'\n    }, {\n      id: 'slot-4',\n      label: '10:00 AM - 11:00 AM',\n      status: 'available'\n    }, {\n      id: 'slot-5',\n      label: '11:00 AM - 12:00 PM',\n      status: 'disabled'\n    }, {\n      id: 'slot-6',\n      label: '4:00 PM - 5:00 PM',\n      status: 'available'\n    }, {\n      id: 'slot-7',\n      label: '5:00 PM - 6:00 PM',\n      status: 'available'\n    }, {\n      id: 'slot-8',\n      label: '6:00 PM - 7:00 PM',\n      status: 'available'\n    }, {\n      id: 'slot-9',\n      label: '7:00 PM - 8:00 PM',\n      status: 'available'\n    }, {\n      id: 'slot-10',\n      label: '8:00 PM - 9:00 PM',\n      status: 'selected'\n    }];\n  }\n  ngOnInit() {\n    // Set initial selected time if needed\n    const selectedSlot = this.timeSlots.find(slot => slot.status === 'selected');\n    if (selectedSlot) {\n      this.selectedTime = selectedSlot.label;\n      this.selectedTimeId = selectedSlot.id;\n    }\n  }\n  onDoctorChange(event) {\n    const selectElement = event.target;\n    const selectedOption = selectElement.options[selectElement.selectedIndex];\n    this.selectedDoctor = selectedOption.text;\n  }\n  selectTimeSlot(slotId) {\n    const slot = this.timeSlots.find(s => s.id === slotId);\n    if (slot && slot.status !== 'disabled') {\n      // Update previous selected slot\n      this.timeSlots.forEach(s => {\n        if (s.id === this.selectedTimeId && s.status === 'selected') {\n          s.status = 'available';\n        }\n      });\n      // Set new selected slot\n      slot.status = 'selected';\n      this.selectedTime = slot.label;\n      this.selectedTimeId = slotId;\n    }\n  }\n  getTimeSlotClasses(slot) {\n    return {\n      'disabled': slot.status === 'disabled',\n      'available': slot.status === 'available',\n      'selected': slot.status === 'selected'\n    };\n  }\n  confirmAppointment() {\n    if (this.selectedDoctor && this.selectedTime) {\n      console.log('Appointment confirmed:', {\n        doctor: this.selectedDoctor,\n        timeSlot: this.selectedTime\n      });\n      // TODO: Call service to send appointment confirmation\n      alert(`Appointment scheduled with ${this.selectedDoctor} at ${this.selectedTime}`);\n    }\n  }\n  static #_ = this.ɵfac = function DoctorSelectComponent_Factory(t) {\n    return new (t || DoctorSelectComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DoctorSelectComponent,\n    selectors: [[\"app-doctor-select\"]],\n    decls: 2,\n    vars: 0,\n    template: function DoctorSelectComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"doctor-select works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport { DoctorSelectComponent };", "map": {"version": 3, "names": ["DoctorSelectComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "selectedTime", "selectedTimeId", "timeSlots", "id", "label", "status", "ngOnInit", "selectedSlot", "find", "slot", "onDoctorChange", "event", "selectElement", "target", "selectedOption", "options", "selectedIndex", "text", "selectTimeSlot", "slotId", "s", "for<PERSON>ach", "getTimeSlotClasses", "confirmAppointment", "console", "log", "doctor", "timeSlot", "alert", "_", "_2", "selectors", "decls", "vars", "template", "DoctorSelectComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\appointment-popup\\doctor-select\\doctor-select.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\appointment-popup\\doctor-select\\doctor-select.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\n\r\ninterface TimeSlot {\r\n  id: string;\r\n  label: string;\r\n  status: 'available' | 'disabled' | 'selected';\r\n}\r\n\r\n@Component({\r\n  selector: 'app-doctor-select',\r\n  templateUrl: './doctor-select.component.html',\r\n  styleUrls: ['./doctor-select.component.css']\r\n})\r\nexport class DoctorSelectComponent implements OnInit {\r\n  selectedDoctor: string = '';\r\n  selectedTime: string = '';\r\n  selectedTimeId: string = '';\r\n\r\n  timeSlots: TimeSlot[] = [\r\n    { id: 'slot-1', label: '7:00 AM - 8:00 AM', status: 'disabled' },\r\n    { id: 'slot-2', label: '8:00 AM - 9:00 AM', status: 'disabled' },\r\n    { id: 'slot-3', label: '9:00 AM - 10:00 AM', status: 'available' },\r\n    { id: 'slot-4', label: '10:00 AM - 11:00 AM', status: 'available' },\r\n    { id: 'slot-5', label: '11:00 AM - 12:00 PM', status: 'disabled' },\r\n    { id: 'slot-6', label: '4:00 PM - 5:00 PM', status: 'available' },\r\n    { id: 'slot-7', label: '5:00 PM - 6:00 PM', status: 'available' },\r\n    { id: 'slot-8', label: '6:00 PM - 7:00 PM', status: 'available' },\r\n    { id: 'slot-9', label: '7:00 PM - 8:00 PM', status: 'available' },\r\n    { id: 'slot-10', label: '8:00 PM - 9:00 PM', status: 'selected' }\r\n  ];\r\n\r\n  ngOnInit(): void {\r\n    // Set initial selected time if needed\r\n    const selectedSlot = this.timeSlots.find(slot => slot.status === 'selected');\r\n    if (selectedSlot) {\r\n      this.selectedTime = selectedSlot.label;\r\n      this.selectedTimeId = selectedSlot.id;\r\n    }\r\n  }\r\n\r\n  onDoctorChange(event: any): void {\r\n    const selectElement = event.target as HTMLSelectElement;\r\n    const selectedOption = selectElement.options[selectElement.selectedIndex];\r\n    this.selectedDoctor = selectedOption.text;\r\n  }\r\n\r\n  selectTimeSlot(slotId: string): void {\r\n    const slot = this.timeSlots.find(s => s.id === slotId);\r\n    if (slot && slot.status !== 'disabled') {\r\n      // Update previous selected slot\r\n      this.timeSlots.forEach(s => {\r\n        if (s.id === this.selectedTimeId && s.status === 'selected') {\r\n          s.status = 'available';\r\n        }\r\n      });\r\n\r\n      // Set new selected slot\r\n      slot.status = 'selected';\r\n      this.selectedTime = slot.label;\r\n      this.selectedTimeId = slotId;\r\n    }\r\n  }\r\n\r\n  getTimeSlotClasses(slot: TimeSlot): any {\r\n    return {\r\n      'disabled': slot.status === 'disabled',\r\n      'available': slot.status === 'available',\r\n      'selected': slot.status === 'selected'\r\n    };\r\n  }\r\n\r\n  confirmAppointment(): void {\r\n    if (this.selectedDoctor && this.selectedTime) {\r\n      console.log('Appointment confirmed:', {\r\n        doctor: this.selectedDoctor,\r\n        timeSlot: this.selectedTime\r\n      });\r\n      // TODO: Call service to send appointment confirmation\r\n      alert(`Appointment scheduled with ${this.selectedDoctor} at ${this.selectedTime}`);\r\n    }\r\n  }\r\n}\r\n", "<p>doctor-select works!</p>\r\n"], "mappings": ";AAQA,MAKaA,qBAAqB;EALlCC,YAAA;IAME,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,cAAc,GAAW,EAAE;IAE3B,KAAAC,SAAS,GAAe,CACtB;MAAEC,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAU,CAAE,EAChE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAU,CAAE,EAChE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAE;IAAW,CAAE,EAClE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAW,CAAE,EACnE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAU,CAAE,EAClE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAW,CAAE,EACjE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAW,CAAE,EACjE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAW,CAAE,EACjE;MAAEF,EAAE,EAAE,QAAQ;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAW,CAAE,EACjE;MAAEF,EAAE,EAAE,SAAS;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAU,CAAE,CAClE;;EAEDC,QAAQA,CAAA;IACN;IACA,MAAMC,YAAY,GAAG,IAAI,CAACL,SAAS,CAACM,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,MAAM,KAAK,UAAU,CAAC;IAC5E,IAAIE,YAAY,EAAE;MAChB,IAAI,CAACP,YAAY,GAAGO,YAAY,CAACH,KAAK;MACtC,IAAI,CAACH,cAAc,GAAGM,YAAY,CAACJ,EAAE;;EAEzC;EAEAO,cAAcA,CAACC,KAAU;IACvB,MAAMC,aAAa,GAAGD,KAAK,CAACE,MAA2B;IACvD,MAAMC,cAAc,GAAGF,aAAa,CAACG,OAAO,CAACH,aAAa,CAACI,aAAa,CAAC;IACzE,IAAI,CAACjB,cAAc,GAAGe,cAAc,CAACG,IAAI;EAC3C;EAEAC,cAAcA,CAACC,MAAc;IAC3B,MAAMV,IAAI,GAAG,IAAI,CAACP,SAAS,CAACM,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKgB,MAAM,CAAC;IACtD,IAAIV,IAAI,IAAIA,IAAI,CAACJ,MAAM,KAAK,UAAU,EAAE;MACtC;MACA,IAAI,CAACH,SAAS,CAACmB,OAAO,CAACD,CAAC,IAAG;QACzB,IAAIA,CAAC,CAACjB,EAAE,KAAK,IAAI,CAACF,cAAc,IAAImB,CAAC,CAACf,MAAM,KAAK,UAAU,EAAE;UAC3De,CAAC,CAACf,MAAM,GAAG,WAAW;;MAE1B,CAAC,CAAC;MAEF;MACAI,IAAI,CAACJ,MAAM,GAAG,UAAU;MACxB,IAAI,CAACL,YAAY,GAAGS,IAAI,CAACL,KAAK;MAC9B,IAAI,CAACH,cAAc,GAAGkB,MAAM;;EAEhC;EAEAG,kBAAkBA,CAACb,IAAc;IAC/B,OAAO;MACL,UAAU,EAAEA,IAAI,CAACJ,MAAM,KAAK,UAAU;MACtC,WAAW,EAAEI,IAAI,CAACJ,MAAM,KAAK,WAAW;MACxC,UAAU,EAAEI,IAAI,CAACJ,MAAM,KAAK;KAC7B;EACH;EAEAkB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACxB,cAAc,IAAI,IAAI,CAACC,YAAY,EAAE;MAC5CwB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCC,MAAM,EAAE,IAAI,CAAC3B,cAAc;QAC3B4B,QAAQ,EAAE,IAAI,CAAC3B;OAChB,CAAC;MACF;MACA4B,KAAK,CAAC,8BAA8B,IAAI,CAAC7B,cAAc,OAAO,IAAI,CAACC,YAAY,EAAE,CAAC;;EAEtF;EAAC,QAAA6B,CAAA,G;qBAnEUhC,qBAAqB;EAAA;EAAA,QAAAiC,EAAA,G;UAArBjC,qBAAqB;IAAAkC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCblCE,EAAA,CAAAC,cAAA,QAAG;QAAAD,EAAA,CAAAE,MAAA,2BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;SDad5C,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}