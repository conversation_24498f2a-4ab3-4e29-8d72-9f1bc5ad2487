{"ast": null, "code": "import { LaboratorySetup } from \"../laboratory/laboratory\";\nimport { User } from \"../user/user\";\nexport class Clinic {\n  constructor() {\n    this.clinicId = 0;\n    this.clinicCategories = new ClinicCategories();\n    this.userId = new User();\n    this.name = '';\n    this.address = '';\n    this.city = '';\n    this.state = '';\n    this.country = '';\n    this.contactPerson = '';\n    this.tele = '';\n    this.email = '';\n    this.web = '';\n    this.registeredDate = '';\n    this.latitude = '';\n    this.longitude = '';\n  }\n}\nexport class ClinicCategories {\n  constructor() {\n    this.clinicCategoryId = 0;\n    this.clinicCategory = '';\n  }\n}\nexport class ClinicLaboratoryOrder {\n  constructor() {\n    this.laboratoryOrderId = 0;\n    this.clinicId = 0;\n    this.laboratorySetupId = new LaboratorySetup();\n    this.status = '';\n    this.patientName = '';\n    this.dateOfBirth = '';\n    this.contactNumber = '';\n    this.expectedDate = '';\n    this.toothNumber = 0;\n    this.toothSurface = '';\n    this.typeOfCrown = '';\n    this.shade = '';\n    this.materialSpecifications = '';\n    this.occlusion = '';\n    this.marginType = '';\n    this.crownDesign = '';\n    this.additionalInstructions = '';\n    this.laboratoryOrderDate = '';\n    this.laboratoryOrderTime = '';\n  }\n}\nexport class ClinicDoctor {}\nexport class ClinicServices {\n  constructor() {\n    this.clinicServiceCategoryId = 0;\n    this.clinicServiceCategoryName = '';\n  }\n}", "map": {"version": 3, "names": ["LaboratorySetup", "User", "Clinic", "constructor", "clinicId", "clinicCategories", "ClinicCategories", "userId", "name", "address", "city", "state", "country", "<PERSON><PERSON><PERSON>", "tele", "email", "web", "registeredDate", "latitude", "longitude", "clinicCategoryId", "clinicCategory", "ClinicLaboratoryOrder", "laboratoryOrderId", "laboratorySetupId", "status", "patientName", "dateOfBirth", "contactNumber", "expectedDate", "toothNumber", "toothSurface", "typeOfCrown", "shade", "materialSpecifications", "occlusion", "marginType", "crownDesign", "additionalInstructions", "laboratoryOrderDate", "laboratoryOrderTime", "ClinicDoctor", "ClinicServices", "clinicServiceCategoryId", "clinicServiceCategoryName"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic.ts"], "sourcesContent": ["import { signal, WritableSignal } from \"@angular/core\";\r\nimport { LaboratorySetup } from \"../laboratory/laboratory\";\r\nimport { Supplier } from \"../supplier/supplier\";\r\nimport { User } from \"../user/user\";\r\n\r\nexport class Clinic {\r\n  clinicId: number = 0;\r\n  clinicCategories: ClinicCategories = new ClinicCategories();\r\n  userId: User = new User();\r\n  name: string = '';\r\n  address: string = '';\r\n  city: string = '';\r\n  state: string = '';\r\n  country: string = '';\r\n  contactPerson: string = '';\r\n  tele: string = '';\r\n  email: string = '';\r\n  web: string = '';\r\n  registeredDate: string = '';\r\n  latitude: string = '';\r\n  longitude: string = '';\r\n}\r\n\r\nexport class ClinicCategories {\r\n  clinicCategoryId: number = 0;\r\n  clinicCategory: string = '';\r\n}\r\n\r\nexport class ClinicLaboratoryOrder {\r\n  laboratoryOrderId: number = 0;\r\n  clinicId: number = 0;\r\n  laboratorySetupId: LaboratorySetup = new LaboratorySetup();\r\n  status: string = '';\r\n  patientName: string = '';\r\n  dateOfBirth: string = '';\r\n  contactNumber: string = '';\r\n  expectedDate: string = '';\r\n  toothNumber: number = 0;\r\n  toothSurface: string = '';\r\n  typeOfCrown: string = '';\r\n  shade: string = '';\r\n  materialSpecifications: string = '';\r\n  occlusion: string = '';\r\n  marginType: string = '';\r\n  crownDesign: string = '';\r\n  additionalInstructions: string = '';\r\n  laboratoryOrderDate: string = '';\r\n  laboratoryOrderTime: string = '';\r\n}\r\n\r\nexport class ClinicDoctor {}\r\n\r\nexport class ClinicServices{\r\n  clinicServiceCategoryId:number = 0;\r\n  clinicServiceCategoryName: string = '';\r\n}\r\n\r\nexport interface ClinicAppointment{\r\n  id: number;\r\n  patientName: string;\r\n  time: string;\r\n  date: string;\r\n  timeAgo: string;\r\n  isConfirmed: boolean;\r\n}\r\n\r\n\r\n// Clinic - Supplier Inventory Items\r\n\r\nexport interface SupplierInventoryItem {\r\n  inventoryItemId: string;\r\n  inventoryName: string;\r\n  inventoryPrice: number;\r\n  inventoryQty: number;\r\n  imageURL: string;\r\n}\r\n\r\nexport interface CartItemHead{\r\n  supplierId:Supplier\r\n}\r\n\r\nexport interface CartItemDetails {\r\n  item: SupplierInventoryItem;\r\n  quantity: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SupplierCategoryItemList{\r\n  id: number;\r\n  name: string;\r\n}\r\n\r\nexport interface InventoryOrderRequestDto {\r\n  supplierId: number;\r\n  cartItemList: CartItemDetails[];\r\n  userId: number;\r\n}\r\nexport interface TimeSlot {\r\n  time: string;\r\n  status: 'available' | 'disabled' | 'selected';\r\n}\r\n\r\n\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,IAAI,QAAQ,cAAc;AAEnC,OAAM,MAAOC,MAAM;EAAnBC,YAAA;IACE,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,gBAAgB,GAAqB,IAAIC,gBAAgB,EAAE;IAC3D,KAAAC,MAAM,GAAS,IAAIN,IAAI,EAAE;IACzB,KAAAO,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;EACxB;;AAEA,OAAM,MAAOb,gBAAgB;EAA7BH,YAAA;IACE,KAAAiB,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,cAAc,GAAW,EAAE;EAC7B;;AAEA,OAAM,MAAOC,qBAAqB;EAAlCnB,YAAA;IACE,KAAAoB,iBAAiB,GAAW,CAAC;IAC7B,KAAAnB,QAAQ,GAAW,CAAC;IACpB,KAAAoB,iBAAiB,GAAoB,IAAIxB,eAAe,EAAE;IAC1D,KAAAyB,MAAM,GAAW,EAAE;IACnB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,sBAAsB,GAAW,EAAE;IACnC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,sBAAsB,GAAW,EAAE;IACnC,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,mBAAmB,GAAW,EAAE;EAClC;;AAEA,OAAM,MAAOC,YAAY;AAEzB,OAAM,MAAOC,cAAc;EAA3BvC,YAAA;IACE,KAAAwC,uBAAuB,GAAU,CAAC;IAClC,KAAAC,yBAAyB,GAAW,EAAE;EACxC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}