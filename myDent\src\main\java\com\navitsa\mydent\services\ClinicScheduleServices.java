package com.navitsa.mydent.services;


import com.navitsa.mydent.entity.ClinicSchedule;
import com.navitsa.mydent.repositories.ClinicScheduleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

@Service
public class ClinicScheduleServices {

    @Autowired
    private ClinicScheduleRepository clinicScheduleRepository;

    public ClinicSchedule saveSchedules(ClinicSchedule clinicSchedule){
        try{
        if(!clinicSchedule.getHolidayDate()){
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");

            LocalTime fromTime = LocalTime.parse(clinicSchedule.getFromTime(), timeFormatter);
            LocalTime toTime = LocalTime.parse(clinicSchedule.getToTime(), timeFormatter);

            // Prevent same time
            if (fromTime.equals(toTime)) {
                throw new IllegalArgumentException("From time and To time cannot be the same.");
            }

            // Prevent invalid range
            if (toTime.isBefore(fromTime)) {
                throw new IllegalArgumentException("To time must be after From time.");
            }
            return clinicScheduleRepository.save(clinicSchedule);
        }
        return clinicScheduleRepository.save(clinicSchedule);

    } catch (
    DateTimeParseException e) {
        throw new IllegalArgumentException("Invalid time format. Please use HH:mm format.");
    }
    }

    public List<ClinicSchedule> findAllSchedules(){
        try {
            return clinicScheduleRepository.findAll();
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of schedules.");
        }
    }

    public ClinicSchedule getScheduleById(int id){
        try {
            return clinicScheduleRepository.findById(id).orElse(null);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the schedules");
        }
    }

    public ClinicSchedule updateSchedule(int id, ClinicSchedule clinicScheduleDetails) {
        try {
            ClinicSchedule clinicSchedule = clinicScheduleRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("Schedule not found with id: " + id));

            // Update fields
            clinicSchedule.setDate(clinicScheduleDetails.getDate());
            clinicSchedule.setFromTime(clinicScheduleDetails.getFromTime());
            clinicSchedule.setToTime(clinicScheduleDetails.getToTime());
            clinicSchedule.setHolidayDate(clinicScheduleDetails.getHolidayDate());

            return clinicScheduleRepository.save(clinicSchedule);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while updating the schedule");
        }
    }

    public void deleteShedule(int id){
        try{
            clinicScheduleRepository.deleteById(id);
        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException("An error occurred while deleting the schedules");
        }
    }

}
