package com.navitsa.mydent.config;

import java.util.Base64;
import java.util.Collections;
import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.navitsa.mydent.dtos.UserDto;
import com.navitsa.mydent.services.UserService;

import jakarta.annotation.PostConstruct;


@Component
public class UserAuthenticationProvider {

	private static final long TOKEN_VALIDITY_DURATION = 24 * 60 * 60 * 1000L; // 1 day in milliseconds
    private static final String FIRST_NAME_CLAIM = "firstName";
    private static final String LAST_NAME_CLAIM = "lastName";

    @Value("${security.jwt.token.secret-key:secret-key}")
    private String secretKey;
    
    @Autowired
    private UserService userService;

    @PostConstruct
    protected void init() {
        // This is to avoid having the raw secret key available in the JVM
        secretKey = Base64.getEncoder().encodeToString(secretKey.getBytes());
    }

    public String createToken(UserDto user) {
        Date now = new Date();
        Date validity = new Date(now.getTime() + TOKEN_VALIDITY_DURATION);

        Algorithm algorithm = Algorithm.HMAC256(secretKey);
        return JWT.create()
                .withSubject(user.getUsername())
                .withIssuedAt(now)
                .withExpiresAt(validity)
                .withClaim(FIRST_NAME_CLAIM, user.getFirstName())
                .withClaim(LAST_NAME_CLAIM, user.getLastName())
                .sign(algorithm);
    }

    public Authentication validateToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secretKey);

            JWTVerifier verifier = JWT.require(algorithm)
                    .build();

            DecodedJWT decoded = verifier.verify(token);

            UserDto user = new UserDto.Builder()
            		.username(decoded.getSubject())
                    .firstName(decoded.getClaim(FIRST_NAME_CLAIM).asString())
                    .lastName(decoded.getClaim(LAST_NAME_CLAIM).asString())
                    .build();

            return new UsernamePasswordAuthenticationToken(user, null, Collections.emptyList());
        } catch (JWTVerificationException e) {
            // Handle verification exception (e.g., log it or throw a custom exception)
            // For simplicity, rethrowing the exception here
            throw e;
        }
    }

    public Authentication validateTokenStrongly(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secretKey);

            JWTVerifier verifier = JWT.require(algorithm)
                    .build();

            DecodedJWT decoded = verifier.verify(token);

            UserDto user = userService.findByUsername(decoded.getSubject());

            return new UsernamePasswordAuthenticationToken(user, null, Collections.emptyList());
        } catch (JWTVerificationException e) {
            // Handle verification exception (e.g., log it or throw a custom exception)
            // For simplicity, rethrowing the exception here
            throw e;
        }
    }
}
