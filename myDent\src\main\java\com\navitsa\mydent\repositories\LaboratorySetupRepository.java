package com.navitsa.mydent.repositories;

import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.entity.LaboratorySetup;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface LaboratorySetupRepository extends JpaRepository<LaboratorySetup, Integer> {
	
	List <LaboratorySetup> findByLaboratoryId(Laboratory laboratory);
}
