package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.*;
import com.navitsa.mydent.enums.UserStatus;
import com.navitsa.mydent.enums.UserTempStatus;
import com.navitsa.mydent.enums.UserTempType;
import com.navitsa.mydent.exceptions.AppException;
import com.navitsa.mydent.repositories.UserTempRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.nio.CharBuffer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
public class UserTempServices {
    private final UserTempRepository userTempRepository;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UserCategoryService userCategoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private DoctorService doctorService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private LaboratoryService laboratoryService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private EmailService emailService;

    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserTempServices(UserTempRepository userTempRepository, PasswordEncoder passwordEncoder) {
        this.userTempRepository = userTempRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public UserTemp getUserTempByEmail(String userEmail){
        Optional<UserTemp> currentUser = userTempRepository.findByUserEmail(userEmail);
        return currentUser.orElse(null);
    }

    public UserTemp saveUserAsTemp(UserTemp userTemp){
        // Check if email already exists in main User table
        if (userService.usernameExists(userTemp.getUserEmail())) {
            throw new AppException("This email is already registered in the system. Please use a different email or try logging in.", HttpStatus.BAD_REQUEST);
        }

        // Check if email already exists in UserTemp table and DELETE for testing
        Optional<UserTemp> existingUser = userTempRepository.findByUserEmail(userTemp.getUserEmail());
        if (existingUser.isPresent()){
            // DELETE the existing record to allow re-registration for testing
            userTempRepository.delete(existingUser.get());
            System.out.println("Deleted existing UserTemp record for email: " + userTemp.getUserEmail());
        }

        // Validate required fields
        if (userTemp.getMainName() == null || userTemp.getMainName().trim().isEmpty()) {
            throw new AppException("Main name is required", HttpStatus.BAD_REQUEST);
        }
        if (userTemp.getUserEmail() == null || userTemp.getUserEmail().trim().isEmpty()) {
            throw new AppException("Email is required", HttpStatus.BAD_REQUEST);
        }
        if (userTemp.getUserPassword() == null || userTemp.getUserPassword().trim().isEmpty()) {
            throw new AppException("Password is required", HttpStatus.BAD_REQUEST);
        }

        // Generate verification token (UUID)
        String verificationToken = UUID.randomUUID().toString();
        userTemp.setVerificationToken(verificationToken);

        // Set initial status
        userTemp.setUserTempStatus(UserTempStatus.CREATED);

        // Set creation date time
        userTemp.setCreateDateTime(LocalDateTime.now());

        // Encode the password
        String userPassword = userTemp.getUserPassword();
        userTemp.setUserPassword(passwordEncoder.encode(CharBuffer.wrap(userPassword)));

        // Save the UserTemp
        UserTemp savedUserTemp = userTempRepository.save(userTemp);

        // Send registration email
        String verificationTokenWithUserType = savedUserTemp.getVerificationToken() + "&userType=" + savedUserTemp.getUserTempType().toString();
           System.out.println("Sending verification email to: " + savedUserTemp.getUserEmail());
        System.out.println("Verification link: https://mydent-backend-erduerewh8gnhufp.australiaeast-01.azurewebsites.net/user/verify-user?token=" + verificationTokenWithUserType);
        String verificationLink = "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType;

        System.out.println(verificationLink);

        CompletableFuture.runAsync(() -> {
            try {
                emailService.sendRegistrationEmail(
                    savedUserTemp.getUserEmail(),
                    savedUserTemp.getMainName(),
                    "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType,
                    savedUserTemp.getUserTempType().toString()
                );
                System.out.println("Email sent successfully to: " + savedUserTemp.getUserEmail());
            } catch (Exception e) {
                System.err.println("Failed to send email to: " + savedUserTemp.getUserEmail());
                e.printStackTrace();
            }
        });

        return savedUserTemp;
    }

    public String approveUserTemp(Long userTempId){

        String responseText = "Saved Failed";
        // Check UserTemp Avb
        Optional<UserTemp> currentUserTempOpt = userTempRepository.findById(userTempId);
        if (currentUserTempOpt.isPresent()){

            // Gte the User Temp
            UserTemp currentUserTemp = currentUserTempOpt.get();

            // Verification Process Goes Here
            if (currentUserTemp.getUserTempStatus() == UserTempStatus.USER_VERIFIED){
                // 1. Update UserTemp as Admin Approved
                // 2. if it does insert to UserMaster
                // 3. if it does insert to according to the UserTempType
                currentUserTemp.setUserTempStatus(UserTempStatus.ADMIN_APPROVED);
                UserTemp updatedUserTemp = userTempRepository.save(currentUserTemp);

                // Get the Company & Category
                Company company = companyService.getCompanyById(1);
                UserCategory userCategory = userCategoryService.getUserCategoryByName(updatedUserTemp.getUserTempType().toString());

                if (company !=null && userCategory !=null){
                    // Save User
                    User savedUser = userService.saveUser(new User(updatedUserTemp.getMainName(), updatedUserTemp.getAdditionalName(), updatedUserTemp.getUserEmail(), updatedUserTemp.getUserPassword(), company, userCategory, UserStatus.ACTIVE, updatedUserTemp.getVerificationToken()));
                    if (savedUser !=null){

                        if (updatedUserTemp.getUserTempType() == UserTempType.DOCTOR){
                            Doctor doctor = new Doctor(savedUser, updatedUserTemp.getUserTitle(), updatedUserTemp.getMainName(), updatedUserTemp.getAdditionalName(), updatedUserTemp.getRegistrationNumber(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            doctorService.save(doctor);
                            responseText = "Doctor Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.CLINIC){
                            Clinic clinic = new Clinic(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            clinicService.save(clinic);
                            responseText = "Clinic Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.LABORATORY){
                            Laboratory laboratory = new Laboratory(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            laboratoryService.save(laboratory);
                            responseText = "Laboratory Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.SUPPLIER){
                            Supplier supplier = new Supplier(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactPersonDesignation(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            supplierService.save(supplier);
                            responseText = "Supplier Saved";
                        }

                    }
                }
            }

            // Decline Process Goes Here
            if (currentUserTemp.getUserTempStatus() == UserTempStatus.USER_VERIFIED){
            }
        }
        return responseText;
    }

    public List<UserTemp> getAllUserTemps(){
        return userTempRepository.findAll();
    }

    public String verifyUserTempEmail(String verificationToken) {
        // Find UserTemp by verification token
        Optional<UserTemp> userTempOpt = userTempRepository.findByVerificationToken(verificationToken);

        if (!userTempOpt.isPresent()) {
            return "Invalid or expired verification token.";
        }

        UserTemp userTemp = userTempOpt.get();

        // Check if already verified
        if (userTemp.getUserTempStatus() == UserTempStatus.USER_VERIFIED ||
            userTemp.getUserTempStatus() == UserTempStatus.ADMIN_APPROVED) {
           return "Your email is already verified and your account is active. You can now login to the system.";
        }

        // Update status to USER_VERIFIED
        userTemp.setUserTempStatus(UserTempStatus.USER_VERIFIED);
        userTempRepository.save(userTemp);

       // AUTO-APPROVE: Automatically create user account after email verification
        try {
            String approvalResult = this.approveUserTemp(userTemp.getUserTempId());
            if (approvalResult.contains("Saved")) {
                return "Email verified successfully! Your account has been activated. You can now login to the system.";
            } else {
                return "Email verified successfully! However, there was an issue activating your account. Please contact support.";
            }
        } catch (Exception e) {
            System.err.println("Error during auto-approval: " + e.getMessage());
            e.printStackTrace();
            return "Email verified successfully! Your account is under review and will be activated once approved by our team.";
        }
    }


}
