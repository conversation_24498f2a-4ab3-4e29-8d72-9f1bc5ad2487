package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.*;
import com.navitsa.mydent.enums.UserStatus;
import com.navitsa.mydent.enums.UserTempStatus;
import com.navitsa.mydent.enums.UserTempType;
import com.navitsa.mydent.exceptions.AppException;
import com.navitsa.mydent.repositories.UserTempRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.nio.CharBuffer;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class UserTempServices {
    private final UserTempRepository userTempRepository;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private UserCategoryService userCategoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private DoctorService doctorService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private LaboratoryService laboratoryService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private EmailService emailService;

    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserTempServices(UserTempRepository userTempRepository, PasswordEncoder passwordEncoder) {
        this.userTempRepository = userTempRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public UserTemp getUserTempByEmail(String userEmail){
        Optional<UserTemp> currentUser = userTempRepository.findByUserEmail(userEmail);
        return currentUser.orElse(null);
    }

    public UserTemp saveUserAsTemp(UserTemp userTemp){
        // COMMENTED OUT FOR TESTING - Allows same email to be used multiple times
        // Optional<UserTemp> currentUser = userTempRepository.findByUserEmail(userTemp.getUserEmail());

        // if (currentUser.isPresent()){
        //     throw new AppException("U Have Already Registered", HttpStatus.BAD_REQUEST);
        // }

        // Encode the password
        String userPassword = userTemp.getUserPassword();
        userTemp.setUserPassword(passwordEncoder.encode(CharBuffer.wrap(userPassword)));

        // Save the UserTemp
        UserTemp savedUserTemp = userTempRepository.save(userTemp);

        // Send registration email
        String verificationTokenWithUserType = savedUserTemp.getVerificationToken() + "&userType=" + savedUserTemp.getUserTempType().toString();
        CompletableFuture.runAsync(() ->
            emailService.sendRegistrationEmail(
                savedUserTemp.getUserEmail(),
                savedUserTemp.getMainName(),
                "http://localhost:4200/user/verify-user?token=" + verificationTokenWithUserType,
                savedUserTemp.getUserTempType().toString()
            )
        );

        return savedUserTemp;
    }

    public String approveUserTemp(Long userTempId){

        String responseText = "Saved Failed";
        // Check UserTemp Avb
        Optional<UserTemp> currentUserTempOpt = userTempRepository.findById(userTempId);
        if (currentUserTempOpt.isPresent()){

            // Gte the User Temp
            UserTemp currentUserTemp = currentUserTempOpt.get();

            // Verification Process Goes Here
            if (currentUserTemp.getUserTempStatus() == UserTempStatus.USER_VERIFIED){
                // 1. Update UserTemp as Admin Approved
                // 2. if it does insert to UserMaster
                // 3. if it does insert to according to the UserTempType
                currentUserTemp.setUserTempStatus(UserTempStatus.ADMIN_APPROVED);
                UserTemp updatedUserTemp = userTempRepository.save(currentUserTemp);

                // Get the Company & Category
                Company company = companyService.getCompanyById(1);
                UserCategory userCategory = userCategoryService.getUserCategoryByName(updatedUserTemp.getUserTempType().toString());

                if (company !=null && userCategory !=null){
                    // Save User
                    User savedUser = userService.saveUser(new User(updatedUserTemp.getMainName(), updatedUserTemp.getAdditionalName(), updatedUserTemp.getUserEmail(), updatedUserTemp.getUserPassword(), company, userCategory, UserStatus.ACTIVE, updatedUserTemp.getVerificationToken()));
                    if (savedUser !=null){

                        if (updatedUserTemp.getUserTempType() == UserTempType.DOCTOR){
                            Doctor doctor = new Doctor(savedUser, updatedUserTemp.getUserTitle(), updatedUserTemp.getMainName(), updatedUserTemp.getAdditionalName(), updatedUserTemp.getRegistrationNumber(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            doctorService.save(doctor);
                            responseText = "Doctor Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.CLINIC){
                            Clinic clinic = new Clinic(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            clinicService.save(clinic);
                            responseText = "Clinic Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.LABORATORY){
                            Laboratory laboratory = new Laboratory(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            laboratoryService.save(laboratory);
                            responseText = "Laboratory Saved";
                        }

                        if (updatedUserTemp.getUserTempType() == UserTempType.SUPPLIER){
                            Supplier supplier = new Supplier(savedUser, updatedUserTemp.getMainName(), updatedUserTemp.getAddress(), updatedUserTemp.getCity(), updatedUserTemp.getState(), updatedUserTemp.getDistrict(), updatedUserTemp.getContactPerson(), updatedUserTemp.getContactPersonDesignation(), updatedUserTemp.getContactNumber(), updatedUserTemp.getUserEmail());
                            supplierService.save(supplier);
                            responseText = "Supplier Saved";
                        }

                    }
                }
            }

            // Decline Process Goes Here
            if (currentUserTemp.getUserTempStatus() == UserTempStatus.USER_VERIFIED){
            }
        }
        return responseText;
    }

    public List<UserTemp> getAllUserTemps(){
        return userTempRepository.findAll();
    }

}
