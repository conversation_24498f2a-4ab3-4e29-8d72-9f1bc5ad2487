{"ast": null, "code": "export class Customer {\n  constructor() {\n    this.customerId = 0;\n    this.userId = 0;\n    this.firstName = '';\n    this.lastName = '';\n    this.address = '';\n    this.city = '';\n    this.state = '';\n    this.country = '';\n    this.telephone = '';\n    this.email = '';\n  }\n}", "map": {"version": 3, "names": ["Customer", "constructor", "customerId", "userId", "firstName", "lastName", "address", "city", "state", "country", "telephone", "email"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\modules\\appointments\\customer.ts"], "sourcesContent": ["export class Customer{\r\ncustomerId:number = 0;\r\nuserId:number=0;\r\nfirstName: string = '';\r\nlastName: string = '';\r\naddress: string = '';\r\ncity: string = '';\r\nstate: string = '';\r\ncountry: string = '';\r\ntelephone: string = '';\r\nemail: string = '';\r\n}\r\n\r\n"], "mappings": "AAAA,OAAM,MAAOA,QAAQ;EAArBC,YAAA;IACA,KAAAC,UAAU,GAAU,CAAC;IACrB,KAAAC,MAAM,GAAQ,CAAC;IACf,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,KAAK,GAAW,EAAE;EAClB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}