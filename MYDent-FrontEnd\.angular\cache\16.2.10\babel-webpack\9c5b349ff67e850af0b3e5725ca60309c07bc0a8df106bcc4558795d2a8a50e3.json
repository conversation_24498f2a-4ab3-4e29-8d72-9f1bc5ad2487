{"ast": null, "code": "import Swal from 'sweetalert2';\nimport { AppointmentDetailsDialogComponent } from '../clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../clinic.service\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../core/primary-action-button/primary-action-button.component\";\nfunction ClinicDashboardComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14)(2, \"h2\");\n    i0.ɵɵtext(3, \"Pending Appointments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 15)(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16)(10, \"h2\");\n    i0.ɵɵtext(11, \"Completed Appointments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getPendingAppointmentsCount());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"You have \", ctx_r0.getPendingAppointmentsCount(), \" pending appointments.\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getCompletedAppointmentsCount());\n  }\n}\nfunction ClinicDashboardComponent_ng_container_22_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function ClinicDashboardComponent_ng_container_22_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const appointment_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.completedAppoinment(appointment_r2));\n    });\n    i0.ɵɵtext(1, \"Complete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicDashboardComponent_ng_container_22_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ClinicDashboardComponent_ng_container_22_button_14_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const appointment_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.acceptAppointment(appointment_r2));\n    });\n    i0.ɵɵtext(1, \"No Show\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicDashboardComponent_ng_container_22_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵtext(1, \"Completed\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicDashboardComponent_ng_container_22_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵtext(1, \"No Showing\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClinicDashboardComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function ClinicDashboardComponent_ng_container_22_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const appointment_r2 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.openAppointmentDetails(appointment_r2));\n    });\n    i0.ɵɵelementStart(2, \"div\", 18)(3, \"div\")(4, \"h6\", 19);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"h6\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h6\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 21)(12, \"div\", 22);\n    i0.ɵɵtemplate(13, ClinicDashboardComponent_ng_container_22_button_13_Template, 2, 0, \"button\", 23);\n    i0.ɵɵtemplate(14, ClinicDashboardComponent_ng_container_22_button_14_Template, 2, 0, \"button\", 24);\n    i0.ɵɵtemplate(15, ClinicDashboardComponent_ng_container_22_button_15_Template, 2, 0, \"button\", 25);\n    i0.ɵɵtemplate(16, ClinicDashboardComponent_ng_container_22_button_16_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const appointment_r2 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", appointment_r2.firstName, \" \", appointment_r2.lastName, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", appointment_r2.fromTime, \" - \", appointment_r2.toTime, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"| \", appointment_r2.fromDate, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", appointment_r2.status == \"Pending\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r2.status == \"Pending\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r2.status == \"Completed\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", appointment_r2.status == \"NoShow\");\n  }\n}\nclass ClinicDashboardComponent {\n  constructor(clinicService, dialog, router, scheduleService) {\n    this.clinicService = clinicService;\n    this.dialog = dialog;\n    this.router = router;\n    this.scheduleService = scheduleService;\n    this.appointments = null;\n    this.todayAppointments = [];\n    this.selectedClinicAppointment = null;\n    this.selectedClinicAppointments = new Set();\n  }\n  ngOnInit() {\n    const userIdString = localStorage.getItem('userid'); // Retrieve userId from localStorage\n    if (userIdString) {\n      const userId = parseInt(userIdString, 10);\n      this.fetchAndStoreClinicId(userId); // Fetch and store clinicId\n    }\n\n    this.loadAppointments();\n  }\n  // Fetch appointments based on clinicId from local storage\n  loadAppointments() {\n    const companyIdString = localStorage.getItem('userid'); // Retrieve clinicId from localStorage\n    console.log('Company ID (string):', companyIdString);\n    if (companyIdString) {\n      const companyId = Number(companyIdString); // Convert the string to a number\n      console.log('Company ID (number):', companyId);\n      this.clinicService.getAppointmentsByClinicId(companyId).subscribe(data => {\n        console.log('Appointments data:', data); // Add this to check the response\n        this.appointments = data;\n        const todayStr = new Date().toISOString().split('T')[0]; // \"yyyy-MM-dd\"\n        this.todayAppointments = this.appointments.filter(app => {\n          if (!app.fromDate) return false; // skip if fromDate is missing\n          const appDateStr = app.fromDate.includes('T') ? app.fromDate.split('T')[0] : app.fromDate;\n          return appDateStr === todayStr;\n        });\n        console.log('Today Appointments:', this.todayAppointments);\n      }, error => {\n        console.error('Error fetching appointments:', error);\n      });\n    } else {\n      console.error('Company ID not found in local storage');\n    }\n  }\n  updateStatus(appointmentId, status) {\n    if (appointmentId === undefined) {\n      console.error('Appointment ID is undefined');\n      return; // Early exit to avoid making an invalid HTTP call\n    }\n\n    this.clinicService.updateAppointmentStatus(appointmentId, status).subscribe(() => {\n      console.log('Status updated successfully');\n    }, error => console.error(error));\n  }\n  acceptAppointment(appointment) {\n    if (!appointment || !appointment.appointmentId) {\n      console.error('Appointment is undefined or missing appointmentId.');\n      return;\n    }\n    // Open the appointment details in a dialog\n    const dialogRef = this.dialog.open(AppointmentDetailsDialogComponent, {\n      width: '400px',\n      data: appointment\n    });\n    // Handle the result after closing the dialog\n    dialogRef.afterClosed().subscribe(result => {\n      if (result === 'accept') {\n        Swal.fire({\n          title: 'Are you sure?',\n          text: 'Do you want to accept this appointment?',\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonColor: '#00C820',\n          cancelButtonColor: '#d33',\n          confirmButtonText: 'Yes, accept it!'\n        }).then(result => {\n          if (result.isConfirmed) {\n            this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Accepted').subscribe(response => {\n              console.log('Appointment accepted:', response);\n              appointment.status = 'Accepted';\n              this.loadAppointments();\n              Swal.fire('Accepted', 'The appointment has been accepted.', 'success');\n            }, error => {\n              console.log('Error accepting appointment:', error);\n            });\n          }\n        });\n      } else if (result === 'reject') {\n        // Reject the appointment\n        Swal.fire({\n          title: 'Are you sure?',\n          text: 'Do you want to reject this appointment?',\n          icon: 'warning',\n          showCancelButton: true,\n          confirmButtonColor: '#B93426',\n          cancelButtonColor: '#d33',\n          confirmButtonText: 'Yes, reject it!'\n        }).then(result => {\n          if (result.isConfirmed) {\n            this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Rejected').subscribe(response => {\n              console.log('Appointment rejected:', response);\n              appointment.status = 'Rejected';\n              this.loadAppointments();\n              Swal.fire('Rejected!', 'The appointment has been rejected.', 'success');\n            }, error => {\n              console.log('Error rejecting appointment:', error);\n            });\n          }\n        });\n      }\n    });\n  }\n  rejectAppointment(appointment) {\n    if (!appointment || !appointment.appointmentId) {\n      console.error('Appointment is undefined or missing appointmentId.');\n      return;\n    }\n    Swal.fire({\n      title: 'Are you sure?',\n      text: 'Do you want to reject this appointment?',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#B93426',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, reject it!'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Rejected').subscribe(response => {\n          console.log('Appointment rejected:', response);\n          appointment.status = 'Rejected';\n          this.loadAppointments();\n          Swal.fire('Rejected!', 'The appointment has been rejected.', 'success');\n        }, error => {\n          console.log('Error rejecting appointment:', error);\n        });\n      }\n    });\n  }\n  completedAppoinment(appointment) {\n    if (!appointment || !appointment.appointmentId) {\n      console.error('Appointment is undefined or missing appointmentId.');\n      return;\n    }\n    Swal.fire({\n      title: 'Are you sure?',\n      text: 'Do you want to Complete this appointment?',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#00C820',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, complete it!'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'Completed').subscribe(response => {\n          console.log('Appointment completed:', response);\n          appointment.status = 'Completed';\n          this.loadAppointments();\n          Swal.fire('Completed!', 'The appointment has been completed.', 'success');\n        }, error => {\n          console.log('Error completing appointment:', error);\n        });\n      }\n    });\n  }\n  noShowAppointment(appointment) {\n    if (!appointment || !appointment.appointmentId) {\n      console.error('Appointment is undefined or missing appointmentId.');\n      return;\n    }\n    Swal.fire({\n      title: 'Are you sure?',\n      text: 'Do you want to mark this appointment as No Show?',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#FFC107',\n      cancelButtonColor: '#d33',\n      confirmButtonText: 'Yes, mark as No Show!'\n    }).then(result => {\n      if (result.isConfirmed) {\n        this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'NoShow').subscribe(response => {\n          console.log('Appointment marked as No Show:', response);\n          appointment.status = 'No Show';\n          this.loadAppointments();\n          Swal.fire('No Show!', 'The appointment has been marked as No Show.', 'success');\n        }, error => {\n          console.log('Error marking appointment as No Show:', error);\n        });\n      }\n    });\n  }\n  confirmAppointment(appointment, event) {\n    event.stopPropagation(); // Prevents the event from bubbling up to the parent elements\n    appointment.isConfirmed = true;\n    // Add logic to handle the confirmation of the appointment\n  }\n\n  openAppointmentDetails(appointment) {\n    this.selectedClinicAppointment = appointment;\n  }\n  closeAppointmentDetails() {\n    this.selectedClinicAppointment = null;\n  }\n  toggleAppointmentSelection(id) {\n    if (this.selectedClinicAppointments.has(id)) {\n      this.selectedClinicAppointments.delete(id);\n    } else {\n      this.selectedClinicAppointments.add(id);\n    }\n  }\n  deleteSelectedAppointments() {\n    this.appointments = this.appointments.filter(a => !this.selectedClinicAppointments.has(a.id));\n    this.selectedClinicAppointments.clear();\n  }\n  getPendingAppointmentsCount() {\n    return this.appointments.filter(a => a.status === 'Pending').length;\n  }\n  getConfirmedAppointmentsCount() {\n    return this.appointments.filter(a => a.status === 'Accepted').length;\n  }\n  getCompletedAppointmentsCount() {\n    return this.appointments.filter(a => a.status === 'Completed').length;\n  }\n  goToCreateNewAppointment() {\n    this.router.navigate(['clinic/new-appointment']);\n  }\n  handleError(error) {\n    console.error('Error occurred:', error);\n    alert('An error occurred. Please try again.');\n  }\n  // Fetch and store clinicId from the backend\n  fetchAndStoreClinicId(userid) {\n    this.scheduleService.getClinicById(userid).subscribe(response => {\n      console.log('Received clinicId:', response.clinicId); // Debugging line\n      if (response) {\n        localStorage.setItem('clinicId', response.clinicId.toString());\n        console.log('Clinic ID stored in localStorage:', response);\n      } else {\n        this.handleError('No clinic found for the provided user ID');\n      }\n    }, error => {\n      this.handleError('Error fetching clinic ID: ' + error);\n    });\n  }\n  static #_ = this.ɵfac = function ClinicDashboardComponent_Factory(t) {\n    return new (t || ClinicDashboardComponent)(i0.ɵɵdirectiveInject(i1.ClinicService), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i1.ClinicService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClinicDashboardComponent,\n    selectors: [[\"app-clinic-dashboard\"]],\n    inputs: {\n      status: \"status\"\n    },\n    decls: 24,\n    vars: 3,\n    consts: [[1, \"row\"], [1, \"col-12\", \"g-0\"], [1, \"appointment-dashboard-container\"], [1, \"col-12\"], [1, \"col-6\"], [1, \"col-6\", \"position-relative\"], [\"buttonText\", \"New Appointment\", \"buttonUI\", \"secondary\", \"buttonType\", \"button\", 1, \"float-end\", 3, \"buttonClicked\"], [\"class\", \"appointments-summary\", 4, \"ngIf\"], [1, \"pending-appointments-notification\"], [1, \"pending-appointments-list\"], [1, \"appointment-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"pagination\"], [1, \"appointments-summary\"], [1, \"appointment-status\", \"pending\"], [1, \"pending-appointments-notification-middle\"], [1, \"appointment-status\", \"confirmed\"], [1, \"appointment-request\", 2, \"display\", \"flex\", 3, \"click\"], [1, \"appointment-info\"], [2, \"font-weight\", \"700\", \"font-size\", \"17px\"], [1, \"appointment-details\"], [1, \"appointment-actions\", 2, \"margin-right\", \"5px\"], [1, \"accept-reject-buttons\", 2, \"display\", \"flex\", \"width\", \"100%\", \"justify-content\", \"end\", \"flex-direction\", \"row\", \"gap\", \"20px\"], [\"style\", \"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;\", 4, \"ngIf\"], [\"style\", \"width: 120px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;\", 4, \"ngIf\"], [2, \"width\", \"100px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #00C820 , #0E6001)\", \"border\", \"none\", \"color\", \"white\", 3, \"click\"], [2, \"width\", \"100px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #FB751E , #B93426)\", \"border\", \"none\", \"color\", \"white\", 3, \"click\"], [2, \"width\", \"100px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #00C820 , #0E6001)\", \"border\", \"none\", \"color\", \"white\"], [2, \"width\", \"120px\", \"border-radius\", \"50px\", \"padding-block\", \"4px\", \"background\", \"linear-gradient(to right, #FB751E , #B93426)\", \"border\", \"none\", \"color\", \"white\"]],\n    template: function ClinicDashboardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 0)(4, \"div\", 3)(5, \"div\", 0)(6, \"div\", 4)(7, \"h2\");\n        i0.ɵɵtext(8, \"Appointments\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"app-primary-action-button\", 6);\n        i0.ɵɵlistener(\"buttonClicked\", function ClinicDashboardComponent_Template_app_primary_action_button_buttonClicked_10_listener() {\n          return ctx.goToCreateNewAppointment();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(11, \"hr\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(12, ClinicDashboardComponent_div_12_Template, 14, 3, \"div\", 7);\n        i0.ɵɵelementStart(13, \"div\", 8)(14, \"p\");\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 9)(17, \"h6\")(18, \"strong\");\n        i0.ɵɵtext(19, \"Pending Appointment Requests for Today\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(20, \"br\");\n        i0.ɵɵelementStart(21, \"div\", 10);\n        i0.ɵɵtemplate(22, ClinicDashboardComponent_ng_container_22_Template, 17, 9, \"ng-container\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(23, \"div\", 12);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngIf\", ctx.appointments.length);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"You have \", ctx.getPendingAppointmentsCount(), \" pending appointments.\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngForOf\", ctx.todayAppointments);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.PrimaryActionButtonComponent],\n    styles: [\"\\n\\n\\n\\n\\n\\n\\n\\n\\nh1[_ngcontent-%COMP%] {\\n  color: black;\\n}\\n\\n.show[_ngcontent-%COMP%]{\\n  display: block;\\n}\\n\\n\\n\\n\\n.appointments-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  margin-bottom: 20px;\\n}\\n\\n.appointment-status[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border-radius: 10px;\\n  color: white;\\n  flex: 1;\\n  margin: 0 10px;\\n  text-align: center;\\n}\\n\\n.appointment-status.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #CCAE0F 0%, #C89000 100%);\\n  margin-left: 0;\\n}\\n\\n.appointment-status.confirmed[_ngcontent-%COMP%] {\\n  background: linear-gradient(180deg, #0075FF 0%, #25233A 100%);\\n  margin-right: 0;\\n}\\n\\n.pending-appointments-notification[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  color: #E15524;\\n}\\n\\n.pending-appointments-notification-middle[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  margin-top: 20px;\\n  color: #E15524;\\n}\\n\\n\\n.pending-appointments-list[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  background-color: rgba(251, 117, 30, 0.88);\\n  color: white;\\n  padding: 10px;\\n  border-radius: 5px;\\n}\\n\\n\\n\\n\\n\\n.appointment-request[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  background-color: #F5F5F5;\\n  padding: 10px;\\n  gap: 20px;\\n  margin-bottom: 10px;\\n  border-radius: 5px;\\n  border-left: 3px solid #0075FF;\\n}\\n\\n.appointment-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n\\n.appointment-details[_ngcontent-%COMP%]{\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 25px;\\n  \\n}\\n\\n.appointment-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:first-child {\\n  font-weight: bold;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.appointment-info[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:first-child::before {\\n  content: ' ';\\n  width: 24px;\\n  height: 24px;\\n  margin-right: 8px;\\n}\\n\\n.appointment-info[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.appointment-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n\\n}\\n\\n.appointment-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 5px 10px;\\n  cursor: pointer;\\n}\\n\\n.confirm-btn[_ngcontent-%COMP%] {\\n  background-color: #0075FF12;\\n  color: #0075FF;\\n  border-radius: 7px;\\n  border: 1px solid #0075FF;\\n  margin-right: 10px;\\n}\\n\\n.reject-btn[_ngcontent-%COMP%] {\\n  background-color: #B934261F;\\n  color: #B93426;\\n  border-radius: 7px;\\n  border: 1px solid #B93426;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1050;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  outline: 0;\\n}\\n@media (max-width: 200px) {\\n  .popup-overlay[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.modal-dialog-centered[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  min-height: calc(100% - (.5rem * 2));\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  width: 100%;\\n  pointer-events: auto;\\n  background-color: #fff;\\n  background-clip: padding-box;\\n  border: 1px solid rgba(0, 0, 0, .2);\\n  border-radius: .3rem;\\n  outline: 0;\\n}\\n\\n.modal-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: space-between;\\n  padding: 1rem;\\n  border-bottom: 1px solid #dee2e6;\\n  border-top-left-radius: .3rem;\\n  border-top-right-radius: .3rem;\\n}\\n\\n.modal-body[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1 1 auto;\\n  padding: 1rem;\\n}\\n\\n.btn-close[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.25rem;\\n  color: #fff;\\n  background-color: #6c757d;\\n  border: none;\\n  cursor: pointer;\\n}\\n\\n\\n\\n\\n\\n@media (max-width: 800px) {\\n  \\n  .appointment-details-mobile[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-direction: column;\\n    gap: 15px;\\n    padding-left: 10px;\\n  }\\n  \\n  .appointment-info-mobile[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-direction: column;\\n    gap: 15px;\\n    \\n  }\\n \\n  .appointment-request[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-direction: column;\\n  }\\n  .accept-reject-buttons[_ngcontent-%COMP%]{\\n    display: flex;\\n    flex-wrap: wrap;\\n  }\\n}\\n\\n@media (min-width: 801px) {\\n  .appointment-info-mobile[_ngcontent-%COMP%]{\\n    display: none;\\n  }\\n  .appointment-dashboard-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .appointments-summary[_ngcontent-%COMP%] {\\n      flex-direction: column;\\n      align-items: center;\\n  }\\n  .appointment-status[_ngcontent-%COMP%] {\\n      margin: 0;\\n      width: 100%;\\n  }\\n  .appointment-status.pending[_ngcontent-%COMP%] {\\n      margin-top: 5px;\\n      \\n\\n  }\\n  \\n  .appointment-actions[_ngcontent-%COMP%] {\\n      flex-direction: column;\\n      align-items: flex-start;\\n  }\\n  .appointment-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n      width: 100%;\\n      margin-bottom: 5px;\\n  }\\n  .pending-appointments-notification[_ngcontent-%COMP%]{\\n    display: none;\\n  }\\n  .appointment-status.confirmed[_ngcontent-%COMP%]{\\n    margin-bottom: 20px;\\n  }\\n}\\n\\n@media (min-width: 769px) {\\n  .pending-appointments-notification-middle[_ngcontent-%COMP%]{\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 800px) {\\n  .appointment-list[_ngcontent-%COMP%]{\\n    padding: 10px;\\n  }\\n  \\n}\\n\\n@media (max-width: 480px) {\\n  .appointment-dashboard-container[_ngcontent-%COMP%] {\\n      padding: 10px;\\n  }\\n  .appointment-status[_ngcontent-%COMP%] {\\n      padding: 15px;\\n      font-size: 14px;\\n  }\\n  .pending-appointments-list[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n      font-size: 16px;\\n      padding: 8px;\\n  }\\n  .appointment-request[_ngcontent-%COMP%] {\\n      padding: 8px;\\n  }\\n  .appointment-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n      font-size: 14px;\\n      padding: 8px;\\n  }\\n  .modal-dialog-centered[_ngcontent-%COMP%] {\\n      min-height: auto;\\n  }\\n  .modal-content[_ngcontent-%COMP%] {\\n      border-radius: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport { ClinicDashboardComponent };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "AppointmentDetailsDialogComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "getPendingAppointmentsCount", "ɵɵtextInterpolate1", "getCompletedAppointmentsCount", "ɵɵlistener", "ClinicDashboardComponent_ng_container_22_button_13_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "appointment_r2", "ɵɵnextContext", "$implicit", "ctx_r8", "ɵɵresetView", "completedAppoinment", "ClinicDashboardComponent_ng_container_22_button_14_Template_button_click_0_listener", "_r13", "ctx_r11", "acceptAppointment", "ɵɵelementContainerStart", "ClinicDashboardComponent_ng_container_22_Template_div_click_1_listener", "restoredCtx", "_r15", "ctx_r14", "openAppointmentDetails", "ɵɵtemplate", "ClinicDashboardComponent_ng_container_22_button_13_Template", "ClinicDashboardComponent_ng_container_22_button_14_Template", "ClinicDashboardComponent_ng_container_22_button_15_Template", "ClinicDashboardComponent_ng_container_22_button_16_Template", "ɵɵelementContainerEnd", "ɵɵtextInterpolate2", "firstName", "lastName", "fromTime", "toTime", "fromDate", "ɵɵproperty", "status", "ClinicDashboardComponent", "constructor", "clinicService", "dialog", "router", "scheduleService", "appointments", "todayAppointments", "selectedClinicAppointment", "selectedClinicAppointments", "Set", "ngOnInit", "userIdString", "localStorage", "getItem", "userId", "parseInt", "fetchAndStoreClinicId", "loadAppointments", "companyIdString", "console", "log", "companyId", "Number", "getAppointmentsByClinicId", "subscribe", "data", "todayStr", "Date", "toISOString", "split", "filter", "app", "appDateStr", "includes", "error", "updateStatus", "appointmentId", "undefined", "updateAppointmentStatus", "appointment", "dialogRef", "open", "width", "afterClosed", "result", "fire", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "then", "isConfirmed", "response", "rejectAppointment", "noShowAppointment", "confirmAppointment", "event", "stopPropagation", "closeAppointmentDetails", "toggleAppointmentSelection", "id", "has", "delete", "add", "deleteSelectedAppointments", "a", "clear", "length", "getConfirmedAppointmentsCount", "goToCreateNewAppointment", "navigate", "handleError", "alert", "userid", "getClinicById", "clinicId", "setItem", "toString", "_", "ɵɵdirectiveInject", "i1", "ClinicService", "i2", "MatDialog", "i3", "Router", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ClinicDashboardComponent_Template", "rf", "ctx", "ClinicDashboardComponent_Template_app_primary_action_button_buttonClicked_10_listener", "ɵɵelement", "ClinicDashboardComponent_div_12_Template", "ClinicDashboardComponent_ng_container_22_Template"], "sources": ["E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\clinic-dashboard.component.ts", "E:\\Github\\MYDent-FrontEnd\\src\\app\\clinic\\clinic-dashboard\\clinic-dashboard.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { ClinicAppointment } from '../clinic';\r\nimport { ClinicService } from '../clinic.service';\r\nimport Swal from 'sweetalert2';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { AppointmentDetailsDialogComponent } from '../clinic-dashboard/appointment-popup/appointment-details-dialog/appointment-details-dialog.component';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-clinic-dashboard',\r\n  templateUrl: './clinic-dashboard.component.html',\r\n  styleUrls: ['./clinic-dashboard.component.css'],\r\n})\r\nexport class ClinicDashboardComponent implements OnInit {\r\n  @Input() status: any;\r\n\r\n  appointments: any = null;\r\n  todayAppointments: any[] = [];\r\n\r\n  selectedClinicAppointment: ClinicAppointment | null = null;\r\n  selectedClinicAppointments: Set<number> = new Set();\r\n\r\n  constructor(\r\n    private clinicService: ClinicService,\r\n    private dialog: MatDialog,\r\n    private router: Router,\r\n    private scheduleService: ClinicService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const userIdString = localStorage.getItem('userid'); // Retrieve userId from localStorage\r\n    if (userIdString) {\r\n      const userId = parseInt(userIdString, 10);\r\n      this.fetchAndStoreClinicId(userId); // Fetch and store clinicId\r\n    }\r\n    this.loadAppointments();\r\n  }\r\n\r\n  // Fetch appointments based on clinicId from local storage\r\n  loadAppointments() {\r\n    const companyIdString = localStorage.getItem('userid'); // Retrieve clinicId from localStorage\r\n    console.log('Company ID (string):', companyIdString);\r\n\r\n    if (companyIdString) {\r\n      const companyId = Number(companyIdString); // Convert the string to a number\r\n      console.log('Company ID (number):', companyId);\r\n      this.clinicService.getAppointmentsByClinicId(companyId).subscribe(\r\n        (data) => {\r\n          console.log('Appointments data:', data); // Add this to check the response\r\n          this.appointments = data;\r\n          const todayStr = new Date().toISOString().split('T')[0]; // \"yyyy-MM-dd\"\r\n\r\n          this.todayAppointments = this.appointments.filter((app: any) => {\r\n            if (!app.fromDate) return false; // skip if fromDate is missing\r\n            const appDateStr = app.fromDate.includes('T')\r\n              ? app.fromDate.split('T')[0]\r\n              : app.fromDate;\r\n            return appDateStr === todayStr;\r\n          });\r\n\r\n          console.log('Today Appointments:', this.todayAppointments);\r\n        },\r\n\r\n        (error: any) => {\r\n          console.error('Error fetching appointments:', error);\r\n        }\r\n      );\r\n    } else {\r\n      console.error('Company ID not found in local storage');\r\n    }\r\n  }\r\n\r\n  updateStatus(appointmentId: number, status: String): void {\r\n    if (appointmentId === undefined) {\r\n      console.error('Appointment ID is undefined');\r\n      return; // Early exit to avoid making an invalid HTTP call\r\n    }\r\n\r\n    this.clinicService.updateAppointmentStatus(appointmentId, status).subscribe(\r\n      () => {\r\n        console.log('Status updated successfully');\r\n      },\r\n      (error) => console.error(error)\r\n    );\r\n  }\r\n\r\n  acceptAppointment(appointment: any) {\r\n    if (!appointment || !appointment.appointmentId) {\r\n      console.error('Appointment is undefined or missing appointmentId.');\r\n      return;\r\n    }\r\n\r\n    // Open the appointment details in a dialog\r\n    const dialogRef = this.dialog.open(AppointmentDetailsDialogComponent, {\r\n      width: '400px',\r\n      data: appointment,\r\n    });\r\n\r\n    // Handle the result after closing the dialog\r\n    dialogRef.afterClosed().subscribe((result) => {\r\n      if (result === 'accept') {\r\n        Swal.fire({\r\n          title: 'Are you sure?',\r\n          text: 'Do you want to accept this appointment?',\r\n          icon: 'warning',\r\n          showCancelButton: true,\r\n          confirmButtonColor: '#00C820',\r\n          cancelButtonColor: '#d33',\r\n          confirmButtonText: 'Yes, accept it!',\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            this.clinicService\r\n              .updateAppointmentStatus(appointment.appointmentId, 'Accepted')\r\n              .subscribe(\r\n                (response) => {\r\n                  console.log('Appointment accepted:', response);\r\n                  appointment.status = 'Accepted';\r\n                  this.loadAppointments();\r\n                  Swal.fire(\r\n                    'Accepted',\r\n                    'The appointment has been accepted.',\r\n                    'success'\r\n                  );\r\n                },\r\n                (error) => {\r\n                  console.log('Error accepting appointment:', error);\r\n                }\r\n              );\r\n          }\r\n        });\r\n      } else if (result === 'reject') {\r\n        // Reject the appointment\r\n        Swal.fire({\r\n          title: 'Are you sure?',\r\n          text: 'Do you want to reject this appointment?',\r\n          icon: 'warning',\r\n          showCancelButton: true,\r\n          confirmButtonColor: '#B93426',\r\n          cancelButtonColor: '#d33',\r\n          confirmButtonText: 'Yes, reject it!',\r\n        }).then((result) => {\r\n          if (result.isConfirmed) {\r\n            this.clinicService\r\n              .updateAppointmentStatus(appointment.appointmentId, 'Rejected')\r\n              .subscribe(\r\n                (response) => {\r\n                  console.log('Appointment rejected:', response);\r\n                  appointment.status = 'Rejected';\r\n                  this.loadAppointments();\r\n                  Swal.fire(\r\n                    'Rejected!',\r\n                    'The appointment has been rejected.',\r\n                    'success'\r\n                  );\r\n                },\r\n                (error) => {\r\n                  console.log('Error rejecting appointment:', error);\r\n                }\r\n              );\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  rejectAppointment(appointment: any) {\r\n    if (!appointment || !appointment.appointmentId) {\r\n      console.error('Appointment is undefined or missing appointmentId.');\r\n      return;\r\n    }\r\n\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: 'Do you want to reject this appointment?',\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#B93426',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: 'Yes, reject it!',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.clinicService\r\n          .updateAppointmentStatus(appointment.appointmentId, 'Rejected')\r\n          .subscribe(\r\n            (response) => {\r\n              console.log('Appointment rejected:', response);\r\n              appointment.status = 'Rejected';\r\n              this.loadAppointments();\r\n              Swal.fire(\r\n                'Rejected!',\r\n                'The appointment has been rejected.',\r\n                'success'\r\n              );\r\n            },\r\n            (error) => {\r\n              console.log('Error rejecting appointment:', error);\r\n            }\r\n          );\r\n      }\r\n    });\r\n  }\r\n\r\n  completedAppoinment(appointment: any){\r\n    if (!appointment || !appointment.appointmentId) {\r\n      console.error('Appointment is undefined or missing appointmentId.');\r\n      return;\r\n    }\r\n\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: 'Do you want to Complete this appointment?',\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#00C820',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: 'Yes, complete it!',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.clinicService\r\n          .updateAppointmentStatus(appointment.appointmentId, 'Completed')\r\n          .subscribe(\r\n            (response) => {\r\n              console.log('Appointment completed:', response);\r\n              appointment.status = 'Completed';\r\n              this.loadAppointments();\r\n              Swal.fire(\r\n                'Completed!',\r\n                'The appointment has been completed.',\r\n                'success'\r\n              );\r\n            },\r\n            (error) => {\r\n              console.log('Error completing appointment:', error);\r\n            }\r\n          );\r\n      }\r\n    });\r\n  }\r\n\r\n  noShowAppointment(appointment: any){\r\n    if (!appointment || !appointment.appointmentId) {\r\n      console.error('Appointment is undefined or missing appointmentId.');\r\n      return;\r\n    }\r\n    Swal.fire({\r\n      title: 'Are you sure?',\r\n      text: 'Do you want to mark this appointment as No Show?',\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonColor: '#FFC107',\r\n      cancelButtonColor: '#d33',\r\n      confirmButtonText: 'Yes, mark as No Show!',\r\n    }).then((result) => {\r\n      if (result.isConfirmed) {\r\n        this.clinicService.updateAppointmentStatus(appointment.appointmentId, 'NoShow').subscribe(\r\n            (response) => {\r\n              console.log('Appointment marked as No Show:', response);\r\n              appointment.status = 'No Show';\r\n              this.loadAppointments();\r\n              Swal.fire(\r\n                'No Show!',\r\n                'The appointment has been marked as No Show.',\r\n                'success'\r\n              );\r\n            },\r\n            (error) => {\r\n              console.log('Error marking appointment as No Show:', error);\r\n            }\r\n          );\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n  confirmAppointment(appointment: ClinicAppointment, event: Event): void {\r\n    event.stopPropagation(); // Prevents the event from bubbling up to the parent elements\r\n    appointment.isConfirmed = true;\r\n    // Add logic to handle the confirmation of the appointment\r\n  }\r\n\r\n  openAppointmentDetails(appointment: ClinicAppointment): void {\r\n    this.selectedClinicAppointment = appointment;\r\n  }\r\n\r\n  closeAppointmentDetails(): void {\r\n    this.selectedClinicAppointment = null;\r\n  }\r\n\r\n  toggleAppointmentSelection(id: number): void {\r\n    if (this.selectedClinicAppointments.has(id)) {\r\n      this.selectedClinicAppointments.delete(id);\r\n    } else {\r\n      this.selectedClinicAppointments.add(id);\r\n    }\r\n  }\r\n\r\n  deleteSelectedAppointments(): void {\r\n    this.appointments = this.appointments.filter(\r\n      (a: { id: number }) => !this.selectedClinicAppointments.has(a.id)\r\n    );\r\n    this.selectedClinicAppointments.clear();\r\n  }\r\n\r\n  getPendingAppointmentsCount(): number {\r\n    return this.appointments.filter(\r\n      (a: { status: string }) => a.status === 'Pending'\r\n    ).length;\r\n  }\r\n\r\n  getConfirmedAppointmentsCount(): number {\r\n    return this.appointments.filter(\r\n      (a: { status: string }) => a.status === 'Accepted'\r\n    ).length;\r\n  }\r\n\r\n  getCompletedAppointmentsCount(): number {\r\n      return this.appointments.filter(\r\n      (a: { status: string }) => a.status === 'Completed'\r\n    ).length;\r\n  }\r\n\r\n  goToCreateNewAppointment() {\r\n    this.router.navigate(['clinic/new-appointment']);\r\n  }\r\n\r\n  handleError(error: any): void {\r\n    console.error('Error occurred:', error);\r\n    alert('An error occurred. Please try again.');\r\n  }\r\n\r\n  // Fetch and store clinicId from the backend\r\n  fetchAndStoreClinicId(userid: number): void {\r\n    this.scheduleService.getClinicById(userid).subscribe(\r\n      (response) => {\r\n        console.log('Received clinicId:', response.clinicId); // Debugging line\r\n        if (response) {\r\n          localStorage.setItem('clinicId', response.clinicId.toString());\r\n          console.log('Clinic ID stored in localStorage:', response);\r\n        } else {\r\n          this.handleError('No clinic found for the provided user ID');\r\n        }\r\n      },\r\n      (error) => {\r\n        this.handleError('Error fetching clinic ID: ' + error);\r\n      }\r\n    );\r\n  }\r\n}\r\n", "<div class=\"row\">\r\n  <div class=\"col-12 g-0\">\r\n    <div class=\"appointment-dashboard-container \">\r\n        <div class=\"row\">\r\n            <div class=\"col-12\">\r\n                <div class=\"row\">\r\n                    <div class=\"col-6\">\r\n                        <h2>Appointments</h2>\r\n                    </div>\r\n                    <div class=\"col-6 position-relative\">\r\n                        <app-primary-action-button\r\n                            class=\"float-end\"\r\n                            (buttonClicked)=\"goToCreateNewAppointment()\"\r\n                            buttonText=\"New Appointment\"\r\n                            buttonUI=\"secondary\"\r\n                            buttonType=\"button\"\r\n                        >\r\n                        </app-primary-action-button>\r\n                    </div>\r\n                </div>\r\n                <hr>\r\n            </div>\r\n        </div>\r\n\r\n      <div class=\"appointments-summary\" *ngIf=\"appointments.length\">\r\n          <div class=\"appointment-status pending\">\r\n              <h2>Pending Appointments</h2>\r\n              <p>{{ getPendingAppointmentsCount() }}</p>\r\n          </div>\r\n          <div class=\"pending-appointments-notification-middle\">\r\n            <p>You have {{ getPendingAppointmentsCount() }} pending appointments.</p>\r\n        </div>\r\n          <div class=\"appointment-status confirmed\">\r\n              <h2>Completed Appointments</h2>\r\n              <p>{{ getCompletedAppointmentsCount() }}</p>\r\n          </div>\r\n      </div>\r\n\r\n      <div class=\"pending-appointments-notification\">\r\n          <p>You have {{ getPendingAppointmentsCount() }} pending appointments.</p>\r\n      </div>\r\n      <div class=\"pending-appointments-list\">\r\n          <h6><strong>Pending Appointment Requests for Today</strong></h6>\r\n      </div>\r\n      <br>\r\n      <div class=\"appointment-list\">\r\n        <ng-container *ngFor=\"let appointment of todayAppointments; let i = index\">\r\n            <div   class=\"appointment-request\" (click)=\"openAppointmentDetails(appointment)\" style=\"display: flex;\">\r\n                <div class=\"appointment-info\">\r\n                    <div ><h6 style=\"font-weight: 700; font-size: 17px;\">{{ appointment.firstName }} {{ appointment.lastName }}</h6></div>\r\n                    <div class=\"appointment-details\">\r\n                        <h6> {{ appointment.fromTime }} - {{ appointment.toTime }}</h6>\r\n                        <h6>| {{ appointment.fromDate }}</h6>\r\n                        <!-- <span>{{ appointment.timeAgo }}</span> -->\r\n                        <!-- <h6>| {{ appointment.address }}</h6>\r\n                        <h6>| {{appointment.telephone }}</h6> -->\r\n                    </div>\r\n                </div>\r\n\r\n                \r\n                <div class=\"appointment-actions\" style=\" margin-right: 5px;\">\r\n                    <div style=\"display: flex; width: 100%; justify-content: end; flex-direction: row;  gap: 20px; \" class=\"accept-reject-buttons\">\r\n                        <!-- <button *ngIf=\"appointment.status == 'Pending'\" style=\"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;\" (click)=\"acceptAppointment(appointment)\">Accept</button>\r\n                        <button *ngIf=\"appointment.status == 'Pending'\" style=\"width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;\"  (click)=\"rejectAppointment(appointment)\">Reject</button> -->\r\n                        <button *ngIf=\"appointment.status == 'Pending'\" style=\"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;\" (click)=\"completedAppoinment(appointment)\">Complete</button>\r\n                        <button *ngIf=\"appointment.status == 'Pending'\" style=\"width: 100px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;\"  (click)=\"acceptAppointment(appointment)\">No Show</button>\r\n\r\n                        <button *ngIf=\"appointment.status == 'Completed'\" style=\"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white;\">Completed</button>\r\n                        <button *ngIf=\"appointment.status == 'NoShow'\" style=\"width: 120px; border-radius: 50px; padding-block: 4px;  background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white;\">No Showing</button>\r\n\r\n\r\n                        <!-- <button *ngIf=\"appointment.status == 'Rejected'\"  style=\"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #FB751E , #B93426); border: none; color: white; cursor:default;\" >Rejected</button>\r\n                        <button *ngIf=\"appointment.status == 'Accepted'\"  style=\"width: 100px; border-radius: 50px; padding-block: 4px; background: linear-gradient(to right, #00C820 , #0E6001); border: none; color: white; cursor:default;\" >Accepted</button> -->\r\n                    </div>\r\n                    <!-- Display 'Accepted' or 'Rejected' after updating the status -->\r\n            \r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n    </div>\r\n\r\n      <div class=\"pagination\">\r\n          <!-- Implement pagination controls here -->\r\n      </div>\r\n  </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAGA,OAAOA,IAAI,MAAM,aAAa;AAE9B,SAASC,iCAAiC,QAAQ,uGAAuG;;;;;;;;;ICmBnJC,EAAA,CAAAC,cAAA,cAA8D;IAElDD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9CH,EAAA,CAAAC,cAAA,cAAsD;IACjDD,EAAA,CAAAE,MAAA,GAAkE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE3EH,EAAA,CAAAC,cAAA,cAA0C;IAClCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAPzCH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,2BAAA,GAAmC;IAGrCP,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAQ,kBAAA,cAAAF,MAAA,CAAAC,2BAAA,6BAAkE;IAIhEP,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAG,6BAAA,GAAqC;;;;;;IA8B9BT,EAAA,CAAAC,cAAA,iBAAgP;IAA3CD,EAAA,CAAAU,UAAA,mBAAAC,oFAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,cAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAD,MAAA,CAAAE,mBAAA,CAAAL,cAAA,CAAgC;IAAA,EAAC;IAACd,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACjQH,EAAA,CAAAC,cAAA,iBAAgP;IAAzCD,EAAA,CAAAU,UAAA,mBAAAU,oFAAA;MAAApB,EAAA,CAAAY,aAAA,CAAAS,IAAA;MAAA,MAAAP,cAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,SAAA;MAAA,MAAAM,OAAA,GAAAtB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAI,OAAA,CAAAC,iBAAA,CAAAT,cAAA,CAA8B;IAAA,EAAC;IAACd,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAEhQH,EAAA,CAAAC,cAAA,iBAAuM;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IACzNH,EAAA,CAAAC,cAAA,iBAAqM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAtBxOH,EAAA,CAAAwB,uBAAA,GAA2E;IACvExB,EAAA,CAAAC,cAAA,cAAwG;IAArED,EAAA,CAAAU,UAAA,mBAAAe,uEAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAAY,aAAA,CAAAe,IAAA;MAAA,MAAAb,cAAA,GAAAY,WAAA,CAAAV,SAAA;MAAA,MAAAY,OAAA,GAAA5B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAU,OAAA,CAAAC,sBAAA,CAAAf,cAAA,CAAmC;IAAA,EAAC;IAC5Ed,EAAA,CAAAC,cAAA,cAA8B;IAC2BD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChHH,EAAA,CAAAC,cAAA,cAAiC;IACxBD,EAAA,CAAAE,MAAA,GAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAQ7CH,EAAA,CAAAC,cAAA,eAA6D;IAIrDD,EAAA,CAAA8B,UAAA,KAAAC,2DAAA,qBAAiQ;IACjQ/B,EAAA,CAAA8B,UAAA,KAAAE,2DAAA,qBAAgQ;IAEhQhC,EAAA,CAAA8B,UAAA,KAAAG,2DAAA,qBAAyN;IACzNjC,EAAA,CAAA8B,UAAA,KAAAI,2DAAA,qBAAwN;IAK5NlC,EAAA,CAAAG,YAAA,EAAM;IAKlBH,EAAA,CAAAmC,qBAAA,EAAe;;;;IA7BkDnC,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAoC,kBAAA,KAAAtB,cAAA,CAAAuB,SAAA,OAAAvB,cAAA,CAAAwB,QAAA,KAAsD;IAElGtC,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAoC,kBAAA,MAAAtB,cAAA,CAAAyB,QAAA,SAAAzB,cAAA,CAAA0B,MAAA,KAAqD;IACtDxC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAQ,kBAAA,OAAAM,cAAA,CAAA2B,QAAA,KAA4B;IAYvBzC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA0C,UAAA,SAAA5B,cAAA,CAAA6B,MAAA,cAAqC;IACrC3C,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA0C,UAAA,SAAA5B,cAAA,CAAA6B,MAAA,cAAqC;IAErC3C,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAA0C,UAAA,SAAA5B,cAAA,CAAA6B,MAAA,gBAAuC;IACvC3C,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA0C,UAAA,SAAA5B,cAAA,CAAA6B,MAAA,aAAoC;;;AD5DrE,MAKaC,wBAAwB;EASnCC,YACUC,aAA4B,EAC5BC,MAAiB,EACjBC,MAAc,EACdC,eAA8B;IAH9B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAVzB,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,iBAAiB,GAAU,EAAE;IAE7B,KAAAC,yBAAyB,GAA6B,IAAI;IAC1D,KAAAC,0BAA0B,GAAgB,IAAIC,GAAG,EAAE;EAOhD;EAEHC,QAAQA,CAAA;IACN,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrD,IAAIF,YAAY,EAAE;MAChB,MAAMG,MAAM,GAAGC,QAAQ,CAACJ,YAAY,EAAE,EAAE,CAAC;MACzC,IAAI,CAACK,qBAAqB,CAACF,MAAM,CAAC,CAAC,CAAC;;;IAEtC,IAAI,CAACG,gBAAgB,EAAE;EACzB;EAEA;EACAA,gBAAgBA,CAAA;IACd,MAAMC,eAAe,GAAGN,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxDM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,eAAe,CAAC;IAEpD,IAAIA,eAAe,EAAE;MACnB,MAAMG,SAAS,GAAGC,MAAM,CAACJ,eAAe,CAAC,CAAC,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,SAAS,CAAC;MAC9C,IAAI,CAACpB,aAAa,CAACsB,yBAAyB,CAACF,SAAS,CAAC,CAACG,SAAS,CAC9DC,IAAI,IAAI;QACPN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,IAAI,CAAC,CAAC,CAAC;QACzC,IAAI,CAACpB,YAAY,GAAGoB,IAAI;QACxB,MAAMC,QAAQ,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,CAACvB,iBAAiB,GAAG,IAAI,CAACD,YAAY,CAACyB,MAAM,CAAEC,GAAQ,IAAI;UAC7D,IAAI,CAACA,GAAG,CAACnC,QAAQ,EAAE,OAAO,KAAK,CAAC,CAAC;UACjC,MAAMoC,UAAU,GAAGD,GAAG,CAACnC,QAAQ,CAACqC,QAAQ,CAAC,GAAG,CAAC,GACzCF,GAAG,CAACnC,QAAQ,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC1BE,GAAG,CAACnC,QAAQ;UAChB,OAAOoC,UAAU,KAAKN,QAAQ;QAChC,CAAC,CAAC;QAEFP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACd,iBAAiB,CAAC;MAC5D,CAAC,EAEA4B,KAAU,IAAI;QACbf,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CACF;KACF,MAAM;MACLf,OAAO,CAACe,KAAK,CAAC,uCAAuC,CAAC;;EAE1D;EAEAC,YAAYA,CAACC,aAAqB,EAAEtC,MAAc;IAChD,IAAIsC,aAAa,KAAKC,SAAS,EAAE;MAC/BlB,OAAO,CAACe,KAAK,CAAC,6BAA6B,CAAC;MAC5C,OAAO,CAAC;;;IAGV,IAAI,CAACjC,aAAa,CAACqC,uBAAuB,CAACF,aAAa,EAAEtC,MAAM,CAAC,CAAC0B,SAAS,CACzE,MAAK;MACHL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC5C,CAAC,EACAc,KAAK,IAAKf,OAAO,CAACe,KAAK,CAACA,KAAK,CAAC,CAChC;EACH;EAEAxD,iBAAiBA,CAAC6D,WAAgB;IAChC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACH,aAAa,EAAE;MAC9CjB,OAAO,CAACe,KAAK,CAAC,oDAAoD,CAAC;MACnE;;IAGF;IACA,MAAMM,SAAS,GAAG,IAAI,CAACtC,MAAM,CAACuC,IAAI,CAACvF,iCAAiC,EAAE;MACpEwF,KAAK,EAAE,OAAO;MACdjB,IAAI,EAAEc;KACP,CAAC;IAEF;IACAC,SAAS,CAACG,WAAW,EAAE,CAACnB,SAAS,CAAEoB,MAAM,IAAI;MAC3C,IAAIA,MAAM,KAAK,QAAQ,EAAE;QACvB3F,IAAI,CAAC4F,IAAI,CAAC;UACRC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE,yCAAyC;UAC/CC,IAAI,EAAE,SAAS;UACfC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,SAAS;UAC7BC,iBAAiB,EAAE,MAAM;UACzBC,iBAAiB,EAAE;SACpB,CAAC,CAACC,IAAI,CAAET,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACU,WAAW,EAAE;YACtB,IAAI,CAACrD,aAAa,CACfqC,uBAAuB,CAACC,WAAW,CAACH,aAAa,EAAE,UAAU,CAAC,CAC9DZ,SAAS,CACP+B,QAAQ,IAAI;cACXpC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,QAAQ,CAAC;cAC9ChB,WAAW,CAACzC,MAAM,GAAG,UAAU;cAC/B,IAAI,CAACmB,gBAAgB,EAAE;cACvBhE,IAAI,CAAC4F,IAAI,CACP,UAAU,EACV,oCAAoC,EACpC,SAAS,CACV;YACH,CAAC,EACAX,KAAK,IAAI;cACRf,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEc,KAAK,CAAC;YACpD,CAAC,CACF;;QAEP,CAAC,CAAC;OACH,MAAM,IAAIU,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA3F,IAAI,CAAC4F,IAAI,CAAC;UACRC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE,yCAAyC;UAC/CC,IAAI,EAAE,SAAS;UACfC,gBAAgB,EAAE,IAAI;UACtBC,kBAAkB,EAAE,SAAS;UAC7BC,iBAAiB,EAAE,MAAM;UACzBC,iBAAiB,EAAE;SACpB,CAAC,CAACC,IAAI,CAAET,MAAM,IAAI;UACjB,IAAIA,MAAM,CAACU,WAAW,EAAE;YACtB,IAAI,CAACrD,aAAa,CACfqC,uBAAuB,CAACC,WAAW,CAACH,aAAa,EAAE,UAAU,CAAC,CAC9DZ,SAAS,CACP+B,QAAQ,IAAI;cACXpC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,QAAQ,CAAC;cAC9ChB,WAAW,CAACzC,MAAM,GAAG,UAAU;cAC/B,IAAI,CAACmB,gBAAgB,EAAE;cACvBhE,IAAI,CAAC4F,IAAI,CACP,WAAW,EACX,oCAAoC,EACpC,SAAS,CACV;YACH,CAAC,EACAX,KAAK,IAAI;cACRf,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEc,KAAK,CAAC;YACpD,CAAC,CACF;;QAEP,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAsB,iBAAiBA,CAACjB,WAAgB;IAChC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACH,aAAa,EAAE;MAC9CjB,OAAO,CAACe,KAAK,CAAC,oDAAoD,CAAC;MACnE;;IAGFjF,IAAI,CAAC4F,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,yCAAyC;MAC/CC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAET,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACU,WAAW,EAAE;QACtB,IAAI,CAACrD,aAAa,CACfqC,uBAAuB,CAACC,WAAW,CAACH,aAAa,EAAE,UAAU,CAAC,CAC9DZ,SAAS,CACP+B,QAAQ,IAAI;UACXpC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmC,QAAQ,CAAC;UAC9ChB,WAAW,CAACzC,MAAM,GAAG,UAAU;UAC/B,IAAI,CAACmB,gBAAgB,EAAE;UACvBhE,IAAI,CAAC4F,IAAI,CACP,WAAW,EACX,oCAAoC,EACpC,SAAS,CACV;QACH,CAAC,EACAX,KAAK,IAAI;UACRf,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEc,KAAK,CAAC;QACpD,CAAC,CACF;;IAEP,CAAC,CAAC;EACJ;EAEA5D,mBAAmBA,CAACiE,WAAgB;IAClC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACH,aAAa,EAAE;MAC9CjB,OAAO,CAACe,KAAK,CAAC,oDAAoD,CAAC;MACnE;;IAGFjF,IAAI,CAAC4F,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,2CAA2C;MACjDC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAET,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACU,WAAW,EAAE;QACtB,IAAI,CAACrD,aAAa,CACfqC,uBAAuB,CAACC,WAAW,CAACH,aAAa,EAAE,WAAW,CAAC,CAC/DZ,SAAS,CACP+B,QAAQ,IAAI;UACXpC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmC,QAAQ,CAAC;UAC/ChB,WAAW,CAACzC,MAAM,GAAG,WAAW;UAChC,IAAI,CAACmB,gBAAgB,EAAE;UACvBhE,IAAI,CAAC4F,IAAI,CACP,YAAY,EACZ,qCAAqC,EACrC,SAAS,CACV;QACH,CAAC,EACAX,KAAK,IAAI;UACRf,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEc,KAAK,CAAC;QACrD,CAAC,CACF;;IAEP,CAAC,CAAC;EACJ;EAEAuB,iBAAiBA,CAAClB,WAAgB;IAChC,IAAI,CAACA,WAAW,IAAI,CAACA,WAAW,CAACH,aAAa,EAAE;MAC9CjB,OAAO,CAACe,KAAK,CAAC,oDAAoD,CAAC;MACnE;;IAEFjF,IAAI,CAAC4F,IAAI,CAAC;MACRC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,kDAAkD;MACxDC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE,MAAM;MACzBC,iBAAiB,EAAE;KACpB,CAAC,CAACC,IAAI,CAAET,MAAM,IAAI;MACjB,IAAIA,MAAM,CAACU,WAAW,EAAE;QACtB,IAAI,CAACrD,aAAa,CAACqC,uBAAuB,CAACC,WAAW,CAACH,aAAa,EAAE,QAAQ,CAAC,CAACZ,SAAS,CACpF+B,QAAQ,IAAI;UACXpC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmC,QAAQ,CAAC;UACvDhB,WAAW,CAACzC,MAAM,GAAG,SAAS;UAC9B,IAAI,CAACmB,gBAAgB,EAAE;UACvBhE,IAAI,CAAC4F,IAAI,CACP,UAAU,EACV,6CAA6C,EAC7C,SAAS,CACV;QACH,CAAC,EACAX,KAAK,IAAI;UACRf,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEc,KAAK,CAAC;QAC7D,CAAC,CACF;;IAEP,CAAC,CAAC;EACJ;EAGAwB,kBAAkBA,CAACnB,WAA8B,EAAEoB,KAAY;IAC7DA,KAAK,CAACC,eAAe,EAAE,CAAC,CAAC;IACzBrB,WAAW,CAACe,WAAW,GAAG,IAAI;IAC9B;EACF;;EAEAtE,sBAAsBA,CAACuD,WAA8B;IACnD,IAAI,CAAChC,yBAAyB,GAAGgC,WAAW;EAC9C;EAEAsB,uBAAuBA,CAAA;IACrB,IAAI,CAACtD,yBAAyB,GAAG,IAAI;EACvC;EAEAuD,0BAA0BA,CAACC,EAAU;IACnC,IAAI,IAAI,CAACvD,0BAA0B,CAACwD,GAAG,CAACD,EAAE,CAAC,EAAE;MAC3C,IAAI,CAACvD,0BAA0B,CAACyD,MAAM,CAACF,EAAE,CAAC;KAC3C,MAAM;MACL,IAAI,CAACvD,0BAA0B,CAAC0D,GAAG,CAACH,EAAE,CAAC;;EAE3C;EAEAI,0BAA0BA,CAAA;IACxB,IAAI,CAAC9D,YAAY,GAAG,IAAI,CAACA,YAAY,CAACyB,MAAM,CACzCsC,CAAiB,IAAK,CAAC,IAAI,CAAC5D,0BAA0B,CAACwD,GAAG,CAACI,CAAC,CAACL,EAAE,CAAC,CAClE;IACD,IAAI,CAACvD,0BAA0B,CAAC6D,KAAK,EAAE;EACzC;EAEA3G,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAAC2C,YAAY,CAACyB,MAAM,CAC5BsC,CAAqB,IAAKA,CAAC,CAACtE,MAAM,KAAK,SAAS,CAClD,CAACwE,MAAM;EACV;EAEAC,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAAClE,YAAY,CAACyB,MAAM,CAC5BsC,CAAqB,IAAKA,CAAC,CAACtE,MAAM,KAAK,UAAU,CACnD,CAACwE,MAAM;EACV;EAEA1G,6BAA6BA,CAAA;IACzB,OAAO,IAAI,CAACyC,YAAY,CAACyB,MAAM,CAC9BsC,CAAqB,IAAKA,CAAC,CAACtE,MAAM,KAAK,WAAW,CACpD,CAACwE,MAAM;EACV;EAEAE,wBAAwBA,CAAA;IACtB,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;EAClD;EAEAC,WAAWA,CAACxC,KAAU;IACpBf,OAAO,CAACe,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACvCyC,KAAK,CAAC,sCAAsC,CAAC;EAC/C;EAEA;EACA3D,qBAAqBA,CAAC4D,MAAc;IAClC,IAAI,CAACxE,eAAe,CAACyE,aAAa,CAACD,MAAM,CAAC,CAACpD,SAAS,CACjD+B,QAAQ,IAAI;MACXpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmC,QAAQ,CAACuB,QAAQ,CAAC,CAAC,CAAC;MACtD,IAAIvB,QAAQ,EAAE;QACZ3C,YAAY,CAACmE,OAAO,CAAC,UAAU,EAAExB,QAAQ,CAACuB,QAAQ,CAACE,QAAQ,EAAE,CAAC;QAC9D7D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmC,QAAQ,CAAC;OAC3D,MAAM;QACL,IAAI,CAACmB,WAAW,CAAC,0CAA0C,CAAC;;IAEhE,CAAC,EACAxC,KAAK,IAAI;MACR,IAAI,CAACwC,WAAW,CAAC,4BAA4B,GAAGxC,KAAK,CAAC;IACxD,CAAC,CACF;EACH;EAAC,QAAA+C,CAAA,G;qBA7UUlF,wBAAwB,EAAA5C,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAArI,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAK,EAAA,G;UAAxB1F,wBAAwB;IAAA2F,SAAA;IAAAC,MAAA;MAAA7F,MAAA;IAAA;IAAA8F,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbrC9I,EAAA,CAAAC,cAAA,aAAiB;QAOWD,EAAA,CAAAE,MAAA,mBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEzBH,EAAA,CAAAC,cAAA,aAAqC;QAG7BD,EAAA,CAAAU,UAAA,2BAAAsI,sFAAA;UAAA,OAAiBD,GAAA,CAAA1B,wBAAA,EAA0B;QAAA,EAAC;QAKhDrH,EAAA,CAAAG,YAAA,EAA4B;QAGpCH,EAAA,CAAAiJ,SAAA,UAAI;QACRjJ,EAAA,CAAAG,YAAA,EAAM;QAGZH,EAAA,CAAA8B,UAAA,KAAAoH,wCAAA,kBAYM;QAENlJ,EAAA,CAAAC,cAAA,cAA+C;QACxCD,EAAA,CAAAE,MAAA,IAAkE;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAE7EH,EAAA,CAAAC,cAAA,cAAuC;QACvBD,EAAA,CAAAE,MAAA,8CAAsC;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAE/DH,EAAA,CAAAiJ,SAAA,UAAI;QACJjJ,EAAA,CAAAC,cAAA,eAA8B;QAC5BD,EAAA,CAAA8B,UAAA,KAAAqH,iDAAA,4BAgCe;QACnBnJ,EAAA,CAAAG,YAAA,EAAM;QAEJH,EAAA,CAAAiJ,SAAA,eAEM;QACVjJ,EAAA,CAAAG,YAAA,EAAM;;;QA5DiCH,EAAA,CAAAI,SAAA,IAAyB;QAAzBJ,EAAA,CAAA0C,UAAA,SAAAqG,GAAA,CAAA7F,YAAA,CAAAiE,MAAA,CAAyB;QAerDnH,EAAA,CAAAI,SAAA,GAAkE;QAAlEJ,EAAA,CAAAQ,kBAAA,cAAAuI,GAAA,CAAAxI,2BAAA,6BAAkE;QAOjCP,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAA0C,UAAA,YAAAqG,GAAA,CAAA5F,iBAAA,CAAsB;;;;;;;SDjCvDP,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}