package com.navitsa.mydent.entity;

import java.util.Set;

import jakarta.persistence.*;

@Entity
@Table(name= "customer")
public class Customer{
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "customer_id")
	private Integer customerId;

	@ManyToOne
	@JoinColumn(referencedColumnName ="user_id", name = "user_id")
	private User user;
	
	@Column(name= "first_name")
	private String firstName;
	
	@Column(name= "last_name")
	private String lastName;
	
	@Column(name="address")
	private String address;
	
	@Column(name="city")
	private String city;
	
	@Column(name="state")
	private String state;
	
	@Column(name="country")
	private String country;
	
	@Column(name="telephone")
	private String telephone;
	
	@Column(name="email")
	private String email;
	
	@Column(name= "registered_date")
	private String registeredDate;
	
	@Column(name= "latitude")
	private String latitude;
	
	@Column(name= "longitude")
	private String longitude;

    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL)
    private Set<Appointments> appointments;


	public Customer() {

	}
	public Customer(User user, String firstName, String lastName, String address, String city, String state, String country, String telephone, String email, String registeredDate, String latitude, String longitude) {
		this.user = user;
		this.firstName = firstName;
		this.lastName = lastName;
		this.address = address;
		this.city = city;
		this.state = state;
		this.country = country;
		this.telephone = telephone;
		this.email = email;
		this.registeredDate = registeredDate;
		this.latitude = latitude;
		this.longitude = longitude;
	}




	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getRegisteredDate() {
		return registeredDate;
	}

	public void setRegisteredDate(String registeredDate) {
		this.registeredDate = registeredDate;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}


	
}

