package com.navitsa.mydent.entity;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import jakarta.persistence.*;

@Entity
@Table(name = "laboratory_sub_categories")
public class LaboratorySubCategories {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "laboratory_sub_category_id")
    private Integer laboratorySubCategoryId;

    @Column(name = "laboratory_sub_category_name")
    private String laboratorySubCategoryName;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "laboratory_category_id", referencedColumnName = "laboratory_category_id")
    private LaboratoryCategories laboratoryCategoryId;

    public LaboratorySubCategories() {}

    public LaboratorySubCategories(Integer laboratorySubCategoryId, String laboratorySubCategoryName, LaboratoryCategories laboratoryCategoryId) {
        super();
        this.laboratorySubCategoryId = laboratorySubCategoryId;
        this.laboratorySubCategoryName = laboratorySubCategoryName;
        this.laboratoryCategoryId = laboratoryCategoryId;
    }

    public Integer getLaboratorySubCategoryId() {
        return laboratorySubCategoryId;
    }

    public void setLaboratorySubCategoryId(Integer laboratorySubCategoryId) {
        this.laboratorySubCategoryId = laboratorySubCategoryId;
    }

    public String getLaboratorySubCategoryName() {
        return laboratorySubCategoryName;
    }

    public void setLaboratorySubCategoryName(String laboratorySubCategoryName) {
        this.laboratorySubCategoryName = laboratorySubCategoryName;
    }
    
    public LaboratoryCategories getLaboratoryCategoryId() {
        return laboratoryCategoryId;
    }

    public void setLaboratoryCategoryId(LaboratoryCategories laboratoryCategoryId) {
        this.laboratoryCategoryId = laboratoryCategoryId;
    }
}
