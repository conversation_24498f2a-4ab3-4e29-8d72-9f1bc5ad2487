spring.jpa.hibernate.ddl-auto=update


spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# MySQL datasource configuration
spring.datasource.url=**************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Root123456789
# Show SQL statements in the console

spring.jpa.show-sql=true

# Disable Spring Security basic authentication
#security.basic.enabled=false


# Set the MySQL dialect
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect


# Configure max file size for multipart file upload
spring.servlet.multipart.max-file-size=1024MB
spring.servlet.multipart.max-request-size=1024MB


# Set the server port
server.port=8185


# Set a custom username and password
#spring.security.user.name=admin
#spring.security.user.password=adminpassword

#Set a local path for image saving
upload.path=C:/mydent