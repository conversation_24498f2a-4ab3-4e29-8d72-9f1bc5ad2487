package com.navitsa.mydent.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name= "services")
public class Services{
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name= "service_id")
	private Integer serviceId;
	
	@Column(name= "service")
	private String service;
	
    
    public Services() {
    	
    }


	public Services(Integer serviceId, String service) {
		super();
		this.serviceId = serviceId;
		this.service = service;
	}


	public Integer getServiceId() {
		return serviceId;
	}


	public void setServiceId(Integer serviceId) {
		this.serviceId = serviceId;
	}


	public String getService() {
		return service;
	}


	public void setService(String service) {
		this.service = service;
	}
	
	
}

