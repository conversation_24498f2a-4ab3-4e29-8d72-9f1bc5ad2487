//package com.navitsa.mydent.controller;
//
//import java.util.List;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.navitsa.mydent.entity.Company;
//import com.navitsa.mydent.services.CompanyService;
//
//@RestController
//public class ServicesController {
//	
//	@Autowired
//	private CompanyService companyService;
//	
//	
//	@PostMapping("/saveCompany")
//	public Company saveCompany(@RequestBody Company company) {
//		return companyService.saveCompany(company);
//	}
//	
//	@GetMapping(value = "/companyList")
//	public List<Company> getAllCompanies() {
//		return companyService.findAllCompanies();
//	}
//	
//	@PutMapping("/updateCompany/{id}")
//	public ResponseEntity<Company> updateCompany(@PathVariable int id,@RequestBody Company companyDetails){
//		
//		return ResponseEntity.ok(companyService.updateCompany(id,companyDetails));
//	
//	}
//	
//	@GetMapping("/getCompanyById/{id}")
//	public Company getCompanyeById(@PathVariable int id) {
//		return companyService.getCompanyById(id);
//	}
//	
//}
