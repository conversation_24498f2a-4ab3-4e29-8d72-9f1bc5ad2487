package com.navitsa.mydent.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.navitsa.mydent.entity.ClinicServicesCategory;



@Repository
public interface ClinicServicesCategoryRepositories extends JpaRepository<ClinicServicesCategory, Integer> {
    ClinicServicesCategory findByClinicServiceCategoryName(String clinicServiceCategoryName);
}

