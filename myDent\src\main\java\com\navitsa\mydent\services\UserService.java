package com.navitsa.mydent.services;

import com.navitsa.mydent.dtos.CredentialsDto;
import com.navitsa.mydent.dtos.SignUpDto;
import com.navitsa.mydent.dtos.UserDto;
import com.navitsa.mydent.entity.Company;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.enums.UserStatus;
import com.navitsa.mydent.exceptions.AppException;
import com.navitsa.mydent.repositories.ClinicRepository;
import com.navitsa.mydent.repositories.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.nio.CharBuffer;
import java.util.Optional;
import java.util.UUID;

@Service
public class UserService {

    private final UserRepository userRepository;

    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository,PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    private UserDto toUserDto(User user) {
        UserDto userDto = new UserDto();
        userDto.setId(user.getUserId());
        userDto.setUsername(user.getUsername());
        userDto.setFirstName(user.getFirstName());
        userDto.setLastName(user.getLastName());
        userDto.setCompanyId(user.getCompanyId().getCompanyId());
        userDto.setUserCategoryId(user.getUserCategoryId());
        userDto.setUserVerified(user.getUserVerified());
        // Map other fields as needed
        return userDto;
    }

    private User signUpDtoToUser(SignUpDto signUpDto) {
        User user = new User();
        Company company = new Company();
        company.setCompanyId(1);

        user.setUsername(signUpDto.username());
        user.setFirstName(signUpDto.firstName());
        user.setLastName(signUpDto.lastName());
        user.setCompanyId(company);
        user.setUserCategoryId(signUpDto.userCategoryId());
        user.setUserVerified(UserStatus.INACTIVE);
        System.out.println("Signup User :" +user);
        String verificationToken = UUID.randomUUID().toString();
        user.setVerificationToken(verificationToken);
        
        // Map other fields as needed
        return user;
    }

    public UserDto login(CredentialsDto credentialsDto) {
        User user = userRepository.findByUsername(credentialsDto.username())
                .orElseThrow(() -> new AppException("Unknown user", HttpStatus.NOT_FOUND));

        if (user.getUserVerified()== UserStatus.INACTIVE){
            throw new AppException("User not verified", HttpStatus.BAD_REQUEST);
        }

        if(user.getUserVerified() == UserStatus.ACTIVE){
            if (passwordEncoder.matches(CharBuffer.wrap(credentialsDto.password()), user.getPassword())) {
                return toUserDto(user);
            }
        }

        throw new AppException("Invalid password", HttpStatus.BAD_REQUEST);
    }

    public UserDto register(SignUpDto userDto) {

        Optional<User> optionalUser = userRepository.findByUsername(userDto.username());
        if (optionalUser.isPresent()) {
            throw new AppException("Login already exists", HttpStatus.BAD_REQUEST);
        }

        User user = signUpDtoToUser(userDto);
        user.setPassword(passwordEncoder.encode(CharBuffer.wrap(userDto.password())));
        
        User savedUser = userRepository.save(user);

        return toUserDto(savedUser);
    }


    public User saveUser(User user){
        return userRepository.save(user);
    }

    public UserDto findByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new AppException("Unknown user", HttpStatus.NOT_FOUND));
        return toUserDto(user);
    }

    public boolean usernameExists(String username) {
        Optional<User> existingUser = userRepository.findByUsername(username);
        return existingUser.isPresent();
    }
    
    public String verifyEmail(String verificationToken) {
        User user = userRepository.findByVerificationToken(verificationToken);

        if (user == null) {
            return "Invalid or expired verification token.";
        }

        if (user.getUserVerified() == UserStatus.ACTIVE) {
            return "Your email is already verified.";
        }

        user.setUserVerified(UserStatus.ACTIVE);
        userRepository.save(user);

        return "Email verified successfully!";
    }

}
