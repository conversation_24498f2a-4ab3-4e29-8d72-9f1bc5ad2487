package com.navitsa.mydent.services;

import com.navitsa.mydent.dtos.CredentialsDto;
import com.navitsa.mydent.dtos.SignUpDto;
import com.navitsa.mydent.dtos.UserDto;
import com.navitsa.mydent.entity.Company;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.entity.UserCategory;
import com.navitsa.mydent.enums.UserStatus;
import com.navitsa.mydent.exceptions.AppException;
import com.navitsa.mydent.repositories.ClinicRepository;
import com.navitsa.mydent.repositories.UserCategoryRepository;
import com.navitsa.mydent.repositories.UserRepository;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.nio.CharBuffer;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
public class UserService {

    private final UserRepository userRepository;

    private final PasswordEncoder passwordEncoder;

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserCategoryRepository userCategoryRepository;

    @Autowired
    public UserService(UserRepository userRepository,PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    private UserDto toUserDto(User user) {
        UserDto userDto = new UserDto();
        userDto.setId(user.getUserId());
        userDto.setUsername(user.getUsername());
        userDto.setFirstName(user.getFirstName());
        userDto.setLastName(user.getLastName());
        userDto.setCompanyId(user.getCompanyId().getCompanyId());
        userDto.setUserCategoryId(user.getUserCategoryId());
        userDto.setUserVerified(user.getUserVerified());
        // Map other fields as needed
        return userDto;
    }

    private User signUpDtoToUser(SignUpDto signUpDto) {
        User user = new User();
        Company company = new Company();
        company.setCompanyId(1);

        user.setUsername(signUpDto.username());
        user.setFirstName(signUpDto.firstName());
        user.setLastName(signUpDto.lastName());
        user.setCompanyId(company);
        user.setUserCategoryId(signUpDto.userCategoryId());
        user.setUserVerified(UserStatus.INACTIVE);
        System.out.println("Signup User :" +user);
        String verificationToken = UUID.randomUUID().toString();
        user.setVerificationToken(verificationToken);
        
        // Map other fields as needed
        return user;
    }

    public UserDto login(CredentialsDto credentialsDto) {
        User user = userRepository.findByUsername(credentialsDto.username())
                   .orElseThrow(() -> new AppException("Email address not found. Please check your email or register first.", HttpStatus.NOT_FOUND));

        if (user.getUserVerified()== UserStatus.INACTIVE){
            throw new AppException("Your account is not yet activated. Please verify your email address first, or contact support if you have already verified your email.", HttpStatus.BAD_REQUEST);
        }

        if(user.getUserVerified() == UserStatus.ACTIVE){
            if (passwordEncoder.matches(CharBuffer.wrap(credentialsDto.password()), user.getPassword())) {
                return toUserDto(user);
            }

            System.out.println(user+ "hello");
        }

           throw new AppException("Incorrect password. Please try again.", HttpStatus.BAD_REQUEST);
    }

    public UserDto register(SignUpDto userDto) {

        Optional<User> optionalUser = userRepository.findByUsername(userDto.username());
        if (optionalUser.isPresent()) {
            throw new AppException("Login already exists", HttpStatus.BAD_REQUEST);
        }

        User user = signUpDtoToUser(userDto);
        user.setPassword(passwordEncoder.encode(userDto.password()));
        
        User savedUser = userRepository.save(user);

        return toUserDto(savedUser);
    }


    public User saveUser(User user){
        return userRepository.save(user);
    }

    public UserDto findByUsername(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new AppException("Unknown user", HttpStatus.NOT_FOUND));
        return toUserDto(user);
    }

    public boolean usernameExists(String username) {
        Optional<User> existingUser = userRepository.findByUsername(username);
        return existingUser.isPresent();
    }
    
    public String verifyEmail(String verificationToken) {
        User user = userRepository.findByVerificationToken(verificationToken);

        if (user == null) {
            return "Invalid or expired verification token.";
        }

        if (user.getUserVerified() == UserStatus.ACTIVE) {
            return "Your email is already verified.";
        }

        user.setUserVerified(UserStatus.ACTIVE);
        userRepository.save(user);

        return "Email verified successfully!";
    }


    public ResponseEntity<Map<String, String>> passwordChange(String email) {
        Map<String, String> response = new HashMap<>();
        Optional<User> optionalUser = userRepository.findByUsername(email); // Use correct method name

        if (optionalUser.isEmpty()) {
            response.put("status", "false");
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }

        User existingUser = optionalUser.get();

        String verificationToken = UUID.randomUUID().toString();
        existingUser.setUserVerified(UserStatus.INACTIVE);
        existingUser.setVerificationToken(verificationToken);
        userRepository.save(existingUser);

        String verificationTokenWithUserType = verificationToken + "&userType=" + existingUser.getUserCategoryId().getUserCategory();

        try {
            String verificationLink = "http://localhost:4200/user/password-resect?token=" + verificationTokenWithUserType;
            System.out.println(" Verification Link: " + verificationLink);

            CompletableFuture.runAsync(() -> {
                try {
                    emailService.forgetPassword(
                            existingUser.getUsername(),
                            existingUser.getFirstName(),
                            verificationLink,
                            existingUser.getUserCategoryId().getUserCategory()
                    );
                    System.out.println(" Verification email sent to: " + existingUser.getUsername());
                } catch (Exception e) {
                    System.err.println(" Failed to send verification email to: " + existingUser.getEmail());
                    e.printStackTrace();
                }
            });

            response.put("status", "true");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            System.err.println(" Unexpected error verification url: " + existingUser.getEmail());
            e.printStackTrace();
            response.put("status", "false");
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(response);        }
    }

    public ResponseEntity<Map<String, String>> passwordChangeSave(String password, String token) {
        Map<String, String> response = new HashMap<>();
        try {
            User user = userRepository.findByVerificationToken(token);

            if (user == null) {
                response.put("status", "false");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }
            System.out.println(password);
            user.setPassword(passwordEncoder.encode(password));
            System.out.println(user);
            userRepository.save(user);

            response.put("status", "true");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();
            response.put("status", "false");
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(response);
        }
    }


}
