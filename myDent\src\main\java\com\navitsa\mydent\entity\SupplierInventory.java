package com.navitsa.mydent.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import java.io.Serializable;
import java.util.Arrays;


@Entity
@Table(name = "supplier_inventory")
public class SupplierInventory implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "supplier_inventory_id")
    private Integer supplierInventoryId;

    @ManyToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "supplier_id", referencedColumnName = "supplier_id")
    private Supplier supplierId;

    @Column(name = "category")
    private String category;

    @Column(name = "sub_category")
    private String subCategory;

    @Column(name = "description")
    private String description;

    @Column(name = "price")
    private double price;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "item_status")
    private String itemStatus;

    @Column(name = "item_image", columnDefinition = "BLOB")
    private byte[] itemImage;

    public SupplierInventory() {
    }

    public SupplierInventory(Supplier supplierId, String category, String subCategory, String description, double price, Integer quantity, String itemStatus, byte[] itemImage) {
        this.supplierId = supplierId;
        this.category = category;
        this.subCategory = subCategory;
        this.description = description;
        this.price = price;
        this.quantity = quantity;
        this.itemStatus = itemStatus;
        this.itemImage = itemImage;
    }

    public SupplierInventory(Integer supplierInventoryId, Supplier supplierId, String category, String subCategory,
                             String description, double price, Integer quantity, String itemStatus, byte[] itemImage) {
        super();
        this.supplierInventoryId = supplierInventoryId;
        this.supplierId = supplierId;
        this.category = category;
        this.subCategory = subCategory;
        this.description = description;
        this.price = price;
        this.quantity = quantity;
        this.itemStatus = itemStatus;
        this.itemImage = itemImage;
    }

    public Integer getSupplierInventoryId() {
        return supplierInventoryId;
    }

    public void setSupplierInventoryId(Integer supplierInventoryId) {
        this.supplierInventoryId = supplierInventoryId;
    }

    public Supplier getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Supplier supplierId) {
        this.supplierId = supplierId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(String itemStatus) {
        this.itemStatus = itemStatus;
    }

    public byte[] getItemImage() {
        return itemImage;
    }

    public void setItemImage(byte[] itemImage) {
        this.itemImage = itemImage;
    }

    @Override
    public String toString() {
        return "SupplierInventory{" +
                "supplierInventoryId=" + supplierInventoryId +
                ", supplierId=" + supplierId +
                ", category='" + category + '\'' +
                ", subCategory='" + subCategory + '\'' +
                ", description='" + description + '\'' +
                ", price=" + price +
                ", quantity=" + quantity +
                ", itemStatus='" + itemStatus + '\'' +
                ", itemImage=" + Arrays.toString(itemImage) +
                '}';
    }
}
