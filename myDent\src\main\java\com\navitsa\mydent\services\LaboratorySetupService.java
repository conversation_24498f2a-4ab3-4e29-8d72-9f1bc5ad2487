package com.navitsa.mydent.services;

import com.navitsa.mydent.entity.LaboratorySetup;
import com.navitsa.mydent.entity.Laboratory;
import com.navitsa.mydent.entity.User;
import com.navitsa.mydent.repositories.LaboratorySetupRepository;
import com.navitsa.mydent.repositories.LaboratoryRepository;
import com.navitsa.mydent.repositories.UserRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class LaboratorySetupService {

    private final LaboratorySetupRepository laboratorySetupRepository;
    private final LaboratoryRepository laboratoryRepository;
    private final UserRepository userRepository;

    @Autowired
    public LaboratorySetupService(LaboratorySetupRepository laboratorySetupRepository, LaboratoryRepository laboratoryRepository, UserRepository userRepository) {
        this.laboratorySetupRepository = laboratorySetupRepository;
        this.laboratoryRepository = laboratoryRepository;
        this.userRepository = userRepository;
    }

    //Save laboratory setup
    public LaboratorySetup saveLaboratorySetup(int userId, LaboratorySetup laboratorySetup) {
        try {
            // Fetch the user and associated laboratory
            User user = userRepository.findByUserId(userId);
            if (user == null) {
                throw new IllegalArgumentException("User with ID " + userId + " not found.");
            }

            Laboratory laboratory = laboratoryRepository.findByUserId(user);
            if (laboratory == null) {
                throw new IllegalArgumentException("Laboratory for user with ID " + userId + " not found.");
            }

            // Set the laboratory and other required fields
            laboratorySetup.setLaboratoryId(laboratory);

            // Save the laboratory setup
            return laboratorySetupRepository.save(laboratorySetup);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while saving the laboratory setup: " + e.getMessage());
        }
    }


    //Get all laboratory setups
    public List<LaboratorySetup> getAllLaboratorySetups() {
        try {
            return laboratorySetupRepository.findAll();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the list of laboratory setups.");
        }
    }

    //Get laboratory setup by ID
    public LaboratorySetup getLaboratorySetupById(int id) {
        try {
            return laboratorySetupRepository.findById(id).orElse(null);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory setup.");
        }
    }
    
    public List<LaboratorySetup> getLaboratorySetupByUserId(int id) {
        try {
        	User user = userRepository.findByUserId(id);
        	Laboratory laboratory = laboratoryRepository.findByUserId(user);
            return laboratorySetupRepository.findByLaboratoryId(laboratory);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("An error occurred while retrieving the laboratory setup.");
        }
    }
}
