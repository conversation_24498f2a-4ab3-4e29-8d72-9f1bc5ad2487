package com.navitsa.mydent.controller;

import com.navitsa.mydent.config.UserAuthenticationProvider;
import com.navitsa.mydent.dtos.CredentialsDto;
import com.navitsa.mydent.dtos.SignUpDto;
import com.navitsa.mydent.dtos.UserDto;
import com.navitsa.mydent.entity.UserTemp;
import com.navitsa.mydent.enums.UserTempType;
import com.navitsa.mydent.services.UserService;
import com.navitsa.mydent.services.UserTempServices;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

@RestController
public class AuthController {
    private final UserService userService;
    private final UserTempServices userTempServices;
    private final UserAuthenticationProvider userAuthenticationProvider;

    @Autowired
    public AuthController(UserService userService, UserTempServices userTempServices, UserAuthenticationProvider userAuthenticationProvider) {
        this.userService = userService;
        this.userTempServices = userTempServices;
        this.userAuthenticationProvider = userAuthenticationProvider;
    }

    @PostMapping("/login")
    public ResponseEntity<UserDto> login(@RequestBody @Valid CredentialsDto credentialsDto) {
        UserDto userDto = userService.login(credentialsDto);
        userDto.setToken(userAuthenticationProvider.createToken(userDto));
        return ResponseEntity.ok(userDto);
    }

    @PostMapping("/register")
    public ResponseEntity<UserDto> register(@RequestBody @Valid SignUpDto user) {
        UserDto createdUser = userService.register(user);
        createdUser.setToken(userAuthenticationProvider.createToken(createdUser));
        return ResponseEntity.created(URI.create("/users/" + createdUser.getId())).body(createdUser);
    }

    @GetMapping("/check-username")
    public ResponseEntity<Boolean> checkUsernameExists(@RequestParam String username) {
        boolean isUsernameExists = userService.usernameExists(username);
        return ResponseEntity.ok(isUsernameExists);
    }

    @GetMapping("/auth/user_temp_avb")
    public ResponseEntity<UserTemp> checkUserTemp(@RequestParam("userTempEmail") String userTempEmail) {
        UserTemp userTemp = userTempServices.getUserTempByEmail(userTempEmail);
        return ResponseEntity.ok(userTemp);
    }

    @PostMapping("/auth/user_temp_register")
    public ResponseEntity<UserTemp> registerUserTemp(@RequestBody @Valid UserTemp userTemp) {
        UserTemp savedUserTemp = userTempServices.saveUserAsTemp(userTemp);
        return ResponseEntity.ok(savedUserTemp);
    }


}
